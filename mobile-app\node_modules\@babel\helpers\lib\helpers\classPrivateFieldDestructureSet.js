"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _classPrivateFieldDestructureSet;
var _classApplyDescriptorDestructureSet = require("classApplyDescriptorDestructureSet");
var _classPrivateFieldGet = require("classPrivateFieldGet2");
function _classPrivateFieldDestructureSet(receiver, privateMap) {
  var descriptor = _classPrivateFieldGet(privateMap, receiver);
  return _classApplyDescriptorDestructureSet(receiver, descriptor);
}

//# sourceMappingURL=classPrivateFieldDestructureSet.js.map
