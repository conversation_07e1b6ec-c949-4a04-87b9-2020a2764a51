{"version": 3, "names": ["path", "require", "fs", "os", "findCacheDir", "FILENAME", "process", "env", "BABEL_CACHE_PATH", "exports", "initializeCacheFilename", "join", "name", "homedir", "tmpdir", "babel", "version", "getEnv", "data", "cacheDirty", "cacheDisabled", "isCacheDisabled", "_process$env$BABEL_DI", "BABEL_DISABLE_CACHE", "save", "serialised", "JSON", "stringify", "err", "message", "console", "error", "stack", "v", "w", "split", "versions", "node", "mkdirSync", "sync", "dirname", "recursive", "writeFileSync", "e", "code", "warn", "load", "on", "nextTick", "cacheContent", "readFileSync", "parse", "_unused", "get", "set<PERSON>irty", "clear"], "sources": ["../../src/worker/cache.cts"], "sourcesContent": ["\"use strict\";\n\nconst path = require(\"node:path\");\nconst fs = require(\"node:fs\");\nconst os = require(\"node:os\");\nconst findCacheDir = require(\"find-cache-dir\");\n\nlet FILENAME = process.env.BABEL_CACHE_PATH;\n\n// This function needs to be exported before requiring ./babel-core, because\n// there is a circular dependency between these two files.\nexports.initializeCacheFilename = function () {\n  FILENAME ||= path.join(\n    findCacheDir({ name: \"@babel/register\" }) || os.homedir() || os.tmpdir(),\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    `.babel.${babel.version}.${babel.getEnv()}.json`,\n  );\n};\n\nconst babel = require(\"./babel-core.cjs\");\n\nlet data = {};\n\nlet cacheDirty = false;\n\nlet cacheDisabled = false;\n\nfunction isCacheDisabled() {\n  return process.env.BABEL_DISABLE_CACHE ?? cacheDisabled;\n}\n\nexports.save = save;\n/**\n * Write stringified cache to disk.\n */\nfunction save() {\n  if (isCacheDisabled() || !cacheDirty) return;\n  cacheDirty = false;\n\n  let serialised = \"{}\";\n\n  try {\n    serialised = JSON.stringify(data);\n  } catch (err) {\n    if (err.message === \"Invalid string length\") {\n      err.message = \"Cache too large so it's been cleared.\";\n      console.error(err.stack);\n    } else {\n      throw err;\n    }\n  }\n\n  try {\n    fs.mkdirSync(path.dirname(FILENAME), { recursive: true });\n    fs.writeFileSync(FILENAME, serialised);\n  } catch (e) {\n    switch (e.code) {\n      // workaround https://github.com/nodejs/node/issues/31481\n      // todo: remove the ENOENT error check when we drop node.js 13 support\n      case \"ENOENT\":\n      case \"EACCES\":\n      case \"EPERM\":\n        console.warn(\n          `Babel could not write cache to file: ${FILENAME}\ndue to a permission issue. Cache is disabled.`,\n        );\n        cacheDisabled = true;\n        break;\n      case \"EROFS\":\n        console.warn(\n          `Babel could not write cache to file: ${FILENAME}\nbecause it resides in a readonly filesystem. Cache is disabled.`,\n        );\n        cacheDisabled = true;\n        break;\n      default:\n        throw e;\n    }\n  }\n}\n\n/**\n * Load cache from disk and parse.\n */\n\nexports.load = function load() {\n  if (isCacheDisabled()) {\n    data = {};\n    return;\n  }\n\n  process.on(\"exit\", save);\n  process.nextTick(save);\n\n  let cacheContent;\n\n  try {\n    cacheContent = fs.readFileSync(FILENAME, \"utf8\");\n  } catch (e) {\n    switch (e.code) {\n      // check EACCES only as fs.readFileSync will never throw EPERM on Windows\n      // https://github.com/libuv/libuv/blob/076df64dbbda4320f93375913a728efc40e12d37/src/win/fs.c#L735\n      case \"EACCES\":\n        console.warn(\n          `Babel could not read cache file: ${FILENAME}\ndue to a permission issue. Cache is disabled.`,\n        );\n        cacheDisabled = true;\n      /* fall through */\n      default:\n        return;\n    }\n  }\n\n  try {\n    data = JSON.parse(cacheContent);\n  } catch {}\n};\n\n/**\n * Retrieve data from cache.\n */\nexports.get = function get() {\n  return data;\n};\n\n/**\n * Set the cache dirty bit.\n */\nexports.setDirty = function setDirty() {\n  cacheDirty = true;\n};\n\n/**\n * Clear the cache object.\n */\nexports.clear = function clear() {\n  data = {};\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,IAAI,GAAGC,OAAO,CAAC,MAAW,CAAC;AACjC,MAAMC,EAAE,GAAGD,OAAO,CAAC,IAAS,CAAC;AAC7B,MAAME,EAAE,GAAGF,OAAO,CAAC,IAAS,CAAC;AAC7B,MAAMG,YAAY,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAE9C,IAAII,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB;AAI3CC,OAAO,CAACC,uBAAuB,GAAG,YAAY;EAC5CL,QAAQ,KAARA,QAAQ,GAAKL,IAAI,CAACW,IAAI,CACpBP,YAAY,CAAC;IAAEQ,IAAI,EAAE;EAAkB,CAAC,CAAC,IAAIT,EAAE,CAACU,OAAO,CAAC,CAAC,IAAIV,EAAE,CAACW,MAAM,CAAC,CAAC,EAExE,UAAUC,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,CAAC,CAAC,OAC3C,CAAC;AACH,CAAC;AAED,MAAMF,KAAK,GAAGd,OAAO,CAAC,kBAAkB,CAAC;AAEzC,IAAIiB,IAAI,GAAG,CAAC,CAAC;AAEb,IAAIC,UAAU,GAAG,KAAK;AAEtB,IAAIC,aAAa,GAAG,KAAK;AAEzB,SAASC,eAAeA,CAAA,EAAG;EAAA,IAAAC,qBAAA;EACzB,QAAAA,qBAAA,GAAOhB,OAAO,CAACC,GAAG,CAACgB,mBAAmB,YAAAD,qBAAA,GAAIF,aAAa;AACzD;AAEAX,OAAO,CAACe,IAAI,GAAGA,IAAI;AAInB,SAASA,IAAIA,CAAA,EAAG;EACd,IAAIH,eAAe,CAAC,CAAC,IAAI,CAACF,UAAU,EAAE;EACtCA,UAAU,GAAG,KAAK;EAElB,IAAIM,UAAU,GAAG,IAAI;EAErB,IAAI;IACFA,UAAU,GAAGC,IAAI,CAACC,SAAS,CAACT,IAAI,CAAC;EACnC,CAAC,CAAC,OAAOU,GAAG,EAAE;IACZ,IAAIA,GAAG,CAACC,OAAO,KAAK,uBAAuB,EAAE;MAC3CD,GAAG,CAACC,OAAO,GAAG,uCAAuC;MACrDC,OAAO,CAACC,KAAK,CAACH,GAAG,CAACI,KAAK,CAAC;IAC1B,CAAC,MAAM;MACL,MAAMJ,GAAG;IACX;EACF;EAEA,IAAI;IACF,GAAAK,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAA5B,OAAA,CAAA8B,QAAA,CAAAC,IAAA,aAAAnC,EAAA,CAAAoC,SAAA,GAAArC,OAAA,aAAAsC,IAAA,EAAavC,IAAI,CAACwC,OAAO,CAACnC,QAAQ,CAAC,EAAE;MAAEoC,SAAS,EAAE;IAAK,CAAC,CAAC;IACzDvC,EAAE,CAACwC,aAAa,CAACrC,QAAQ,EAAEoB,UAAU,CAAC;EACxC,CAAC,CAAC,OAAOkB,CAAC,EAAE;IACV,QAAQA,CAAC,CAACC,IAAI;MAGZ,KAAK,QAAQ;MACb,KAAK,QAAQ;MACb,KAAK,OAAO;QACVd,OAAO,CAACe,IAAI,CACV,wCAAwCxC,QAAQ;AAC1D,8CACQ,CAAC;QACDe,aAAa,GAAG,IAAI;QACpB;MACF,KAAK,OAAO;QACVU,OAAO,CAACe,IAAI,CACV,wCAAwCxC,QAAQ;AAC1D,gEACQ,CAAC;QACDe,aAAa,GAAG,IAAI;QACpB;MACF;QACE,MAAMuB,CAAC;IACX;EACF;AACF;AAMAlC,OAAO,CAACqC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;EAC7B,IAAIzB,eAAe,CAAC,CAAC,EAAE;IACrBH,IAAI,GAAG,CAAC,CAAC;IACT;EACF;EAEAZ,OAAO,CAACyC,EAAE,CAAC,MAAM,EAAEvB,IAAI,CAAC;EACxBlB,OAAO,CAAC0C,QAAQ,CAACxB,IAAI,CAAC;EAEtB,IAAIyB,YAAY;EAEhB,IAAI;IACFA,YAAY,GAAG/C,EAAE,CAACgD,YAAY,CAAC7C,QAAQ,EAAE,MAAM,CAAC;EAClD,CAAC,CAAC,OAAOsC,CAAC,EAAE;IACV,QAAQA,CAAC,CAACC,IAAI;MAGZ,KAAK,QAAQ;QACXd,OAAO,CAACe,IAAI,CACV,oCAAoCxC,QAAQ;AACtD,8CACQ,CAAC;QACDe,aAAa,GAAG,IAAI;MAEtB;QACE;IACJ;EACF;EAEA,IAAI;IACFF,IAAI,GAAGQ,IAAI,CAACyB,KAAK,CAACF,YAAY,CAAC;EACjC,CAAC,CAAC,OAAAG,OAAA,EAAM,CAAC;AACX,CAAC;AAKD3C,OAAO,CAAC4C,GAAG,GAAG,SAASA,GAAGA,CAAA,EAAG;EAC3B,OAAOnC,IAAI;AACb,CAAC;AAKDT,OAAO,CAAC6C,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACrCnC,UAAU,GAAG,IAAI;AACnB,CAAC;AAKDV,OAAO,CAAC8C,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;EAC/BrC,IAAI,GAAG,CAAC,CAAC;AACX,CAAC", "ignoreList": []}