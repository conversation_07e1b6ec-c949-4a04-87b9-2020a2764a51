{"version": 3, "names": ["useTheme", "React", "StatusBar", "StyleSheet", "ModalStatusBarManager", "dark", "layout", "insets", "style", "darkTheme", "overlapping", "setOverlapping", "useState", "scale", "width", "offset", "top", "flattenedStyle", "flatten", "translateY", "transform", "find", "s", "undefined", "useEffect", "listener", "value", "sub", "addListener", "removeListener", "darkContent"], "sourceRoot": "../../../src", "sources": ["views/ModalStatusBarManager.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,UAAU,QAAQ,cAAc;AAYpD,eAAe,SAASC,qBAAqB,OAKnC;EAAA;EAAA,IALoC;IAC5CC,IAAI;IACJC,MAAM;IACNC,MAAM;IACNC;EACK,CAAC;EACN,MAAM;IAAEH,IAAI,EAAEI;EAAU,CAAC,GAAGT,QAAQ,EAAE;EACtC,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGV,KAAK,CAACW,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAGP,MAAM,CAACQ,KAAK;EACnC,MAAMC,MAAM,GAAG,CAACR,MAAM,CAACS,GAAG,GAAG,EAAE,IAAIH,KAAK;EAExC,MAAMI,cAAc,GAAGd,UAAU,CAACe,OAAO,CAACV,KAAK,CAAC;EAChD,MAAMW,UAAU,GAAGF,cAAc,aAAdA,cAAc,gDAAdA,cAAc,CAAEG,SAAS,oFAAzB,sBAA2BC,IAAI,CAC/CC,CAAM,IAAKA,CAAC,CAACH,UAAU,KAAKI,SAAS,CACvC,2DAFkB,uBAEhBJ,UAAU;EAEblB,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpB,MAAMC,QAAQ,GAAG,SAAkC;MAAA,IAAjC;QAAEC;MAAyB,CAAC;MAC5Cf,cAAc,CAACe,KAAK,GAAGX,MAAM,CAAC;IAChC,CAAC;IAED,MAAMY,GAAG,GAAGR,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,WAAW,CAACH,QAAQ,CAAC;IAE7C,OAAO,MAAMN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEU,cAAc,CAACF,GAAG,CAAC;EAC9C,CAAC,EAAE,CAACZ,MAAM,EAAEI,UAAU,CAAC,CAAC;EAExB,MAAMW,WAAW,GAAGzB,IAAI,IAAI,CAACI,SAAS;EAEtC,oBACE,oBAAC,SAAS;IACR,QAAQ;IACR,QAAQ,EAAEC,WAAW,IAAIoB,WAAW,GAAG,cAAc,GAAG;EAAgB,EACxE;AAEN"}