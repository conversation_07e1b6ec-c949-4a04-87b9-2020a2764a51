{"version": 3, "names": ["DirectoryAlreadyExistsError", "CLIError", "constructor", "directory"], "sources": ["../../../../src/commands/init/errors/DirectoryAlreadyExistsError.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\n\nexport default class DirectoryAlreadyExistsError extends CLIError {\n  constructor(directory: string) {\n    super(\n      `Cannot initialize new project because directory \"${directory}\" already exists.`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEe,MAAMA,2BAA2B,SAASC,oBAAQ,CAAC;EAChEC,WAAW,CAACC,SAAiB,EAAE;IAC7B,KAAK,CACF,oDAAmDA,SAAU,mBAAkB,CACjF;EACH;AACF;AAAC"}