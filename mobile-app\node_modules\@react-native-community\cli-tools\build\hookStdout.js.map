{"version": 3, "names": ["hookStdout", "callback", "old_write", "process", "stdout", "write", "str", "apply", "arguments"], "sources": ["../src/hookStdout.ts"], "sourcesContent": ["// https://gist.github.com/pguillory/729616\n\nfunction hookStdout(callback: Function) {\n  let old_write = process.stdout.write;\n\n  // @ts-ignore\n  process.stdout.write = ((write: any) =>\n    function (str: string) {\n      write.apply(process.stdout, arguments);\n      callback(str);\n    })(process.stdout.write);\n\n  return () => {\n    process.stdout.write = old_write;\n  };\n}\n\nexport default hookStdout;\n"], "mappings": ";;;;;;AAAA;;AAEA,SAASA,UAAU,CAACC,QAAkB,EAAE;EACtC,IAAIC,SAAS,GAAGC,OAAO,CAACC,MAAM,CAACC,KAAK;;EAEpC;EACAF,OAAO,CAACC,MAAM,CAACC,KAAK,GAAG,CAAEA,KAAU,IACjC,UAAUC,GAAW,EAAE;IACrBD,KAAK,CAACE,KAAK,CAACJ,OAAO,CAACC,MAAM,EAAEI,SAAS,CAAC;IACtCP,QAAQ,CAACK,GAAG,CAAC;EACf,CAAC,EAAEH,OAAO,CAACC,MAAM,CAACC,KAAK,CAAC;EAE1B,OAAO,MAAM;IACXF,OAAO,CAACC,MAAM,CAACC,KAAK,GAAGH,SAAS;EAClC,CAAC;AACH;AAAC,eAEcF,UAAU;AAAA"}