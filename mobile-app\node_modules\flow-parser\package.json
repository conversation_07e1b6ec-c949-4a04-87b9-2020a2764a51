{"name": "flow-parser", "version": "0.206.0", "description": "JavaScript parser written in OCaml. Produces ESTree AST", "homepage": "https://flow.org", "license": "MIT", "author": {"name": "Flow Team", "email": "<EMAIL>"}, "files": ["flow_parser.js"], "main": "flow_parser.js", "repository": {"type": "git", "url": "https://github.com/facebook/flow.git"}, "scripts": {"test": "node test/run_tests.js", "prepublish": "make js"}, "dependencies": {}, "devDependencies": {"ast-types": "^0.15.2", "chalk": "^4.1.2", "esprima-fb": "15001.1001.0-dev-harmony-fb", "minimist": ">=1.2.6"}, "engines": {"node": ">=0.4.0"}}