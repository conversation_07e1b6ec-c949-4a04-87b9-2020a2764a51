{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "getName", "key", "t", "isIdentifier", "name", "value", "toString", "_default", "exports", "default", "declare", "api", "assertVersion", "visitor", "ObjectExpression", "path", "node", "plainProps", "properties", "filter", "prop", "isSpreadElement", "computed", "alreadySeenData", "Object", "create", "alreadySeenGetters", "alreadySeenSetters", "isDuplicate", "kind", "stringLiteral"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nfunction getName(\n  key: t.Identifier | t.StringLiteral | t.NumericLiteral | t.BigIntLiteral,\n) {\n  if (t.isIdentifier(key)) {\n    return key.name;\n  }\n  return key.value.toString();\n}\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-duplicate-keys\",\n\n    visitor: {\n      ObjectExpression(path) {\n        const { node } = path;\n        const plainProps = node.properties.filter(\n          prop => !t.isSpreadElement(prop) && !prop.computed,\n        ) as (t.ObjectMethod | t.ObjectProperty)[];\n\n        // A property is a duplicate key if:\n        // * the property is a data property, and is preceded by a data,\n        //   getter, or setter property of the same name.\n        // * the property is a getter property, and is preceded by a data or\n        //   getter property of the same name.\n        // * the property is a setter property, and is preceded by a data or\n        //   setter property of the same name.\n\n        const alreadySeenData = Object.create(null);\n        const alreadySeenGetters = Object.create(null);\n        const alreadySeenSetters = Object.create(null);\n\n        for (const prop of plainProps) {\n          const name = getName(\n            // prop must be non-computed\n            prop.key as\n              | t.Identifier\n              | t.StringLiteral\n              | t.NumericLiteral\n              | t.BigIntLiteral,\n          );\n          let isDuplicate = false;\n          // @ts-expect-error prop.kind is not defined in ObjectProperty\n          switch (prop.kind) {\n            case \"get\":\n              if (alreadySeenData[name] || alreadySeenGetters[name]) {\n                isDuplicate = true;\n              }\n              alreadySeenGetters[name] = true;\n              break;\n            case \"set\":\n              if (alreadySeenData[name] || alreadySeenSetters[name]) {\n                isDuplicate = true;\n              }\n              alreadySeenSetters[name] = true;\n              break;\n            default:\n              if (\n                alreadySeenData[name] ||\n                alreadySeenGetters[name] ||\n                alreadySeenSetters[name]\n              ) {\n                isDuplicate = true;\n              }\n              alreadySeenData[name] = true;\n          }\n\n          if (isDuplicate) {\n            // Rely on the computed properties transform to split the property\n            // assignment out of the object literal.\n            prop.computed = true;\n            prop.key = t.stringLiteral(name);\n          }\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,SAASE,OAAOA,CACdC,GAAwE,EACxE;EACA,IAAIC,WAAC,CAACC,YAAY,CAACF,GAAG,CAAC,EAAE;IACvB,OAAOA,GAAG,CAACG,IAAI;EACjB;EACA,OAAOH,GAAG,CAACI,KAAK,CAACC,QAAQ,CAAC,CAAC;AAC7B;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLR,IAAI,EAAE,0BAA0B;IAEhCS,OAAO,EAAE;MACPC,gBAAgBA,CAACC,IAAI,EAAE;QACrB,MAAM;UAAEC;QAAK,CAAC,GAAGD,IAAI;QACrB,MAAME,UAAU,GAAGD,IAAI,CAACE,UAAU,CAACC,MAAM,CACvCC,IAAI,IAAI,CAAClB,WAAC,CAACmB,eAAe,CAACD,IAAI,CAAC,IAAI,CAACA,IAAI,CAACE,QAC5C,CAA0C;QAU1C,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QAC3C,MAAMC,kBAAkB,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QAC9C,MAAME,kBAAkB,GAAGH,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QAE9C,KAAK,MAAML,IAAI,IAAIH,UAAU,EAAE;UAC7B,MAAMb,IAAI,GAAGJ,OAAO,CAElBoB,IAAI,CAACnB,GAKP,CAAC;UACD,IAAI2B,WAAW,GAAG,KAAK;UAEvB,QAAQR,IAAI,CAACS,IAAI;YACf,KAAK,KAAK;cACR,IAAIN,eAAe,CAACnB,IAAI,CAAC,IAAIsB,kBAAkB,CAACtB,IAAI,CAAC,EAAE;gBACrDwB,WAAW,GAAG,IAAI;cACpB;cACAF,kBAAkB,CAACtB,IAAI,CAAC,GAAG,IAAI;cAC/B;YACF,KAAK,KAAK;cACR,IAAImB,eAAe,CAACnB,IAAI,CAAC,IAAIuB,kBAAkB,CAACvB,IAAI,CAAC,EAAE;gBACrDwB,WAAW,GAAG,IAAI;cACpB;cACAD,kBAAkB,CAACvB,IAAI,CAAC,GAAG,IAAI;cAC/B;YACF;cACE,IACEmB,eAAe,CAACnB,IAAI,CAAC,IACrBsB,kBAAkB,CAACtB,IAAI,CAAC,IACxBuB,kBAAkB,CAACvB,IAAI,CAAC,EACxB;gBACAwB,WAAW,GAAG,IAAI;cACpB;cACAL,eAAe,CAACnB,IAAI,CAAC,GAAG,IAAI;UAChC;UAEA,IAAIwB,WAAW,EAAE;YAGfR,IAAI,CAACE,QAAQ,GAAG,IAAI;YACpBF,IAAI,CAACnB,GAAG,GAAGC,WAAC,CAAC4B,aAAa,CAAC1B,IAAI,CAAC;UAClC;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}