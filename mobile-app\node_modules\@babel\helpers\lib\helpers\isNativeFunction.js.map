{"version": 3, "names": ["_isNativeFunction", "fn", "Function", "toString", "call", "indexOf", "_e"], "sources": ["../../src/helpers/isNativeFunction.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _isNativeFunction(fn: unknown): fn is Function {\n  // Note: This function returns \"true\" for core-js functions.\n  try {\n    return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n  } catch (_e) {\n    // Firefox 31 throws when \"toString\" is applied to an HTMLElement\n    return typeof fn === \"function\";\n  }\n}\n"], "mappings": ";;;;;;AAEe,SAASA,iBAAiBA,CAACC,EAAW,EAAkB;EAErE,IAAI;IACF,OAAOC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACH,EAAE,CAAC,CAACI,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;EACnE,CAAC,CAAC,OAAOC,EAAE,EAAE;IAEX,OAAO,OAAOL,EAAE,KAAK,UAAU;EACjC;AACF", "ignoreList": []}