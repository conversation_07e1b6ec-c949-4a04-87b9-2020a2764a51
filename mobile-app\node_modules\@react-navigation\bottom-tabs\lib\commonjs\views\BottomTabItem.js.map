{"version": 3, "names": ["BottomTabBarItem", "focused", "route", "descriptor", "label", "icon", "badge", "badgeStyle", "to", "button", "children", "style", "onPress", "accessibilityRole", "rest", "Platform", "OS", "styles", "e", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "preventDefault", "accessibilityLabel", "testID", "onLongPress", "horizontal", "activeTintColor", "customActiveTintColor", "inactiveTintColor", "customInactiveTintColor", "activeBackgroundColor", "inactiveBackgroundColor", "showLabel", "allowFontScaling", "labelStyle", "iconStyle", "colors", "useTheme", "undefined", "primary", "Color", "text", "mix", "card", "hex", "renderLabel", "color", "labelBeside", "labelBeneath", "options", "tabBarLabel", "title", "name", "position", "renderIcon", "activeOpacity", "inactiveOpacity", "scene", "backgroundColor", "select", "ios", "default", "accessibilityState", "selected", "accessibilityStates", "tab", "tabLandscape", "tabPortrait", "StyleSheet", "create", "flex", "alignItems", "justifyContent", "flexDirection", "textAlign", "fontSize", "marginLeft", "marginTop", "display"], "sourceRoot": "../../../src", "sources": ["views/BottomTabItem.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAgBA;AAAsC;AAAA;AA+GvB,SAASA,gBAAgB,OAiE9B;EAAA,IAjE+B;IACvCC,OAAO;IACPC,KAAK;IACLC,UAAU;IACVC,KAAK;IACLC,IAAI;IACJC,KAAK;IACLC,UAAU;IACVC,EAAE;IACFC,MAAM,GAAG,SAOsB;MAAA,IAPrB;QACRC,QAAQ;QACRC,KAAK;QACLC,OAAO;QACPJ,EAAE;QACFK,iBAAiB;QACjB,GAAGC;MACoB,CAAC;MACxB,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIR,EAAE,EAAE;QAC/B;QACA;QACA,oBACE,6BAAC,YAAI,eACCM,IAAI;UACR,EAAE,EAAEN,EAAG;UACP,KAAK,EAAE,CAACS,MAAM,CAACR,MAAM,EAAEE,KAAK,CAAE;UAC9B,OAAO,EAAGO,CAAM,IAAK;YACnB,IACE,EAAEA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,MAAM,IAAIF,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACI,QAAQ,CAAC;YAAI;YACtDJ,CAAC,CAACT,MAAM,IAAI,IAAI,IAAIS,CAAC,CAACT,MAAM,KAAK,CAAC,CAAC,CAAC;YAAA,EACrC;cACAS,CAAC,CAACK,cAAc,EAAE;cAClBX,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGM,CAAC,CAAC;YACd;UACF;QAAE,IAEDR,QAAQ,CACJ;MAEX,CAAC,MAAM;QACL,oBACE,6BAAC,sBAAS,eACJI,IAAI;UACR,iBAAiB,EAAED,iBAAkB;UACrC,OAAO,EAAED,OAAQ;UACjB,KAAK,EAAED;QAAM,IAEZD,QAAQ,CACC;MAEhB;IACF,CAAC;IACDc,kBAAkB;IAClBC,MAAM;IACNb,OAAO;IACPc,WAAW;IACXC,UAAU;IACVC,eAAe,EAAEC,qBAAqB;IACtCC,iBAAiB,EAAEC,uBAAuB;IAC1CC,qBAAqB,GAAG,aAAa;IACrCC,uBAAuB,GAAG,aAAa;IACvCC,SAAS,GAAG,IAAI;IAChBC,gBAAgB;IAChBC,UAAU;IACVC,SAAS;IACT1B;EACK,CAAC;EACN,MAAM;IAAE2B;EAAO,CAAC,GAAG,IAAAC,gBAAQ,GAAE;EAE7B,MAAMX,eAAe,GACnBC,qBAAqB,KAAKW,SAAS,GAC/BF,MAAM,CAACG,OAAO,GACdZ,qBAAqB;EAE3B,MAAMC,iBAAiB,GACrBC,uBAAuB,KAAKS,SAAS,GACjC,IAAAE,cAAK,EAACJ,MAAM,CAACK,IAAI,CAAC,CAACC,GAAG,CAAC,IAAAF,cAAK,EAACJ,MAAM,CAACO,IAAI,CAAC,EAAE,GAAG,CAAC,CAACC,GAAG,EAAE,GACrDf,uBAAuB;EAE7B,MAAMgB,WAAW,GAAG,SAAuC;IAAA,IAAtC;MAAE9C;IAA8B,CAAC;IACpD,IAAIiC,SAAS,KAAK,KAAK,EAAE;MACvB,OAAO,IAAI;IACb;IAEA,MAAMc,KAAK,GAAG/C,OAAO,GAAG2B,eAAe,GAAGE,iBAAiB;IAE3D,IAAI,OAAO1B,KAAK,KAAK,QAAQ,EAAE;MAC7B,oBACE,6BAAC,iBAAI;QACH,aAAa,EAAE,CAAE;QACjB,KAAK,EAAE,CACLa,MAAM,CAACb,KAAK,EACZ;UAAE4C;QAAM,CAAC,EACTrB,UAAU,GAAGV,MAAM,CAACgC,WAAW,GAAGhC,MAAM,CAACiC,YAAY,EACrDd,UAAU,CACV;QACF,gBAAgB,EAAED;MAAiB,GAElC/B,KAAK,CACD;IAEX;IAEA,MAAM;MAAE+C;IAAQ,CAAC,GAAGhD,UAAU;IAC9B,MAAMO,QAAQ,GACZ,OAAOyC,OAAO,CAACC,WAAW,KAAK,QAAQ,GACnCD,OAAO,CAACC,WAAW,GACnBD,OAAO,CAACE,KAAK,KAAKb,SAAS,GAC3BW,OAAO,CAACE,KAAK,GACbnD,KAAK,CAACoD,IAAI;IAEhB,OAAOlD,KAAK,CAAC;MACXH,OAAO;MACP+C,KAAK;MACLO,QAAQ,EAAE5B,UAAU,GAAG,aAAa,GAAG,YAAY;MACnDjB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8C,UAAU,GAAG,SAAuC;IAAA,IAAtC;MAAEvD;IAA8B,CAAC;IACnD,IAAII,IAAI,KAAKmC,SAAS,EAAE;MACtB,OAAO,IAAI;IACb;IAEA,MAAMiB,aAAa,GAAGxD,OAAO,GAAG,CAAC,GAAG,CAAC;IACrC,MAAMyD,eAAe,GAAGzD,OAAO,GAAG,CAAC,GAAG,CAAC;IAEvC,oBACE,6BAAC,mBAAU;MACT,KAAK,EAAEC,KAAM;MACb,UAAU,EAAEyB,UAAW;MACvB,KAAK,EAAErB,KAAM;MACb,UAAU,EAAEC,UAAW;MACvB,aAAa,EAAEkD,aAAc;MAC7B,eAAe,EAAEC,eAAgB;MACjC,eAAe,EAAE9B,eAAgB;MACjC,iBAAiB,EAAEE,iBAAkB;MACrC,UAAU,EAAEzB,IAAK;MACjB,KAAK,EAAEgC;IAAU,EACjB;EAEN,CAAC;EAED,MAAMsB,KAAK,GAAG;IAAEzD,KAAK;IAAED;EAAQ,CAAC;EAEhC,MAAM2D,eAAe,GAAG3D,OAAO,GAC3B+B,qBAAqB,GACrBC,uBAAuB;EAE3B,OAAOxB,MAAM,CAAC;IACZD,EAAE;IACFI,OAAO;IACPc,WAAW;IACXD,MAAM;IACND,kBAAkB;IAClB;IACAX,iBAAiB,EAAEE,qBAAQ,CAAC8C,MAAM,CAAC;MAAEC,GAAG,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;IACrEC,kBAAkB,EAAE;MAAEC,QAAQ,EAAEhE;IAAQ,CAAC;IACzC;IACAiE,mBAAmB,EAAEjE,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE;IAChDU,KAAK,EAAE,CACLM,MAAM,CAACkD,GAAG,EACV;MAAEP;IAAgB,CAAC,EACnBjC,UAAU,GAAGV,MAAM,CAACmD,YAAY,GAAGnD,MAAM,CAACoD,WAAW,EACrD1D,KAAK,CACN;IACDD,QAAQ,eACN,6BAAC,cAAK,CAAC,QAAQ,QACZ8C,UAAU,CAACG,KAAK,CAAC,EACjBZ,WAAW,CAACY,KAAK,CAAC;EAGzB,CAAC,CAAC;AACJ;AAEA,MAAM1C,MAAM,GAAGqD,uBAAU,CAACC,MAAM,CAAC;EAC/BJ,GAAG,EAAE;IACHK,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE;EACd,CAAC;EACDJ,WAAW,EAAE;IACXK,cAAc,EAAE,UAAU;IAC1BC,aAAa,EAAE;EACjB,CAAC;EACDP,YAAY,EAAE;IACZM,cAAc,EAAE,QAAQ;IACxBC,aAAa,EAAE;EACjB,CAAC;EACDvE,KAAK,EAAE;IACLwE,SAAS,EAAE,QAAQ;IACnBhB,eAAe,EAAE;EACnB,CAAC;EACDV,YAAY,EAAE;IACZ2B,QAAQ,EAAE;EACZ,CAAC;EACD5B,WAAW,EAAE;IACX4B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE;EACb,CAAC;EACDtE,MAAM,EAAE;IACNuE,OAAO,EAAE;EACX;AACF,CAAC,CAAC"}