{"version": 3, "names": ["CommonActions", "React", "checkDuplicateRouteNames", "checkSerializable", "NOT_INITIALIZED_ERROR", "EnsureSingleNavigator", "findFocusedRoute", "NavigationBuilderContext", "NavigationContainerRefContext", "NavigationContext", "NavigationRouteContext", "NavigationStateContext", "UnhandledActionContext", "useChildListeners", "useEventEmitter", "useKeyedChildListeners", "useOptionsGetters", "ScheduleUpdateContext", "useSyncState", "serializableWarnings", "duplicateName<PERSON><PERSON>nings", "getPartialState", "state", "undefined", "key", "routeNames", "partialState", "stale", "routes", "map", "route", "BaseNavigationContainer", "forwardRef", "ref", "initialState", "onStateChange", "onUnhandledAction", "independent", "children", "parent", "useContext", "isDefault", "Error", "getState", "setState", "scheduleUpdate", "flushUpdates", "isFirstMountRef", "useRef", "navigator<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "useCallback", "current", "<PERSON><PERSON><PERSON>", "listeners", "addListener", "keyedListeners", "addKeyedListener", "dispatch", "action", "focus", "console", "error", "navigation", "canGoBack", "result", "handled", "resetRoot", "target", "root", "reset", "getRootState", "getCurrentRoute", "emitter", "addOptionsGetter", "getCurrentOptions", "useMemo", "Object", "keys", "reduce", "acc", "name", "create", "isFocused", "getParent", "stateRef", "isReady", "setOptions", "useImperativeHandle", "onDispatchAction", "noop", "emit", "type", "data", "stack", "stackRef", "lastEmittedOptionsRef", "onOptionsChange", "options", "builderContext", "scheduleContext", "isInitialRef", "getIsInitial", "context", "onStateChangeRef", "useEffect", "hydratedState", "process", "env", "NODE_ENV", "serializableResult", "serializable", "location", "reason", "path", "pointer", "params", "i", "length", "curr", "prev", "test", "JSON", "stringify", "message", "includes", "push", "warn", "duplicateRouteNamesResult", "locations", "join", "defaultOnUnhandledAction", "payload", "element"], "sourceRoot": "../../src", "sources": ["BaseNavigationContainer.tsx"], "mappings": "AAAA,SACEA,aAAa,QAOR,2BAA2B;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,6BAA6B,MAAM,iCAAiC;AAC3E,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,sBAAsB,MAAM,0BAA0B;AAM7D,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,OAAOC,YAAY,MAAM,gBAAgB;AAIzC,MAAMC,oBAA8B,GAAG,EAAE;AACzC,MAAMC,qBAA+B,GAAG,EAAE;;AAE1C;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GACnBC,KAA+B,IACe;EAC9C,IAAIA,KAAK,KAAKC,SAAS,EAAE;IACvB;EACF;;EAEA;EACA,MAAM;IAAEC,GAAG;IAAEC,UAAU;IAAE,GAAGC;EAAa,CAAC,GAAGJ,KAAK;EAElD,OAAO;IACL,GAAGI,YAAY;IACfC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAEN,KAAK,CAACM,MAAM,CAACC,GAAG,CAAEC,KAAK,IAAK;MAClC,IAAIA,KAAK,CAACR,KAAK,KAAKC,SAAS,EAAE;QAC7B,OAAOO,KAAK;MAGd;MAEA,OAAO;QAAE,GAAGA,KAAK;QAAER,KAAK,EAAED,eAAe,CAACS,KAAK,CAACR,KAAK;MAAE,CAAC;IAC1D,CAAC;EACH,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,uBAAuB,gBAAG9B,KAAK,CAAC+B,UAAU,CAC9C,SAASD,uBAAuB,OAQ9BE,GAAsD,EACtD;EAAA,IARA;IACEC,YAAY;IACZC,aAAa;IACbC,iBAAiB;IACjBC,WAAW;IACXC;EACwB,CAAC;EAG3B,MAAMC,MAAM,GAAGtC,KAAK,CAACuC,UAAU,CAAC7B,sBAAsB,CAAC;EAEvD,IAAI,CAAC4B,MAAM,CAACE,SAAS,IAAI,CAACJ,WAAW,EAAE;IACrC,MAAM,IAAIK,KAAK,CACb,0VAA0V,CAC3V;EACH;EAEA,MAAM,CAACpB,KAAK,EAAEqB,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,YAAY,CAAC,GAC7D5B,YAAY,CAAQ,MAClBG,eAAe,CAACa,YAAY,IAAI,IAAI,GAAGX,SAAS,GAAGW,YAAY,CAAC,CACjE;EAEH,MAAMa,eAAe,GAAG9C,KAAK,CAAC+C,MAAM,CAAU,IAAI,CAAC;EAEnD,MAAMC,eAAe,GAAGhD,KAAK,CAAC+C,MAAM,EAAsB;EAE1D,MAAME,MAAM,GAAGjD,KAAK,CAACkD,WAAW,CAAC,MAAMF,eAAe,CAACG,OAAO,EAAE,EAAE,CAAC;EAEnE,MAAMC,MAAM,GAAGpD,KAAK,CAACkD,WAAW,CAAE3B,GAAW,IAAK;IAChDyB,eAAe,CAACG,OAAO,GAAG5B,GAAG;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAE8B,SAAS;IAAEC;EAAY,CAAC,GAAG1C,iBAAiB,EAAE;EAEtD,MAAM;IAAE2C,cAAc;IAAEC;EAAiB,CAAC,GAAG1C,sBAAsB,EAAE;EAErE,MAAM2C,QAAQ,GAAGzD,KAAK,CAACkD,WAAW,CAE9BQ,MAEkD,IAC/C;IACH,IAAIL,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MAC9BC,OAAO,CAACC,KAAK,CAAC1D,qBAAqB,CAAC;IACtC,CAAC,MAAM;MACLkD,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC,CAAEG,UAAU,IAAKA,UAAU,CAACL,QAAQ,CAACC,MAAM,CAAC,CAAC;IACjE;EACF,CAAC,EACD,CAACL,SAAS,CAACM,KAAK,CAAC,CAClB;EAED,MAAMI,SAAS,GAAG/D,KAAK,CAACkD,WAAW,CAAC,MAAM;IACxC,IAAIG,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MAC9B,OAAO,KAAK;IACd;IAEA,MAAM;MAAEK,MAAM;MAAEC;IAAQ,CAAC,GAAGZ,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC,CAAEG,UAAU,IACxDA,UAAU,CAACC,SAAS,EAAE,CACvB;IAED,IAAIE,OAAO,EAAE;MACX,OAAOD,MAAM;IACf,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAACX,SAAS,CAACM,KAAK,CAAC,CAAC;EAErB,MAAMO,SAAS,GAAGlE,KAAK,CAACkD,WAAW,CAChC7B,KAAuD,IAAK;IAAA;IAC3D,MAAM8C,MAAM,GAAG,CAAA9C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,GAAG,+BAAI,0BAAAgC,cAAc,CAACb,QAAQ,EAAC0B,IAAI,0DAA5B,kDAAgC,CAAC7C,GAAG;IAEjE,IAAI4C,MAAM,IAAI,IAAI,EAAE;MAClBP,OAAO,CAACC,KAAK,CAAC1D,qBAAqB,CAAC;IACtC,CAAC,MAAM;MACLkD,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC,CAAEG,UAAU,IAC5BA,UAAU,CAACL,QAAQ,CAAC;QAClB,GAAG1D,aAAa,CAACsE,KAAK,CAAChD,KAAK,CAAC;QAC7B8C;MACF,CAAC,CAAC,CACH;IACH;EACF,CAAC,EACD,CAACZ,cAAc,CAACb,QAAQ,EAAEW,SAAS,CAACM,KAAK,CAAC,CAC3C;EAED,MAAMW,YAAY,GAAGtE,KAAK,CAACkD,WAAW,CAAC,MAAM;IAAA;IAC3C,iCAAO,0BAAAK,cAAc,CAACb,QAAQ,EAAC0B,IAAI,2DAA5B,mDAAgC;EACzC,CAAC,EAAE,CAACb,cAAc,CAACb,QAAQ,CAAC,CAAC;EAE7B,MAAM6B,eAAe,GAAGvE,KAAK,CAACkD,WAAW,CAAC,MAAM;IAC9C,MAAM7B,KAAK,GAAGiD,YAAY,EAAE;IAE5B,IAAIjD,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOC,SAAS;IAClB;IAEA,MAAMO,KAAK,GAAGxB,gBAAgB,CAACgB,KAAK,CAAC;IAErC,OAAOQ,KAAK;EACd,CAAC,EAAE,CAACyC,YAAY,CAAC,CAAC;EAElB,MAAME,OAAO,GAAG3D,eAAe,EAA+B;EAE9D,MAAM;IAAE4D,gBAAgB;IAAEC;EAAkB,CAAC,GAAG3D,iBAAiB,CAAC,CAAC,CAAC,CAAC;EAErE,MAAM+C,UAAiD,GAAG9D,KAAK,CAAC2E,OAAO,CACrE,OAAO;IACL,GAAGC,MAAM,CAACC,IAAI,CAAC9E,aAAa,CAAC,CAAC+E,MAAM,CAAM,CAACC,GAAG,EAAEC,IAAI,KAAK;MACvDD,GAAG,CAACC,IAAI,CAAC,GAAG;QAAA;UACV;UACAvB,QAAQ,CAAC1D,aAAa,CAACiF,IAAI,CAAC,CAAC,YAAO,CAAC;QAAC;MAAA;MACxC,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,GAAGP,OAAO,CAACS,MAAM,CAAC,MAAM,CAAC;IACzBxB,QAAQ;IACRS,SAAS;IACTgB,SAAS,EAAE,MAAM,IAAI;IACrBnB,SAAS;IACToB,SAAS,EAAE,MAAM7D,SAAS;IAC1BoB,QAAQ,EAAE,MAAM0C,QAAQ,CAACjC,OAAO;IAChCmB,YAAY;IACZC,eAAe;IACfG,iBAAiB;IACjBW,OAAO,EAAE,MAAMhC,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IACzC2B,UAAU,EAAE,MAAM;MAChB,MAAM,IAAI7C,KAAK,CAAC,yCAAyC,CAAC;IAC5D;EACF,CAAC,CAAC,EACF,CACEsB,SAAS,EACTN,QAAQ,EACRe,OAAO,EACPE,iBAAiB,EACjBH,eAAe,EACfD,YAAY,EACZjB,SAAS,CAACM,KAAK,EACfO,SAAS,CACV,CACF;EAEDlE,KAAK,CAACuF,mBAAmB,CAACvD,GAAG,EAAE,MAAM8B,UAAU,EAAE,CAACA,UAAU,CAAC,CAAC;EAE9D,MAAM0B,gBAAgB,GAAGxF,KAAK,CAACkD,WAAW,CACxC,CAACQ,MAAwB,EAAE+B,IAAa,KAAK;IAC3CjB,OAAO,CAACkB,IAAI,CAAC;MACXC,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE;QAAElC,MAAM;QAAE+B,IAAI;QAAEI,KAAK,EAAEC,QAAQ,CAAC3C;MAAQ;IAChD,CAAC,CAAC;EACJ,CAAC,EACD,CAACqB,OAAO,CAAC,CACV;EAED,MAAMuB,qBAAqB,GAAG/F,KAAK,CAAC+C,MAAM,EAAsB;EAEhE,MAAMiD,eAAe,GAAGhG,KAAK,CAACkD,WAAW,CACtC+C,OAAe,IAAK;IACnB,IAAIF,qBAAqB,CAAC5C,OAAO,KAAK8C,OAAO,EAAE;MAC7C;IACF;IAEAF,qBAAqB,CAAC5C,OAAO,GAAG8C,OAAO;IAEvCzB,OAAO,CAACkB,IAAI,CAAC;MACXC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE;QAAEK;MAAQ;IAClB,CAAC,CAAC;EACJ,CAAC,EACD,CAACzB,OAAO,CAAC,CACV;EAED,MAAMsB,QAAQ,GAAG9F,KAAK,CAAC+C,MAAM,EAAsB;EAEnD,MAAMmD,cAAc,GAAGlG,KAAK,CAAC2E,OAAO,CAClC,OAAO;IACLrB,WAAW;IACXE,gBAAgB;IAChBgC,gBAAgB;IAChBQ,eAAe;IACfF;EACF,CAAC,CAAC,EACF,CAACxC,WAAW,EAAEE,gBAAgB,EAAEgC,gBAAgB,EAAEQ,eAAe,CAAC,CACnE;EAED,MAAMG,eAAe,GAAGnG,KAAK,CAAC2E,OAAO,CACnC,OAAO;IAAE/B,cAAc;IAAEC;EAAa,CAAC,CAAC,EACxC,CAACD,cAAc,EAAEC,YAAY,CAAC,CAC/B;EAED,MAAMuD,YAAY,GAAGpG,KAAK,CAAC+C,MAAM,CAAC,IAAI,CAAC;EAEvC,MAAMsD,YAAY,GAAGrG,KAAK,CAACkD,WAAW,CAAC,MAAMkD,YAAY,CAACjD,OAAO,EAAE,EAAE,CAAC;EAEtE,MAAMmD,OAAO,GAAGtG,KAAK,CAAC2E,OAAO,CAC3B,OAAO;IACLtD,KAAK;IACLqB,QAAQ;IACRC,QAAQ;IACRM,MAAM;IACNG,MAAM;IACNiD,YAAY;IACZ5B;EACF,CAAC,CAAC,EACF,CACEpD,KAAK,EACLqB,QAAQ,EACRC,QAAQ,EACRM,MAAM,EACNG,MAAM,EACNiD,YAAY,EACZ5B,gBAAgB,CACjB,CACF;EAED,MAAM8B,gBAAgB,GAAGvG,KAAK,CAAC+C,MAAM,CAACb,aAAa,CAAC;EACpD,MAAMkD,QAAQ,GAAGpF,KAAK,CAAC+C,MAAM,CAAC1B,KAAK,CAAC;EAEpCrB,KAAK,CAACwG,SAAS,CAAC,MAAM;IACpBJ,YAAY,CAACjD,OAAO,GAAG,KAAK;IAC5BoD,gBAAgB,CAACpD,OAAO,GAAGjB,aAAa;IACxCkD,QAAQ,CAACjC,OAAO,GAAG9B,KAAK;EAC1B,CAAC,CAAC;EAEFrB,KAAK,CAACwG,SAAS,CAAC,MAAM;IACpB,MAAMC,aAAa,GAAGnC,YAAY,EAAE;IAEpC,IAAIoC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIH,aAAa,KAAKnF,SAAS,EAAE;QAC/B,MAAMuF,kBAAkB,GAAG3G,iBAAiB,CAACuG,aAAa,CAAC;QAE3D,IAAI,CAACI,kBAAkB,CAACC,YAAY,EAAE;UACpC,MAAM;YAAEC,QAAQ;YAAEC;UAAO,CAAC,GAAGH,kBAAkB;UAE/C,IAAII,IAAI,GAAG,EAAE;UACb,IAAIC,OAAyB,GAAGT,aAAa;UAC7C,IAAIU,MAAM,GAAG,KAAK;UAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;YACxC,MAAME,IAAI,GAAGP,QAAQ,CAACK,CAAC,CAAC;YACxB,MAAMG,IAAI,GAAGR,QAAQ,CAACK,CAAC,GAAG,CAAC,CAAC;YAE5BF,OAAO,GAAGA,OAAO,CAACI,IAAI,CAAC;YAEvB,IAAI,CAACH,MAAM,IAAIG,IAAI,KAAK,OAAO,EAAE;cAC/B;YACF,CAAC,MAAM,IAAI,CAACH,MAAM,IAAIG,IAAI,KAAK,QAAQ,EAAE;cACvC,IAAIL,IAAI,EAAE;gBACRA,IAAI,IAAI,KAAK;cACf;YACF,CAAC,MAAM,IACL,CAACE,MAAM,IACP,OAAOG,IAAI,KAAK,QAAQ,IACxBC,IAAI,KAAK,QAAQ,EACjB;cAAA;cACAN,IAAI,gBAAIC,OAAO,6CAAP,SAASlC,IAAI;YACvB,CAAC,MAAM,IAAI,CAACmC,MAAM,EAAE;cAClBF,IAAI,IAAK,MAAKK,IAAK,EAAC;cACpBH,MAAM,GAAG,IAAI;YACf,CAAC,MAAM;cACL,IAAI,OAAOG,IAAI,KAAK,QAAQ,IAAI,UAAU,CAACE,IAAI,CAACF,IAAI,CAAC,EAAE;gBACrDL,IAAI,IAAK,IAAGK,IAAK,GAAE;cACrB,CAAC,MAAM,IAAI,aAAa,CAACE,IAAI,CAACF,IAAI,CAAC,EAAE;gBACnCL,IAAI,IAAK,IAAGK,IAAK,EAAC;cACpB,CAAC,MAAM;gBACLL,IAAI,IAAK,IAAGQ,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAE,GAAE;cACrC;YACF;UACF;UAEA,MAAMK,OAAO,GAAI,yEAAwEV,IAAK,KAAID,MAAO,4aAA2a;UAEphB,IAAI,CAAC9F,oBAAoB,CAAC0G,QAAQ,CAACD,OAAO,CAAC,EAAE;YAC3CzG,oBAAoB,CAAC2G,IAAI,CAACF,OAAO,CAAC;YAClC/D,OAAO,CAACkE,IAAI,CAACH,OAAO,CAAC;UACvB;QACF;QAEA,MAAMI,yBAAyB,GAC7B9H,wBAAwB,CAACwG,aAAa,CAAC;QAEzC,IAAIsB,yBAAyB,CAACV,MAAM,EAAE;UACpC,MAAMM,OAAO,GAAI,uEAAsEI,yBAAyB,CAACnG,GAAG,CACjHoG,SAAS,IAAM,KAAIA,SAAS,CAACC,IAAI,CAAC,IAAI,CAAE,EAAC,CAC1C,+GAA8G;UAEhH,IAAI,CAAC9G,qBAAqB,CAACyG,QAAQ,CAACD,OAAO,CAAC,EAAE;YAC5CxG,qBAAqB,CAAC0G,IAAI,CAACF,OAAO,CAAC;YACnC/D,OAAO,CAACkE,IAAI,CAACH,OAAO,CAAC;UACvB;QACF;MACF;IACF;IAEAnD,OAAO,CAACkB,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;QAAEvE;MAAM;IAAE,CAAC,CAAC;IAEhD,IAAI,CAACyB,eAAe,CAACK,OAAO,IAAIoD,gBAAgB,CAACpD,OAAO,EAAE;MACxDoD,gBAAgB,CAACpD,OAAO,CAACsD,aAAa,CAAC;IACzC;IAEA3D,eAAe,CAACK,OAAO,GAAG,KAAK;EACjC,CAAC,EAAE,CAACmB,YAAY,EAAEE,OAAO,EAAEnD,KAAK,CAAC,CAAC;EAElC,MAAM6G,wBAAwB,GAAGlI,KAAK,CAACkD,WAAW,CAC/CQ,MAAwB,IAAK;IAC5B,IAAIgD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;IACF;IAEA,MAAMuB,OAAwC,GAAGzE,MAAM,CAACyE,OAAO;IAE/D,IAAIR,OAAO,GAAI,eAAcjE,MAAM,CAACiC,IAAK,IACvCwC,OAAO,GAAI,iBAAgBV,IAAI,CAACC,SAAS,CAAChE,MAAM,CAACyE,OAAO,CAAE,EAAC,GAAG,EAC/D,oCAAmC;IAEpC,QAAQzE,MAAM,CAACiC,IAAI;MACjB,KAAK,UAAU;MACf,KAAK,MAAM;MACX,KAAK,SAAS;MACd,KAAK,SAAS;QACZ,IAAIwC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEnD,IAAI,EAAE;UACjB2C,OAAO,IAAK,mCAAkCQ,OAAO,CAACnD,IAAK,6KAA4K;QACzO,CAAC,MAAM;UACL2C,OAAO,IAAK,mIAAkI;QAChJ;QAEA;MACF,KAAK,SAAS;MACd,KAAK,KAAK;MACV,KAAK,YAAY;QACfA,OAAO,IAAK,wCAAuC;QACnD;MACF,KAAK,aAAa;MAClB,KAAK,cAAc;MACnB,KAAK,eAAe;QAClBA,OAAO,IAAK,+CAA8C;QAC1D;IAAM;IAGVA,OAAO,IAAK,0EAAyE;IAErF/D,OAAO,CAACC,KAAK,CAAC8D,OAAO,CAAC;EACxB,CAAC,EACD,EAAE,CACH;EAED,IAAIS,OAAO,gBACT,oBAAC,6BAA6B,CAAC,QAAQ;IAAC,KAAK,EAAEtE;EAAW,gBACxD,oBAAC,qBAAqB,CAAC,QAAQ;IAAC,KAAK,EAAEqC;EAAgB,gBACrD,oBAAC,wBAAwB,CAAC,QAAQ;IAAC,KAAK,EAAED;EAAe,gBACvD,oBAAC,sBAAsB,CAAC,QAAQ;IAAC,KAAK,EAAEI;EAAQ,gBAC9C,oBAAC,sBAAsB,CAAC,QAAQ;IAC9B,KAAK,EAAEnE,iBAAiB,IAAI+F;EAAyB,gBAErD,oBAAC,qBAAqB,QAAE7F,QAAQ,CAAyB,CACzB,CACF,CACA,CACL,CAEpC;EAED,IAAID,WAAW,EAAE;IACf;IACAgG,OAAO,gBACL,oBAAC,sBAAsB,CAAC,QAAQ;MAAC,KAAK,EAAE9G;IAAU,gBAChD,oBAAC,iBAAiB,CAAC,QAAQ;MAAC,KAAK,EAAEA;IAAU,GAC1C8G,OAAO,CACmB,CAEhC;EACH;EAEA,OAAOA,OAAO;AAChB,CAAC,CACF;AAED,eAAetG,uBAAuB"}