{"version": 3, "names": ["serializableWarnings", "duplicateName<PERSON><PERSON>nings", "getPartialState", "state", "undefined", "key", "routeNames", "partialState", "stale", "routes", "map", "route", "BaseNavigationContainer", "React", "forwardRef", "ref", "initialState", "onStateChange", "onUnhandledAction", "independent", "children", "parent", "useContext", "NavigationStateContext", "isDefault", "Error", "getState", "setState", "scheduleUpdate", "flushUpdates", "useSyncState", "isFirstMountRef", "useRef", "navigator<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "useCallback", "current", "<PERSON><PERSON><PERSON>", "listeners", "addListener", "useChildListeners", "keyedListeners", "addKeyedListener", "useKeyedChildListeners", "dispatch", "action", "focus", "console", "error", "NOT_INITIALIZED_ERROR", "navigation", "canGoBack", "result", "handled", "resetRoot", "target", "root", "CommonActions", "reset", "getRootState", "getCurrentRoute", "findFocusedRoute", "emitter", "useEventEmitter", "addOptionsGetter", "getCurrentOptions", "useOptionsGetters", "useMemo", "Object", "keys", "reduce", "acc", "name", "create", "isFocused", "getParent", "stateRef", "isReady", "setOptions", "useImperativeHandle", "onDispatchAction", "noop", "emit", "type", "data", "stack", "stackRef", "lastEmittedOptionsRef", "onOptionsChange", "options", "builderContext", "scheduleContext", "isInitialRef", "getIsInitial", "context", "onStateChangeRef", "useEffect", "hydratedState", "process", "env", "NODE_ENV", "serializableResult", "checkSerializable", "serializable", "location", "reason", "path", "pointer", "params", "i", "length", "curr", "prev", "test", "JSON", "stringify", "message", "includes", "push", "warn", "duplicateRouteNamesResult", "checkDuplicateRouteNames", "locations", "join", "defaultOnUnhandledAction", "payload", "element"], "sourceRoot": "../../src", "sources": ["BaseNavigationContainer.tsx"], "mappings": ";;;;;;AAAA;AASA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AAA0C;AAAA;AAAA;AAI1C,MAAMA,oBAA8B,GAAG,EAAE;AACzC,MAAMC,qBAA+B,GAAG,EAAE;;AAE1C;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GACnBC,KAA+B,IACe;EAC9C,IAAIA,KAAK,KAAKC,SAAS,EAAE;IACvB;EACF;;EAEA;EACA,MAAM;IAAEC,GAAG;IAAEC,UAAU;IAAE,GAAGC;EAAa,CAAC,GAAGJ,KAAK;EAElD,OAAO;IACL,GAAGI,YAAY;IACfC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAEN,KAAK,CAACM,MAAM,CAACC,GAAG,CAAEC,KAAK,IAAK;MAClC,IAAIA,KAAK,CAACR,KAAK,KAAKC,SAAS,EAAE;QAC7B,OAAOO,KAAK;MAGd;MAEA,OAAO;QAAE,GAAGA,KAAK;QAAER,KAAK,EAAED,eAAe,CAACS,KAAK,CAACR,KAAK;MAAE,CAAC;IAC1D,CAAC;EACH,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,uBAAuB,gBAAGC,KAAK,CAACC,UAAU,CAC9C,SAASF,uBAAuB,OAQ9BG,GAAsD,EACtD;EAAA,IARA;IACEC,YAAY;IACZC,aAAa;IACbC,iBAAiB;IACjBC,WAAW;IACXC;EACwB,CAAC;EAG3B,MAAMC,MAAM,GAAGR,KAAK,CAACS,UAAU,CAACC,+BAAsB,CAAC;EAEvD,IAAI,CAACF,MAAM,CAACG,SAAS,IAAI,CAACL,WAAW,EAAE;IACrC,MAAM,IAAIM,KAAK,CACb,0VAA0V,CAC3V;EACH;EAEA,MAAM,CAACtB,KAAK,EAAEuB,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,YAAY,CAAC,GAC7D,IAAAC,qBAAY,EAAQ,MAClB5B,eAAe,CAACc,YAAY,IAAI,IAAI,GAAGZ,SAAS,GAAGY,YAAY,CAAC,CACjE;EAEH,MAAMe,eAAe,GAAGlB,KAAK,CAACmB,MAAM,CAAU,IAAI,CAAC;EAEnD,MAAMC,eAAe,GAAGpB,KAAK,CAACmB,MAAM,EAAsB;EAE1D,MAAME,MAAM,GAAGrB,KAAK,CAACsB,WAAW,CAAC,MAAMF,eAAe,CAACG,OAAO,EAAE,EAAE,CAAC;EAEnE,MAAMC,MAAM,GAAGxB,KAAK,CAACsB,WAAW,CAAE9B,GAAW,IAAK;IAChD4B,eAAe,CAACG,OAAO,GAAG/B,GAAG;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEiC,SAAS;IAAEC;EAAY,CAAC,GAAG,IAAAC,0BAAiB,GAAE;EAEtD,MAAM;IAAEC,cAAc;IAAEC;EAAiB,CAAC,GAAG,IAAAC,+BAAsB,GAAE;EAErE,MAAMC,QAAQ,GAAG/B,KAAK,CAACsB,WAAW,CAE9BU,MAEkD,IAC/C;IACH,IAAIP,SAAS,CAACQ,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MAC9BC,OAAO,CAACC,KAAK,CAACC,mDAAqB,CAAC;IACtC,CAAC,MAAM;MACLX,SAAS,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAEI,UAAU,IAAKA,UAAU,CAACN,QAAQ,CAACC,MAAM,CAAC,CAAC;IACjE;EACF,CAAC,EACD,CAACP,SAAS,CAACQ,KAAK,CAAC,CAClB;EAED,MAAMK,SAAS,GAAGtC,KAAK,CAACsB,WAAW,CAAC,MAAM;IACxC,IAAIG,SAAS,CAACQ,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MAC9B,OAAO,KAAK;IACd;IAEA,MAAM;MAAEM,MAAM;MAAEC;IAAQ,CAAC,GAAGf,SAAS,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAEI,UAAU,IACxDA,UAAU,CAACC,SAAS,EAAE,CACvB;IAED,IAAIE,OAAO,EAAE;MACX,OAAOD,MAAM;IACf,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAACd,SAAS,CAACQ,KAAK,CAAC,CAAC;EAErB,MAAMQ,SAAS,GAAGzC,KAAK,CAACsB,WAAW,CAChChC,KAAuD,IAAK;IAAA;IAC3D,MAAMoD,MAAM,GAAG,CAAApD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,GAAG,+BAAI,0BAAAoC,cAAc,CAACf,QAAQ,EAAC8B,IAAI,0DAA5B,kDAAgC,CAACnD,GAAG;IAEjE,IAAIkD,MAAM,IAAI,IAAI,EAAE;MAClBR,OAAO,CAACC,KAAK,CAACC,mDAAqB,CAAC;IACtC,CAAC,MAAM;MACLX,SAAS,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAEI,UAAU,IAC5BA,UAAU,CAACN,QAAQ,CAAC;QAClB,GAAGa,sBAAa,CAACC,KAAK,CAACvD,KAAK,CAAC;QAC7BoD;MACF,CAAC,CAAC,CACH;IACH;EACF,CAAC,EACD,CAACd,cAAc,CAACf,QAAQ,EAAEY,SAAS,CAACQ,KAAK,CAAC,CAC3C;EAED,MAAMa,YAAY,GAAG9C,KAAK,CAACsB,WAAW,CAAC,MAAM;IAAA;IAC3C,iCAAO,0BAAAM,cAAc,CAACf,QAAQ,EAAC8B,IAAI,2DAA5B,mDAAgC;EACzC,CAAC,EAAE,CAACf,cAAc,CAACf,QAAQ,CAAC,CAAC;EAE7B,MAAMkC,eAAe,GAAG/C,KAAK,CAACsB,WAAW,CAAC,MAAM;IAC9C,MAAMhC,KAAK,GAAGwD,YAAY,EAAE;IAE5B,IAAIxD,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOC,SAAS;IAClB;IAEA,MAAMO,KAAK,GAAG,IAAAkD,yBAAgB,EAAC1D,KAAK,CAAC;IAErC,OAAOQ,KAAK;EACd,CAAC,EAAE,CAACgD,YAAY,CAAC,CAAC;EAElB,MAAMG,OAAO,GAAG,IAAAC,wBAAe,GAA+B;EAE9D,MAAM;IAAEC,gBAAgB;IAAEC;EAAkB,CAAC,GAAG,IAAAC,0BAAiB,EAAC,CAAC,CAAC,CAAC;EAErE,MAAMhB,UAAiD,GAAGrC,KAAK,CAACsD,OAAO,CACrE,OAAO;IACL,GAAGC,MAAM,CAACC,IAAI,CAACZ,sBAAa,CAAC,CAACa,MAAM,CAAM,CAACC,GAAG,EAAEC,IAAI,KAAK;MACvDD,GAAG,CAACC,IAAI,CAAC,GAAG;QAAA;UACV;UACA5B,QAAQ,CAACa,sBAAa,CAACe,IAAI,CAAC,CAAC,YAAO,CAAC;QAAC;MAAA;MACxC,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,GAAGT,OAAO,CAACW,MAAM,CAAC,MAAM,CAAC;IACzB7B,QAAQ;IACRU,SAAS;IACToB,SAAS,EAAE,MAAM,IAAI;IACrBvB,SAAS;IACTwB,SAAS,EAAE,MAAMvE,SAAS;IAC1BsB,QAAQ,EAAE,MAAMkD,QAAQ,CAACxC,OAAO;IAChCuB,YAAY;IACZC,eAAe;IACfK,iBAAiB;IACjBY,OAAO,EAAE,MAAMvC,SAAS,CAACQ,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IACzCgC,UAAU,EAAE,MAAM;MAChB,MAAM,IAAIrD,KAAK,CAAC,yCAAyC,CAAC;IAC5D;EACF,CAAC,CAAC,EACF,CACE0B,SAAS,EACTP,QAAQ,EACRkB,OAAO,EACPG,iBAAiB,EACjBL,eAAe,EACfD,YAAY,EACZrB,SAAS,CAACQ,KAAK,EACfQ,SAAS,CACV,CACF;EAEDzC,KAAK,CAACkE,mBAAmB,CAAChE,GAAG,EAAE,MAAMmC,UAAU,EAAE,CAACA,UAAU,CAAC,CAAC;EAE9D,MAAM8B,gBAAgB,GAAGnE,KAAK,CAACsB,WAAW,CACxC,CAACU,MAAwB,EAAEoC,IAAa,KAAK;IAC3CnB,OAAO,CAACoB,IAAI,CAAC;MACXC,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE;QAAEvC,MAAM;QAAEoC,IAAI;QAAEI,KAAK,EAAEC,QAAQ,CAAClD;MAAQ;IAChD,CAAC,CAAC;EACJ,CAAC,EACD,CAAC0B,OAAO,CAAC,CACV;EAED,MAAMyB,qBAAqB,GAAG1E,KAAK,CAACmB,MAAM,EAAsB;EAEhE,MAAMwD,eAAe,GAAG3E,KAAK,CAACsB,WAAW,CACtCsD,OAAe,IAAK;IACnB,IAAIF,qBAAqB,CAACnD,OAAO,KAAKqD,OAAO,EAAE;MAC7C;IACF;IAEAF,qBAAqB,CAACnD,OAAO,GAAGqD,OAAO;IAEvC3B,OAAO,CAACoB,IAAI,CAAC;MACXC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE;QAAEK;MAAQ;IAClB,CAAC,CAAC;EACJ,CAAC,EACD,CAAC3B,OAAO,CAAC,CACV;EAED,MAAMwB,QAAQ,GAAGzE,KAAK,CAACmB,MAAM,EAAsB;EAEnD,MAAM0D,cAAc,GAAG7E,KAAK,CAACsD,OAAO,CAClC,OAAO;IACL5B,WAAW;IACXG,gBAAgB;IAChBsC,gBAAgB;IAChBQ,eAAe;IACfF;EACF,CAAC,CAAC,EACF,CAAC/C,WAAW,EAAEG,gBAAgB,EAAEsC,gBAAgB,EAAEQ,eAAe,CAAC,CACnE;EAED,MAAMG,eAAe,GAAG9E,KAAK,CAACsD,OAAO,CACnC,OAAO;IAAEvC,cAAc;IAAEC;EAAa,CAAC,CAAC,EACxC,CAACD,cAAc,EAAEC,YAAY,CAAC,CAC/B;EAED,MAAM+D,YAAY,GAAG/E,KAAK,CAACmB,MAAM,CAAC,IAAI,CAAC;EAEvC,MAAM6D,YAAY,GAAGhF,KAAK,CAACsB,WAAW,CAAC,MAAMyD,YAAY,CAACxD,OAAO,EAAE,EAAE,CAAC;EAEtE,MAAM0D,OAAO,GAAGjF,KAAK,CAACsD,OAAO,CAC3B,OAAO;IACLhE,KAAK;IACLuB,QAAQ;IACRC,QAAQ;IACRO,MAAM;IACNG,MAAM;IACNwD,YAAY;IACZ7B;EACF,CAAC,CAAC,EACF,CACE7D,KAAK,EACLuB,QAAQ,EACRC,QAAQ,EACRO,MAAM,EACNG,MAAM,EACNwD,YAAY,EACZ7B,gBAAgB,CACjB,CACF;EAED,MAAM+B,gBAAgB,GAAGlF,KAAK,CAACmB,MAAM,CAACf,aAAa,CAAC;EACpD,MAAM2D,QAAQ,GAAG/D,KAAK,CAACmB,MAAM,CAAC7B,KAAK,CAAC;EAEpCU,KAAK,CAACmF,SAAS,CAAC,MAAM;IACpBJ,YAAY,CAACxD,OAAO,GAAG,KAAK;IAC5B2D,gBAAgB,CAAC3D,OAAO,GAAGnB,aAAa;IACxC2D,QAAQ,CAACxC,OAAO,GAAGjC,KAAK;EAC1B,CAAC,CAAC;EAEFU,KAAK,CAACmF,SAAS,CAAC,MAAM;IACpB,MAAMC,aAAa,GAAGtC,YAAY,EAAE;IAEpC,IAAIuC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIH,aAAa,KAAK7F,SAAS,EAAE;QAC/B,MAAMiG,kBAAkB,GAAG,IAAAC,0BAAiB,EAACL,aAAa,CAAC;QAE3D,IAAI,CAACI,kBAAkB,CAACE,YAAY,EAAE;UACpC,MAAM;YAAEC,QAAQ;YAAEC;UAAO,CAAC,GAAGJ,kBAAkB;UAE/C,IAAIK,IAAI,GAAG,EAAE;UACb,IAAIC,OAAyB,GAAGV,aAAa;UAC7C,IAAIW,MAAM,GAAG,KAAK;UAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;YACxC,MAAME,IAAI,GAAGP,QAAQ,CAACK,CAAC,CAAC;YACxB,MAAMG,IAAI,GAAGR,QAAQ,CAACK,CAAC,GAAG,CAAC,CAAC;YAE5BF,OAAO,GAAGA,OAAO,CAACI,IAAI,CAAC;YAEvB,IAAI,CAACH,MAAM,IAAIG,IAAI,KAAK,OAAO,EAAE;cAC/B;YACF,CAAC,MAAM,IAAI,CAACH,MAAM,IAAIG,IAAI,KAAK,QAAQ,EAAE;cACvC,IAAIL,IAAI,EAAE;gBACRA,IAAI,IAAI,KAAK;cACf;YACF,CAAC,MAAM,IACL,CAACE,MAAM,IACP,OAAOG,IAAI,KAAK,QAAQ,IACxBC,IAAI,KAAK,QAAQ,EACjB;cAAA;cACAN,IAAI,gBAAIC,OAAO,6CAAP,SAASnC,IAAI;YACvB,CAAC,MAAM,IAAI,CAACoC,MAAM,EAAE;cAClBF,IAAI,IAAK,MAAKK,IAAK,EAAC;cACpBH,MAAM,GAAG,IAAI;YACf,CAAC,MAAM;cACL,IAAI,OAAOG,IAAI,KAAK,QAAQ,IAAI,UAAU,CAACE,IAAI,CAACF,IAAI,CAAC,EAAE;gBACrDL,IAAI,IAAK,IAAGK,IAAK,GAAE;cACrB,CAAC,MAAM,IAAI,aAAa,CAACE,IAAI,CAACF,IAAI,CAAC,EAAE;gBACnCL,IAAI,IAAK,IAAGK,IAAK,EAAC;cACpB,CAAC,MAAM;gBACLL,IAAI,IAAK,IAAGQ,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAE,GAAE;cACrC;YACF;UACF;UAEA,MAAMK,OAAO,GAAI,yEAAwEV,IAAK,KAAID,MAAO,4aAA2a;UAEphB,IAAI,CAACzG,oBAAoB,CAACqH,QAAQ,CAACD,OAAO,CAAC,EAAE;YAC3CpH,oBAAoB,CAACsH,IAAI,CAACF,OAAO,CAAC;YAClCrE,OAAO,CAACwE,IAAI,CAACH,OAAO,CAAC;UACvB;QACF;QAEA,MAAMI,yBAAyB,GAC7B,IAAAC,iCAAwB,EAACxB,aAAa,CAAC;QAEzC,IAAIuB,yBAAyB,CAACV,MAAM,EAAE;UACpC,MAAMM,OAAO,GAAI,uEAAsEI,yBAAyB,CAAC9G,GAAG,CACjHgH,SAAS,IAAM,KAAIA,SAAS,CAACC,IAAI,CAAC,IAAI,CAAE,EAAC,CAC1C,+GAA8G;UAEhH,IAAI,CAAC1H,qBAAqB,CAACoH,QAAQ,CAACD,OAAO,CAAC,EAAE;YAC5CnH,qBAAqB,CAACqH,IAAI,CAACF,OAAO,CAAC;YACnCrE,OAAO,CAACwE,IAAI,CAACH,OAAO,CAAC;UACvB;QACF;MACF;IACF;IAEAtD,OAAO,CAACoB,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;QAAEjF;MAAM;IAAE,CAAC,CAAC;IAEhD,IAAI,CAAC4B,eAAe,CAACK,OAAO,IAAI2D,gBAAgB,CAAC3D,OAAO,EAAE;MACxD2D,gBAAgB,CAAC3D,OAAO,CAAC6D,aAAa,CAAC;IACzC;IAEAlE,eAAe,CAACK,OAAO,GAAG,KAAK;EACjC,CAAC,EAAE,CAACuB,YAAY,EAAEG,OAAO,EAAE3D,KAAK,CAAC,CAAC;EAElC,MAAMyH,wBAAwB,GAAG/G,KAAK,CAACsB,WAAW,CAC/CU,MAAwB,IAAK;IAC5B,IAAIqD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;IACF;IAEA,MAAMyB,OAAwC,GAAGhF,MAAM,CAACgF,OAAO;IAE/D,IAAIT,OAAO,GAAI,eAAcvE,MAAM,CAACsC,IAAK,IACvC0C,OAAO,GAAI,iBAAgBX,IAAI,CAACC,SAAS,CAACtE,MAAM,CAACgF,OAAO,CAAE,EAAC,GAAG,EAC/D,oCAAmC;IAEpC,QAAQhF,MAAM,CAACsC,IAAI;MACjB,KAAK,UAAU;MACf,KAAK,MAAM;MACX,KAAK,SAAS;MACd,KAAK,SAAS;QACZ,IAAI0C,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAErD,IAAI,EAAE;UACjB4C,OAAO,IAAK,mCAAkCS,OAAO,CAACrD,IAAK,6KAA4K;QACzO,CAAC,MAAM;UACL4C,OAAO,IAAK,mIAAkI;QAChJ;QAEA;MACF,KAAK,SAAS;MACd,KAAK,KAAK;MACV,KAAK,YAAY;QACfA,OAAO,IAAK,wCAAuC;QACnD;MACF,KAAK,aAAa;MAClB,KAAK,cAAc;MACnB,KAAK,eAAe;QAClBA,OAAO,IAAK,+CAA8C;QAC1D;IAAM;IAGVA,OAAO,IAAK,0EAAyE;IAErFrE,OAAO,CAACC,KAAK,CAACoE,OAAO,CAAC;EACxB,CAAC,EACD,EAAE,CACH;EAED,IAAIU,OAAO,gBACT,oBAAC,sCAA6B,CAAC,QAAQ;IAAC,KAAK,EAAE5E;EAAW,gBACxD,oBAAC,wCAAqB,CAAC,QAAQ;IAAC,KAAK,EAAEyC;EAAgB,gBACrD,oBAAC,iCAAwB,CAAC,QAAQ;IAAC,KAAK,EAAED;EAAe,gBACvD,oBAAC,+BAAsB,CAAC,QAAQ;IAAC,KAAK,EAAEI;EAAQ,gBAC9C,oBAAC,+BAAsB,CAAC,QAAQ;IAC9B,KAAK,EAAE5E,iBAAiB,IAAI0G;EAAyB,gBAErD,oBAAC,8BAAqB,QAAExG,QAAQ,CAAyB,CACzB,CACF,CACA,CACL,CAEpC;EAED,IAAID,WAAW,EAAE;IACf;IACA2G,OAAO,gBACL,oBAAC,+BAAsB,CAAC,QAAQ;MAAC,KAAK,EAAE1H;IAAU,gBAChD,oBAAC,0BAAiB,CAAC,QAAQ;MAAC,KAAK,EAAEA;IAAU,GAC1C0H,OAAO,CACmB,CAEhC;EACH;EAEA,OAAOA,OAAO;AAChB,CAAC,CACF;AAAC,eAEalH,uBAAuB;AAAA"}