import type { InputConfigT } from 'metro-config';
import type { ConfigLoadingContext } from './loadMetroConfig';
/**
 * Get the static Metro config defaults for a React Native project.
 *
 * @deprecated (React Native 0.72.0) Defaults should be updated here and in
 *   https://github.com/facebook/react-native/tree/main/package/metro-config/index.js
 */
export default function getDefaultMetroConfig(ctx: ConfigLoadingContext): InputConfigT;
//# sourceMappingURL=getDefaultMetroConfig.d.ts.map