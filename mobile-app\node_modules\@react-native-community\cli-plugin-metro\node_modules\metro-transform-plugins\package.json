{"name": "metro-transform-plugins", "version": "0.76.7", "description": "🚇 Transform plugins for Metro.", "main": "src/index.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "license": "MIT", "dependencies": {"@babel/core": "^7.20.0", "@babel/generator": "^7.20.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.20.0", "nullthrows": "^1.1.1"}, "devDependencies": {"@babel/code-frame": "^7.0.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-transform-flow-strip-types": "^7.20.0", "@babel/types": "^7.20.0", "metro": "0.76.7"}, "engines": {"node": ">=16"}}