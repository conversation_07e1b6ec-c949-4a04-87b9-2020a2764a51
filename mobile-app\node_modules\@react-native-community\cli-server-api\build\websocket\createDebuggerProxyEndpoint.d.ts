/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 */
import ws from 'ws';
export default function createDebuggerProxyEndpoint(): {
    server: ws.Server;
    isDebuggerConnected: () => boolean;
};
//# sourceMappingURL=createDebuggerProxyEndpoint.d.ts.map