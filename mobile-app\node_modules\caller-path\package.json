{"name": "caller-path", "version": "2.0.0", "description": "Get the path of the caller function", "license": "MIT", "repository": "sindresorhus/caller-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["caller", "calling", "module", "path", "parent", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"caller-callsite": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}}