{"version": 3, "sources": ["defaultConfiguration.ts"], "names": ["DEFAULT_CONFIGURATION", "reachabilityUrl", "reachabilityMethod", "reachabilityHeaders", "reachabilityTest", "response", "Promise", "resolve", "status", "reachabilityShortTimeout", "reachabilityLongTimeout", "reachabilityRequestTimeout", "reachabilityShouldRun", "shouldFetchWiFiSSID", "useNativeReachability"], "mappings": "AAEA,MAAMA,qBAAiD,GAAG;AACxDC,EAAAA,eAAe,EAAE,0CADuC;AAExDC,EAAAA,kBAAkB,EAAE,MAFoC;AAGxDC,EAAAA,mBAAmB,EAAE,EAHmC;AAIxDC,EAAAA,gBAAgB,EAAGC,QAAD,IAChBC,OAAO,CAACC,OAAR,CAAgBF,QAAQ,CAACG,MAAT,KAAoB,GAApC,CALsD;AAMxDC,EAAAA,wBAAwB,EAAE,IAAI,IAN0B;AAMpB;AACpCC,EAAAA,uBAAuB,EAAE,KAAK,IAP0B;AAOpB;AACpCC,EAAAA,0BAA0B,EAAE,KAAK,IARuB;AAQjB;AACvCC,EAAAA,qBAAqB,EAAE,MAAe,IATkB;AAUxDC,EAAAA,mBAAmB,EAAE,KAVmC;AAWxDC,EAAAA,qBAAqB,EAAE;AAXiC,CAA1D;AAcA,eAAed,qBAAf", "sourcesContent": ["import * as Types from './types';\n\nconst DEFAULT_CONFIGURATION: Types.NetInfoConfiguration = {\n  reachabilityUrl: 'https://clients3.google.com/generate_204',\n  reachabilityMethod: 'HEAD',\n  reachabilityHeaders: {},\n  reachabilityTest: (response: Response): Promise<boolean> =>\n    Promise.resolve(response.status === 204),\n  reachabilityShortTimeout: 5 * 1000, // 5s\n  reachabilityLongTimeout: 60 * 1000, // 60s\n  reachabilityRequestTimeout: 15 * 1000, // 15s\n  reachabilityShouldRun: (): boolean => true,\n  shouldFetchWiFiSSID: false,\n  useNativeReachability: true\n};\n\nexport default DEFAULT_CONFIGURATION;"]}