{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "mode", "scenes", "layout", "getPreviousScene", "getFocusedRoute", "onContentHeightChange", "style", "focusedRoute", "parentHeaderBack", "React", "useContext", "HeaderBackContext", "slice", "map", "scene", "i", "self", "length", "header", "headerMode", "headerShown", "headerTransparent", "headerStyleInterpolator", "descriptor", "options", "isFocused", "key", "route", "previousScene", "headerBack", "title", "getHeaderTitle", "name", "previousDescriptor", "nextDescriptor", "previousHeaderShown", "previousHeaderMode", "nextHeaderlessScene", "find", "currentHeaderShown", "currentHeaderMode", "gestureDirection", "nextHeaderlessGestureDirection", "isHeaderStatic", "props", "back", "progress", "navigation", "styleInterpolator", "forSlideUp", "forSlideRight", "forSlideLeft", "forNoAnimation", "e", "height", "nativeEvent", "undefined", "styles", "StyleSheet", "create", "position", "top", "left", "right"], "sourceRoot": "../../../../src", "sources": ["views/Header/HeaderContainer.tsx"], "mappings": ";;;;;;AAAA;AACA;AAMA;AACA;AAEA;AAaA;AAA8B;AAAA;AAAA;AAef,SAASA,eAAe,OAQ7B;EAAA,IAR8B;IACtCC,IAAI;IACJC,MAAM;IACNC,MAAM;IACNC,gBAAgB;IAChBC,eAAe;IACfC,qBAAqB;IACrBC;EACK,CAAC;EACN,MAAMC,YAAY,GAAGH,eAAe,EAAE;EACtC,MAAMI,gBAAgB,GAAGC,KAAK,CAACC,UAAU,CAACC,2BAAiB,CAAC;EAE5D,oBACE,oBAAC,qBAAQ,CAAC,IAAI;IAAC,aAAa,EAAC,UAAU;IAAC,KAAK,EAAEL;EAAM,GAClDL,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,EAAEC,IAAI,KAAK;IAAA;IACxC,IAAKhB,IAAI,KAAK,QAAQ,IAAIe,CAAC,KAAKC,IAAI,CAACC,MAAM,GAAG,CAAC,IAAK,CAACH,KAAK,EAAE;MAC1D,OAAO,IAAI;IACb;IAEA,MAAM;MACJI,MAAM;MACNC,UAAU;MACVC,WAAW,GAAG,IAAI;MAClBC,iBAAiB;MACjBC;IACF,CAAC,GAAGR,KAAK,CAACS,UAAU,CAACC,OAAO;IAE5B,IAAIL,UAAU,KAAKnB,IAAI,IAAI,CAACoB,WAAW,EAAE;MACvC,OAAO,IAAI;IACb;IAEA,MAAMK,SAAS,GAAGlB,YAAY,CAACmB,GAAG,KAAKZ,KAAK,CAACS,UAAU,CAACI,KAAK,CAACD,GAAG;IACjE,MAAME,aAAa,GAAGzB,gBAAgB,CAAC;MACrCwB,KAAK,EAAEb,KAAK,CAACS,UAAU,CAACI;IAC1B,CAAC,CAAC;IAEF,IAAIE,UAAU,GAAGrB,gBAAgB;IAEjC,IAAIoB,aAAa,EAAE;MACjB,MAAM;QAAEJ,OAAO;QAAEG;MAAM,CAAC,GAAGC,aAAa,CAACL,UAAU;MAEnDM,UAAU,GAAGD,aAAa,GACtB;QAAEE,KAAK,EAAE,IAAAC,wBAAc,EAACP,OAAO,EAAEG,KAAK,CAACK,IAAI;MAAE,CAAC,GAC9CxB,gBAAgB;IACtB;;IAEA;IACA;IACA,MAAMyB,kBAAkB,YAAGjB,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC,0CAAX,MAAaQ,UAAU;IAClD,MAAMW,cAAc,aAAGlB,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC,2CAAX,OAAaQ,UAAU;IAE9C,MAAM;MACJH,WAAW,EAAEe,mBAAmB,GAAG,IAAI;MACvChB,UAAU,EAAEiB;IACd,CAAC,GAAG,CAAAH,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAET,OAAO,KAAI,CAAC,CAAC;;IAErC;IACA;IACA,MAAMa,mBAAmB,GAAGrB,IAAI,CAACJ,KAAK,CAACG,CAAC,GAAG,CAAC,CAAC,CAACuB,IAAI,CAAExB,KAAK,IAAK;MAC5D,MAAM;QACJM,WAAW,EAAEmB,kBAAkB,GAAG,IAAI;QACtCpB,UAAU,EAAEqB;MACd,CAAC,GAAG,CAAA1B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAES,UAAU,CAACC,OAAO,KAAI,CAAC,CAAC;MAEnC,OAAOe,kBAAkB,KAAK,KAAK,IAAIC,iBAAiB,KAAK,QAAQ;IACvE,CAAC,CAAC;IAEF,MAAM;MAAEC,gBAAgB,EAAEC;IAA+B,CAAC,GACxD,CAAAL,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEd,UAAU,CAACC,OAAO,KAAI,CAAC,CAAC;IAE/C,MAAMmB,cAAc,GACjB,CAACR,mBAAmB,KAAK,KAAK,IAAIC,kBAAkB,KAAK,QAAQ;IAChE;IACA;IACA,CAACF,cAAc,IACjBG,mBAAmB;IAErB,MAAMO,KAAuB,GAAG;MAC9B1C,MAAM;MACN2C,IAAI,EAAEhB,UAAU;MAChBiB,QAAQ,EAAEhC,KAAK,CAACgC,QAAQ;MACxBtB,OAAO,EAAEV,KAAK,CAACS,UAAU,CAACC,OAAO;MACjCG,KAAK,EAAEb,KAAK,CAACS,UAAU,CAACI,KAAK;MAC7BoB,UAAU,EAAEjC,KAAK,CAACS,UAAU,CACzBwB,UAAgD;MACnDC,iBAAiB,EACfhD,IAAI,KAAK,OAAO,GACZ2C,cAAc,GACZD,8BAA8B,KAAK,UAAU,IAC7CA,8BAA8B,KAAK,mBAAmB,GACpDO,oCAAU,GACVP,8BAA8B,KAAK,qBAAqB,GACxDQ,uCAAa,GACbC,sCAAY,GACd7B,uBAAuB,GACzB8B;IACR,CAAC;IAED,oBACE,oBAAC,yBAAiB,CAAC,QAAQ;MACzB,GAAG,EAAEtC,KAAK,CAACS,UAAU,CAACI,KAAK,CAACD,GAAI;MAChC,KAAK,EAAEZ,KAAK,CAACS,UAAU,CAACwB;IAAW,gBAEnC,oBAAC,8BAAsB,CAAC,QAAQ;MAAC,KAAK,EAAEjC,KAAK,CAACS,UAAU,CAACI;IAAM,gBAC7D,oBAAC,iBAAI;MACH,QAAQ,EACNtB,qBAAqB,GAChBgD,CAAC,IAAK;QACL,MAAM;UAAEC;QAAO,CAAC,GAAGD,CAAC,CAACE,WAAW,CAACrD,MAAM;QAEvCG,qBAAqB,CAAC;UACpBsB,KAAK,EAAEb,KAAK,CAACS,UAAU,CAACI,KAAK;UAC7B2B;QACF,CAAC,CAAC;MACJ,CAAC,GACDE,SACL;MACD,aAAa,EAAE/B,SAAS,GAAG,UAAU,GAAG,MAAO;MAC/C,2BAA2B,EAAE,CAACA,SAAU;MACxC,yBAAyB,EACvBA,SAAS,GAAG,MAAM,GAAG,qBACtB;MACD,KAAK;MACH;MACA;MACCzB,IAAI,KAAK,OAAO,IAAI,CAACyB,SAAS,IAAKJ,iBAAiB,GACjDoC,MAAM,CAACvC,MAAM,GACb;IACL,GAEAA,MAAM,KAAKsC,SAAS,GAAGtC,MAAM,CAAC0B,KAAK,CAAC,gBAAG,oBAAC,eAAM,EAAKA,KAAK,CAAI,CACxD,CACyB,CACP;EAEjC,CAAC,CAAC,CACY;AAEpB;AAEA,MAAMa,MAAM,GAAGC,uBAAU,CAACC,MAAM,CAAC;EAC/BzC,MAAM,EAAE;IACN0C,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT;AACF,CAAC,CAAC"}