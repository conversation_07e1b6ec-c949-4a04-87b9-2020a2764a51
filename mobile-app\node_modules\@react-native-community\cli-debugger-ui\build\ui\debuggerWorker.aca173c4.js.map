{"version": 3, "sources": ["../../../../node_modules/@babel/runtime/helpers/typeof.js", "debuggerWorker.js"], "names": ["onmessage", "visibilityState", "hasWarned", "showVisibilityWarning", "console", "warn", "toString", "includes", "messageHandlers", "executeApplicationScript", "message", "sendReply", "key", "inject", "self", "JSON", "parse", "error", "importScripts", "url", "err", "setDebuggerVisibility", "object", "data", "result", "postMessage", "replyID", "id", "handler", "method", "returnValue", "__fbBatchedBridge", "apply", "arguments", "stringify"], "mappings": ";AAAA,SAAA,EAAA,GAaA,MAVA,mBAAA,QAAA,iBAAA,OAAA,SACA,OAAA,QAAA,EAAA,SAAA,GACA,cAAA,GAGA,OAAA,QAAA,EAAA,SAAA,GACA,OAAA,GAAA,mBAAA,QAAA,EAAA,cAAA,QAAA,IAAA,OAAA,UAAA,gBAAA,GAIA,EAAA,GAGA,OAAA,QAAA;;ACLA,aAAA,IAAA,EAAA,EAAA,QAAA,kCAAA,SAAA,EAAA,GAAA,OAAA,GAAA,EAAA,WAAA,EAAA,CAAA,QAAA,GAAAA,UAAa,WACPC,IAAAA,EAEEC,EADFC,GACED,GAAY,EACT,WAEDA,GAAaE,QAAQC,KAAKC,WAAWC,SAAS,mBAGlDL,GAAY,EACZE,QAAQC,KACN,6JAOFG,EAAkB,CACpBC,yBAA0B,SAAUC,EAASC,GACtC,IAAA,IAAIC,KAAOF,EAAQG,OACtBC,KAAKF,GAAOG,KAAKC,MAAMN,EAAQG,OAAOD,IAEpCK,IAAAA,EACA,IACFC,cAAcR,EAAQS,KACtB,MAAOC,GACPH,EAAQG,EAAIV,QAEdC,EAAU,KAAmBM,IAE/BI,sBAAuB,SAAUX,GAC/BT,EAAkBS,EAAQT,kBAIvB,OAAA,SAAUS,GACS,WAApBT,GACFE,IAGEmB,IAAAA,EAASZ,EAAQa,KAEjBZ,EAAY,SAAUa,EAAQP,GAChCQ,YAAY,CAACC,QAASJ,EAAOK,GAAIH,OAAQA,EAAQP,MAAOA,KAGtDW,EAAUpB,EAAgBc,EAAOO,QACjCD,GAAAA,EAEFA,EAAQN,EAAQX,OACX,CAEDmB,IACAb,EADAa,EAAc,CAAC,GAAI,GAAI,GAAI,GAE3B,IAC+B,YAAtBC,oBAAAA,kBAAAA,aAAAA,EAAAA,EAAAA,SAAAA,oBACTD,EAAcC,kBAAkBT,EAAOO,QAAQG,MAC7C,KACAV,EAAOW,WAGThB,EAAQ,0DAEV,MAAOG,GACPH,EAAQG,EAAIV,QACJ,QACRC,EAAUI,KAAKmB,UAAUJ,GAAcb,MAnElC", "file": "debuggerWorker.aca173c4.js", "sourceRoot": "../../src/ui", "sourcesContent": ["function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    module.exports = _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    module.exports = _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nmodule.exports = _typeof;", "/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/* global __fbBatchedBridge, self, importScripts, postMessage, onmessage: true */\n/* eslint no-unused-vars: 0 */\n\nonmessage = (function () {\n  var visibilityState;\n  var showVisibilityWarning = (function () {\n    var hasWarned = false;\n    return function () {\n      // Wait until `YellowBox` gets initialized before displaying the warning.\n      if (hasWarned || console.warn.toString().includes('[native code]')) {\n        return;\n      }\n      hasWarned = true;\n      console.warn(\n        'Remote debugger is in a background tab which may cause apps to ' +\n          'perform slowly. Fix this by foregrounding the tab (or opening it in ' +\n          'a separate window).',\n      );\n    };\n  })();\n\n  var messageHandlers = {\n    executeApplicationScript: function (message, sendReply) {\n      for (var key in message.inject) {\n        self[key] = JSON.parse(message.inject[key]);\n      }\n      var error;\n      try {\n        importScripts(message.url);\n      } catch (err) {\n        error = err.message;\n      }\n      sendReply(null /* result */, error);\n    },\n    setDebuggerVisibility: function (message) {\n      visibilityState = message.visibilityState;\n    },\n  };\n\n  return function (message) {\n    if (visibilityState === 'hidden') {\n      showVisibilityWarning();\n    }\n\n    var object = message.data;\n\n    var sendReply = function (result, error) {\n      postMessage({replyID: object.id, result: result, error: error});\n    };\n\n    var handler = messageHandlers[object.method];\n    if (handler) {\n      // Special cased handlers\n      handler(object, sendReply);\n    } else {\n      // Other methods get called on the bridge\n      var returnValue = [[], [], [], 0];\n      var error;\n      try {\n        if (typeof __fbBatchedBridge === 'object') {\n          returnValue = __fbBatchedBridge[object.method].apply(\n            null,\n            object.arguments,\n          );\n        } else {\n          error = 'Failed to call function, __fbBatchedBridge is undefined';\n        }\n      } catch (err) {\n        error = err.message;\n      } finally {\n        sendReply(JSON.stringify(returnValue), error);\n      }\n    }\n  };\n})();\n"]}