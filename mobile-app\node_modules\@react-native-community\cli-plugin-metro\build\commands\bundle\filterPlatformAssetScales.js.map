{"version": 3, "names": ["ALLOWED_SCALES", "ios", "filterPlatformAssetScales", "platform", "scales", "whitelist", "result", "filter", "scale", "indexOf", "length", "maxScale", "push"], "sources": ["../../../src/commands/bundle/filterPlatformAssetScales.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst ALLOWED_SCALES: {[key: string]: number[]} = {\n  ios: [1, 2, 3],\n};\n\nfunction filterPlatformAssetScales(\n  platform: string,\n  scales: ReadonlyArray<number>,\n): ReadonlyArray<number> {\n  const whitelist: number[] = ALLOWED_SCALES[platform];\n  if (!whitelist) {\n    return scales;\n  }\n  const result = scales.filter((scale) => whitelist.indexOf(scale) > -1);\n  if (result.length === 0 && scales.length > 0) {\n    // No matching scale found, but there are some available. Ideally we don't\n    // want to be in this situation and should throw, but for now as a fallback\n    // let's just use the closest larger image\n    const maxScale = whitelist[whitelist.length - 1];\n    for (const scale of scales) {\n      if (scale > maxScale) {\n        result.push(scale);\n        break;\n      }\n    }\n\n    // There is no larger scales available, use the largest we have\n    if (result.length === 0) {\n      result.push(scales[scales.length - 1]);\n    }\n  }\n  return result;\n}\n\nexport default filterPlatformAssetScales;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,cAAyC,GAAG;EAChDC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AACf,CAAC;AAED,SAASC,yBAAyB,CAChCC,QAAgB,EAChBC,MAA6B,EACN;EACvB,MAAMC,SAAmB,GAAGL,cAAc,CAACG,QAAQ,CAAC;EACpD,IAAI,CAACE,SAAS,EAAE;IACd,OAAOD,MAAM;EACf;EACA,MAAME,MAAM,GAAGF,MAAM,CAACG,MAAM,CAAEC,KAAK,IAAKH,SAAS,CAACI,OAAO,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACtE,IAAIF,MAAM,CAACI,MAAM,KAAK,CAAC,IAAIN,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE;IAC5C;IACA;IACA;IACA,MAAMC,QAAQ,GAAGN,SAAS,CAACA,SAAS,CAACK,MAAM,GAAG,CAAC,CAAC;IAChD,KAAK,MAAMF,KAAK,IAAIJ,MAAM,EAAE;MAC1B,IAAII,KAAK,GAAGG,QAAQ,EAAE;QACpBL,MAAM,CAACM,IAAI,CAACJ,KAAK,CAAC;QAClB;MACF;IACF;;IAEA;IACA,IAAIF,MAAM,CAACI,MAAM,KAAK,CAAC,EAAE;MACvBJ,MAAM,CAACM,IAAI,CAACR,MAAM,CAACA,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC,CAAC;IACxC;EACF;EACA,OAAOJ,MAAM;AACf;AAAC,eAEcJ,yBAAyB;AAAA"}