import type { BaseQueryFn } from './baseQueryTypes';
declare const _NEVER: unique symbol;
export declare type NEVER = typeof _NEVER;
/**
 * Creates a "fake" baseQuery to be used if your api *only* uses the `queryFn` definition syntax.
 * This also allows you to specify a specific error type to be shared by all your `queryFn` definitions.
 */
export declare function fakeBaseQuery<ErrorType>(): BaseQueryFn<void, NEVER, ErrorType, {}>;
export {};
