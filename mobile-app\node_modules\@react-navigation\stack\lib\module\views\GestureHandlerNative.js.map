{"version": 3, "names": ["React", "PanGestureHandler", "PanGestureHandlerNative", "GestureHandlerRefContext", "props", "gestureRef", "useRef", "GestureHandlerRootView", "State", "GestureState"], "sourceRoot": "../../../src", "sources": ["views/GestureHandlerNative.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,iBAAiB,IAAIC,uBAAuB,QAEvC,8BAA8B;AAErC,OAAOC,wBAAwB,MAAM,mCAAmC;AAExE,OAAO,SAASF,iBAAiB,CAACG,KAAkC,EAAE;EACpE,MAAMC,UAAU,GAAGL,KAAK,CAACM,MAAM,CAA0B,IAAI,CAAC;EAE9D,oBACE,oBAAC,wBAAwB,CAAC,QAAQ;IAAC,KAAK,EAAED;EAAW,gBACnD,oBAAC,uBAAuB,eAAKD,KAAK;IAAE,GAAG,EAAEC;EAAW,GAAG,CACrB;AAExC;AAGA,SACEE,sBAAsB,EACtBC,KAAK,IAAIC,YAAY,QAChB,8BAA8B"}