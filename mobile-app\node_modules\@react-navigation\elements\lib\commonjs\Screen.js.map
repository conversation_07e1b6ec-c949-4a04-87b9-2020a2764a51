{"version": 3, "names": ["Screen", "props", "dimensions", "useSafeAreaFrame", "insets", "useSafeAreaInsets", "isParentHeaderShown", "React", "useContext", "HeaderShownContext", "parentHeaderHeight", "HeaderHeightContext", "focused", "modal", "header", "headerShown", "headerTransparent", "headerStatusBarHeight", "top", "navigation", "route", "children", "style", "headerHeight", "setHeaderHeight", "useState", "getDefaultHeaderHeight", "styles", "container", "content", "e", "height", "nativeEvent", "layout", "absolute", "StyleSheet", "create", "flex", "flexDirection", "position", "left", "right"], "sourceRoot": "../../src", "sources": ["Screen.tsx"], "mappings": ";;;;;;AAAA;AAOA;AACA;AACA;AAKA;AACA;AACA;AACA;AAA6D;AAAA;AAAA;AAe9C,SAASA,MAAM,CAACC,KAAY,EAAE;EAC3C,MAAMC,UAAU,GAAG,IAAAC,4CAAgB,GAAE;EACrC,MAAMC,MAAM,GAAG,IAAAC,6CAAiB,GAAE;EAElC,MAAMC,mBAAmB,GAAGC,KAAK,CAACC,UAAU,CAACC,2BAAkB,CAAC;EAChE,MAAMC,kBAAkB,GAAGH,KAAK,CAACC,UAAU,CAACG,4BAAmB,CAAC;EAEhE,MAAM;IACJC,OAAO;IACPC,KAAK,GAAG,KAAK;IACbC,MAAM;IACNC,WAAW,GAAG,IAAI;IAClBC,iBAAiB;IACjBC,qBAAqB,GAAGX,mBAAmB,GAAG,CAAC,GAAGF,MAAM,CAACc,GAAG;IAC5DC,UAAU;IACVC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGrB,KAAK;EAET,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,KAAK,CAACkB,QAAQ,CAAC,MACrD,IAAAC,+BAAsB,EAACxB,UAAU,EAAEW,KAAK,EAAEI,qBAAqB,CAAC,CACjE;EAED,oBACE,oBAAC,mBAAU;IACT,2BAA2B,EAAE,CAACL,OAAQ;IACtC,yBAAyB,EAAEA,OAAO,GAAG,MAAM,GAAG,qBAAsB;IACpE,KAAK,EAAE,CAACe,MAAM,CAACC,SAAS,EAAEN,KAAK;EAAE,gBAEjC,oBAAC,iBAAI;IAAC,KAAK,EAAEK,MAAM,CAACE;EAAQ,gBAC1B,oBAAC,2BAAkB,CAAC,QAAQ;IAC1B,KAAK,EAAEvB,mBAAmB,IAAIS,WAAW,KAAK;EAAM,gBAEpD,oBAAC,4BAAmB,CAAC,QAAQ;IAC3B,KAAK,EAAEA,WAAW,GAAGQ,YAAY,GAAGb,kBAAkB,IAAI;EAAE,GAE3DW,QAAQ,CACoB,CACH,CACzB,EACNN,WAAW,gBACV,oBAAC,yBAAiB,CAAC,QAAQ;IAAC,KAAK,EAAEI;EAAW,gBAC5C,oBAAC,8BAAsB,CAAC,QAAQ;IAAC,KAAK,EAAEC;EAAM,gBAC5C,oBAAC,iBAAI;IACH,QAAQ,EAAGU,CAAC,IAAK;MACf,MAAM;QAAEC;MAAO,CAAC,GAAGD,CAAC,CAACE,WAAW,CAACC,MAAM;MAEvCT,eAAe,CAACO,MAAM,CAAC;IACzB,CAAE;IACF,KAAK,EAAEf,iBAAiB,GAAGW,MAAM,CAACO,QAAQ,GAAG;EAAK,GAEjDpB,MAAM,CACF,CACyB,CACP,GAC3B,IAAI,CACG;AAEjB;AAEA,MAAMa,MAAM,GAAGQ,uBAAU,CAACC,MAAM,CAAC;EAC/BR,SAAS,EAAE;IACTS,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB,CAAC;EACD;EACAT,OAAO,EAAE;IACPQ,IAAI,EAAE;EACR,CAAC;EACDH,QAAQ,EAAE;IACRK,QAAQ,EAAE,UAAU;IACpBrB,GAAG,EAAE,CAAC;IACNsB,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT;AACF,CAAC,CAAC"}