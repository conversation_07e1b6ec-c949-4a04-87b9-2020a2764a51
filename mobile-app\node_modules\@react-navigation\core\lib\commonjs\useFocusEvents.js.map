{"version": 3, "names": ["useFocusEvents", "state", "emitter", "navigation", "React", "useContext", "NavigationContext", "lastFocusedKeyRef", "useRef", "currentFocusedKey", "routes", "index", "key", "useEffect", "addListener", "current", "emit", "type", "target", "undefined", "lastFocused<PERSON>ey", "isFocused"], "sourceRoot": "../../src", "sources": ["useFocusEvents.tsx"], "mappings": ";;;;;;AACA;AAEA;AAAoD;AAAA;AAAA;AASpD;AACA;AACA;AACe,SAASA,cAAc,OAGnB;EAAA,IAHmD;IACpEC,KAAK;IACLC;EACc,CAAC;EACf,MAAMC,UAAU,GAAGC,KAAK,CAACC,UAAU,CAACC,0BAAiB,CAAC;EACtD,MAAMC,iBAAiB,GAAGH,KAAK,CAACI,MAAM,EAAsB;EAE5D,MAAMC,iBAAiB,GAAGR,KAAK,CAACS,MAAM,CAACT,KAAK,CAACU,KAAK,CAAC,CAACC,GAAG;;EAEvD;EACA;EACAR,KAAK,CAACS,SAAS,CACb,MACEV,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEW,WAAW,CAAC,OAAO,EAAE,MAAM;IACrCP,iBAAiB,CAACQ,OAAO,GAAGN,iBAAiB;IAC7CP,OAAO,CAACc,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC5D,CAAC,CAAC,EACJ,CAACA,iBAAiB,EAAEP,OAAO,EAAEC,UAAU,CAAC,CACzC;EAEDC,KAAK,CAACS,SAAS,CACb,MACEV,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEW,WAAW,CAAC,MAAM,EAAE,MAAM;IACpCP,iBAAiB,CAACQ,OAAO,GAAGI,SAAS;IACrCjB,OAAO,CAACc,IAAI,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC3D,CAAC,CAAC,EACJ,CAACA,iBAAiB,EAAEP,OAAO,EAAEC,UAAU,CAAC,CACzC;EAEDC,KAAK,CAACS,SAAS,CAAC,MAAM;IACpB,MAAMO,cAAc,GAAGb,iBAAiB,CAACQ,OAAO;IAEhDR,iBAAiB,CAACQ,OAAO,GAAGN,iBAAiB;;IAE7C;IACA;IACA,IAAIW,cAAc,KAAKD,SAAS,IAAI,CAAChB,UAAU,EAAE;MAC/CD,OAAO,CAACc,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,MAAM,EAAET;MAAkB,CAAC,CAAC;IAC5D;;IAEA;IACA;IACA,IACEW,cAAc,KAAKX,iBAAiB,IACpC,EAAEN,UAAU,GAAGA,UAAU,CAACkB,SAAS,EAAE,GAAG,IAAI,CAAC,EAC7C;MACA;IACF;IAEA,IAAID,cAAc,KAAKD,SAAS,EAAE;MAChC;MACA;IACF;IAEAjB,OAAO,CAACc,IAAI,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAEC,MAAM,EAAEE;IAAe,CAAC,CAAC;IACtDlB,OAAO,CAACc,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACA,iBAAiB,EAAEP,OAAO,EAAEC,UAAU,CAAC,CAAC;AAC9C"}