{"name": "@react-native/eslint-config", "version": "0.72.2", "description": "ESLint config for React Native", "main": "index.js", "license": "MIT", "repository": {"type": "git", "url": "**************:facebook/react-native.git", "directory": "packages/eslint-config-react-native-community"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/eslint-config-react-native-community#readme", "dependencies": {"@babel/core": "^7.20.0", "@babel/eslint-parser": "^7.20.0", "@react-native/eslint-plugin": "^0.72.0", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-ft-flow": "^2.0.1", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0"}, "peerDependencies": {"eslint": ">=8", "prettier": ">=2"}, "devDependencies": {"eslint": "^8.19.0", "prettier": "^2.4.1"}}