{"version": 3, "names": ["searchPlaces", "readConfigFromDisk", "rootFolder", "explorer", "cosmiconfig", "stopDir", "searchResult", "searchSync", "config", "undefined", "result", "schema", "projectConfig", "validate", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "readDependencyConfigFromDisk", "dependencyName", "emptyDependencyConfig", "dependencyConfig", "abort<PERSON><PERSON><PERSON>", "validationError", "logger", "warn", "inlineString", "chalk", "bold", "message", "dependency", "platforms", "commands"], "sources": ["../src/readConfigFromDisk.ts"], "sourcesContent": ["import cosmiconfig from 'cosmiconfig';\nimport {JoiError} from './errors';\nimport * as schema from './schema';\nimport {\n  UserConfig,\n  UserDependencyConfig,\n} from '@react-native-community/cli-types';\nimport {logger, inlineString} from '@react-native-community/cli-tools';\nimport chalk from 'chalk';\n\n/**\n * Places to look for the configuration file.\n */\nconst searchPlaces = ['react-native.config.js'];\n\n/**\n * Reads a project configuration as defined by the user in the current\n * workspace.\n */\nexport function readConfigFromDisk(rootFolder: string): UserConfig {\n  const explorer = cosmiconfig('react-native', {\n    searchPlaces,\n    stopDir: rootFolder,\n  });\n\n  const searchResult = explorer.searchSync(rootFolder);\n  const config = searchResult ? searchResult.config : undefined;\n  const result = schema.projectConfig.validate(config);\n\n  if (result.error) {\n    throw new JoiError(result.error);\n  }\n\n  return result.value as UserConfig;\n}\n\n/**\n * Reads a dependency configuration as defined by the developer\n * inside `node_modules`.\n */\nexport function readDependencyConfigFromDisk(\n  rootFolder: string,\n  dependencyName: string,\n): UserDependencyConfig {\n  const explorer = cosmiconfig('react-native', {\n    stopDir: rootFolder,\n    searchPlaces,\n  });\n\n  const searchResult = explorer.searchSync(rootFolder);\n  const config = searchResult ? searchResult.config : emptyDependencyConfig;\n\n  const result = schema.dependencyConfig.validate(config, {abortEarly: false});\n\n  if (result.error) {\n    const validationError = new JoiError(result.error);\n    logger.warn(\n      inlineString(`\n        Package ${chalk.bold(\n          dependencyName,\n        )} contains invalid configuration: ${chalk.bold(\n        validationError.message,\n      )}.\n      \n      Please verify it's properly linked using \"react-native config\" command and contact the package maintainers about this.`),\n    );\n  }\n\n  return result.value as UserDependencyConfig;\n}\n\nconst emptyDependencyConfig = {\n  dependency: {\n    platforms: {},\n  },\n  commands: [],\n  platforms: {},\n};\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAKA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAAA;AAAA;AAE1B;AACA;AACA;AACA,MAAMA,YAAY,GAAG,CAAC,wBAAwB,CAAC;;AAE/C;AACA;AACA;AACA;AACO,SAASC,kBAAkB,CAACC,UAAkB,EAAc;EACjE,MAAMC,QAAQ,GAAG,IAAAC,sBAAW,EAAC,cAAc,EAAE;IAC3CJ,YAAY;IACZK,OAAO,EAAEH;EACX,CAAC,CAAC;EAEF,MAAMI,YAAY,GAAGH,QAAQ,CAACI,UAAU,CAACL,UAAU,CAAC;EACpD,MAAMM,MAAM,GAAGF,YAAY,GAAGA,YAAY,CAACE,MAAM,GAAGC,SAAS;EAC7D,MAAMC,MAAM,GAAGC,MAAM,CAACC,aAAa,CAACC,QAAQ,CAACL,MAAM,CAAC;EAEpD,IAAIE,MAAM,CAACI,KAAK,EAAE;IAChB,MAAM,IAAIC,gBAAQ,CAACL,MAAM,CAACI,KAAK,CAAC;EAClC;EAEA,OAAOJ,MAAM,CAACM,KAAK;AACrB;;AAEA;AACA;AACA;AACA;AACO,SAASC,4BAA4B,CAC1Cf,UAAkB,EAClBgB,cAAsB,EACA;EACtB,MAAMf,QAAQ,GAAG,IAAAC,sBAAW,EAAC,cAAc,EAAE;IAC3CC,OAAO,EAAEH,UAAU;IACnBF;EACF,CAAC,CAAC;EAEF,MAAMM,YAAY,GAAGH,QAAQ,CAACI,UAAU,CAACL,UAAU,CAAC;EACpD,MAAMM,MAAM,GAAGF,YAAY,GAAGA,YAAY,CAACE,MAAM,GAAGW,qBAAqB;EAEzE,MAAMT,MAAM,GAAGC,MAAM,CAACS,gBAAgB,CAACP,QAAQ,CAACL,MAAM,EAAE;IAACa,UAAU,EAAE;EAAK,CAAC,CAAC;EAE5E,IAAIX,MAAM,CAACI,KAAK,EAAE;IAChB,MAAMQ,eAAe,GAAG,IAAIP,gBAAQ,CAACL,MAAM,CAACI,KAAK,CAAC;IAClDS,kBAAM,CAACC,IAAI,CACT,IAAAC,wBAAY,EAAE;AACpB,kBAAkBC,gBAAK,CAACC,IAAI,CAClBT,cAAc,CACd,oCAAmCQ,gBAAK,CAACC,IAAI,CAC/CL,eAAe,CAACM,OAAO,CACvB;AACR;AACA,6HAA6H,CAAC,CACzH;EACH;EAEA,OAAOlB,MAAM,CAACM,KAAK;AACrB;AAEA,MAAMG,qBAAqB,GAAG;EAC5BU,UAAU,EAAE;IACVC,SAAS,EAAE,CAAC;EACd,CAAC;EACDC,QAAQ,EAAE,EAAE;EACZD,SAAS,EAAE,CAAC;AACd,CAAC"}