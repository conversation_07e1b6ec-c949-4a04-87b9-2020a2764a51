{"name": "jest-watcher", "description": "Delightful JavaScript Testing.", "version": "29.7.0", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.7.0", "string-length": "^4.0.1"}, "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-watcher"}, "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "homepage": "https://jestjs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}