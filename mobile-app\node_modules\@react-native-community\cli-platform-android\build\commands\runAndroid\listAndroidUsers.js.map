{"version": 3, "names": ["checkUsers", "device", "adbPath", "adbArgs", "logger", "debug", "stdout", "execa", "sync", "encoding", "regex", "RegExp", "users", "lines", "split", "line", "res", "exec", "groups", "push", "id", "userId", "name", "userName", "length", "map", "user", "join", "error", "promptForUser", "selected<PERSON>ser", "prompts", "type", "message", "choices", "title", "value", "min"], "sources": ["../../../src/commands/runAndroid/listAndroidUsers.ts"], "sourcesContent": ["import {logger} from '@react-native-community/cli-tools';\nimport execa from 'execa';\nimport prompts from 'prompts';\n\ntype User = {\n  id: string;\n  name: string;\n};\n\nexport function checkUsers(device: string, adbPath: string) {\n  try {\n    const adbArgs = ['-s', device, 'shell', 'pm', 'list', 'users'];\n\n    logger.debug(`Checking users on \"${device}\"...`);\n    const {stdout} = execa.sync(adbPath, adbArgs, {encoding: 'utf-8'});\n    const regex = new RegExp(\n      /^\\s*UserInfo\\{(?<userId>\\d+):(?<userName>.*):(?<userFlags>[0-9a-f]*)}/,\n    );\n    const users: User[] = [];\n\n    const lines = stdout.split('\\n');\n    for (const line of lines) {\n      const res = regex.exec(line);\n      if (res?.groups) {\n        users.push({id: res.groups.userId, name: res.groups.userName});\n      }\n    }\n\n    if (users.length > 1) {\n      logger.debug(\n        `Available users are:\\n${users\n          .map((user) => `${user.name} - ${user.id}`)\n          .join('\\n')}`,\n      );\n    }\n\n    return users;\n  } catch (error) {\n    logger.error('Failed to check users of device.', error as any);\n    return [];\n  }\n}\n\nexport async function promptForUser(users: User[]) {\n  const {selectedUser}: {selectedUser: User} = await prompts({\n    type: 'select',\n    name: 'selectedUser',\n    message: 'Which profile would you like to launch your app into?',\n    choices: users.map((user: User) => ({\n      title: user.name,\n      value: user,\n    })),\n    min: 1,\n  });\n\n  return selectedUser;\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA8B;AAOvB,SAASA,UAAU,CAACC,MAAc,EAAEC,OAAe,EAAE;EAC1D,IAAI;IACF,MAAMC,OAAO,GAAG,CAAC,IAAI,EAAEF,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC;IAE9DG,kBAAM,CAACC,KAAK,CAAE,sBAAqBJ,MAAO,MAAK,CAAC;IAChD,MAAM;MAACK;IAAM,CAAC,GAAGC,gBAAK,CAACC,IAAI,CAACN,OAAO,EAAEC,OAAO,EAAE;MAACM,QAAQ,EAAE;IAAO,CAAC,CAAC;IAClE,MAAMC,KAAK,GAAG,IAAIC,MAAM,CACtB,uEAAuE,CACxE;IACD,MAAMC,KAAa,GAAG,EAAE;IAExB,MAAMC,KAAK,GAAGP,MAAM,CAACQ,KAAK,CAAC,IAAI,CAAC;IAChC,KAAK,MAAMC,IAAI,IAAIF,KAAK,EAAE;MACxB,MAAMG,GAAG,GAAGN,KAAK,CAACO,IAAI,CAACF,IAAI,CAAC;MAC5B,IAAIC,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,MAAM,EAAE;QACfN,KAAK,CAACO,IAAI,CAAC;UAACC,EAAE,EAAEJ,GAAG,CAACE,MAAM,CAACG,MAAM;UAAEC,IAAI,EAAEN,GAAG,CAACE,MAAM,CAACK;QAAQ,CAAC,CAAC;MAChE;IACF;IAEA,IAAIX,KAAK,CAACY,MAAM,GAAG,CAAC,EAAE;MACpBpB,kBAAM,CAACC,KAAK,CACT,yBAAwBO,KAAK,CAC3Ba,GAAG,CAAEC,IAAI,IAAM,GAAEA,IAAI,CAACJ,IAAK,MAAKI,IAAI,CAACN,EAAG,EAAC,CAAC,CAC1CO,IAAI,CAAC,IAAI,CAAE,EAAC,CAChB;IACH;IAEA,OAAOf,KAAK;EACd,CAAC,CAAC,OAAOgB,KAAK,EAAE;IACdxB,kBAAM,CAACwB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAQ;IAC9D,OAAO,EAAE;EACX;AACF;AAEO,eAAeC,aAAa,CAACjB,KAAa,EAAE;EACjD,MAAM;IAACkB;EAAkC,CAAC,GAAG,MAAM,IAAAC,kBAAO,EAAC;IACzDC,IAAI,EAAE,QAAQ;IACdV,IAAI,EAAE,cAAc;IACpBW,OAAO,EAAE,uDAAuD;IAChEC,OAAO,EAAEtB,KAAK,CAACa,GAAG,CAAEC,IAAU,KAAM;MAClCS,KAAK,EAAET,IAAI,CAACJ,IAAI;MAChBc,KAAK,EAAEV;IACT,CAAC,CAAC,CAAC;IACHW,GAAG,EAAE;EACP,CAAC,CAAC;EAEF,OAAOP,YAAY;AACrB"}