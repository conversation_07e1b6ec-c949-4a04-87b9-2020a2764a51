{"version": 3, "file": "RuleTester.d.ts", "sourceRoot": "", "sources": ["../../../src/eslint-utils/rule-tester/RuleTester.ts"], "names": [], "mappings": "AAOA,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEvD,OAAO,KAAK,cAAc,MAAM,4BAA4B,CAAC;AAE7D,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAGpE,QAAA,MAAM,gBAAgB,8BAA8B,CAAC;AAGrD,KAAK,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,QAAQ,CAAC,GAAG;IACxE,MAAM,EAAE,OAAO,gBAAgB,CAAC;IAChC;;OAEG;IACH,qBAAqB,CAAC,EAAE,oBAAoB,CAAC;CAC9C,CAAC;AAEF,UAAU,eAAe,CACvB,WAAW,SAAS,MAAM,EAC1B,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC,CACpC,SAAQ,cAAc,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC7D;;OAEG;IACH,qBAAqB,CAAC,EAAE,oBAAoB,CAAC;CAC9C;AACD,UAAU,aAAa,CAAC,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC,CAC1D,SAAQ,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC;IAC9C;;OAEG;IACH,qBAAqB,CAAC,EAAE,oBAAoB,CAAC;CAC9C;AACD,UAAU,QAAQ,CAChB,WAAW,SAAS,MAAM,EAC1B,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC;IAGpC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;IAC9D,QAAQ,CAAC,OAAO,EAAE,SAAS,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,CAAC;CACrE;AAED,KAAK,QAAQ,GAAG,CAAC,EAAE,EAAE,MAAM,IAAI,KAAK,IAAI,CAAC;AAezC,cAAM,UAAW,SAAQ,cAAc,CAAC,UAAU;;IAIhD;;;OAGG;IACH,MAAM,KAAK,QAAQ,IAAI,QAAQ,CAK9B;IACD,MAAM,KAAK,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,SAAS,EAE9C;IAED,OAAO,KAAK,UAAU,GAGrB;gBAEW,WAAW,EAAE,gBAAgB;IAiCzC,OAAO,CAAC,WAAW;IAoBnB,GAAG,CAAC,WAAW,SAAS,MAAM,EAAE,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC,EAClE,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,EACvC,aAAa,EAAE,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC,GAC7C,IAAI;CA6JR;AAED;;;GAGG;AACH,iBAAS,QAAQ,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAEtE;AAED,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AAChC,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC"}