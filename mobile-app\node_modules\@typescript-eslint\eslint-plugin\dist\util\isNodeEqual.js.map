{"version": 3, "file": "isNodeEqual.js", "sourceRoot": "", "sources": ["../../src/util/isNodeEqual.ts"], "names": [], "mappings": ";;;AACA,oDAA0D;AAE1D,SAAgB,WAAW,CAAC,CAAgB,EAAE,CAAgB;IAC5D,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE;QACrB,OAAO,KAAK,CAAC;KACd;IACD,IACE,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QACxC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EACxC;QACA,OAAO,IAAI,CAAC;KACb;IACD,IAAI,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE;QAC1E,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC;KAC5B;IACD,IACE,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QACpC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EACpC;QACA,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;KAC1B;IACD,IACE,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC1C,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAC1C;QACA,OAAO,CACL,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CACvE,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AA5BD,kCA4BC"}