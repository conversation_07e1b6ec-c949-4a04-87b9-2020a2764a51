{"version": 3, "names": ["MissingIcon", "CommonActions", "NavigationContext", "NavigationRouteContext", "useLinkBuilder", "useTheme", "React", "Animated", "Platform", "StyleSheet", "View", "useSafeAreaFrame", "BottomTabBarHeightCallbackContext", "useIsKeyboardShown", "BottomTabItem", "DEFAULT_TABBAR_HEIGHT", "COMPACT_TABBAR_HEIGHT", "DEFAULT_MAX_TAB_ITEM_WIDTH", "useNativeDriver", "OS", "shouldUseHorizontalLabels", "state", "descriptors", "layout", "dimensions", "tabBarLabelPosition", "routes", "index", "key", "options", "width", "max<PERSON>ab<PERSON><PERSON><PERSON>", "reduce", "acc", "route", "tabBarItemStyle", "flattenedStyle", "flatten", "max<PERSON><PERSON><PERSON>", "height", "getPaddingBottom", "insets", "Math", "max", "bottom", "select", "ios", "default", "getTabBarHeight", "style", "rest", "customHeight", "isLandscape", "horizontalLabels", "paddingBottom", "isPad", "BottomTabBar", "navigation", "colors", "buildLink", "focusedRoute", "focusedDescriptor", "focusedOptions", "tabBarShowLabel", "tabBarHideOnKeyboard", "tabBarVisibilityAnimationConfig", "tabBarStyle", "tabBarBackground", "tabBarActiveTintColor", "tabBarInactiveTintColor", "tabBarActiveBackgroundColor", "tabBarInactiveBackgroundColor", "isKeyboardShown", "onHeightChange", "useContext", "shouldShowTabBar", "visibilityAnimationConfigRef", "useRef", "useEffect", "current", "isTabBarHidden", "setIsTabBarHidden", "useState", "visible", "Value", "visibilityAnimationConfig", "animation", "show", "spring", "timing", "toValue", "duration", "config", "start", "finished", "hide", "stopAnimation", "setLayout", "handleLayout", "e", "nativeEvent", "tabBarHeight", "hasHorizontalLabels", "tabBarBackgroundElement", "styles", "tabBar", "backgroundColor", "card", "borderTopColor", "border", "transform", "translateY", "interpolate", "inputRange", "outputRange", "hairlineWidth", "position", "paddingHorizontal", "left", "right", "absoluteFill", "content", "map", "focused", "onPress", "event", "emit", "type", "target", "canPreventDefault", "defaultPrevented", "dispatch", "navigate", "name", "merge", "onLongPress", "label", "tabBarLabel", "undefined", "title", "accessibilityLabel", "tabBarAccessibilityLabel", "length", "params", "tabBarTestID", "tabBarAllowFontScaling", "tabBarButton", "tabBarIcon", "color", "size", "tabBarBadge", "tabBarBadgeStyle", "tabBarLabelStyle", "tabBarIconStyle", "create", "borderTopWidth", "elevation", "flex", "flexDirection"], "sourceRoot": "../../../src", "sources": ["views/BottomTabBar.tsx"], "mappings": "AAAA,SAASA,WAAW,QAAQ,4BAA4B;AACxD,SACEC,aAAa,EACbC,iBAAiB,EACjBC,sBAAsB,EAGtBC,cAAc,EACdC,QAAQ,QACH,0BAA0B;AACjC,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EAERC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AACrB,SAAqBC,gBAAgB,QAAQ,gCAAgC;AAG7E,OAAOC,iCAAiC,MAAM,4CAA4C;AAC1F,OAAOC,kBAAkB,MAAM,6BAA6B;AAC5D,OAAOC,aAAa,MAAM,iBAAiB;AAM3C,MAAMC,qBAAqB,GAAG,EAAE;AAChC,MAAMC,qBAAqB,GAAG,EAAE;AAChC,MAAMC,0BAA0B,GAAG,GAAG;AAEtC,MAAMC,eAAe,GAAGV,QAAQ,CAACW,EAAE,KAAK,KAAK;AAS7C,MAAMC,yBAAyB,GAAG,QAKnB;EAAA,IALoB;IACjCC,KAAK;IACLC,WAAW;IACXC,MAAM;IACNC;EACO,CAAC;EACR,MAAM;IAAEC;EAAoB,CAAC,GAC3BH,WAAW,CAACD,KAAK,CAACK,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO;EAEpD,IAAIJ,mBAAmB,EAAE;IACvB,QAAQA,mBAAmB;MACzB,KAAK,aAAa;QAChB,OAAO,IAAI;MACb,KAAK,YAAY;QACf,OAAO,KAAK;IAAC;EAEnB;EAEA,IAAIF,MAAM,CAACO,KAAK,IAAI,GAAG,EAAE;IACvB;IACA,MAAMC,WAAW,GAAGV,KAAK,CAACK,MAAM,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;MACtD,MAAM;QAAEC;MAAgB,CAAC,GAAGb,WAAW,CAACY,KAAK,CAACN,GAAG,CAAC,CAACC,OAAO;MAC1D,MAAMO,cAAc,GAAG3B,UAAU,CAAC4B,OAAO,CAACF,eAAe,CAAC;MAE1D,IAAIC,cAAc,EAAE;QAClB,IAAI,OAAOA,cAAc,CAACN,KAAK,KAAK,QAAQ,EAAE;UAC5C,OAAOG,GAAG,GAAGG,cAAc,CAACN,KAAK;QACnC,CAAC,MAAM,IAAI,OAAOM,cAAc,CAACE,QAAQ,KAAK,QAAQ,EAAE;UACtD,OAAOL,GAAG,GAAGG,cAAc,CAACE,QAAQ;QACtC;MACF;MAEA,OAAOL,GAAG,GAAGhB,0BAA0B;IACzC,CAAC,EAAE,CAAC,CAAC;IAEL,OAAOc,WAAW,IAAIR,MAAM,CAACO,KAAK;EACpC,CAAC,MAAM;IACL,OAAON,UAAU,CAACM,KAAK,GAAGN,UAAU,CAACe,MAAM;EAC7C;AACF,CAAC;AAED,MAAMC,gBAAgB,GAAIC,MAAkB,IAC1CC,IAAI,CAACC,GAAG,CAACF,MAAM,CAACG,MAAM,GAAGpC,QAAQ,CAACqC,MAAM,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAEtE,OAAO,MAAMC,eAAe,GAAG,SAUzB;EAAA;EAAA,IAV0B;IAC9B3B,KAAK;IACLC,WAAW;IACXE,UAAU;IACViB,MAAM;IACNQ,KAAK;IACL,GAAGC;EAIL,CAAC;EACC;EACA,MAAMC,YAAY,0BAAG1C,UAAU,CAAC4B,OAAO,CAACY,KAAK,CAAC,wDAAzB,oBAA2BV,MAAM;EAEtD,IAAI,OAAOY,YAAY,KAAK,QAAQ,EAAE;IACpC,OAAOA,YAAY;EACrB;EAEA,MAAMC,WAAW,GAAG5B,UAAU,CAACM,KAAK,GAAGN,UAAU,CAACe,MAAM;EACxD,MAAMc,gBAAgB,GAAGjC,yBAAyB,CAAC;IACjDC,KAAK;IACLC,WAAW;IACXE,UAAU;IACV,GAAG0B;EACL,CAAC,CAAC;EACF,MAAMI,aAAa,GAAGd,gBAAgB,CAACC,MAAM,CAAC;EAE9C,IACEjC,QAAQ,CAACW,EAAE,KAAK,KAAK,IACrB,CAACX,QAAQ,CAAC+C,KAAK,IACfH,WAAW,IACXC,gBAAgB,EAChB;IACA,OAAOrC,qBAAqB,GAAGsC,aAAa;EAC9C;EAEA,OAAOvC,qBAAqB,GAAGuC,aAAa;AAC9C,CAAC;AAED,eAAe,SAASE,YAAY,QAM1B;EAAA,IAN2B;IACnCnC,KAAK;IACLoC,UAAU;IACVnC,WAAW;IACXmB,MAAM;IACNQ;EACK,CAAC;EACN,MAAM;IAAES;EAAO,CAAC,GAAGrD,QAAQ,EAAE;EAC7B,MAAMsD,SAAS,GAAGvD,cAAc,EAAE;EAElC,MAAMwD,YAAY,GAAGvC,KAAK,CAACK,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC;EAC9C,MAAMkC,iBAAiB,GAAGvC,WAAW,CAACsC,YAAY,CAAChC,GAAG,CAAC;EACvD,MAAMkC,cAAc,GAAGD,iBAAiB,CAAChC,OAAO;EAEhD,MAAM;IACJkC,eAAe;IACfC,oBAAoB,GAAG,KAAK;IAC5BC,+BAA+B;IAC/BC,WAAW;IACXC,gBAAgB;IAChBC,qBAAqB;IACrBC,uBAAuB;IACvBC,2BAA2B;IAC3BC;EACF,CAAC,GAAGT,cAAc;EAElB,MAAMtC,UAAU,GAAGb,gBAAgB,EAAE;EACrC,MAAM6D,eAAe,GAAG3D,kBAAkB,EAAE;EAE5C,MAAM4D,cAAc,GAAGnE,KAAK,CAACoE,UAAU,CAAC9D,iCAAiC,CAAC;EAE1E,MAAM+D,gBAAgB,GAAG,EAAEX,oBAAoB,IAAIQ,eAAe,CAAC;EAEnE,MAAMI,4BAA4B,GAAGtE,KAAK,CAACuE,MAAM,CAC/CZ,+BAA+B,CAChC;EAED3D,KAAK,CAACwE,SAAS,CAAC,MAAM;IACpBF,4BAA4B,CAACG,OAAO,GAAGd,+BAA+B;EACxE,CAAC,CAAC;EAEF,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,KAAK,CAAC4E,QAAQ,CAAC,CAACP,gBAAgB,CAAC;EAE7E,MAAM,CAACQ,OAAO,CAAC,GAAG7E,KAAK,CAAC4E,QAAQ,CAC9B,MAAM,IAAI3E,QAAQ,CAAC6E,KAAK,CAACT,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC,CACnD;EAEDrE,KAAK,CAACwE,SAAS,CAAC,MAAM;IACpB,MAAMO,yBAAyB,GAAGT,4BAA4B,CAACG,OAAO;IAEtE,IAAIJ,gBAAgB,EAAE;MAAA;MACpB,MAAMW,SAAS,GACb,CAAAD,yBAAyB,aAAzBA,yBAAyB,gDAAzBA,yBAAyB,CAAEE,IAAI,0DAA/B,sBAAiCD,SAAS,MAAK,QAAQ,GACnD/E,QAAQ,CAACiF,MAAM,GACfjF,QAAQ,CAACkF,MAAM;MAErBH,SAAS,CAACH,OAAO,EAAE;QACjBO,OAAO,EAAE,CAAC;QACVxE,eAAe;QACfyE,QAAQ,EAAE,GAAG;QACb,IAAGN,yBAAyB,aAAzBA,yBAAyB,iDAAzBA,yBAAyB,CAAEE,IAAI,2DAA/B,uBAAiCK,MAAM;MAC5C,CAAC,CAAC,CAACC,KAAK,CAAC,SAAkB;QAAA,IAAjB;UAAEC;QAAS,CAAC;QACpB,IAAIA,QAAQ,EAAE;UACZb,iBAAiB,CAAC,KAAK,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MAAA;MACLA,iBAAiB,CAAC,IAAI,CAAC;MAEvB,MAAMK,SAAS,GACb,CAAAD,yBAAyB,aAAzBA,yBAAyB,iDAAzBA,yBAAyB,CAAEU,IAAI,2DAA/B,uBAAiCT,SAAS,MAAK,QAAQ,GACnD/E,QAAQ,CAACiF,MAAM,GACfjF,QAAQ,CAACkF,MAAM;MAErBH,SAAS,CAACH,OAAO,EAAE;QACjBO,OAAO,EAAE,CAAC;QACVxE,eAAe;QACfyE,QAAQ,EAAE,GAAG;QACb,IAAGN,yBAAyB,aAAzBA,yBAAyB,iDAAzBA,yBAAyB,CAAEU,IAAI,2DAA/B,uBAAiCH,MAAM;MAC5C,CAAC,CAAC,CAACC,KAAK,EAAE;IACZ;IAEA,OAAO,MAAMV,OAAO,CAACa,aAAa,EAAE;EACtC,CAAC,EAAE,CAACb,OAAO,EAAER,gBAAgB,CAAC,CAAC;EAE/B,MAAM,CAACpD,MAAM,EAAE0E,SAAS,CAAC,GAAG3F,KAAK,CAAC4E,QAAQ,CAAC;IACzC3C,MAAM,EAAE,CAAC;IACTT,KAAK,EAAEN,UAAU,CAACM;EACpB,CAAC,CAAC;EAEF,MAAMoE,YAAY,GAAIC,CAAoB,IAAK;IAC7C,MAAM;MAAE5D,MAAM;MAAET;IAAM,CAAC,GAAGqE,CAAC,CAACC,WAAW,CAAC7E,MAAM;IAE9CkD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGlC,MAAM,CAAC;IAExB0D,SAAS,CAAE1E,MAAM,IAAK;MACpB,IAAIgB,MAAM,KAAKhB,MAAM,CAACgB,MAAM,IAAIT,KAAK,KAAKP,MAAM,CAACO,KAAK,EAAE;QACtD,OAAOP,MAAM;MACf,CAAC,MAAM;QACL,OAAO;UACLgB,MAAM;UACNT;QACF,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM;IAAEJ;EAAO,CAAC,GAAGL,KAAK;EAExB,MAAMiC,aAAa,GAAGd,gBAAgB,CAACC,MAAM,CAAC;EAC9C,MAAM4D,YAAY,GAAGrD,eAAe,CAAC;IACnC3B,KAAK;IACLC,WAAW;IACXmB,MAAM;IACNjB,UAAU;IACVD,MAAM;IACN0B,KAAK,EAAE,CAACiB,WAAW,EAAEjB,KAAK;EAC5B,CAAC,CAAC;EAEF,MAAMqD,mBAAmB,GAAGlF,yBAAyB,CAAC;IACpDC,KAAK;IACLC,WAAW;IACXE,UAAU;IACVD;EACF,CAAC,CAAC;EAEF,MAAMgF,uBAAuB,GAAGpC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,EAAI;EAEpD,oBACE,oBAAC,QAAQ,CAAC,IAAI;IACZ,KAAK,EAAE,CACLqC,MAAM,CAACC,MAAM,EACb;MACEC,eAAe,EACbH,uBAAuB,IAAI,IAAI,GAAG,aAAa,GAAG7C,MAAM,CAACiD,IAAI;MAC/DC,cAAc,EAAElD,MAAM,CAACmD;IACzB,CAAC,EACD;MACEC,SAAS,EAAE,CACT;QACEC,UAAU,EAAE5B,OAAO,CAAC6B,WAAW,CAAC;UAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CACX3F,MAAM,CAACgB,MAAM,GAAGe,aAAa,GAAG7C,UAAU,CAAC0G,aAAa,EACxD,CAAC;QAEL,CAAC;MACH,CAAC,CACF;MACD;MACA;MACAC,QAAQ,EAAEpC,cAAc,GAAG,UAAU,GAAI;IAC3C,CAAC,EACD;MACEzC,MAAM,EAAE8D,YAAY;MACpB/C,aAAa;MACb+D,iBAAiB,EAAE3E,IAAI,CAACC,GAAG,CAACF,MAAM,CAAC6E,IAAI,EAAE7E,MAAM,CAAC8E,KAAK;IACvD,CAAC,EACDrD,WAAW,CACX;IACF,aAAa,EAAEc,cAAc,GAAG,MAAM,GAAG,MAAO;IAChD,QAAQ,EAAEkB;EAAa,gBAEvB,oBAAC,IAAI;IAAC,aAAa,EAAC,MAAM;IAAC,KAAK,EAAEzF,UAAU,CAAC+G;EAAa,GACvDjB,uBAAuB,CACnB,eACP,oBAAC,IAAI;IAAC,iBAAiB,EAAC,SAAS;IAAC,KAAK,EAAEC,MAAM,CAACiB;EAAQ,GACrD/F,MAAM,CAACgG,GAAG,CAAC,CAACxF,KAAK,EAAEP,KAAK,KAAK;IAC5B,MAAMgG,OAAO,GAAGhG,KAAK,KAAKN,KAAK,CAACM,KAAK;IACrC,MAAM;MAAEE;IAAQ,CAAC,GAAGP,WAAW,CAACY,KAAK,CAACN,GAAG,CAAC;IAE1C,MAAMgG,OAAO,GAAG,MAAM;MACpB,MAAMC,KAAK,GAAGpE,UAAU,CAACqE,IAAI,CAAC;QAC5BC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAE9F,KAAK,CAACN,GAAG;QACjBqG,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAI,CAACN,OAAO,IAAI,CAACE,KAAK,CAACK,gBAAgB,EAAE;QACvCzE,UAAU,CAAC0E,QAAQ,CAAC;UAClB,GAAGlI,aAAa,CAACmI,QAAQ,CAAC;YAAEC,IAAI,EAAEnG,KAAK,CAACmG,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAC,CAAC;UAC5DN,MAAM,EAAE3G,KAAK,CAACO;QAChB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,MAAM2G,WAAW,GAAG,MAAM;MACxB9E,UAAU,CAACqE,IAAI,CAAC;QACdC,IAAI,EAAE,cAAc;QACpBC,MAAM,EAAE9F,KAAK,CAACN;MAChB,CAAC,CAAC;IACJ,CAAC;IAED,MAAM4G,KAAK,GACT3G,OAAO,CAAC4G,WAAW,KAAKC,SAAS,GAC7B7G,OAAO,CAAC4G,WAAW,GACnB5G,OAAO,CAAC8G,KAAK,KAAKD,SAAS,GAC3B7G,OAAO,CAAC8G,KAAK,GACbzG,KAAK,CAACmG,IAAI;IAEhB,MAAMO,kBAAkB,GACtB/G,OAAO,CAACgH,wBAAwB,KAAKH,SAAS,GAC1C7G,OAAO,CAACgH,wBAAwB,GAChC,OAAOL,KAAK,KAAK,QAAQ,IAAIhI,QAAQ,CAACW,EAAE,KAAK,KAAK,GACjD,GAAEqH,KAAM,UAAS7G,KAAK,GAAG,CAAE,OAAMD,MAAM,CAACoH,MAAO,EAAC,GACjDJ,SAAS;IAEf,oBACE,oBAAC,iBAAiB,CAAC,QAAQ;MACzB,GAAG,EAAExG,KAAK,CAACN,GAAI;MACf,KAAK,EAAEN,WAAW,CAACY,KAAK,CAACN,GAAG,CAAC,CAAC6B;IAAW,gBAEzC,oBAAC,sBAAsB,CAAC,QAAQ;MAAC,KAAK,EAAEvB;IAAM,gBAC5C,oBAAC,aAAa;MACZ,KAAK,EAAEA,KAAM;MACb,UAAU,EAAEZ,WAAW,CAACY,KAAK,CAACN,GAAG,CAAE;MACnC,OAAO,EAAE+F,OAAQ;MACjB,UAAU,EAAErB,mBAAoB;MAChC,OAAO,EAAEsB,OAAQ;MACjB,WAAW,EAAEW,WAAY;MACzB,kBAAkB,EAAEK,kBAAmB;MACvC,EAAE,EAAEjF,SAAS,CAACzB,KAAK,CAACmG,IAAI,EAAEnG,KAAK,CAAC6G,MAAM,CAAE;MACxC,MAAM,EAAElH,OAAO,CAACmH,YAAa;MAC7B,gBAAgB,EAAEnH,OAAO,CAACoH,sBAAuB;MACjD,eAAe,EAAE7E,qBAAsB;MACvC,iBAAiB,EAAEC,uBAAwB;MAC3C,qBAAqB,EAAEC,2BAA4B;MACnD,uBAAuB,EAAEC,6BAA8B;MACvD,MAAM,EAAE1C,OAAO,CAACqH,YAAa;MAC7B,IAAI,EACFrH,OAAO,CAACsH,UAAU,KACjB;QAAA,IAAC;UAAEC,KAAK;UAAEC;QAAK,CAAC;QAAA,oBACf,oBAAC,WAAW;UAAC,KAAK,EAAED,KAAM;UAAC,IAAI,EAAEC;QAAK,EAAG;MAAA,CAC1C,CACF;MACD,KAAK,EAAExH,OAAO,CAACyH,WAAY;MAC3B,UAAU,EAAEzH,OAAO,CAAC0H,gBAAiB;MACrC,KAAK,EAAEf,KAAM;MACb,SAAS,EAAEzE,eAAgB;MAC3B,UAAU,EAAElC,OAAO,CAAC2H,gBAAiB;MACrC,SAAS,EAAE3H,OAAO,CAAC4H,eAAgB;MACnC,KAAK,EAAE5H,OAAO,CAACM;IAAgB,EAC/B,CAC8B,CACP;EAEjC,CAAC,CAAC,CACG,CACO;AAEpB;AAEA,MAAMqE,MAAM,GAAG/F,UAAU,CAACiJ,MAAM,CAAC;EAC/BjD,MAAM,EAAE;IACNa,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACR3E,MAAM,EAAE,CAAC;IACT+G,cAAc,EAAElJ,UAAU,CAAC0G,aAAa;IACxCyC,SAAS,EAAE;EACb,CAAC;EACDnC,OAAO,EAAE;IACPoC,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC"}