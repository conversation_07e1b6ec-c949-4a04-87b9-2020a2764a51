{"version": 3, "names": ["React", "MISSING_CONTEXT_ERROR", "createContext", "isDefault", "<PERSON><PERSON><PERSON>", "Error", "<PERSON><PERSON><PERSON>", "getState", "setState", "getIsInitial"], "sourceRoot": "../../src", "sources": ["NavigationStateContext.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,qBAAqB,GACzB,wKAAwK;AAE1K,4BAAeD,KAAK,CAACE,aAAa,CAc/B;EACDC,SAAS,EAAE,IAAI;EAEf,IAAIC,MAAM,GAAQ;IAChB,MAAM,IAAIC,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIK,MAAM,GAAQ;IAChB,MAAM,IAAID,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIM,QAAQ,GAAQ;IAClB,MAAM,IAAIF,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIO,QAAQ,GAAQ;IAClB,MAAM,IAAIH,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIQ,YAAY,GAAQ;IACtB,MAAM,IAAIJ,KAAK,CAACJ,qBAAqB,CAAC;EACxC;AACF,CAAC,CAAC"}