{"version": 3, "names": ["walk", "current", "fs", "lstatSync", "isDirectory", "files", "readdirSync", "map", "child", "path", "join", "result", "concat", "apply"], "sources": ["../../src/tools/walk.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport path from 'path';\n\nfunction walk(current: string): string[] {\n  if (!fs.lstatSync(current).isDirectory()) {\n    return [current];\n  }\n\n  const files = fs\n    .readdirSync(current)\n    .map((child) => walk(path.join(current, child)));\n  const result: string[] = [];\n  return result.concat.apply([current], files);\n}\n\nexport default walk;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AATxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,SAASA,IAAI,CAACC,OAAe,EAAY;EACvC,IAAI,CAACC,aAAE,CAACC,SAAS,CAACF,OAAO,CAAC,CAACG,WAAW,EAAE,EAAE;IACxC,OAAO,CAACH,OAAO,CAAC;EAClB;EAEA,MAAMI,KAAK,GAAGH,aAAE,CACbI,WAAW,CAACL,OAAO,CAAC,CACpBM,GAAG,CAAEC,KAAK,IAAKR,IAAI,CAACS,eAAI,CAACC,IAAI,CAACT,OAAO,EAAEO,KAAK,CAAC,CAAC,CAAC;EAClD,MAAMG,MAAgB,GAAG,EAAE;EAC3B,OAAOA,MAAM,CAACC,MAAM,CAACC,KAAK,CAAC,CAACZ,OAAO,CAAC,EAAEI,KAAK,CAAC;AAC9C;AAAC,eAEcL,IAAI;AAAA"}