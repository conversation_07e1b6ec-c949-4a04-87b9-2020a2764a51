{"version": 3, "names": ["React", "NavigationBuilderContext", "useOnPreventRemove", "shouldPreventRemove", "useOnAction", "router", "getState", "setState", "key", "actionListeners", "beforeRemoveListeners", "routerConfigOptions", "emitter", "onAction", "onActionParent", "onRouteFocus", "onRouteFocusParent", "addListener", "addListenerParent", "onDispatchAction", "useContext", "routerConfigOptionsRef", "useRef", "useEffect", "current", "useCallback", "action", "visitedNavigators", "Set", "state", "has", "add", "target", "result", "getStateForAction", "isPrevented", "routes", "undefined", "shouldFocus", "shouldActionChangeFocus", "i", "length", "listener"], "sourceRoot": "../../src", "sources": ["useOnAction.tsx"], "mappings": "AAOA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB,MAGxB,4BAA4B;AAGnC,OAAOC,kBAAkB,IAAIC,mBAAmB,QAAQ,sBAAsB;AAa9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAW,OASvB;EAAA,IATwB;IAClCC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,GAAG;IACHC,eAAe;IACfC,qBAAqB;IACrBC,mBAAmB;IACnBC;EACO,CAAC;EACR,MAAM;IACJC,QAAQ,EAAEC,cAAc;IACxBC,YAAY,EAAEC,kBAAkB;IAChCC,WAAW,EAAEC,iBAAiB;IAC9BC;EACF,CAAC,GAAGnB,KAAK,CAACoB,UAAU,CAACnB,wBAAwB,CAAC;EAE9C,MAAMoB,sBAAsB,GAC1BrB,KAAK,CAACsB,MAAM,CAAsBX,mBAAmB,CAAC;EAExDX,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpBF,sBAAsB,CAACG,OAAO,GAAGb,mBAAmB;EACtD,CAAC,CAAC;EAEF,MAAME,QAAQ,GAAGb,KAAK,CAACyB,WAAW,CAChC,UACEC,MAAwB,EAErB;IAAA,IADHC,iBAA8B,uEAAG,IAAIC,GAAG,EAAU;IAElD,MAAMC,KAAK,GAAGvB,QAAQ,EAAE;;IAExB;IACA;IACA,IAAIqB,iBAAiB,CAACG,GAAG,CAACD,KAAK,CAACrB,GAAG,CAAC,EAAE;MACpC,OAAO,KAAK;IACd;IAEAmB,iBAAiB,CAACI,GAAG,CAACF,KAAK,CAACrB,GAAG,CAAC;IAEhC,IAAI,OAAOkB,MAAM,CAACM,MAAM,KAAK,QAAQ,IAAIN,MAAM,CAACM,MAAM,KAAKH,KAAK,CAACrB,GAAG,EAAE;MACpE,IAAIyB,MAAM,GAAG5B,MAAM,CAAC6B,iBAAiB,CACnCL,KAAK,EACLH,MAAM,EACNL,sBAAsB,CAACG,OAAO,CAC/B;;MAED;MACA;MACAS,MAAM,GACJA,MAAM,KAAK,IAAI,IAAIP,MAAM,CAACM,MAAM,KAAKH,KAAK,CAACrB,GAAG,GAAGqB,KAAK,GAAGI,MAAM;MAEjE,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnBd,gBAAgB,CAACO,MAAM,EAAEG,KAAK,KAAKI,MAAM,CAAC;QAE1C,IAAIJ,KAAK,KAAKI,MAAM,EAAE;UACpB,MAAME,WAAW,GAAGhC,mBAAmB,CACrCS,OAAO,EACPF,qBAAqB,EACrBmB,KAAK,CAACO,MAAM,EACZH,MAAM,CAACG,MAAM,EACbV,MAAM,CACP;UAED,IAAIS,WAAW,EAAE;YACf,OAAO,IAAI;UACb;UAEA5B,QAAQ,CAAC0B,MAAM,CAAC;QAClB;QAEA,IAAIjB,kBAAkB,KAAKqB,SAAS,EAAE;UACpC;UACA;UACA,MAAMC,WAAW,GAAGjC,MAAM,CAACkC,uBAAuB,CAACb,MAAM,CAAC;UAE1D,IAAIY,WAAW,IAAI9B,GAAG,KAAK6B,SAAS,EAAE;YACpCrB,kBAAkB,CAACR,GAAG,CAAC;UACzB;QACF;QAEA,OAAO,IAAI;MACb;IACF;IAEA,IAAIM,cAAc,KAAKuB,SAAS,EAAE;MAChC;MACA,IAAIvB,cAAc,CAACY,MAAM,EAAEC,iBAAiB,CAAC,EAAE;QAC7C,OAAO,IAAI;MACb;IACF;;IAEA;IACA,KAAK,IAAIa,CAAC,GAAG/B,eAAe,CAACgC,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpD,MAAME,QAAQ,GAAGjC,eAAe,CAAC+B,CAAC,CAAC;MAEnC,IAAIE,QAAQ,CAAChB,MAAM,EAAEC,iBAAiB,CAAC,EAAE;QACvC,OAAO,IAAI;MACb;IACF;IAEA,OAAO,KAAK;EACd,CAAC,EACD,CACElB,eAAe,EACfC,qBAAqB,EACrBE,OAAO,EACPN,QAAQ,EACRE,GAAG,EACHM,cAAc,EACdK,gBAAgB,EAChBH,kBAAkB,EAClBX,MAAM,EACNE,QAAQ,CACT,CACF;EAEDL,kBAAkB,CAAC;IACjBI,QAAQ;IACRM,OAAO;IACPF;EACF,CAAC,CAAC;EAEFV,KAAK,CAACuB,SAAS,CACb,MAAML,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAG,QAAQ,EAAEL,QAAQ,CAAC,EAC7C,CAACK,iBAAiB,EAAEL,QAAQ,CAAC,CAC9B;EAED,OAAOA,QAAQ;AACjB"}