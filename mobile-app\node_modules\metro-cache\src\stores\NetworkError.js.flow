/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict
 */

'use strict';

class NetworkError extends Error {
  code: string;

  constructor(message: string, code: string) {
    super(message);

    this.code = code;
  }
}

module.exports = NetworkError;
