{"version": 3, "names": [], "sources": ["../src/android.ts"], "sourcesContent": ["export interface AndroidProjectConfig {\n  sourceDir: string;\n  appName: string;\n  packageName: string;\n  dependencyConfiguration?: string;\n  unstable_reactLegacyComponentNames?: string[] | null;\n}\n\nexport type AndroidProjectParams = {\n  sourceDir?: string;\n  appName?: string;\n  manifestPath?: string;\n  packageName?: string;\n  dependencyConfiguration?: string;\n  unstable_reactLegacyComponentNames?: string[] | null;\n};\n\nexport type AndroidDependencyConfig = {\n  sourceDir: string;\n  packageImportPath: string;\n  packageInstance: string;\n  dependencyConfiguration?: string;\n  buildTypes: string[];\n  libraryName?: string | null;\n  componentDescriptors?: string[] | null;\n  cmakeListsPath?: string | null;\n};\n\nexport type AndroidDependencyParams = {\n  sourceDir?: string;\n  manifestPath?: string;\n  packageName?: string;\n  dependencyConfiguration?: string;\n  packageImportPath?: string;\n  packageInstance?: string;\n  buildTypes?: string[];\n  libraryName?: string | null;\n  componentDescriptors?: string[] | null;\n  cmakeListsPath?: string | null;\n};\n"], "mappings": ""}