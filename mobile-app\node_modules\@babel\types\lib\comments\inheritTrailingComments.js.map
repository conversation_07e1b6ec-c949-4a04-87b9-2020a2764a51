{"version": 3, "names": ["_inherit", "require", "inheritTrailingComments", "child", "parent", "inherit"], "sources": ["../../src/comments/inheritTrailingComments.ts"], "sourcesContent": ["import inherit from \"../utils/inherit.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function inheritTrailingComments(\n  child: t.Node,\n  parent: t.Node,\n): void {\n  inherit(\"trailingComments\", child, parent);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAGe,SAASC,uBAAuBA,CAC7CC,KAAa,EACbC,MAAc,EACR;EACN,IAAAC,gBAAO,EAAC,kBAAkB,EAAEF,KAAK,EAAEC,MAAM,CAAC;AAC5C", "ignoreList": []}