{"version": 3, "names": ["xcodeEnvFile", "pathSeparator", "removeLastPathComponent", "pathString", "path", "dirname", "pathHasXcodeEnvFile", "xcodeEnvPath", "fs", "existsSync", "pathDoesNotHaveXcodeEnvFile", "label", "description", "getDiagnostics", "_", "config", "projectRoot", "root", "findProjectRoot", "missingXcodeEnvFile", "findPodfilePaths", "some", "p", "basePath", "needsToBeFixed", "e", "message", "runAutomaticFix", "loader", "stop", "templateXcodeEnv", "templateIosPath", "resolveNodeModuleDir", "src", "copyFileAsync", "promisify", "copyFile", "map", "filter", "for<PERSON>ach", "destFile<PERSON>ath", "succeed", "fail"], "sources": ["../../../src/tools/healthchecks/xcodeEnv.ts"], "sourcesContent": ["import {findPodfilePaths} from '@react-native-community/cli-platform-ios';\nimport {\n  findProjectRoot,\n  resolveNodeModuleDir,\n} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport path from 'path';\nimport {promisify} from 'util';\nimport {HealthCheckInterface} from '../../types';\n\nconst xcodeEnvFile = '.xcode.env';\nconst pathSeparator = '/';\n\nfunction removeLastPathComponent(pathString: string): string {\n  return path.dirname(pathString);\n}\n\nfunction pathHasXcodeEnvFile(pathString: string): boolean {\n  const xcodeEnvPath = pathString + pathSeparator + xcodeEnvFile;\n  return fs.existsSync(xcodeEnvPath);\n}\n\nfunction pathDoesNotHaveXcodeEnvFile(pathString: string): boolean {\n  return !pathHasXcodeEnvFile(pathString);\n}\n\nexport default {\n  label: '.xcode.env',\n  description: 'File to customize Xcode environment',\n  getDiagnostics: async (_, config) => {\n    try {\n      const projectRoot = config?.root ?? findProjectRoot();\n      const missingXcodeEnvFile = findPodfilePaths(projectRoot).some((p) => {\n        const basePath = path.dirname(p);\n        return !pathHasXcodeEnvFile(basePath);\n      });\n      return {\n        needsToBeFixed: missingXcodeEnvFile,\n      };\n    } catch (e) {\n      return {\n        needsToBeFixed: (e as any).message,\n      };\n    }\n  },\n  runAutomaticFix: async ({loader, config}) => {\n    try {\n      loader.stop();\n      const templateXcodeEnv = '_xcode.env';\n      const projectRoot = config?.root ?? findProjectRoot();\n      const templateIosPath = resolveNodeModuleDir(\n        projectRoot,\n        'react-native/template/ios',\n      );\n      const src = templateIosPath + pathSeparator + templateXcodeEnv;\n      const copyFileAsync = promisify(fs.copyFile);\n\n      findPodfilePaths(projectRoot)\n        .map(removeLastPathComponent)\n        // avoid overriding existing .xcode.env\n        .filter(pathDoesNotHaveXcodeEnvFile)\n        .forEach(async (pathString: string) => {\n          const destFilePath = pathString + pathSeparator + xcodeEnvFile;\n          await copyFileAsync(src, destFilePath);\n        });\n      loader.succeed('.xcode.env file have been created!');\n    } catch (e) {\n      loader.fail(e as any);\n    }\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+B;AAG/B,MAAMA,YAAY,GAAG,YAAY;AACjC,MAAMC,aAAa,GAAG,GAAG;AAEzB,SAASC,uBAAuB,CAACC,UAAkB,EAAU;EAC3D,OAAOC,eAAI,CAACC,OAAO,CAACF,UAAU,CAAC;AACjC;AAEA,SAASG,mBAAmB,CAACH,UAAkB,EAAW;EACxD,MAAMI,YAAY,GAAGJ,UAAU,GAAGF,aAAa,GAAGD,YAAY;EAC9D,OAAOQ,aAAE,CAACC,UAAU,CAACF,YAAY,CAAC;AACpC;AAEA,SAASG,2BAA2B,CAACP,UAAkB,EAAW;EAChE,OAAO,CAACG,mBAAmB,CAACH,UAAU,CAAC;AACzC;AAAC,eAEc;EACbQ,KAAK,EAAE,YAAY;EACnBC,WAAW,EAAE,qCAAqC;EAClDC,cAAc,EAAE,OAAOC,CAAC,EAAEC,MAAM,KAAK;IACnC,IAAI;MACF,MAAMC,WAAW,GAAG,CAAAD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,IAAI,KAAI,IAAAC,2BAAe,GAAE;MACrD,MAAMC,mBAAmB,GAAG,IAAAC,kCAAgB,EAACJ,WAAW,CAAC,CAACK,IAAI,CAAEC,CAAC,IAAK;QACpE,MAAMC,QAAQ,GAAGnB,eAAI,CAACC,OAAO,CAACiB,CAAC,CAAC;QAChC,OAAO,CAAChB,mBAAmB,CAACiB,QAAQ,CAAC;MACvC,CAAC,CAAC;MACF,OAAO;QACLC,cAAc,EAAEL;MAClB,CAAC;IACH,CAAC,CAAC,OAAOM,CAAC,EAAE;MACV,OAAO;QACLD,cAAc,EAAGC,CAAC,CAASC;MAC7B,CAAC;IACH;EACF,CAAC;EACDC,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEb;EAAM,CAAC,KAAK;IAC3C,IAAI;MACFa,MAAM,CAACC,IAAI,EAAE;MACb,MAAMC,gBAAgB,GAAG,YAAY;MACrC,MAAMd,WAAW,GAAG,CAAAD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,IAAI,KAAI,IAAAC,2BAAe,GAAE;MACrD,MAAMa,eAAe,GAAG,IAAAC,gCAAoB,EAC1ChB,WAAW,EACX,2BAA2B,CAC5B;MACD,MAAMiB,GAAG,GAAGF,eAAe,GAAG9B,aAAa,GAAG6B,gBAAgB;MAC9D,MAAMI,aAAa,GAAG,IAAAC,iBAAS,EAAC3B,aAAE,CAAC4B,QAAQ,CAAC;MAE5C,IAAAhB,kCAAgB,EAACJ,WAAW,CAAC,CAC1BqB,GAAG,CAACnC,uBAAuB;MAC5B;MAAA,CACCoC,MAAM,CAAC5B,2BAA2B,CAAC,CACnC6B,OAAO,CAAC,MAAOpC,UAAkB,IAAK;QACrC,MAAMqC,YAAY,GAAGrC,UAAU,GAAGF,aAAa,GAAGD,YAAY;QAC9D,MAAMkC,aAAa,CAACD,GAAG,EAAEO,YAAY,CAAC;MACxC,CAAC,CAAC;MACJZ,MAAM,CAACa,OAAO,CAAC,oCAAoC,CAAC;IACtD,CAAC,CAAC,OAAOhB,CAAC,EAAE;MACVG,MAAM,CAACc,IAAI,CAACjB,CAAC,CAAQ;IACvB;EACF;AACF,CAAC;AAAA"}