{"version": 3, "file": "simple-traverse.js", "sourceRoot": "", "sources": ["../src/simple-traverse.ts"], "names": [], "mappings": ";;;AAAA,kEAA8D;AAI9D,8DAA8D;AAC9D,SAAS,WAAW,CAAC,CAAM;IACzB,sEAAsE;IACtE,OAAO,CAAC,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;AAC1E,CAAC;AAED,SAAS,qBAAqB,CAC5B,cAAkC,EAClC,IAAmB;IAEnB,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,OAAO,CAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,EAAE,CAAU,CAAC;AAC/B,CAAC;AAaD,MAAM,eAAe;IAKnB,YAAY,SAAgC,EAAE,iBAAiB,GAAG,KAAK;QAJtD,mBAAc,GAAG,0BAAW,CAAC;QAK5C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC7C,CAAC;IAED,QAAQ,CAAC,IAAa,EAAE,MAAiC;QACvD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;YACtB,OAAO;SACR;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE;YAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACpC;aAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACtC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACzC;QAED,MAAM,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACnB,OAAO;SACR;QAED,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YAElC,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;gBAClC,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE;oBACnC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;iBAC5B;aACF;iBAAM;gBACL,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;aACtC;SACF;IACH,CAAC;CACF;AAED,SAAgB,cAAc,CAC5B,YAA2B,EAC3B,OAA8B,EAC9B,iBAAiB,GAAG,KAAK;IAEzB,IAAI,eAAe,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CACtD,YAAY,EACZ,SAAS,CACV,CAAC;AACJ,CAAC;AATD,wCASC"}