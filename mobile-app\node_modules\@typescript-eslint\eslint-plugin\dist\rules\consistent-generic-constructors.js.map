{"version": 3, "file": "consistent-generic-constructors.js", "sourceRoot": "", "sources": ["../../src/rules/consistent-generic-constructors.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAqC;AAKrC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iCAAiC;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,wGAAwG;YAC1G,WAAW,EAAE,QAAQ;SACtB;QACD,QAAQ,EAAE;YACR,oBAAoB,EAClB,gFAAgF;YAClF,iBAAiB,EACf,2FAA2F;SAC9F;QACD,OAAO,EAAE,MAAM;QACf,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,CAAC,iBAAiB,EAAE,aAAa,CAAC;aACzC;SACF;KACF;IACD,cAAc,EAAE,CAAC,aAAa,CAAC;IAC/B,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QACpB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO;YACL,4GAA4G,CAC1G,IAG8B;;gBAE9B,SAAS,SAAS;oBAIhB,QAAQ,IAAI,CAAC,IAAI,EAAE;wBACjB,KAAK,sBAAc,CAAC,kBAAkB;4BACpC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC9B,KAAK,sBAAc,CAAC,kBAAkB;4BACpC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC5B,KAAK,sBAAc,CAAC,iBAAiB;4BACnC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;wBACjC;4BACE,MAAM,IAAI,KAAK,CACb,wBAAyB,IAAyB,CAAC,IAAI,EAAE,CAC1D,CAAC;qBACL;gBACH,CAAC;gBACD,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,SAAS,EAAE,CAAC;gBACnC,MAAM,GAAG,GAAG,MAAA,OAAO,CAAC,cAAc,0CAAE,cAAc,CAAC;gBAEnD,IACE,CAAC,GAAG;oBACJ,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;oBACzC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAC7C;oBACA,OAAO;iBACR;gBACD,IACE,GAAG;oBACH,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;wBAC1C,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;wBAC/C,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EACxC;oBACA,OAAO;iBACR;gBACD,IAAI,IAAI,KAAK,iBAAiB,EAAE;oBAC9B,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,cAAc,EAAE;wBAC9B,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC;wBACvC,MAAM,cAAc,GAClB,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;wBAClE,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,sBAAsB;4BACjC,GAAG,CAAC,KAAK;gCACP,SAAS,uBAAuB;oCAG9B,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE;wCACnD,OAAO,OAAO,CAAC;qCAChB;oCACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;wCAClB,OAAO,IAAI,CAAC,GAAG,CAAC;qCACjB;oCACD,oDAAoD;oCACpD,mEAAmE;oCACnE,OAAO,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAE,CAAC;gCAC7C,CAAC;gCACD,OAAO;oCACL,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC;oCAC5B,KAAK,CAAC,eAAe,CACnB,uBAAuB,EAAE,EACzB,IAAI,GAAG,cAAc,CACtB;iCACF,CAAC;4BACJ,CAAC;yBACF,CAAC,CAAC;qBACJ;oBACD,OAAO;iBACR;gBACD,IAAI,IAAI,KAAK,aAAa,EAAE;oBAC1B,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,cAAc,KAAI,CAAC,GAAG,CAAC,cAAc,EAAE;wBAC9C,MAAM,SAAS,GACb,CAAA,MAAA,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,0CAAE,KAAK,MAAK,GAAG,CAAC;wBACtD,MAAM,aAAa,GAAG,IAAI,GAAG,CAC3B,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAO,CAAC,CAC1C,CAAC;wBACF,UAAU;6BACP,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC;6BACrC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzC,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,mBAAmB;4BAC9B,CAAC,GAAG,CAAC,KAAK;gCACR,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,CAAC;gCAChC,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE;oCACnC,MAAM,KAAK,CAAC,eAAe,CACzB,GAAG,CAAC,MAAM,EACV,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAC5B,CAAC;iCACH;gCACD,MAAM,KAAK,CAAC,eAAe,CACzB,GAAG,CAAC,MAAM,EACV,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CACvC,CAAC;gCACF,IAAI,CAAC,SAAS,EAAE;oCACd,MAAM,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iCAC/C;4BACH,CAAC;yBACF,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}