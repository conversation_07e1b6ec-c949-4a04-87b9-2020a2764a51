{"version": 3, "names": ["ACTIONS", "exports", "GET_DEFAULT_EXTENSIONS", "SET_OPTIONS", "TRANSFORM", "TRANSFORM_SYNC"], "sources": ["../src/types.cts"], "sourcesContent": ["export const enum ACTIONS {\n  GET_DEFAULT_EXTENSIONS = \"GET_DEFAULT_EXTENSIONS\",\n  SET_OPTIONS = \"SET_OPTIONS\",\n  TRANSFORM = \"TRANSFORM\",\n  TRANSFORM_SYNC = \"TRANSFORM_SYNC\",\n}\n\nexport type Options = {\n  extensions?: string[];\n};\n\nexport interface IClient {\n  getDefaultExtensions(): string[];\n  setOptions(options: Options): void;\n  transform(\n    code: string,\n    filename: string,\n  ): { code: string; map: object } | null;\n}\n"], "mappings": ";;;;;;MAAkBA,OAAO,GAAAC,OAAA,CAAAD,OAAA;EAAAE,sBAAA;EAAAC,WAAA;EAAAC,SAAA;EAAAC,cAAA;AAAA", "ignoreList": []}