{"version": 3, "names": ["TYPE_ROUTE", "TabActions", "jumpTo", "name", "params", "type", "payload", "getRouteHistory", "routes", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialRouteName", "history", "key", "initialRouteIndex", "i", "unshift", "findIndex", "route", "changeIndex", "state", "current<PERSON><PERSON>", "filter", "it", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "router", "BaseRouter", "getInitialState", "routeNames", "routeParamList", "undefined", "includes", "indexOf", "map", "nanoid", "stale", "getRehydratedState", "partialState", "find", "r", "Math", "min", "max", "length", "getStateForRouteNamesChange", "routeKeyChanges", "getStateForRouteFocus", "getStateForAction", "action", "routeGetIdList", "getId", "currentId", "nextId", "merge", "path", "previousKey", "slice", "shouldActionChangeFocus", "actionCreators"], "sourceRoot": "../../src", "sources": ["TabRouter.tsx"], "mappings": ";;;;;;;AAAA;AAEA;AAAsC;AAyDtC,MAAMA,UAAU,GAAG,OAAgB;AAE5B,MAAMC,UAAU,GAAG;EACxBC,MAAM,CAACC,IAAY,EAAEC,MAAe,EAAiB;IACnD,OAAO;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;QAAEH,IAAI;QAAEC;MAAO;IAAE,CAAC;EACvD;AACF,CAAC;AAAC;AAEF,MAAMG,eAAe,GAAG,CACtBC,MAAuB,EACvBC,KAAa,EACbC,YAA0B,EAC1BC,gBAAoC,KACjC;EACH,MAAMC,OAAO,GAAG,CAAC;IAAEP,IAAI,EAAEL,UAAU;IAAEa,GAAG,EAAEL,MAAM,CAACC,KAAK,CAAC,CAACI;EAAI,CAAC,CAAC;EAC9D,IAAIC,iBAAiB;EAErB,QAAQJ,YAAY;IAClB,KAAK,OAAO;MACV,KAAK,IAAIK,CAAC,GAAGN,KAAK,EAAEM,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9BH,OAAO,CAACI,OAAO,CAAC;UAAEX,IAAI,EAAEL,UAAU;UAAEa,GAAG,EAAEL,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,CAACF;QAAI,CAAC,CAAC;MAC/D;MACA;IACF,KAAK,YAAY;MACf,IAAIJ,KAAK,KAAK,CAAC,EAAE;QACfG,OAAO,CAACI,OAAO,CAAC;UACdX,IAAI,EAAEL,UAAU;UAChBa,GAAG,EAAEL,MAAM,CAAC,CAAC,CAAC,CAACK;QACjB,CAAC,CAAC;MACJ;MACA;IACF,KAAK,cAAc;MACjBC,iBAAiB,GAAGN,MAAM,CAACS,SAAS,CACjCC,KAAK,IAAKA,KAAK,CAACf,IAAI,KAAKQ,gBAAgB,CAC3C;MACDG,iBAAiB,GAAGA,iBAAiB,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,iBAAiB;MAEpE,IAAIL,KAAK,KAAKK,iBAAiB,EAAE;QAC/BF,OAAO,CAACI,OAAO,CAAC;UACdX,IAAI,EAAEL,UAAU;UAChBa,GAAG,EAAEL,MAAM,CAACM,iBAAiB,CAAC,CAACD;QACjC,CAAC,CAAC;MACJ;MACA;IACF,KAAK,SAAS;MACZ;MACA;EAAM;EAGV,OAAOD,OAAO;AAChB,CAAC;AAED,MAAMO,WAAW,GAAG,CAClBC,KAAwC,EACxCX,KAAa,EACbC,YAA0B,EAC1BC,gBAAoC,KACjC;EACH,IAAIC,OAAO;EAEX,IAAIF,YAAY,KAAK,SAAS,EAAE;IAC9B,MAAMW,UAAU,GAAGD,KAAK,CAACZ,MAAM,CAACC,KAAK,CAAC,CAACI,GAAG;IAE1CD,OAAO,GAAGQ,KAAK,CAACR,OAAO,CACpBU,MAAM,CAAEC,EAAE,IAAMA,EAAE,CAAClB,IAAI,KAAK,OAAO,GAAGkB,EAAE,CAACV,GAAG,KAAKQ,UAAU,GAAG,KAAM,CAAC,CACrEG,MAAM,CAAC;MAAEnB,IAAI,EAAEL,UAAU;MAAEa,GAAG,EAAEQ;IAAW,CAAC,CAAC;EAClD,CAAC,MAAM;IACLT,OAAO,GAAGL,eAAe,CACvBa,KAAK,CAACZ,MAAM,EACZC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;EACH;EAEA,OAAO;IACL,GAAGS,KAAK;IACRX,KAAK;IACLG;EACF,CAAC;AACH,CAAC;AAEc,SAASa,SAAS,OAGZ;EAAA,IAHa;IAChCd,gBAAgB;IAChBD,YAAY,GAAG;EACC,CAAC;EACjB,MAAMgB,MAGL,GAAG;IACF,GAAGC,mBAAU;IAEbtB,IAAI,EAAE,KAAK;IAEXuB,eAAe,QAAiC;MAAA,IAAhC;QAAEC,UAAU;QAAEC;MAAe,CAAC;MAC5C,MAAMrB,KAAK,GACTE,gBAAgB,KAAKoB,SAAS,IAAIF,UAAU,CAACG,QAAQ,CAACrB,gBAAgB,CAAC,GACnEkB,UAAU,CAACI,OAAO,CAACtB,gBAAgB,CAAC,GACpC,CAAC;MAEP,MAAMH,MAAM,GAAGqB,UAAU,CAACK,GAAG,CAAE/B,IAAI,KAAM;QACvCA,IAAI;QACJU,GAAG,EAAG,GAAEV,IAAK,IAAG,IAAAgC,iBAAM,GAAG,EAAC;QAC1B/B,MAAM,EAAE0B,cAAc,CAAC3B,IAAI;MAC7B,CAAC,CAAC,CAAC;MAEH,MAAMS,OAAO,GAAGL,eAAe,CAC7BC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;MAED,OAAO;QACLyB,KAAK,EAAE,KAAK;QACZ/B,IAAI,EAAE,KAAK;QACXQ,GAAG,EAAG,OAAM,IAAAsB,iBAAM,GAAG,EAAC;QACtB1B,KAAK;QACLoB,UAAU;QACVjB,OAAO;QACPJ;MACF,CAAC;IACH,CAAC;IAED6B,kBAAkB,CAACC,YAAY,SAAkC;MAAA;MAAA,IAAhC;QAAET,UAAU;QAAEC;MAAe,CAAC;MAC7D,IAAIV,KAAK,GAAGkB,YAAY;MAExB,IAAIlB,KAAK,CAACgB,KAAK,KAAK,KAAK,EAAE;QACzB,OAAOhB,KAAK;MACd;MAEA,MAAMZ,MAAM,GAAGqB,UAAU,CAACK,GAAG,CAAE/B,IAAI,IAAK;QACtC,MAAMe,KAAK,GACTE,KAAK,CACLZ,MAAM,CAAC+B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrC,IAAI,KAAKA,IAAI,CAAC;QAErC,OAAO;UACL,GAAGe,KAAK;UACRf,IAAI;UACJU,GAAG,EACDK,KAAK,IAAIA,KAAK,CAACf,IAAI,KAAKA,IAAI,IAAIe,KAAK,CAACL,GAAG,GACrCK,KAAK,CAACL,GAAG,GACR,GAAEV,IAAK,IAAG,IAAAgC,iBAAM,GAAG,EAAC;UAC3B/B,MAAM,EACJ0B,cAAc,CAAC3B,IAAI,CAAC,KAAK4B,SAAS,GAC9B;YACE,GAAGD,cAAc,CAAC3B,IAAI,CAAC;YACvB,IAAIe,KAAK,GAAGA,KAAK,CAACd,MAAM,GAAG2B,SAAS;UACtC,CAAC,GACDb,KAAK,GACLA,KAAK,CAACd,MAAM,GACZ2B;QACR,CAAC;MACH,CAAC,CAAC;MAEF,MAAMtB,KAAK,GAAGgC,IAAI,CAACC,GAAG,CACpBD,IAAI,CAACE,GAAG,CAACd,UAAU,CAACI,OAAO,kBAACb,KAAK,CAACZ,MAAM,CAAC,CAAAY,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEX,KAAK,KAAI,CAAC,CAAC,kDAA/B,cAAiCN,IAAI,CAAC,EAAE,CAAC,CAAC,EACtEK,MAAM,CAACoC,MAAM,GAAG,CAAC,CAClB;MAED,MAAMhC,OAAO,GACX,mBAAAQ,KAAK,CAACR,OAAO,mDAAb,eAAeU,MAAM,CAAEC,EAAE,IAAKf,MAAM,CAAC+B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,GAAG,KAAKU,EAAE,CAACV,GAAG,CAAC,CAAC,KACnE,EAAE;MAEJ,OAAOM,WAAW,CAChB;QACEiB,KAAK,EAAE,KAAK;QACZ/B,IAAI,EAAE,KAAK;QACXQ,GAAG,EAAG,OAAM,IAAAsB,iBAAM,GAAG,EAAC;QACtB1B,KAAK;QACLoB,UAAU;QACVjB,OAAO;QACPJ;MACF,CAAC,EACDC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;IACH,CAAC;IAEDkC,2BAA2B,CACzBzB,KAAK,SAEL;MAAA,IADA;QAAES,UAAU;QAAEC,cAAc;QAAEgB;MAAgB,CAAC;MAE/C,MAAMtC,MAAM,GAAGqB,UAAU,CAACK,GAAG,CAC1B/B,IAAI,IACHiB,KAAK,CAACZ,MAAM,CAAC+B,IAAI,CACdC,CAAC,IAAKA,CAAC,CAACrC,IAAI,KAAKA,IAAI,IAAI,CAAC2C,eAAe,CAACd,QAAQ,CAACQ,CAAC,CAACrC,IAAI,CAAC,CAC5D,IAAI;QACHA,IAAI;QACJU,GAAG,EAAG,GAAEV,IAAK,IAAG,IAAAgC,iBAAM,GAAG,EAAC;QAC1B/B,MAAM,EAAE0B,cAAc,CAAC3B,IAAI;MAC7B,CAAC,CACJ;MAED,MAAMM,KAAK,GAAGgC,IAAI,CAACE,GAAG,CACpB,CAAC,EACDd,UAAU,CAACI,OAAO,CAACb,KAAK,CAACZ,MAAM,CAACY,KAAK,CAACX,KAAK,CAAC,CAACN,IAAI,CAAC,CACnD;MAED,IAAIS,OAAO,GAAGQ,KAAK,CAACR,OAAO,CAACU,MAAM;MAChC;MACCC,EAAE,IAAKA,EAAE,CAAClB,IAAI,KAAK,OAAO,IAAIG,MAAM,CAAC+B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,GAAG,KAAKU,EAAE,CAACV,GAAG,CAAC,CACpE;MAED,IAAI,CAACD,OAAO,CAACgC,MAAM,EAAE;QACnBhC,OAAO,GAAGL,eAAe,CACvBC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;MACH;MAEA,OAAO;QACL,GAAGS,KAAK;QACRR,OAAO;QACPiB,UAAU;QACVrB,MAAM;QACNC;MACF,CAAC;IACH,CAAC;IAEDsC,qBAAqB,CAAC3B,KAAK,EAAEP,GAAG,EAAE;MAChC,MAAMJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAAEuB,CAAC,IAAKA,CAAC,CAAC3B,GAAG,KAAKA,GAAG,CAAC;MAE1D,IAAIJ,KAAK,KAAK,CAAC,CAAC,IAAIA,KAAK,KAAKW,KAAK,CAACX,KAAK,EAAE;QACzC,OAAOW,KAAK;MACd;MAEA,OAAOD,WAAW,CAACC,KAAK,EAAEX,KAAK,EAAEC,YAAY,EAAEC,gBAAgB,CAAC;IAClE,CAAC;IAEDqC,iBAAiB,CAAC5B,KAAK,EAAE6B,MAAM,SAAsC;MAAA,IAApC;QAAEnB,cAAc;QAAEoB;MAAe,CAAC;MACjE,QAAQD,MAAM,CAAC5C,IAAI;QACjB,KAAK,SAAS;QACd,KAAK,UAAU;UAAE;YACf,IAAII,KAAK,GAAG,CAAC,CAAC;YAEd,IAAIwC,MAAM,CAAC5C,IAAI,KAAK,UAAU,IAAI4C,MAAM,CAAC3C,OAAO,CAACO,GAAG,EAAE;cACpDJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAC3BC,KAAK,IAAKA,KAAK,CAACL,GAAG,KAAKoC,MAAM,CAAC3C,OAAO,CAACO,GAAG,CAC5C;YACH,CAAC,MAAM;cACLJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAC3BC,KAAK,IAAKA,KAAK,CAACf,IAAI,KAAK8C,MAAM,CAAC3C,OAAO,CAACH,IAAI,CAC9C;YACH;YAEA,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,OAAO,IAAI;YACb;YAEA,OAAOU,WAAW,CAChB;cACE,GAAGC,KAAK;cACRZ,MAAM,EAAEY,KAAK,CAACZ,MAAM,CAAC0B,GAAG,CAAC,CAAChB,KAAK,EAAEH,CAAC,KAAK;gBACrC,IAAIA,CAAC,KAAKN,KAAK,EAAE;kBACf,OAAOS,KAAK;gBACd;gBAEA,MAAMiC,KAAK,GAAGD,cAAc,CAAChC,KAAK,CAACf,IAAI,CAAC;gBAExC,MAAMiD,SAAS,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;kBAAE/C,MAAM,EAAEc,KAAK,CAACd;gBAAO,CAAC,CAAC;gBACnD,MAAMiD,MAAM,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;kBAAE/C,MAAM,EAAE6C,MAAM,CAAC3C,OAAO,CAACF;gBAAO,CAAC,CAAC;gBAEzD,MAAMS,GAAG,GACPuC,SAAS,KAAKC,MAAM,GAChBnC,KAAK,CAACL,GAAG,GACR,GAAEK,KAAK,CAACf,IAAK,IAAG,IAAAgC,iBAAM,GAAG,EAAC;gBAEjC,IAAI/B,MAAM;gBAEV,IACE6C,MAAM,CAAC5C,IAAI,KAAK,UAAU,IAC1B4C,MAAM,CAAC3C,OAAO,CAACgD,KAAK,IACpBF,SAAS,KAAKC,MAAM,EACpB;kBACAjD,MAAM,GACJ6C,MAAM,CAAC3C,OAAO,CAACF,MAAM,KAAK2B,SAAS,IACnCD,cAAc,CAACZ,KAAK,CAACf,IAAI,CAAC,KAAK4B,SAAS,GACpC;oBACE,GAAGD,cAAc,CAACZ,KAAK,CAACf,IAAI,CAAC;oBAC7B,GAAGe,KAAK,CAACd,MAAM;oBACf,GAAG6C,MAAM,CAAC3C,OAAO,CAACF;kBACpB,CAAC,GACDc,KAAK,CAACd,MAAM;gBACpB,CAAC,MAAM;kBACLA,MAAM,GACJ0B,cAAc,CAACZ,KAAK,CAACf,IAAI,CAAC,KAAK4B,SAAS,GACpC;oBACE,GAAGD,cAAc,CAACZ,KAAK,CAACf,IAAI,CAAC;oBAC7B,GAAG8C,MAAM,CAAC3C,OAAO,CAACF;kBACpB,CAAC,GACD6C,MAAM,CAAC3C,OAAO,CAACF,MAAM;gBAC7B;gBAEA,MAAMmD,IAAI,GACRN,MAAM,CAAC5C,IAAI,KAAK,UAAU,IAAI4C,MAAM,CAAC3C,OAAO,CAACiD,IAAI,IAAI,IAAI,GACrDN,MAAM,CAAC3C,OAAO,CAACiD,IAAI,GACnBrC,KAAK,CAACqC,IAAI;gBAEhB,OAAOnD,MAAM,KAAKc,KAAK,CAACd,MAAM,IAAImD,IAAI,KAAKrC,KAAK,CAACqC,IAAI,GACjD;kBAAE,GAAGrC,KAAK;kBAAEL,GAAG;kBAAE0C,IAAI;kBAAEnD;gBAAO,CAAC,GAC/Bc,KAAK;cACX,CAAC;YACH,CAAC,EACDT,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;UACH;QAEA,KAAK,SAAS;UAAE;YACd,IAAIS,KAAK,CAACR,OAAO,CAACgC,MAAM,KAAK,CAAC,EAAE;cAC9B,OAAO,IAAI;YACb;YAEA,MAAMY,WAAW,GAAGpC,KAAK,CAACR,OAAO,CAACQ,KAAK,CAACR,OAAO,CAACgC,MAAM,GAAG,CAAC,CAAC,CAAC/B,GAAG;YAC/D,MAAMJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CACjCC,KAAK,IAAKA,KAAK,CAACL,GAAG,KAAK2C,WAAW,CACrC;YAED,IAAI/C,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,OAAO,IAAI;YACb;YAEA,OAAO;cACL,GAAGW,KAAK;cACRR,OAAO,EAAEQ,KAAK,CAACR,OAAO,CAAC6C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACnChD;YACF,CAAC;UACH;QAEA;UACE,OAAOkB,mBAAU,CAACqB,iBAAiB,CAAC5B,KAAK,EAAE6B,MAAM,CAAC;MAAC;IAEzD,CAAC;IAEDS,uBAAuB,CAACT,MAAM,EAAE;MAC9B,OAAOA,MAAM,CAAC5C,IAAI,KAAK,UAAU;IACnC,CAAC;IAEDsD,cAAc,EAAE1D;EAClB,CAAC;EAED,OAAOyB,MAAM;AACf"}