{"name": "exit", "description": "A replacement for process.exit that ensures stdio are fully drained before exiting.", "version": "0.1.2", "homepage": "https://github.com/cowboy/node-exit", "author": {"name": "\"Cowboy\" <PERSON>", "url": "http://benalman.com/"}, "repository": {"type": "git", "url": "git://github.com/cowboy/node-exit.git"}, "bugs": {"url": "https://github.com/cowboy/node-exit/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/cowboy/node-exit/blob/master/LICENSE-MIT"}], "main": "lib/exit", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "grunt nodeunit"}, "devDependencies": {"grunt-contrib-jshint": "~0.6.4", "grunt-contrib-nodeunit": "~0.2.0", "grunt-contrib-watch": "~0.5.3", "grunt": "~0.4.1", "which": "~1.0.5"}, "keywords": ["exit", "process", "stdio", "stdout", "stderr", "drain", "flush", "3584"]}