{"version": 3, "sources": ["nativeInterface.ts"], "names": ["NativeEventEmitter", "RNCNetInfo", "Error", "nativeEventEmitter", "eventEmitter"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,SAAQA,kBAAR,QAAiC,cAAjC;AACA,OAAOC,UAAP,MAAuB,gBAAvB,C,CAEA;;AACA,IAAI,CAACA,UAAL,EAAiB;AACf,QAAM,IAAIC,KAAJ,CAAW;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8IARQ,CAAN;AASD;AAED;AACA;AACA;AACA;;;AACA,IAAIC,kBAA6C,GAAG,IAApD;AACA,eAAe,EACb,GAAGF,UADU;;AAEb,MAAIG,YAAJ,GAAuC;AACrC,QAAI,CAACD,kBAAL,EAAyB;AACvB;AACA;AACAA,MAAAA,kBAAkB,GAAG,IAAIH,kBAAJ,CAAuBC,UAAvB,CAArB;AACD,KALoC,CAMrC;AACA;;;AACA,WAAOE,kBAAP;AACD;;AAXY,CAAf", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {NativeEventEmitter} from 'react-native';\nimport RNCNetInfo from './nativeModule';\n\n// Produce an error if we don't have the native module\nif (!RNCNetInfo) {\n  throw new Error(`@react-native-community/netinfo: NativeModule.RNCNetInfo is null. To fix this issue try these steps:\n\n• Run \\`react-native link @react-native-community/netinfo\\` in the project root.\n• Rebuild and re-run the app.\n• If you are using CocoaPods on iOS, run \\`pod install\\` in the \\`ios\\` directory and then rebuild and re-run the app. You may also need to re-open Xcode to get the new pods.\n• Check that the library was linked correctly when you used the link command by running through the manual installation instructions in the README.\n* If you are getting this error while unit testing you need to mock the native module. Follow the guide in the README.\n\nIf none of these fix the issue, please open an issue on the Github repository: https://github.com/react-native-community/react-native-netinfo`);\n}\n\n/**\n * We export the native interface in this way to give easy shared access to it between the\n * JavaScript code and the tests\n */\nlet nativeEventEmitter: NativeEventEmitter | null = null;\nexport default {\n  ...RNCNetInfo,\n  get eventEmitter(): NativeEventEmitter {\n    if (!nativeEventEmitter) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      /// @ts-ignore\n      nativeEventEmitter = new NativeEventEmitter(RNCNetInfo);\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    /// @ts-ignore\n    return nativeEventEmitter;\n  },\n};\n"]}