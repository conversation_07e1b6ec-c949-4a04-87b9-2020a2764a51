export declare const typeHandlers: {
    bmp: import("./interface").IImage;
    cur: import("./interface").IImage;
    dds: import("./interface").IImage;
    gif: import("./interface").IImage;
    heif: import("./interface").IImage;
    icns: import("./interface").IImage;
    ico: import("./interface").IImage;
    j2c: import("./interface").IImage;
    jp2: import("./interface").IImage;
    jpg: import("./interface").IImage;
    jxl: import("./interface").IImage;
    'jxl-stream': import("./interface").IImage;
    ktx: import("./interface").IImage;
    png: import("./interface").IImage;
    pnm: import("./interface").IImage;
    psd: import("./interface").IImage;
    svg: import("./interface").IImage;
    tga: import("./interface").IImage;
    tiff: import("./interface").IImage;
    webp: import("./interface").IImage;
};
export type imageType = keyof typeof typeHandlers;
