{"version": 3, "names": ["CHILD_STATE", "Symbol", "useRouteCache", "routes", "cache", "React", "useMemo", "current", "Map", "process", "env", "NODE_ENV", "reduce", "acc", "route", "previous", "get", "set", "state", "proxy", "Object", "defineProperty", "enumerable", "value", "Array", "from", "values"], "sourceRoot": "../../src", "sources": ["useRouteCache.tsx"], "mappings": ";;;;;;;AAKA;AAA+B;AAAA;AAM/B;AACA;AACA;AACA;AACA;AACO,MAAMA,WAAW,GAAGC,MAAM,CAAC,aAAa,CAAC;;AAEhD;AACA;AACA;AACA;AAHA;AAIe,SAASC,aAAa,CACnCC,MAAuB,EACvB;EACA;EACA,MAAMC,KAAK,GAAGC,KAAK,CAACC,OAAO,CAAC,OAAO;IAAEC,OAAO,EAAE,IAAIC,GAAG;EAAiB,CAAC,CAAC,EAAE,EAAE,CAAC;EAE7E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,OAAOR,MAAM;EACf;EAEAC,KAAK,CAACG,OAAO,GAAGJ,MAAM,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC5C,MAAMC,QAAQ,GAAGX,KAAK,CAACG,OAAO,CAACS,GAAG,CAACF,KAAK,CAAC;IAEzC,IAAIC,QAAQ,EAAE;MACZ;MACAF,GAAG,CAACI,GAAG,CAACH,KAAK,EAAEC,QAAQ,CAAC;IAC1B,CAAC,MAAM;MACL,MAAM;QAAEG,KAAK;QAAE,GAAGC;MAAM,CAAC,GAAGL,KAAK;MAEjCM,MAAM,CAACC,cAAc,CAACF,KAAK,EAAEnB,WAAW,EAAE;QACxCsB,UAAU,EAAE,KAAK;QACjBC,KAAK,EAAEL;MACT,CAAC,CAAC;MAEFL,GAAG,CAACI,GAAG,CAACH,KAAK,EAAEK,KAAK,CAAC;IACvB;IAEA,OAAON,GAAG;EACZ,CAAC,EAAE,IAAIL,GAAG,EAAE,CAAe;EAE3B,OAAOgB,KAAK,CAACC,IAAI,CAACrB,KAAK,CAACG,OAAO,CAACmB,MAAM,EAAE,CAAC;AAC3C"}