{"version": 3, "names": ["isMacLike", "test", "navigator", "platform", "refreshShortcut", "window", "onload", "document", "getElementById", "innerHTML", "Page", "render", "onReloadClicked", "xhr", "XMLHttpRequest", "open", "location", "origin", "send", "state", "isDark", "localStorage", "getItem", "matchMedia", "matches", "isPriorityMaintained", "status", "type", "visibilityState", "setState", "partialState", "Object", "assign", "statusNode", "textContent", "error", "reason", "linkNode", "querySelector", "href", "grayIcon", "blueIcon", "orangeIcon", "darkCheckbox", "body", "classList", "toggle", "checked", "setItem", "maintainPriorityCheckbox", "silence", "volume", "play", "pause", "toggleDarkTheme", "togglePriorityMaintenance", "connectToDebuggerProxy", "ws", "WebSocket", "host", "worker", "createJSRuntime", "Worker", "onmessage", "message", "JSON", "stringify", "data", "onbeforeunload", "updateVisibility", "shutdownJSRuntime", "terminate", "postMessage", "method", "onopen", "object", "parse", "$event", "console", "clear", "replyID", "id", "onclose", "warn", "setTimeout", "addEventListener"], "sources": ["../../src/ui/index.js"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/* eslint-env browser */\nimport './index.css';\nimport blueIcon from './assets/blue-icon.png';\nimport grayIcon from './assets/gray-icon.png';\nimport orangeIcon from './assets/orange-icon.png';\n\nconst isMacLike = /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);\nconst refreshShortcut = isMacLike ? '⌘R' : 'Ctrl R';\nwindow.onload = function () {\n  if (!isMacLike) {\n    document.getElementById('shortcut').innerHTML = 'Ctrl⇧J';\n  }\n  Page.render();\n};\n\nwindow.onReloadClicked = function () {\n  var xhr = new XMLHttpRequest();\n  xhr.open('GET', `${window.location.origin}/reload`, true);\n  xhr.send();\n};\n\nconst Page = (window.Page = {\n  state: {\n    isDark:\n      localStorage.getItem('darkTheme') === null\n        ? window.matchMedia('(prefers-color-scheme: dark)').matches\n        : localStorage.getItem('darkTheme') === 'on',\n    isPriorityMaintained: localStorage.getItem('maintainPriority') === 'on',\n    status: {type: 'disconnected'},\n    visibilityState: document.visibilityState,\n  },\n\n  setState(partialState) {\n    Page.state = Object.assign({}, Page.state, partialState);\n    Page.render();\n  },\n\n  render() {\n    const {isDark, isPriorityMaintained, status, visibilityState} = Page.state;\n\n    const statusNode = document.getElementById('status');\n    switch (status.type) {\n      case 'connected':\n        statusNode.textContent = 'Debugger session active.';\n        break;\n      case 'error':\n        statusNode.textContent =\n          status.error.reason ||\n          'Disconnected from proxy. Attempting reconnection. Is node server running?';\n        break;\n      case 'connecting':\n      case 'disconnected':\n      // Fall through.\n      default:\n        statusNode.innerHTML =\n          'Waiting, press <span class=\"shortcut\">' +\n          refreshShortcut +\n          '</span> in simulator to reload and connect.';\n        break;\n    }\n\n    const linkNode = document.querySelector('link[rel=icon]');\n    if (status.type === 'disconnected' || status.type === 'error') {\n      linkNode.href = grayIcon;\n    } else {\n      if (visibilityState === 'visible' || isPriorityMaintained) {\n        linkNode.href = blueIcon;\n      } else {\n        linkNode.href = orangeIcon;\n      }\n    }\n\n    const darkCheckbox = document.getElementById('dark');\n    document.body.classList.toggle('dark', isDark);\n    darkCheckbox.checked = isDark;\n    localStorage.setItem('darkTheme', isDark ? 'on' : '');\n\n    const maintainPriorityCheckbox = document.getElementById(\n      'maintain-priority',\n    );\n    const silence = document.getElementById('silence');\n    silence.volume = 0.1;\n    if (isPriorityMaintained) {\n      silence.play();\n    } else {\n      silence.pause();\n    }\n    maintainPriorityCheckbox.checked = isPriorityMaintained;\n    localStorage.setItem('maintainPriority', isPriorityMaintained ? 'on' : '');\n  },\n\n  toggleDarkTheme() {\n    Page.setState({isDark: !Page.state.isDark});\n  },\n\n  togglePriorityMaintenance() {\n    Page.setState({isPriorityMaintained: !Page.state.isPriorityMaintained});\n  },\n});\n\nfunction connectToDebuggerProxy() {\n  const ws = new WebSocket(\n    'ws://' +\n      window.location.host +\n      '/debugger-proxy?role=debugger&name=Chrome',\n  );\n  let worker;\n\n  function createJSRuntime() {\n    // This worker will run the application JavaScript code,\n    // making sure that it's run in an environment without a global\n    // document, to make it consistent with the JSC executor environment.\n    worker = new Worker('./debuggerWorker.js');\n    worker.onmessage = function (message) {\n      ws.send(JSON.stringify(message.data));\n    };\n    window.onbeforeunload = function () {\n      return (\n        'If you reload this page, it is going to break the debugging session. ' +\n        'Press ' +\n        refreshShortcut +\n        ' on the device to reload.'\n      );\n    };\n    updateVisibility();\n  }\n\n  function shutdownJSRuntime() {\n    if (worker) {\n      worker.terminate();\n      worker = null;\n      window.onbeforeunload = null;\n    }\n  }\n\n  function updateVisibility() {\n    if (worker && !Page.state.isPriorityMaintained) {\n      worker.postMessage({\n        method: 'setDebuggerVisibility',\n        visibilityState: document.visibilityState,\n      });\n    }\n    Page.setState({visibilityState: document.visibilityState});\n  }\n\n  ws.onopen = function () {\n    Page.setState({status: {type: 'connecting'}});\n  };\n\n  ws.onmessage = async function (message) {\n    if (!message.data) {\n      return;\n    }\n    const object = JSON.parse(message.data);\n\n    if (object.$event === 'client-disconnected') {\n      shutdownJSRuntime();\n      Page.setState({status: {type: 'disconnected'}});\n      return;\n    }\n\n    if (!object.method) {\n      return;\n    }\n\n    // Special message that asks for a new JS runtime\n    if (object.method === 'prepareJSRuntime') {\n      shutdownJSRuntime();\n      console.clear();\n      createJSRuntime();\n      ws.send(JSON.stringify({replyID: object.id}));\n      Page.setState({status: {type: 'connected', id: object.id}});\n    } else if (object.method === '$disconnected') {\n      shutdownJSRuntime();\n      Page.setState({status: {type: 'disconnected'}});\n    } else {\n      worker.postMessage(object);\n    }\n  };\n\n  ws.onclose = function (error) {\n    shutdownJSRuntime();\n    Page.setState({status: {type: 'error', error}});\n    if (error.reason) {\n      console.warn(error.reason);\n    }\n    setTimeout(connectToDebuggerProxy, 500);\n  };\n\n  // Let debuggerWorker.js know when we're not visible so that we can warn about\n  // poor performance when using remote debugging.\n  document.addEventListener('visibilitychange', updateVisibility, false);\n}\n\nconnectToDebuggerProxy();\n"], "mappings": ";;;AASA;AACA;AACA;AACA;AAZA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAMA,MAAMA,SAAS,GAAG,yBAAyB,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC;AACpE,MAAMC,eAAe,GAAGJ,SAAS,GAAG,IAAI,GAAG,QAAQ;AACnDK,MAAM,CAACC,MAAM,GAAG,YAAY;EAC1B,IAAI,CAACN,SAAS,EAAE;IACdO,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,SAAS,GAAG,QAAQ;EAC1D;EACAC,IAAI,CAACC,MAAM,EAAE;AACf,CAAC;AAEDN,MAAM,CAACO,eAAe,GAAG,YAAY;EACnC,IAAIC,GAAG,GAAG,IAAIC,cAAc,EAAE;EAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAG,GAAEV,MAAM,CAACW,QAAQ,CAACC,MAAO,SAAQ,EAAE,IAAI,CAAC;EACzDJ,GAAG,CAACK,IAAI,EAAE;AACZ,CAAC;AAED,MAAMR,IAAI,GAAIL,MAAM,CAACK,IAAI,GAAG;EAC1BS,KAAK,EAAE;IACLC,MAAM,EACJC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,GACtCjB,MAAM,CAACkB,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,GACzDH,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI;IAChDG,oBAAoB,EAAEJ,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,KAAK,IAAI;IACvEI,MAAM,EAAE;MAACC,IAAI,EAAE;IAAc,CAAC;IAC9BC,eAAe,EAAErB,QAAQ,CAACqB;EAC5B,CAAC;EAEDC,QAAQ,CAACC,YAAY,EAAE;IACrBpB,IAAI,CAACS,KAAK,GAAGY,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtB,IAAI,CAACS,KAAK,EAAEW,YAAY,CAAC;IACxDpB,IAAI,CAACC,MAAM,EAAE;EACf,CAAC;EAEDA,MAAM,GAAG;IACP,MAAM;MAACS,MAAM;MAAEK,oBAAoB;MAAEC,MAAM;MAAEE;IAAe,CAAC,GAAGlB,IAAI,CAACS,KAAK;IAE1E,MAAMc,UAAU,GAAG1B,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAC;IACpD,QAAQkB,MAAM,CAACC,IAAI;MACjB,KAAK,WAAW;QACdM,UAAU,CAACC,WAAW,GAAG,0BAA0B;QACnD;MACF,KAAK,OAAO;QACVD,UAAU,CAACC,WAAW,GACpBR,MAAM,CAACS,KAAK,CAACC,MAAM,IACnB,2EAA2E;QAC7E;MACF,KAAK,YAAY;MACjB,KAAK,cAAc;MACnB;MACA;QACEH,UAAU,CAACxB,SAAS,GAClB,wCAAwC,GACxCL,eAAe,GACf,6CAA6C;QAC/C;IAAM;IAGV,MAAMiC,QAAQ,GAAG9B,QAAQ,CAAC+B,aAAa,CAAC,gBAAgB,CAAC;IACzD,IAAIZ,MAAM,CAACC,IAAI,KAAK,cAAc,IAAID,MAAM,CAACC,IAAI,KAAK,OAAO,EAAE;MAC7DU,QAAQ,CAACE,IAAI,GAAGC,iBAAQ;IAC1B,CAAC,MAAM;MACL,IAAIZ,eAAe,KAAK,SAAS,IAAIH,oBAAoB,EAAE;QACzDY,QAAQ,CAACE,IAAI,GAAGE,iBAAQ;MAC1B,CAAC,MAAM;QACLJ,QAAQ,CAACE,IAAI,GAAGG,mBAAU;MAC5B;IACF;IAEA,MAAMC,YAAY,GAAGpC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;IACpDD,QAAQ,CAACqC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,MAAM,EAAE1B,MAAM,CAAC;IAC9CuB,YAAY,CAACI,OAAO,GAAG3B,MAAM;IAC7BC,YAAY,CAAC2B,OAAO,CAAC,WAAW,EAAE5B,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;IAErD,MAAM6B,wBAAwB,GAAG1C,QAAQ,CAACC,cAAc,CACtD,mBAAmB,CACpB;IACD,MAAM0C,OAAO,GAAG3C,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC;IAClD0C,OAAO,CAACC,MAAM,GAAG,GAAG;IACpB,IAAI1B,oBAAoB,EAAE;MACxByB,OAAO,CAACE,IAAI,EAAE;IAChB,CAAC,MAAM;MACLF,OAAO,CAACG,KAAK,EAAE;IACjB;IACAJ,wBAAwB,CAACF,OAAO,GAAGtB,oBAAoB;IACvDJ,YAAY,CAAC2B,OAAO,CAAC,kBAAkB,EAAEvB,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;EAC5E,CAAC;EAED6B,eAAe,GAAG;IAChB5C,IAAI,CAACmB,QAAQ,CAAC;MAACT,MAAM,EAAE,CAACV,IAAI,CAACS,KAAK,CAACC;IAAM,CAAC,CAAC;EAC7C,CAAC;EAEDmC,yBAAyB,GAAG;IAC1B7C,IAAI,CAACmB,QAAQ,CAAC;MAACJ,oBAAoB,EAAE,CAACf,IAAI,CAACS,KAAK,CAACM;IAAoB,CAAC,CAAC;EACzE;AACF,CAAE;AAEF,SAAS+B,sBAAsB,GAAG;EAChC,MAAMC,EAAE,GAAG,IAAIC,SAAS,CACtB,OAAO,GACLrD,MAAM,CAACW,QAAQ,CAAC2C,IAAI,GACpB,2CAA2C,CAC9C;EACD,IAAIC,MAAM;EAEV,SAASC,eAAe,GAAG;IACzB;IACA;IACA;IACAD,MAAM,GAAG,IAAIE,MAAM,CAAC,qBAAqB,CAAC;IAC1CF,MAAM,CAACG,SAAS,GAAG,UAAUC,OAAO,EAAE;MACpCP,EAAE,CAACvC,IAAI,CAAC+C,IAAI,CAACC,SAAS,CAACF,OAAO,CAACG,IAAI,CAAC,CAAC;IACvC,CAAC;IACD9D,MAAM,CAAC+D,cAAc,GAAG,YAAY;MAClC,OACE,uEAAuE,GACvE,QAAQ,GACRhE,eAAe,GACf,2BAA2B;IAE/B,CAAC;IACDiE,gBAAgB,EAAE;EACpB;EAEA,SAASC,iBAAiB,GAAG;IAC3B,IAAIV,MAAM,EAAE;MACVA,MAAM,CAACW,SAAS,EAAE;MAClBX,MAAM,GAAG,IAAI;MACbvD,MAAM,CAAC+D,cAAc,GAAG,IAAI;IAC9B;EACF;EAEA,SAASC,gBAAgB,GAAG;IAC1B,IAAIT,MAAM,IAAI,CAAClD,IAAI,CAACS,KAAK,CAACM,oBAAoB,EAAE;MAC9CmC,MAAM,CAACY,WAAW,CAAC;QACjBC,MAAM,EAAE,uBAAuB;QAC/B7C,eAAe,EAAErB,QAAQ,CAACqB;MAC5B,CAAC,CAAC;IACJ;IACAlB,IAAI,CAACmB,QAAQ,CAAC;MAACD,eAAe,EAAErB,QAAQ,CAACqB;IAAe,CAAC,CAAC;EAC5D;EAEA6B,EAAE,CAACiB,MAAM,GAAG,YAAY;IACtBhE,IAAI,CAACmB,QAAQ,CAAC;MAACH,MAAM,EAAE;QAACC,IAAI,EAAE;MAAY;IAAC,CAAC,CAAC;EAC/C,CAAC;EAED8B,EAAE,CAACM,SAAS,GAAG,gBAAgBC,OAAO,EAAE;IACtC,IAAI,CAACA,OAAO,CAACG,IAAI,EAAE;MACjB;IACF;IACA,MAAMQ,MAAM,GAAGV,IAAI,CAACW,KAAK,CAACZ,OAAO,CAACG,IAAI,CAAC;IAEvC,IAAIQ,MAAM,CAACE,MAAM,KAAK,qBAAqB,EAAE;MAC3CP,iBAAiB,EAAE;MACnB5D,IAAI,CAACmB,QAAQ,CAAC;QAACH,MAAM,EAAE;UAACC,IAAI,EAAE;QAAc;MAAC,CAAC,CAAC;MAC/C;IACF;IAEA,IAAI,CAACgD,MAAM,CAACF,MAAM,EAAE;MAClB;IACF;;IAEA;IACA,IAAIE,MAAM,CAACF,MAAM,KAAK,kBAAkB,EAAE;MACxCH,iBAAiB,EAAE;MACnBQ,OAAO,CAACC,KAAK,EAAE;MACflB,eAAe,EAAE;MACjBJ,EAAE,CAACvC,IAAI,CAAC+C,IAAI,CAACC,SAAS,CAAC;QAACc,OAAO,EAAEL,MAAM,CAACM;MAAE,CAAC,CAAC,CAAC;MAC7CvE,IAAI,CAACmB,QAAQ,CAAC;QAACH,MAAM,EAAE;UAACC,IAAI,EAAE,WAAW;UAAEsD,EAAE,EAAEN,MAAM,CAACM;QAAE;MAAC,CAAC,CAAC;IAC7D,CAAC,MAAM,IAAIN,MAAM,CAACF,MAAM,KAAK,eAAe,EAAE;MAC5CH,iBAAiB,EAAE;MACnB5D,IAAI,CAACmB,QAAQ,CAAC;QAACH,MAAM,EAAE;UAACC,IAAI,EAAE;QAAc;MAAC,CAAC,CAAC;IACjD,CAAC,MAAM;MACLiC,MAAM,CAACY,WAAW,CAACG,MAAM,CAAC;IAC5B;EACF,CAAC;EAEDlB,EAAE,CAACyB,OAAO,GAAG,UAAU/C,KAAK,EAAE;IAC5BmC,iBAAiB,EAAE;IACnB5D,IAAI,CAACmB,QAAQ,CAAC;MAACH,MAAM,EAAE;QAACC,IAAI,EAAE,OAAO;QAAEQ;MAAK;IAAC,CAAC,CAAC;IAC/C,IAAIA,KAAK,CAACC,MAAM,EAAE;MAChB0C,OAAO,CAACK,IAAI,CAAChD,KAAK,CAACC,MAAM,CAAC;IAC5B;IACAgD,UAAU,CAAC5B,sBAAsB,EAAE,GAAG,CAAC;EACzC,CAAC;;EAED;EACA;EACAjD,QAAQ,CAAC8E,gBAAgB,CAAC,kBAAkB,EAAEhB,gBAAgB,EAAE,KAAK,CAAC;AACxE;AAEAb,sBAAsB,EAAE"}