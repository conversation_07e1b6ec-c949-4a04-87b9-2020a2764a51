# iterator.prototype <sup>[![Version Badge][npm-version-svg]][package-url]</sup>

[![github actions][actions-image]][actions-url]
[![coverage][codecov-image]][codecov-url]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]

[![npm badge][npm-badge-png]][package-url]

`Iterator.prototype`, or a shared object to use.

## Usage

```javascript
var iterProto = require('iterator.prototype');
var assert = require('assert');

assert.equal(Object.getPrototypeOf(Object.getPrototypeOf([].keys())), iterProto);
```

[package-url]: https://npmjs.org/package/iterator.prototype
[npm-version-svg]: https://versionbadg.es/ljharb/Iterator.prototype.svg
[deps-svg]: https://david-dm.org/ljharb/Iterator.prototype.svg
[deps-url]: https://david-dm.org/ljharb/Iterator.prototype
[dev-deps-svg]: https://david-dm.org/ljharb/Iterator.prototype/dev-status.svg
[dev-deps-url]: https://david-dm.org/ljharb/Iterator.prototype#info=devDependencies
[npm-badge-png]: https://nodei.co/npm/iterator.prototype.png?downloads=true&stars=true
[license-image]: https://img.shields.io/npm/l/iterator.prototype.svg
[license-url]: LICENSE
[downloads-image]: https://img.shields.io/npm/dm/iterator.prototype.svg
[downloads-url]: https://npm-stat.com/charts.html?package=iterator.prototype
[codecov-image]: https://codecov.io/gh/ljharb/Iterator.prototype/branch/main/graphs/badge.svg
[codecov-url]: https://app.codecov.io/gh/ljharb/Iterator.prototype/
[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/Iterator.prototype
[actions-url]: https://github.com/ljharb/Iterator.prototype/actions
