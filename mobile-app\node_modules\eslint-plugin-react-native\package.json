{"name": "eslint-plugin-react-native", "version": "4.1.0", "author": "<PERSON> <<EMAIL>>", "description": "React Native specific linting rules for ESLint", "main": "index.js", "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint ./lib && eslint ./tests", "test": "npm run lint && npm run unit-test", "unit-test": "nyc --silent --reporter=text mocha tests/**/*.js"}, "files": ["LICENSE", "README.md", "index.js", "lib"], "repository": {"type": "git", "url": "https://github.com/intellicode/eslint-plugin-react-native"}, "homepage": "https://github.com/intellicode/eslint-plugin-react-native", "bugs": "https://github.com/intellicode/eslint-plugin-react-native/issues", "devDependencies": {"@babel/eslint-parser": "^7.16.3", "@typescript-eslint/parser": "^6.6.0", "coveralls": "^3.1.1", "eslint": "^8.4.0", "eslint-config-airbnb": "^19.0.2", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.26.1", "mocha": "^10.2.0", "nyc": "^15.1.0", "typescript": "^5.2.2"}, "peerDependencies": {"eslint": "^3.17.0 || ^4 || ^5 || ^6 || ^7 || ^8"}, "keywords": ["eslint", "eslint-plugin", "eslintplugin", "react", "react-native", "react native"], "license": "MIT", "dependencies": {"eslint-plugin-react-native-globals": "^0.1.1"}}