{"version": 3, "names": [], "sources": ["../src/types.ts"], "sourcesContent": ["export interface Device {\n  availability?: string;\n  state?: string;\n  isAvailable?: boolean;\n  name: string;\n  udid: string;\n  version?: string;\n  availabilityError?: string;\n  type?: 'simulator' | 'device' | 'catalyst';\n  booted?: boolean;\n  lastBootedAt?: string;\n}\n\nexport interface IosProjectInfo {\n  configurations: string[];\n  name: string;\n  schemes: string[];\n  targets: string[];\n}\n"], "mappings": ""}