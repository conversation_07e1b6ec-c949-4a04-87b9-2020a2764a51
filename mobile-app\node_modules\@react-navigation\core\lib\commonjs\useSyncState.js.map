{"version": 3, "names": ["UNINTIALIZED_STATE", "useSyncState", "initialState", "stateRef", "React", "useRef", "isSchedulingRef", "isMountedRef", "useEffect", "current", "trackingState", "setTrackingState", "useState", "getState", "useCallback", "setState", "state", "scheduleUpdate", "callback", "flushUpdates", "useDebugValue"], "sourceRoot": "../../src", "sources": ["useSyncState.tsx"], "mappings": ";;;;;;AAAA;AAA+B;AAAA;AAE/B,MAAMA,kBAAkB,GAAG,CAAC,CAAC;;AAE7B;AACA;AACA;AACe,SAASC,YAAY,CAAIC,YAA4B,EAAE;EACpE,MAAMC,QAAQ,GAAGC,KAAK,CAACC,MAAM,CAAIL,kBAAkB,CAAQ;EAC3D,MAAMM,eAAe,GAAGF,KAAK,CAACC,MAAM,CAAC,KAAK,CAAC;EAC3C,MAAME,YAAY,GAAGH,KAAK,CAACC,MAAM,CAAC,IAAI,CAAC;EAEvCD,KAAK,CAACI,SAAS,CAAC,MAAM;IACpBD,YAAY,CAACE,OAAO,GAAG,IAAI;IAE3B,OAAO,MAAM;MACXF,YAAY,CAACE,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIN,QAAQ,CAACM,OAAO,KAAKT,kBAAkB,EAAE;IAC3CG,QAAQ,CAACM,OAAO;IACd;IACA,OAAOP,YAAY,KAAK,UAAU,GAAGA,YAAY,EAAE,GAAGA,YAAY;EACtE;EAEA,MAAM,CAACQ,aAAa,EAAEC,gBAAgB,CAAC,GAAGP,KAAK,CAACQ,QAAQ,CAACT,QAAQ,CAACM,OAAO,CAAC;EAE1E,MAAMI,QAAQ,GAAGT,KAAK,CAACU,WAAW,CAAC,MAAMX,QAAQ,CAACM,OAAO,EAAE,EAAE,CAAC;EAE9D,MAAMM,QAAQ,GAAGX,KAAK,CAACU,WAAW,CAAEE,KAAQ,IAAK;IAC/C,IAAIA,KAAK,KAAKb,QAAQ,CAACM,OAAO,IAAI,CAACF,YAAY,CAACE,OAAO,EAAE;MACvD;IACF;IAEAN,QAAQ,CAACM,OAAO,GAAGO,KAAK;IAExB,IAAI,CAACV,eAAe,CAACG,OAAO,EAAE;MAC5BE,gBAAgB,CAACK,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGb,KAAK,CAACU,WAAW,CAAEI,QAAoB,IAAK;IACjEZ,eAAe,CAACG,OAAO,GAAG,IAAI;IAE9B,IAAI;MACFS,QAAQ,EAAE;IACZ,CAAC,SAAS;MACRZ,eAAe,CAACG,OAAO,GAAG,KAAK;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,YAAY,GAAGf,KAAK,CAACU,WAAW,CAAC,MAAM;IAC3C,IAAI,CAACP,YAAY,CAACE,OAAO,EAAE;MACzB;IACF;;IAEA;IACA;IACAE,gBAAgB,CAACR,QAAQ,CAACM,OAAO,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA,IAAIC,aAAa,KAAKP,QAAQ,CAACM,OAAO,EAAE;IACtCE,gBAAgB,CAACR,QAAQ,CAACM,OAAO,CAAC;EACpC;EAEA,MAAMO,KAAK,GAAGb,QAAQ,CAACM,OAAO;EAE9BL,KAAK,CAACgB,aAAa,CAACJ,KAAK,CAAC;EAE1B,OAAO,CAACA,KAAK,EAAEH,QAAQ,EAAEE,QAAQ,EAAEE,cAAc,EAAEE,YAAY,CAAC;AAClE"}