{"version": 3, "file": "TabRouter.d.ts", "sourceRoot": "", "sources": ["../../../src/TabRouter.tsx"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAEV,oBAAoB,EACpB,eAAe,EACf,aAAa,EAGb,MAAM,EACP,MAAM,SAAS,CAAC;AAEjB,MAAM,MAAM,aAAa,GAAG;IAC1B,IAAI,EAAE,SAAS,CAAC;IAChB,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IAC3C,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,YAAY,GACpB,cAAc,GACd,YAAY,GACZ,SAAS,GACT,OAAO,GACP,MAAM,CAAC;AAEX,MAAM,MAAM,gBAAgB,GAAG,oBAAoB,GAAG;IACpD,YAAY,CAAC,EAAE,YAAY,CAAC;CAC7B,CAAC;AAEF,MAAM,MAAM,kBAAkB,CAAC,SAAS,SAAS,aAAa,IAAI,IAAI,CACpE,eAAe,CAAC,SAAS,CAAC,EAC1B,SAAS,CACV,GAAG;IACF;;OAEG;IACH,IAAI,EAAE,KAAK,CAAC;IACZ;;OAEG;IACH,OAAO,EAAE;QAAE,IAAI,EAAE,OAAO,CAAC;QAAC,GAAG,EAAE,MAAM,CAAA;KAAE,EAAE,CAAC;CAC3C,CAAC;AAEF,MAAM,MAAM,gBAAgB,CAAC,SAAS,SAAS,aAAa,IAAI;IAC9D;;;;;OAKG;IACH,MAAM,CAAC,SAAS,SAAS,OAAO,CAAC,MAAM,SAAS,EAAE,MAAM,CAAC,EACvD,GAAG,IAAI,EAAE,SAAS,SAAS,SAAS,CAAC,SAAS,CAAC,GAC3C,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GACvE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GACpD,IAAI,CAAC;CACT,CAAC;AAIF,eAAO,MAAM,UAAU;iBACR,MAAM,WAAW,MAAM,GAAG,aAAa;CAGrD,CAAC;AA4EF,MAAM,CAAC,OAAO,UAAU,SAAS,CAAC,EAChC,gBAAgB,EAChB,YAA2B,GAC5B,EAAE,gBAAgB,+FAsQlB"}