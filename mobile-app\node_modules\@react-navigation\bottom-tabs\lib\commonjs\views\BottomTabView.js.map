{"version": 3, "names": ["BottomTabView", "props", "tabBar", "state", "navigation", "descriptors", "safeAreaInsets", "detachInactiveScreens", "Platform", "OS", "sceneContainerStyle", "focusedRouteKey", "routes", "index", "key", "loaded", "setLoaded", "React", "useState", "includes", "dimensions", "SafeAreaProviderCompat", "initialMetrics", "frame", "tabBarHeight", "setTabBarHeight", "getTabBarHeight", "layout", "width", "height", "insets", "style", "options", "tabBarStyle", "renderTabBar", "top", "right", "bottom", "left", "styles", "container", "map", "route", "descriptor", "lazy", "unmountOnBlur", "isFocused", "freezeOnBlur", "header", "getHeaderTitle", "name", "headerShown", "headerStatusBarHeight", "headerTransparent", "StyleSheet", "absoluteFill", "zIndex", "render", "create", "flex", "overflow"], "sourceRoot": "../../../src", "sources": ["views/BottomTabView.tsx"], "mappings": ";;;;;;AAAA;AAUA;AACA;AACA;AAUA;AACA;AACA;AACA;AAAqE;AAAA;AAAA;AAAA;AAQtD,SAASA,aAAa,CAACC,KAAY,EAAE;EAClD,MAAM;IACJC,MAAM,GAAID,KAAwB,iBAAK,oBAAC,qBAAY,EAAKA,KAAK,CAAI;IAClEE,KAAK;IACLC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,qBAAqB,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAC3CD,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBD,qBAAQ,CAACC,EAAE,KAAK,KAAK;IACvBC;EACF,CAAC,GAAGT,KAAK;EAET,MAAMU,eAAe,GAAGR,KAAK,CAACS,MAAM,CAACT,KAAK,CAACU,KAAK,CAAC,CAACC,GAAG;EACrD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAAC,CAACP,eAAe,CAAC,CAAC;EAE7D,IAAI,CAACI,MAAM,CAACI,QAAQ,CAACR,eAAe,CAAC,EAAE;IACrCK,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAEJ,eAAe,CAAC,CAAC;EACzC;EAEA,MAAMS,UAAU,GAAGC,gCAAsB,CAACC,cAAc,CAACC,KAAK;EAC9D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGR,KAAK,CAACC,QAAQ,CAAC,MACrD,IAAAQ,6BAAe,EAAC;IACdvB,KAAK;IACLE,WAAW;IACXe,UAAU;IACVO,MAAM,EAAE;MAAEC,KAAK,EAAER,UAAU,CAACQ,KAAK;MAAEC,MAAM,EAAE;IAAE,CAAC;IAC9CC,MAAM,EAAE;MACN,GAAGT,gCAAsB,CAACC,cAAc,CAACQ,MAAM;MAC/C,GAAG7B,KAAK,CAACK;IACX,CAAC;IACDyB,KAAK,EAAE1B,WAAW,CAACF,KAAK,CAACS,MAAM,CAACT,KAAK,CAACU,KAAK,CAAC,CAACC,GAAG,CAAC,CAACkB,OAAO,CAACC;EAC5D,CAAC,CAAC,CACH;EAED,MAAMC,YAAY,GAAG,MAAM;IACzB,oBACE,oBAAC,iDAAqB,CAAC,QAAQ,QAC3BJ,MAAM,IACN5B,MAAM,CAAC;MACLC,KAAK,EAAEA,KAAK;MACZE,WAAW,EAAEA,WAAW;MACxBD,UAAU,EAAEA,UAAU;MACtB0B,MAAM,EAAE;QACNK,GAAG,EAAE,CAAA7B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6B,GAAG,MAAIL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,GAAG,KAAI,CAAC;QAC5CC,KAAK,EAAE,CAAA9B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8B,KAAK,MAAIN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM,KAAK,KAAI,CAAC;QAClDC,MAAM,EAAE,CAAA/B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE+B,MAAM,MAAIP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,MAAM,KAAI,CAAC;QACrDC,IAAI,EAAE,CAAAhC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgC,IAAI,MAAIR,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,IAAI,KAAI;MAChD;IACF,CAAC,CAAC,CAE2B;EAErC,CAAC;EAED,MAAM;IAAE1B;EAAO,CAAC,GAAGT,KAAK;EAExB,oBACE,oBAAC,gCAAsB,qBACrB,oBAAC,oCAAoB;IACnB,OAAO,EAAEI,qBAAsB;IAC/B,YAAY;IACZ,KAAK,EAAEgC,MAAM,CAACC;EAAU,GAEvB5B,MAAM,CAAC6B,GAAG,CAAC,CAACC,KAAK,EAAE7B,KAAK,KAAK;IAC5B,MAAM8B,UAAU,GAAGtC,WAAW,CAACqC,KAAK,CAAC5B,GAAG,CAAC;IACzC,MAAM;MAAE8B,IAAI,GAAG,IAAI;MAAEC;IAAc,CAAC,GAAGF,UAAU,CAACX,OAAO;IACzD,MAAMc,SAAS,GAAG3C,KAAK,CAACU,KAAK,KAAKA,KAAK;IAEvC,IAAIgC,aAAa,IAAI,CAACC,SAAS,EAAE;MAC/B,OAAO,IAAI;IACb;IAEA,IAAIF,IAAI,IAAI,CAAC7B,MAAM,CAACI,QAAQ,CAACuB,KAAK,CAAC5B,GAAG,CAAC,IAAI,CAACgC,SAAS,EAAE;MACrD;MACA,OAAO,IAAI;IACb;IAEA,MAAM;MACJC,YAAY;MACZC,MAAM,GAAG;QAAA,IAAC;UAAErB,MAAM;UAAEK;QAA8B,CAAC;QAAA,oBACjD,oBAAC,gBAAM,eACDA,OAAO;UACX,MAAM,EAAEL,MAAO;UACf,KAAK,EAAE,IAAAsB,wBAAc,EAACjB,OAAO,EAAEU,KAAK,CAACQ,IAAI;QAAE,GAC3C;MAAA,CACH;MACDC,WAAW;MACXC,qBAAqB;MACrBC;IACF,CAAC,GAAGV,UAAU,CAACX,OAAO;IAEtB,oBACE,oBAAC,2BAAW;MACV,GAAG,EAAEU,KAAK,CAAC5B,GAAI;MACf,KAAK,EAAE,CAACwC,uBAAU,CAACC,YAAY,EAAE;QAAEC,MAAM,EAAEV,SAAS,GAAG,CAAC,GAAG,CAAC;MAAE,CAAC,CAAE;MACjE,OAAO,EAAEA,SAAU;MACnB,OAAO,EAAEvC,qBAAsB;MAC/B,YAAY,EAAEwC;IAAa,gBAE3B,oBAAC,kCAAyB,CAAC,QAAQ;MAAC,KAAK,EAAEvB;IAAa,gBACtD,oBAAC,gBAAM;MACL,OAAO,EAAEsB,SAAU;MACnB,KAAK,EAAEH,UAAU,CAACD,KAAM;MACxB,UAAU,EAAEC,UAAU,CAACvC,UAAW;MAClC,WAAW,EAAE+C,WAAY;MACzB,qBAAqB,EAAEC,qBAAsB;MAC7C,iBAAiB,EAAEC,iBAAkB;MACrC,MAAM,EAAEL,MAAM,CAAC;QACbrB,MAAM,EAAEP,UAAU;QAClBsB,KAAK,EAAEC,UAAU,CAACD,KAAK;QACvBtC,UAAU,EACRuC,UAAU,CAACvC,UAAoD;QACjE4B,OAAO,EAAEW,UAAU,CAACX;MACtB,CAAC,CAAE;MACH,KAAK,EAAEtB;IAAoB,GAE1BiC,UAAU,CAACc,MAAM,EAAE,CACb,CAC0B,CACzB;EAElB,CAAC,CAAC,CACmB,eACvB,oBAAC,0CAAiC,CAAC,QAAQ;IAAC,KAAK,EAAEhC;EAAgB,GAChES,YAAY,EAAE,CAC4B,CACtB;AAE7B;AAEA,MAAMK,MAAM,GAAGe,uBAAU,CAACI,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACTmB,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC"}