{"version": 3, "names": ["ModalStatusBarManager", "dark", "layout", "insets", "style", "darkTheme", "useTheme", "overlapping", "setOverlapping", "React", "useState", "scale", "width", "offset", "top", "flattenedStyle", "StyleSheet", "flatten", "translateY", "transform", "find", "s", "undefined", "useEffect", "listener", "value", "sub", "addListener", "removeListener", "darkContent"], "sourceRoot": "../../../src", "sources": ["views/ModalStatusBarManager.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AAAqD;AAAA;AAYtC,SAASA,qBAAqB,OAKnC;EAAA;EAAA,IALoC;IAC5CC,IAAI;IACJC,MAAM;IACNC,MAAM;IACNC;EACK,CAAC;EACN,MAAM;IAAEH,IAAI,EAAEI;EAAU,CAAC,GAAG,IAAAC,gBAAQ,GAAE;EACtC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAGT,MAAM,CAACU,KAAK;EACnC,MAAMC,MAAM,GAAG,CAACV,MAAM,CAACW,GAAG,GAAG,EAAE,IAAIH,KAAK;EAExC,MAAMI,cAAc,GAAGC,uBAAU,CAACC,OAAO,CAACb,KAAK,CAAC;EAChD,MAAMc,UAAU,GAAGH,cAAc,aAAdA,cAAc,gDAAdA,cAAc,CAAEI,SAAS,oFAAzB,sBAA2BC,IAAI,CAC/CC,CAAM,IAAKA,CAAC,CAACH,UAAU,KAAKI,SAAS,CACvC,2DAFkB,uBAEhBJ,UAAU;EAEbT,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,MAAMC,QAAQ,GAAG,SAAkC;MAAA,IAAjC;QAAEC;MAAyB,CAAC;MAC5CjB,cAAc,CAACiB,KAAK,GAAGZ,MAAM,CAAC;IAChC,CAAC;IAED,MAAMa,GAAG,GAAGR,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,WAAW,CAACH,QAAQ,CAAC;IAE7C,OAAO,MAAMN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEU,cAAc,CAACF,GAAG,CAAC;EAC9C,CAAC,EAAE,CAACb,MAAM,EAAEK,UAAU,CAAC,CAAC;EAExB,MAAMW,WAAW,GAAG5B,IAAI,IAAI,CAACI,SAAS;EAEtC,oBACE,oBAAC,sBAAS;IACR,QAAQ;IACR,QAAQ,EAAEE,WAAW,IAAIsB,WAAW,GAAG,cAAc,GAAG;EAAgB,EACxE;AAEN"}