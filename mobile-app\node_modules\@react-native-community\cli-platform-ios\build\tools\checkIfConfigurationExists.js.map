{"version": 3, "names": ["checkIfConfigurationExists", "project", "mode", "configurations", "includes", "CLIError", "join"], "sources": ["../../src/tools/checkIfConfigurationExists.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\nimport {IosProjectInfo} from '../types';\n\nexport function checkIfConfigurationExists(\n  project: IosProjectInfo,\n  mode: string,\n) {\n  if (!project.configurations.includes(mode)) {\n    throw new CLIError(\n      `Configuration \"${mode}\" does not exist in your project. Please use one of the existing configurations: ${project.configurations.join(\n        ', ',\n      )}`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAGO,SAASA,0BAA0B,CACxCC,OAAuB,EACvBC,IAAY,EACZ;EACA,IAAI,CAACD,OAAO,CAACE,cAAc,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAE;IAC1C,MAAM,KAAIG,oBAAQ,EACf,kBAAiBH,IAAK,oFAAmFD,OAAO,CAACE,cAAc,CAACG,IAAI,CACnI,IAAI,CACJ,EAAC,CACJ;EACH;AACF"}