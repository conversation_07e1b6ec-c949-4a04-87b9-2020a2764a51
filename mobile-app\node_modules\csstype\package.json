{"name": "csstype", "version": "3.1.3", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": "https://github.com/frenic/csstype", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/css-tree": "^2.3.1", "@types/jest": "^29.5.0", "@types/jsdom": "^21.1.1", "@types/node": "^16.18.23", "@types/prettier": "^2.7.2", "@types/request": "^2.48.8", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "eslint": "^8.37.0", "css-tree": "^2.3.1", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "fast-glob": "^3.2.12", "flow-bin": "^0.203.1", "jest": "^29.5.0", "jsdom": "^21.1.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#1bf44517bd08de735e9ec20dbfe8e86c96341054", "mdn-data": "git+https://github.com/mdn/data.git#7f0c865a3c4b5d891285c93308ee5c25cb5cfee8", "prettier": "^2.8.7", "request": "^2.88.2", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "turndown": "^7.1.2", "typescript": "~5.0.3"}, "scripts": {"prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint", "test": "jest --runInBand", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts --runInBand"}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"]}