{"version": 3, "names": [], "sources": ["../src/index.ts"], "sourcesContent": ["export {default as logger} from './logger';\nexport {default as isPackagerRunning} from './isPackagerRunning';\nexport {default as getDefaultUserTerminal} from './getDefaultUserTerminal';\nexport {fetch, fetchToTemp} from './fetch';\nexport {default as launchDefaultBrowser} from './launchDefaultBrowser';\nexport {default as launchDebugger} from './launchDebugger';\nexport {default as launchEditor} from './launchEditor';\nexport * as version from './releaseChecker';\nexport {default as resolveNodeModuleDir} from './resolveNodeModuleDir';\nexport {default as hookStdout} from './hookStdout';\nexport {getLoader, NoopLoader, Loader} from './loader';\nexport {default as findProjectRoot} from './findProjectRoot';\nexport {default as printRunDoctorTip} from './printRunDoctorTip';\nexport * as link from './doclink';\n\nexport * from './errors';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAuD;AAAA;AAEvD;AACA;AACA;AACA;AACA;AAAiE;AAAA;AAGjE;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AAAyB;AAAA;AAAA"}