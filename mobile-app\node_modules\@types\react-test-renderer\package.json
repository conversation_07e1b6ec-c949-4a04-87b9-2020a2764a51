{"name": "@types/react-test-renderer", "version": "18.3.1", "description": "TypeScript definitions for react-test-renderer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-test-renderer", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "ar<PERSON><PERSON><PERSON>", "url": "https://github.com/arvitaly"}, {"name": "Lochbrunner", "githubUsername": "lochbrunner", "url": "https://github.com/lochbrunner"}, {"name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON>", "githubUsername": "jgoz", "url": "https://github.com/jgoz"}, {"name": "<PERSON>", "githubUsername": "Jessidhia", "url": "https://github.com/Jessidhia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/maddhruv"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-test-renderer"}, "scripts": {}, "dependencies": {"@types/react": "^18"}, "peerDependencies": {}, "typesPublisherContentHash": "7f182c3965200293ddf6ce4211020608721cd95cdc24fe632e9627c5defe3c93", "typeScriptVersion": "5.0"}