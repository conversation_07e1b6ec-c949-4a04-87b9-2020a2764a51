{"version": 3, "names": ["getOS", "os", "platform", "_platform", "_version", "doclink", "section", "path", "hashOrOverrides", "url", "URL", "isObj", "hash", "version", "OS", "pathname", "searchParams", "set", "otherKeys", "Object", "keys", "filter", "key", "includes", "assert", "doesNotMatch", "toString", "docs", "bind", "contributing", "community", "showcase", "blog", "setPlatform", "target", "setVersion", "reactNativeVersion"], "sources": ["../src/doclink.ts"], "sourcesContent": ["import os from 'os';\nimport assert from 'assert';\n\ntype Platforms = 'android' | 'ios';\n\nexport function getOS(): string {\n  // Using os.platform instead of process.platform so we can test more easily. Once jest upgrades\n  // to ^29.4 we could use process.platforms and jest.replaceProperty(process, 'platforms', 'someplatform');\n  switch (os.platform()) {\n    case 'aix':\n    case 'freebsd':\n    case 'linux':\n    case 'openbsd':\n    case 'sunos':\n      // King of controversy, right here.\n      return 'linux';\n    case 'darwin':\n      return 'macos';\n    case 'win32':\n      return 'windows';\n    default:\n      return '';\n  }\n}\n\nlet _platform: Platforms = 'android';\nlet _version: string | undefined;\n\ninterface Overrides {\n  os?: string;\n  platform?: string;\n  hash?: string;\n  version?: string;\n}\n\ninterface Other {\n  [key: string]: string;\n}\n\n/**\n * Create a deeplink to our documentation based on the user's OS and the Platform they're trying to build.\n */\nfunction doclink(\n  section: string,\n  path: string,\n  hashOrOverrides?: string | (Overrides & Other),\n): string {\n  const url = new URL('https://reactnative.dev/');\n\n  // Overrides\n  const isObj = typeof hashOrOverrides === 'object';\n\n  const hash = isObj ? hashOrOverrides.hash : hashOrOverrides;\n  const version =\n    isObj && hashOrOverrides.version ? hashOrOverrides.version : _version;\n  const OS = isObj && hashOrOverrides.os ? hashOrOverrides.os : getOS();\n  const platform =\n    isObj && hashOrOverrides.platform ? hashOrOverrides.platform : _platform;\n\n  url.pathname = _version\n    ? `${section}/${version}/${path}`\n    : `${section}/${path}`;\n\n  url.searchParams.set('os', OS);\n  url.searchParams.set('platform', platform);\n\n  if (isObj) {\n    const otherKeys = Object.keys(hashOrOverrides).filter(\n      (key) => !['hash', 'version', 'os', 'platform'].includes(key),\n    );\n    for (let key of otherKeys) {\n      url.searchParams.set(key, hashOrOverrides[key]);\n    }\n  }\n\n  if (hash) {\n    assert.doesNotMatch(\n      hash,\n      /#/,\n      \"Anchor links should be written withou a '#'\",\n    );\n    url.hash = hash;\n  }\n\n  return url.toString();\n}\n\nexport const docs = doclink.bind(null, 'docs');\nexport const contributing = doclink.bind(null, 'contributing');\nexport const community = doclink.bind(null, 'community');\nexport const showcase = doclink.bind(null, 'showcase');\nexport const blog = doclink.bind(null, 'blog');\n\n/**\n * When the user builds, we should define the target platform globally.\n */\nexport function setPlatform(target: Platforms): void {\n  _platform = target;\n}\n\n/**\n * Can we figure out what version of react native they're using?\n */\nexport function setVersion(reactNativeVersion: string): void {\n  _version = reactNativeVersion;\n}\n"], "mappings": ";;;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA4B;AAIrB,SAASA,KAAK,GAAW;EAC9B;EACA;EACA,QAAQC,aAAE,CAACC,QAAQ,EAAE;IACnB,KAAK,KAAK;IACV,KAAK,SAAS;IACd,KAAK,OAAO;IACZ,KAAK,SAAS;IACd,KAAK,OAAO;MACV;MACA,OAAO,OAAO;IAChB,KAAK,QAAQ;MACX,OAAO,OAAO;IAChB,KAAK,OAAO;MACV,OAAO,SAAS;IAClB;MACE,OAAO,EAAE;EAAC;AAEhB;AAEA,IAAIC,SAAoB,GAAG,SAAS;AACpC,IAAIC,QAA4B;AAahC;AACA;AACA;AACA,SAASC,OAAO,CACdC,OAAe,EACfC,IAAY,EACZC,eAA8C,EACtC;EACR,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,0BAA0B,CAAC;;EAE/C;EACA,MAAMC,KAAK,GAAG,OAAOH,eAAe,KAAK,QAAQ;EAEjD,MAAMI,IAAI,GAAGD,KAAK,GAAGH,eAAe,CAACI,IAAI,GAAGJ,eAAe;EAC3D,MAAMK,OAAO,GACXF,KAAK,IAAIH,eAAe,CAACK,OAAO,GAAGL,eAAe,CAACK,OAAO,GAAGT,QAAQ;EACvE,MAAMU,EAAE,GAAGH,KAAK,IAAIH,eAAe,CAACP,EAAE,GAAGO,eAAe,CAACP,EAAE,GAAGD,KAAK,EAAE;EACrE,MAAME,QAAQ,GACZS,KAAK,IAAIH,eAAe,CAACN,QAAQ,GAAGM,eAAe,CAACN,QAAQ,GAAGC,SAAS;EAE1EM,GAAG,CAACM,QAAQ,GAAGX,QAAQ,GAClB,GAAEE,OAAQ,IAAGO,OAAQ,IAAGN,IAAK,EAAC,GAC9B,GAAED,OAAQ,IAAGC,IAAK,EAAC;EAExBE,GAAG,CAACO,YAAY,CAACC,GAAG,CAAC,IAAI,EAAEH,EAAE,CAAC;EAC9BL,GAAG,CAACO,YAAY,CAACC,GAAG,CAAC,UAAU,EAAEf,QAAQ,CAAC;EAE1C,IAAIS,KAAK,EAAE;IACT,MAAMO,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACZ,eAAe,CAAC,CAACa,MAAM,CAClDC,GAAG,IAAK,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,CAC9D;IACD,KAAK,IAAIA,GAAG,IAAIJ,SAAS,EAAE;MACzBT,GAAG,CAACO,YAAY,CAACC,GAAG,CAACK,GAAG,EAAEd,eAAe,CAACc,GAAG,CAAC,CAAC;IACjD;EACF;EAEA,IAAIV,IAAI,EAAE;IACRY,iBAAM,CAACC,YAAY,CACjBb,IAAI,EACJ,GAAG,EACH,6CAA6C,CAC9C;IACDH,GAAG,CAACG,IAAI,GAAGA,IAAI;EACjB;EAEA,OAAOH,GAAG,CAACiB,QAAQ,EAAE;AACvB;AAEO,MAAMC,IAAI,GAAGtB,OAAO,CAACuB,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;AAAC;AACxC,MAAMC,YAAY,GAAGxB,OAAO,CAACuB,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC;AAAC;AACxD,MAAME,SAAS,GAAGzB,OAAO,CAACuB,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC;AAAC;AAClD,MAAMG,QAAQ,GAAG1B,OAAO,CAACuB,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;AAAC;AAChD,MAAMI,IAAI,GAAG3B,OAAO,CAACuB,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;;AAE9C;AACA;AACA;AAFA;AAGO,SAASK,WAAW,CAACC,MAAiB,EAAQ;EACnD/B,SAAS,GAAG+B,MAAM;AACpB;;AAEA;AACA;AACA;AACO,SAASC,UAAU,CAACC,kBAA0B,EAAQ;EAC3DhC,QAAQ,GAAGgC,kBAAkB;AAC/B"}