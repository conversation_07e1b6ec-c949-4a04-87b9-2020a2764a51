{"version": 3, "file": "Linter.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/Linter.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAC7D,OAAO,KAAK,EAAE,aAAa,IAAI,eAAe,EAAE,MAAM,iBAAiB,CAAC;AACxE,OAAO,KAAK,EACV,kBAAkB,EAClB,OAAO,EACP,UAAU,EACV,2BAA2B,EAC5B,MAAM,QAAQ,CAAC;AAChB,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACrC,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE/C,OAAO,OAAO,UAAU;IACtB;;;OAGG;gBACS,MAAM,CAAC,EAAE,MAAM,CAAC,aAAa;IAEzC;;;;OAIG;IACH,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,YAAY,GAAG,IAAI;IAEvE;;;;OAIG;IACH,UAAU,CAAC,WAAW,SAAS,MAAM,EAAE,QAAQ,SAAS,SAAS,OAAO,EAAE,EACxE,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,GAAG,kBAAkB,GACjE,IAAI;IAEP;;;OAGG;IACH,WAAW,CAAC,WAAW,SAAS,MAAM,EAAE,QAAQ,SAAS,SAAS,OAAO,EAAE,EACzE,aAAa,EAAE,MAAM,CACnB,MAAM,EACN,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,GAAG,kBAAkB,CACvD,GACA,IAAI;IAEP;;;OAGG;IACH,QAAQ,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;IAEtD;;;OAGG;IACH,aAAa,IAAI,UAAU;IAE3B;;;;;;;;OAQG;IACH,MAAM,CACJ,gBAAgB,EAAE,UAAU,GAAG,MAAM,EACrC,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,iBAAiB,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,aAAa,GAChD,MAAM,CAAC,WAAW,EAAE;IAEvB;;;;;;OAMG;IACH,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,OAAO,EAAE,MAAM,CAAC,UAAU,GACzB,MAAM,CAAC,SAAS;IAEnB;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IAMzB;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;CACjC;AAED,kBAAU,MAAM,CAAC;IACf,MAAM,WAAW,aAAa;QAC5B;;WAEG;QACH,GAAG,CAAC,EAAE,MAAM,CAAC;KACd;IAED,MAAM,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjC,MAAM,MAAM,cAAc,GAAG,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;IACtD,MAAM,MAAM,SAAS,GAAG,QAAQ,GAAG,cAAc,CAAC;IAElD,MAAM,MAAM,mBAAmB,GAAG,CAAC,SAAS,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;IAE5D,MAAM,MAAM,SAAS,GAAG,SAAS,GAAG,mBAAmB,CAAC;IACxD,MAAM,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;IAE7D,MAAM,MAAM,oBAAoB,GAAG,UAAU,GAAG,UAAU,GAAG,KAAK,GAAG,OAAO,CAAC;IAG7E,UAAU,UAAU;QAClB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB;;WAEG;QACH,GAAG,CAAC,EAAE;YAAE,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAA;SAAE,CAAC;QAClC;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;QAC5B;;WAEG;QACH,OAAO,CAAC,EAAE;YAAE,CAAC,IAAI,EAAE,MAAM,GAAG,oBAAoB,CAAA;SAAE,CAAC;QACnD;;WAEG;QACH,cAAc,CAAC,EAAE,OAAO,CAAC;QACzB;;WAEG;QACH,SAAS,CAAC,EAAE,cAAc,EAAE,CAAC;QAC7B;;WAEG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB;;WAEG;QACH,aAAa,CAAC,EAAE,aAAa,CAAC;QAC9B;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;QACnB;;WAEG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB;;WAEG;QACH,6BAA6B,CAAC,EAAE,OAAO,CAAC;QACxC;;WAEG;QACH,KAAK,CAAC,EAAE,WAAW,CAAC;QACpB;;WAEG;QACH,QAAQ,CAAC,EAAE,2BAA2B,CAAC;KACxC;IAED,MAAM,WAAW,cAAe,SAAQ,UAAU;QAChD,aAAa,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;QAClC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;KAC1B;IAED,MAAM,WAAW,MAAO,SAAQ,UAAU;QACxC;;WAEG;QACH,cAAc,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;QACnC;;WAEG;QACH,IAAI,CAAC,EAAE,OAAO,CAAC;KAChB;IAED,MAAM,MAAM,aAAa,GAAG,eAAe,CAAC;IAE5C,MAAM,WAAW,aAAa;QAC5B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;QAC5B;;WAEG;QACH,YAAY,CAAC,EAAE,OAAO,CAAC;QACvB;;WAEG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB;;WAEG;QACH,eAAe,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC;QAC9D;;;;;;WAMG;QACH,WAAW,CAAC,EAAE,SAAS,CAAC,aAAa,CAAC,CAAC;QACvC;;;WAGG;QACH,UAAU,CAAC,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;QACrC;;WAEG;QACH,6BAA6B,CAAC,EAAE,OAAO,GAAG,cAAc,CAAC;KAC1D;IAED,MAAM,WAAW,UAAW,SAAQ,aAAa;QAC/C;;WAEG;QACH,GAAG,CAAC,EAAE,OAAO,CAAC;KACf;IAED,MAAM,WAAW,cAAc;QAC7B,IAAI,EAAE,MAAM,CAAC;QACb,GAAG,EAAE,OAAO,CAAC;QACb,SAAS,CAAC,EAAE,MAAM,CAAC;KACpB;IAED,MAAM,WAAW,WAAW;QAC1B;;WAEG;QACH,MAAM,EAAE,MAAM,CAAC;QACf;;WAEG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB;;WAEG;QACH,KAAK,CAAC,EAAE,IAAI,CAAC;QACb;;WAEG;QACH,GAAG,CAAC,EAAE,OAAO,CAAC;QACd;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QACb;;WAEG;QACH,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,EAAE,MAAM,CAAC;QACjB;;WAEG;QACH,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;QACtB;;WAEG;QACH,QAAQ,EAAE,QAAQ,CAAC;QACnB,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;QACtB;;WAEG;QACH,WAAW,CAAC,EAAE,cAAc,EAAE,CAAC;KAChC;IAED,MAAM,WAAW,SAAS;QACxB;;WAEG;QACH,KAAK,EAAE,OAAO,CAAC;QACf;;WAEG;QACH,MAAM,EAAE,MAAM,CAAC;QACf;;WAEG;QACH,QAAQ,EAAE,WAAW,EAAE,CAAC;KACzB;IAED,MAAM,MAAM,YAAY,GACpB;QACE,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC;KAChE,GACD;QACE,cAAc,CACZ,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,aAAa,GACtB,iBAAiB,CAAC;KACtB,CAAC;IAEN,MAAM,WAAW,iBAAiB;QAChC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC;QACtB,QAAQ,CAAC,EAAE,cAAc,CAAC;QAC1B,YAAY,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC;QAClC,WAAW,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC;KACtC;IAED,MAAM,WAAW,SAAS;QACxB;;WAEG;QACH,UAAU,CAAC,EAAE,CACX,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,MAAM,KACb,KAAK,CAAC,MAAM,GAAG;YAAE,IAAI,EAAE,MAAM,CAAC;YAAC,QAAQ,EAAE,MAAM,CAAA;SAAE,CAAC,CAAC;QACxD;;WAEG;QACH,WAAW,CAAC,EAAE,CACZ,YAAY,EAAE,MAAM,CAAC,WAAW,EAAE,EAAE,EACpC,QAAQ,EAAE,MAAM,KACb,MAAM,CAAC,WAAW,EAAE,CAAC;QAC1B;;WAEG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;KAC3B;IAED,MAAM,WAAW,WAAW;QAC1B;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACxC;;WAEG;QACH,aAAa,CAAC,EAAE,aAAa,CAAC;KAC/B;IAED,MAAM,WAAW,MAAM;QACrB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACxC;;WAEG;QACH,YAAY,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC3C;;WAEG;QACH,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACvC;;WAEG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,kBAAkB,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;KAC5E;;CACF;;AAED;;;;GAIG;AACH,cAAM,MAAO,SAAQ,WAAmC;CAAG;AAE3D,OAAO,EAAE,MAAM,EAAE,CAAC"}