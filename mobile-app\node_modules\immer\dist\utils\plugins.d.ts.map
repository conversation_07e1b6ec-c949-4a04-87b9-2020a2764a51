{"version": 3, "file": "plugins.d.ts", "sourceRoot": "", "sources": ["../src/utils/plugins.ts"], "names": [], "mappings": "AAAA,OAAO,EACN,UAAU,EACV,KAAK,EACL,UAAU,EACV,OAAO,EACP,SAAS,EACT,cAAc,EACd,MAAM,EACN,MAAM,EACN,SAAS,EAET,MAAM,aAAa,CAAA;AAEpB,uBAAuB;AACvB,QAAA,MAAM,OAAO,EAAE;IACd,OAAO,CAAC,EAAE;QACT,gBAAgB,CACf,KAAK,EAAE,UAAU,EACjB,QAAQ,EAAE,SAAS,EACnB,OAAO,EAAE,KAAK,EAAE,EAChB,cAAc,EAAE,KAAK,EAAE,GACrB,IAAI,CAAA;QACP,2BAA2B,CAC1B,IAAI,EAAE,GAAG,EACT,WAAW,EAAE,GAAG,EAChB,OAAO,EAAE,KAAK,EAAE,EAChB,cAAc,EAAE,KAAK,EAAE,GACrB,IAAI,CAAA;QACP,aAAa,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,CAAA;KAC/C,CAAA;IACD,GAAG,CAAC,EAAE;QACL,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,GAAG,IAAI,CAAA;QAC3E,eAAe,CAAC,CAAC,EAChB,IAAI,EAAE,CAAC,EACP,MAAM,CAAC,EAAE,UAAU,GACjB,OAAO,CAAC,CAAC,EAAE,cAAc,GAAG,aAAa,CAAC,CAAA;QAC7C,WAAW,CAAC,KAAK,EAAE,aAAa,GAAG,cAAc,GAAG,OAAO,CAAA;KAC3D,CAAA;IACD,MAAM,CAAC,EAAE;QACR,SAAS,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,UAAU,GAAG,CAAC,CAAA;QAC9D,SAAS,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,UAAU,GAAG,CAAC,CAAA;KAC9D,CAAA;CACI,CAAA;AAEN,aAAK,OAAO,GAAG,OAAO,OAAO,CAAA;AAE7B,wBAAgB,SAAS,CAAC,CAAC,SAAS,MAAM,OAAO,EAChD,SAAS,EAAE,CAAC,GACV,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAOhC;AAED,wBAAgB,UAAU,CAAC,CAAC,SAAS,MAAM,OAAO,EACjD,SAAS,EAAE,CAAC,EACZ,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,GACxB,IAAI,CAEN;AAED,iBAAiB;AAEjB,UAAU,YAAa,SAAQ,cAAc;IAC5C,SAAS,EAAE;QAAC,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAC,CAAA;IAC/B,OAAO,CAAC,EAAE,UAAU,CAAA;IACpB,QAAQ,EAAE,OAAO,CAAA;CACjB;AAED,MAAM,WAAW,cAAe,SAAQ,YAAY;IACnD,KAAK,EAAE,SAAS,CAAC,SAAS,CAAA;IAC1B,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;IAC1C,KAAK,EAAE,SAAS,CAAA;IAChB,KAAK,EAAE,SAAS,GAAG,IAAI,CAAA;CACvB;AAED,MAAM,WAAW,aAAc,SAAQ,YAAY;IAClD,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAA;IACzB,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;IACzC,KAAK,EAAE,GAAG,CAAA;IACV,KAAK,EAAE,GAAG,CAAA;CACV;AAED,uBAAuB;AAEvB,MAAM,WAAW,QAAS,SAAQ,cAAc;IAC/C,KAAK,EAAE,SAAS,CAAC,GAAG,CAAA;IACpB,KAAK,EAAE,MAAM,GAAG,SAAS,CAAA;IACzB,SAAS,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,SAAS,CAAA;IACxC,KAAK,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,OAAO,CAAA;IACjB,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;CACjC;AAED,MAAM,WAAW,QAAS,SAAQ,cAAc;IAC/C,KAAK,EAAE,SAAS,CAAC,GAAG,CAAA;IACpB,KAAK,EAAE,MAAM,GAAG,SAAS,CAAA;IACzB,KAAK,EAAE,MAAM,CAAA;IACb,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IAC1B,QAAQ,EAAE,OAAO,CAAA;IACjB,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;CACjC;AAED,qBAAqB;AAErB,oBAAY,SAAS,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAA"}