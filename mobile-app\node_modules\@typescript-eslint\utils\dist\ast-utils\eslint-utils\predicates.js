"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isSemicolonToken = exports.isOpeningParenToken = exports.isOpeningBracketToken = exports.isOpeningBraceToken = exports.isNotSemicolonToken = exports.isNotOpeningParenToken = exports.isNotOpeningBracketToken = exports.isNotOpeningBraceToken = exports.isNotCommentToken = exports.isNotCommaToken = exports.isNotColonToken = exports.isNotClosingParenToken = exports.isNotClosingBracketToken = exports.isNotClosingBraceToken = exports.isNotArrowToken = exports.isCommentToken = exports.isCommaToken = exports.isColonToken = exports.isClosingParenToken = exports.isClosingBracketToken = exports.isClosingBraceToken = exports.isArrowToken = void 0;
const eslintUtils = __importStar(require("@eslint-community/eslint-utils"));
const isArrowToken = eslintUtils.isArrowToken;
exports.isArrowToken = isArrowToken;
const isNotArrowToken = eslintUtils.isNotArrowToken;
exports.isNotArrowToken = isNotArrowToken;
const isClosingBraceToken = eslintUtils.isClosingBraceToken;
exports.isClosingBraceToken = isClosingBraceToken;
const isNotClosingBraceToken = eslintUtils.isNotClosingBraceToken;
exports.isNotClosingBraceToken = isNotClosingBraceToken;
const isClosingBracketToken = eslintUtils.isClosingBracketToken;
exports.isClosingBracketToken = isClosingBracketToken;
const isNotClosingBracketToken = eslintUtils.isNotClosingBracketToken;
exports.isNotClosingBracketToken = isNotClosingBracketToken;
const isClosingParenToken = eslintUtils.isClosingParenToken;
exports.isClosingParenToken = isClosingParenToken;
const isNotClosingParenToken = eslintUtils.isNotClosingParenToken;
exports.isNotClosingParenToken = isNotClosingParenToken;
const isColonToken = eslintUtils.isColonToken;
exports.isColonToken = isColonToken;
const isNotColonToken = eslintUtils.isNotColonToken;
exports.isNotColonToken = isNotColonToken;
const isCommaToken = eslintUtils.isCommaToken;
exports.isCommaToken = isCommaToken;
const isNotCommaToken = eslintUtils.isNotCommaToken;
exports.isNotCommaToken = isNotCommaToken;
const isCommentToken = eslintUtils.isCommentToken;
exports.isCommentToken = isCommentToken;
const isNotCommentToken = eslintUtils.isNotCommentToken;
exports.isNotCommentToken = isNotCommentToken;
const isOpeningBraceToken = eslintUtils.isOpeningBraceToken;
exports.isOpeningBraceToken = isOpeningBraceToken;
const isNotOpeningBraceToken = eslintUtils.isNotOpeningBraceToken;
exports.isNotOpeningBraceToken = isNotOpeningBraceToken;
const isOpeningBracketToken = eslintUtils.isOpeningBracketToken;
exports.isOpeningBracketToken = isOpeningBracketToken;
const isNotOpeningBracketToken = eslintUtils.isNotOpeningBracketToken;
exports.isNotOpeningBracketToken = isNotOpeningBracketToken;
const isOpeningParenToken = eslintUtils.isOpeningParenToken;
exports.isOpeningParenToken = isOpeningParenToken;
const isNotOpeningParenToken = eslintUtils.isNotOpeningParenToken;
exports.isNotOpeningParenToken = isNotOpeningParenToken;
const isSemicolonToken = eslintUtils.isSemicolonToken;
exports.isSemicolonToken = isSemicolonToken;
const isNotSemicolonToken = eslintUtils.isNotSemicolonToken;
exports.isNotSemicolonToken = isNotSemicolonToken;
//# sourceMappingURL=predicates.js.map