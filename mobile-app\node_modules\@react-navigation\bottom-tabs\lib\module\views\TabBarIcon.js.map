{"version": 3, "names": ["React", "StyleSheet", "View", "Badge", "TabBarIcon", "route", "_", "horizontal", "badge", "badgeStyle", "activeOpacity", "inactiveOpacity", "activeTintColor", "inactiveTintColor", "renderIcon", "style", "size", "styles", "iconHorizontal", "iconVertical", "icon", "opacity", "focused", "color", "badgeHorizontal", "badgeVertical", "create", "position", "alignSelf", "alignItems", "justifyContent", "height", "width", "min<PERSON><PERSON><PERSON>", "flex", "marginTop", "left", "top"], "sourceRoot": "../../../src", "sources": ["views/TabBarIcon.tsx"], "mappings": "AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAEEC,UAAU,EAEVC,IAAI,QAEC,cAAc;AAErB,OAAOC,KAAK,MAAM,SAAS;AAmB3B,eAAe,SAASC,UAAU,OAWxB;EAAA,IAXyB;IACjCC,KAAK,EAAEC,CAAC;IACRC,UAAU;IACVC,KAAK;IACLC,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC,eAAe;IACfC,iBAAiB;IACjBC,UAAU;IACVC;EACK,CAAC;EACN,MAAMC,IAAI,GAAG,EAAE;;EAEf;EACA;EACA,oBACE,oBAAC,IAAI;IACH,KAAK,EAAE,CAACT,UAAU,GAAGU,MAAM,CAACC,cAAc,GAAGD,MAAM,CAACE,YAAY,EAAEJ,KAAK;EAAE,gBAEzE,oBAAC,IAAI;IAAC,KAAK,EAAE,CAACE,MAAM,CAACG,IAAI,EAAE;MAAEC,OAAO,EAAEX;IAAc,CAAC;EAAE,GACpDI,UAAU,CAAC;IACVQ,OAAO,EAAE,IAAI;IACbN,IAAI;IACJO,KAAK,EAAEX;EACT,CAAC,CAAC,CACG,eACP,oBAAC,IAAI;IAAC,KAAK,EAAE,CAACK,MAAM,CAACG,IAAI,EAAE;MAAEC,OAAO,EAAEV;IAAgB,CAAC;EAAE,GACtDG,UAAU,CAAC;IACVQ,OAAO,EAAE,KAAK;IACdN,IAAI;IACJO,KAAK,EAAEV;EACT,CAAC,CAAC,CACG,eACP,oBAAC,KAAK;IACJ,OAAO,EAAEL,KAAK,IAAI,IAAK;IACvB,KAAK,EAAE,CACLS,MAAM,CAACT,KAAK,EACZD,UAAU,GAAGU,MAAM,CAACO,eAAe,GAAGP,MAAM,CAACQ,aAAa,EAC1DhB,UAAU,CACV;IACF,IAAI,EAAGO,IAAI,GAAG,CAAC,GAAI;EAAE,GAEpBR,KAAK,CACA,CACH;AAEX;AAEA,MAAMS,MAAM,GAAGhB,UAAU,CAACyB,MAAM,CAAC;EAC/BN,IAAI,EAAE;IACJ;IACA;IACA;IACAO,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACb;IACAC,QAAQ,EAAE;EACZ,CAAC;EACDd,YAAY,EAAE;IACZe,IAAI,EAAE;EACR,CAAC;EACDhB,cAAc,EAAE;IACda,MAAM,EAAE,MAAM;IACdI,SAAS,EAAE;EACb,CAAC;EACD3B,KAAK,EAAE;IACLmB,QAAQ,EAAE,UAAU;IACpBS,IAAI,EAAE;EACR,CAAC;EACDX,aAAa,EAAE;IACbY,GAAG,EAAE;EACP,CAAC;EACDb,eAAe,EAAE;IACfa,GAAG,EAAE;EACP;AACF,CAAC,CAAC"}