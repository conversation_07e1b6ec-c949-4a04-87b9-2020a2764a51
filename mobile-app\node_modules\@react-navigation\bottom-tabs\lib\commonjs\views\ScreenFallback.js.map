{"version": 3, "names": ["Screens", "require", "e", "MaybeScreenContainer", "enabled", "rest", "screensEnabled", "MaybeScreen", "visible", "children"], "sourceRoot": "../../../src", "sources": ["views/ScreenFallback.tsx"], "mappings": ";;;;;;;AAAA;AACA;AACA;AAAqE;AAAA;AAAA;AAUrE,IAAIA,OAA0D;AAE9D,IAAI;EACFA,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC3C,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;AAAA;AAGK,MAAMC,oBAAoB,GAAG,QAO9B;EAAA;EAAA,IAP+B;IACnCC,OAAO;IACP,GAAGC;EAKL,CAAC;EACC,gBAAIL,OAAO,8DAAP,SAASM,cAAc,kDAAvB,oCAA2B,EAAE;IAC/B,oBAAO,oBAAC,OAAO,CAAC,eAAe;MAAC,OAAO,EAAEF;IAAQ,GAAKC,IAAI,EAAI;EAChE;EAEA,oBAAO,oBAAC,iBAAI,EAAKA,IAAI,CAAI;AAC3B,CAAC;AAAC;AAEK,SAASE,WAAW,QAAwC;EAAA;EAAA,IAAvC;IAAEC,OAAO;IAAEC,QAAQ;IAAE,GAAGJ;EAAY,CAAC;EAC/D,iBAAIL,OAAO,+DAAP,UAASM,cAAc,kDAAvB,qCAA2B,EAAE;IAC/B,oBACE,oBAAC,OAAO,CAAC,MAAM;MAAC,aAAa,EAAEE,OAAO,GAAG,CAAC,GAAG;IAAE,GAAKH,IAAI,GACrDI,QAAQ,CACM;EAErB;EAEA,oBACE,oBAAC,4BAAkB;IAAC,OAAO,EAAED;EAAQ,GAAKH,IAAI,GAC3CI,QAAQ,CACU;AAEzB"}