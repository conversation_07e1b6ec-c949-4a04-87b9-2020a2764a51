{"version": 3, "names": ["_ESLINT_VISITOR_KEYS", "require", "babel", "ESLINT_VISITOR_KEYS", "KEYS", "visitorKeys", "getVisitorKeys", "newTypes", "ChainExpression", "ImportExpression", "Literal", "MethodDefinition", "concat", "Property", "PropertyDefinition", "conflictTypes", "ExportAllDeclaration", "Object", "assign", "types", "VISITOR_KEYS", "ClassPrivateMethod", "tokLabels", "getTokLabels", "p", "reduce", "o", "k", "v", "entries", "keys", "map", "tokTypes", "key", "tok", "label"], "sources": ["../../src/worker/ast-info.cts"], "sourcesContent": ["// @ts-expect-error no types\nimport _ESLINT_VISITOR_KEYS = require(\"eslint-visitor-keys\");\nimport babel = require(\"./babel-core.cts\");\n\nconst ESLINT_VISITOR_KEYS = _ESLINT_VISITOR_KEYS.KEYS;\n\nlet visitorKeys: Record<string, string[]>;\nexport function getVisitorKeys() {\n  if (!visitorKeys) {\n    // AST Types that are not presented in Babel AST\n    const newTypes = {\n      ChainExpression: ESLINT_VISITOR_KEYS.ChainExpression,\n      ImportExpression: ESLINT_VISITOR_KEYS.ImportExpression,\n      Literal: ESLINT_VISITOR_KEYS.Literal,\n      MethodDefinition: [\"decorators\"].concat(\n        ESLINT_VISITOR_KEYS.MethodDefinition,\n      ),\n      Property: [\"decorators\"].concat(ESLINT_VISITOR_KEYS.Property),\n      PropertyDefinition: [\"decorators\", \"typeAnnotation\"].concat(\n        ESLINT_VISITOR_KEYS.PropertyDefinition,\n      ),\n    };\n\n    // AST Types that shares `\"type\"` property with Babel but have different shape\n    const conflictTypes = {\n      ExportAllDeclaration: ESLINT_VISITOR_KEYS.ExportAllDeclaration,\n    };\n\n    visitorKeys = {\n      ...newTypes,\n      ...babel.types.VISITOR_KEYS,\n      ...conflictTypes,\n      ...(process.env.BABEL_8_BREAKING\n        ? {}\n        : {\n            ClassPrivateMethod: [\"decorators\"].concat(\n              ESLINT_VISITOR_KEYS.MethodDefinition,\n            ),\n          }),\n    };\n  }\n  return visitorKeys;\n}\n\nlet tokLabels;\nexport function getTokLabels() {\n  return (tokLabels ||= (\n    process.env.BABEL_8_BREAKING\n      ? Object.fromEntries\n      : (p: any[]) => p.reduce((o, [k, v]) => ({ ...o, [k]: v }), {})\n  )(Object.entries(babel.tokTypes).map(([key, tok]) => [key, tok.label])));\n}\n"], "mappings": ";;;;;;;MACOA,oBAAoB,GAAAC,OAAA,CAAW,qBAAqB;AAAA,MACpDC,KAAK,GAAAD,OAAA,CAAW,kBAAkB;AAEzC,MAAME,mBAAmB,GAAGH,oBAAoB,CAACI,IAAI;AAErD,IAAIC,WAAqC;AAClC,SAASC,cAAcA,CAAA,EAAG;EAC/B,IAAI,CAACD,WAAW,EAAE;IAEhB,MAAME,QAAQ,GAAG;MACfC,eAAe,EAAEL,mBAAmB,CAACK,eAAe;MACpDC,gBAAgB,EAAEN,mBAAmB,CAACM,gBAAgB;MACtDC,OAAO,EAAEP,mBAAmB,CAACO,OAAO;MACpCC,gBAAgB,EAAE,CAAC,YAAY,CAAC,CAACC,MAAM,CACrCT,mBAAmB,CAACQ,gBACtB,CAAC;MACDE,QAAQ,EAAE,CAAC,YAAY,CAAC,CAACD,MAAM,CAACT,mBAAmB,CAACU,QAAQ,CAAC;MAC7DC,kBAAkB,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAACF,MAAM,CACzDT,mBAAmB,CAACW,kBACtB;IACF,CAAC;IAGD,MAAMC,aAAa,GAAG;MACpBC,oBAAoB,EAAEb,mBAAmB,CAACa;IAC5C,CAAC;IAEDX,WAAW,GAAAY,MAAA,CAAAC,MAAA,KACNX,QAAQ,EACRL,KAAK,CAACiB,KAAK,CAACC,YAAY,EACxBL,aAAa,EAGZ;MACEM,kBAAkB,EAAE,CAAC,YAAY,CAAC,CAACT,MAAM,CACvCT,mBAAmB,CAACQ,gBACtB;IACF,CAAC,CACN;EACH;EACA,OAAON,WAAW;AACpB;AAEA,IAAIiB,SAAS;AACN,SAASC,YAAYA,CAAA,EAAG;EAC7B,OAAQD,SAAS,KAATA,SAAS,GAAK,CAGfE,CAAQ,IAAKA,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAAX,MAAA,CAAAC,MAAA,KAAWQ,CAAC;IAAE,CAACC,CAAC,GAAGC;EAAC,EAAG,EAAE,CAAC,CAAC,CAAC,EACjE,CAAAX,MAAA,CAAAY,OAAA,KAAAH,CAAA,IAAAT,MAAA,CAAAa,IAAA,CAAAJ,CAAA,EAAAK,GAAA,CAAAJ,CAAA,KAAAA,CAAA,EAAAD,CAAA,CAAAC,CAAA,MAAezB,KAAK,CAAC8B,QAAQ,CAAC,CAACD,GAAG,CAAC,CAAC,CAACE,GAAG,EAAEC,GAAG,CAAC,KAAK,CAACD,GAAG,EAAEC,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC;AACzE", "ignoreList": []}