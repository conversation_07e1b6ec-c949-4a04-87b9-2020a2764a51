{"version": 3, "names": ["React", "EnsureSingleNavigator", "NavigationStateContext", "StaticContainer", "useOptionsGetters", "SceneView", "screen", "route", "navigation", "routeState", "getState", "setState", "options", "clearOptions", "navigator<PERSON><PERSON><PERSON><PERSON>", "useRef", "<PERSON><PERSON><PERSON>", "useCallback", "current", "addOptionsGetter", "key", "<PERSON><PERSON><PERSON>", "getCurrentState", "state", "currentRoute", "routes", "find", "r", "undefined", "setCurrentState", "child", "map", "isInitialRef", "useEffect", "getIsInitial", "context", "useMemo", "ScreenComponent", "getComponent", "component", "name", "children"], "sourceRoot": "../../src", "sources": ["SceneView.tsx"], "mappings": "AAMA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAE/C,OAAOC,iBAAiB,MAAM,qBAAqB;AAmBnD;AACA;AACA;AACA;AACA,eAAe,SAASC,SAAS,OAYD;EAAA,IAT9B;IACAC,MAAM;IACNC,KAAK;IACLC,UAAU;IACVC,UAAU;IACVC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC;EAC2B,CAAC;EAC5B,MAAMC,eAAe,GAAGd,KAAK,CAACe,MAAM,EAAsB;EAC1D,MAAMC,MAAM,GAAGhB,KAAK,CAACiB,WAAW,CAAC,MAAMH,eAAe,CAACI,OAAO,EAAE,EAAE,CAAC;EAEnE,MAAM;IAAEC;EAAiB,CAAC,GAAGf,iBAAiB,CAAC;IAC7CgB,GAAG,EAAEb,KAAK,CAACa,GAAG;IACdR,OAAO;IACPJ;EACF,CAAC,CAAC;EAEF,MAAMa,MAAM,GAAGrB,KAAK,CAACiB,WAAW,CAAEG,GAAW,IAAK;IAChDN,eAAe,CAACI,OAAO,GAAGE,GAAG;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,eAAe,GAAGtB,KAAK,CAACiB,WAAW,CAAC,MAAM;IAC9C,MAAMM,KAAK,GAAGb,QAAQ,EAAE;IACxB,MAAMc,YAAY,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAKb,KAAK,CAACa,GAAG,CAAC;IAElE,OAAOI,YAAY,GAAGA,YAAY,CAACD,KAAK,GAAGK,SAAS;EACtD,CAAC,EAAE,CAAClB,QAAQ,EAAEH,KAAK,CAACa,GAAG,CAAC,CAAC;EAEzB,MAAMS,eAAe,GAAG7B,KAAK,CAACiB,WAAW,CACtCa,KAAkE,IAAK;IACtE,MAAMP,KAAK,GAAGb,QAAQ,EAAE;IAExBC,QAAQ,CAAC;MACP,GAAGY,KAAK;MACRE,MAAM,EAAEF,KAAK,CAACE,MAAM,CAACM,GAAG,CAAEJ,CAAC,IACzBA,CAAC,CAACP,GAAG,KAAKb,KAAK,CAACa,GAAG,GAAG;QAAE,GAAGO,CAAC;QAAEJ,KAAK,EAAEO;MAAM,CAAC,GAAGH,CAAC;IAEpD,CAAC,CAAC;EACJ,CAAC,EACD,CAACjB,QAAQ,EAAEH,KAAK,CAACa,GAAG,EAAET,QAAQ,CAAC,CAChC;EAED,MAAMqB,YAAY,GAAGhC,KAAK,CAACe,MAAM,CAAC,IAAI,CAAC;EAEvCf,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpBD,YAAY,CAACd,OAAO,GAAG,KAAK;EAC9B,CAAC,CAAC;;EAEF;EACAlB,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB,OAAOpB,YAAY;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMqB,YAAY,GAAGlC,KAAK,CAACiB,WAAW,CAAC,MAAMe,YAAY,CAACd,OAAO,EAAE,EAAE,CAAC;EAEtE,MAAMiB,OAAO,GAAGnC,KAAK,CAACoC,OAAO,CAC3B,OAAO;IACLb,KAAK,EAAEd,UAAU;IACjBC,QAAQ,EAAEY,eAAe;IACzBX,QAAQ,EAAEkB,eAAe;IACzBb,MAAM;IACNK,MAAM;IACNa,YAAY;IACZf;EACF,CAAC,CAAC,EACF,CACEV,UAAU,EACVa,eAAe,EACfO,eAAe,EACfb,MAAM,EACNK,MAAM,EACNa,YAAY,EACZf,gBAAgB,CACjB,CACF;EAED,MAAMkB,eAAe,GAAG/B,MAAM,CAACgC,YAAY,GACvChC,MAAM,CAACgC,YAAY,EAAE,GACrBhC,MAAM,CAACiC,SAAS;EAEpB,oBACE,oBAAC,sBAAsB,CAAC,QAAQ;IAAC,KAAK,EAAEJ;EAAQ,gBAC9C,oBAAC,qBAAqB,qBACpB,oBAAC,eAAe;IACd,IAAI,EAAE7B,MAAM,CAACkC,IAAK;IAClB,MAAM,EAAEH,eAAe,IAAI/B,MAAM,CAACmC,QAAS;IAC3C,UAAU,EAAEjC,UAAW;IACvB,KAAK,EAAED;EAAM,GAEZ8B,eAAe,KAAKT,SAAS,gBAC5B,oBAAC,eAAe;IAAC,UAAU,EAAEpB,UAAW;IAAC,KAAK,EAAED;EAAM,EAAG,GACvDD,MAAM,CAACmC,QAAQ,KAAKb,SAAS,GAC/BtB,MAAM,CAACmC,QAAQ,CAAC;IAAEjC,UAAU;IAAED;EAAM,CAAC,CAAC,GACpC,IAAI,CACQ,CACI,CACQ;AAEtC"}