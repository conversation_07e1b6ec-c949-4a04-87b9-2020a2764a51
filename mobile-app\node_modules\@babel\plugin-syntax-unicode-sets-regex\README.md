# @babel/plugin-syntax-unicode-sets-regex

> Parse regular expressions' unicodeSets (v) flag.

See our website [@babel/plugin-syntax-unicode-sets-regex](https://babeljs.io/docs/en/babel-plugin-syntax-unicode-sets-regex) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-syntax-unicode-sets-regex
```

or using yarn:

```sh
yarn add @babel/plugin-syntax-unicode-sets-regex --dev
```
