"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = throwIfNonAllowedProtocol;
/**
 * Check if a url uses an allowed protocol
 */

const ALLOWED_PROTOCOLS = ['http:', 'https:', 'devtools:', 'flipper:'];
function throwIfNonAllowedProtocol(url) {
  const _url = new URL(url);
  const urlProtocol = _url.protocol;
  if (!ALLOWED_PROTOCOLS.includes(urlProtocol)) {
    throw new Error(`Invalid url protocol ${urlProtocol}.\nAllowed protocols: ${ALLOWED_PROTOCOLS.join(', ')}`);
  }
}

//# sourceMappingURL=throwIfNonAllowedProtocol.ts.map