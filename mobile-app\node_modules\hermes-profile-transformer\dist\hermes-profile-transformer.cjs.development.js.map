{"version": 3, "file": "hermes-profile-transformer.cjs.development.js", "sources": ["../src/types/Phases.ts", "../src/profiler/cpuProfilerModel.ts", "../node_modules/babel-plugin-transform-async-to-promises/helpers.js", "../src/utils/fileSystem.ts", "../src/profiler/applySourceMapsToEvents.ts", "../src/index.ts"], "sourcesContent": ["export enum EventsPhase {\n  DURATION_EVENTS_BEGIN = 'B',\n  DURATION_EVENTS_END = 'E',\n  COMPLETE_EVENTS = 'X',\n  INSTANT_EVENTS = 'I',\n  COUNTER_EVENTS = 'C',\n  ASYNC_EVENTS_NESTABLE_START = 'b',\n  ASYNC_EVENTS_NESTABLE_INSTANT = 'n',\n  ASYNC_EVENTS_NESTABLE_END = 'e',\n  FLOW_EVENTS_START = 's',\n  FLOW_EVENTS_STEP = 't',\n  FLOW_EVENTS_END = 'f',\n  SAMPLE_EVENTS = 'P',\n  OBJECT_EVENTS_CREATED = 'N',\n  OBJECT_EVENTS_SNAPSHOT = 'O',\n  OBJECT_EVENTS_DESTROYED = 'D',\n  METADATA_EVENTS = 'M',\n  MEMORY_DUMP_EVENTS_GLOBAL = 'V',\n  MEMORY_DUMP_EVENTS_PROCESS = 'v',\n  MARK_EVENTS = 'R',\n  CLOCK_SYNC_EVENTS = 'c',\n  CONTEXT_EVENTS_ENTER = '(',\n  CONTEXT_EVENTS_LEAVE = ')',\n  // Deprecated\n  ASYNC_EVENTS_START = 'S',\n  ASYNC_EVENTS_STEP_INTO = 'T',\n  ASYNC_EVENTS_STEP_PAST = 'p',\n  ASYNC_EVENTS_END = 'F',\n  LINKED_ID_EVENTS = '=',\n}\n", "/**\n * @license Copyright 2020 The Lighthouse Authors. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.\n *\n * MODIFICATION NOTICE:\n * This file is derived from `https://github.com/GoogleChrome/lighthouse/blob/0422daa9b1b8528dd8436860b153134bd0f959f1/lighthouse-core/lib/tracehouse/cpu-profile-model.js`\n * and has been modified by <PERSON><PERSON><PERSON> (email: <EMAIL>)\n * The following changes have been made to the original file:\n * 1. Converted code to Typescript and defined necessary types\n * 2. Wrote a method @see collectProfileEvents to convert the Hermes Samples to Profile Chunks supported by Lighthouse Parser\n * 3. Modified @see constructNodes to work with the Hermes Samples and StackFrames\n */\n\n/**\n * @fileoverview\n *\n * This model converts the `Profile` and `ProfileChunk` mega trace events from the `disabled-by-default-v8.cpu_profiler`\n * category into B/E-style trace events that main-thread-tasks.js already knows how to parse into a task tree.\n *\n * The CPU profiler measures where time is being spent by sampling the stack (See https://www.jetbrains.com/help/profiler/Profiling_Guidelines__Choosing_the_Right_Profiling_Mode.html\n * for a generic description of the differences between tracing and sampling).\n *\n * A `Profile` event is a record of the stack that was being executed at different sample points in time.\n * It has a structure like this:\n *\n *    nodes: [function A, function B, function C]\n *    samples: [node with id 2, node with id 1, ...]\n *    timeDeltas: [4125μs since last sample, 121μs since last sample, ...]\n *\n * Helpful prior art:\n * @see https://cs.chromium.org/chromium/src/third_party/devtools-frontend/src/front_end/sdk/CPUProfileDataModel.js?sq=package:chromium&g=0&l=42\n * @see https://github.com/v8/v8/blob/99ca333b0efba3236954b823101315aefeac51ab/tools/profile.js\n * @see https://github.com/jlfwong/speedscope/blob/9ed1eb192cb7e9dac43a5f25bd101af169dc654a/src/import/chrome.ts#L200\n */\n\nimport {\n  CPUProfileChunk,\n  CPUProfileChunkNode,\n  CPUProfileChunker,\n} from '../types/CPUProfile';\nimport { DurationEvent } from '../types/EventInterfaces';\nimport {\n  HermesCPUProfile,\n  HermesSample,\n  HermesStackFrame,\n} from '../types/HermesProfile';\nimport { EventsPhase } from '../types/Phases';\n\nexport class CpuProfilerModel {\n  _profile: CPUProfileChunk;\n  _nodesById: Map<number, CPUProfileChunkNode>;\n  _activeNodeArraysById: Map<number, number[]>;\n\n  constructor(profile: CPUProfileChunk) {\n    this._profile = profile;\n    this._nodesById = this._createNodeMap();\n    this._activeNodeArraysById = this._createActiveNodeArrays();\n  }\n\n  /**\n   * Initialization function to enable O(1) access to nodes by node ID.\n   * @return {Map<number, CPUProfileChunkNode}\n   */\n  _createNodeMap(): Map<number, CPUProfileChunkNode> {\n    /** @type {Map<number, CpuProfile['nodes'][0]>} */\n    const map: Map<number, CPUProfileChunkNode> = new Map<\n      number,\n      CPUProfileChunkNode\n    >();\n    for (const node of this._profile.nodes) {\n      map.set(node.id, node);\n    }\n\n    return map;\n  }\n\n  /**\n   * Initialization function to enable O(1) access to the set of active nodes in the stack by node ID.\n   * @return Map<number, number[]>\n   */\n  _createActiveNodeArrays(): Map<number, number[]> {\n    const map: Map<number, number[]> = new Map<number, number[]>();\n\n    /**\n     * Given a nodeId, `getActiveNodes` gets all the parent nodes in reversed call order\n     * @param {number} id\n     */\n    const getActiveNodes = (id: number): number[] => {\n      if (map.has(id)) return map.get(id) || [];\n\n      const node = this._nodesById.get(id);\n      if (!node) throw new Error(`No such node ${id}`);\n      if (node.parent) {\n        const array = getActiveNodes(node.parent).concat([id]);\n        map.set(id, array);\n        return array;\n      } else {\n        return [id];\n      }\n    };\n\n    for (const node of this._profile.nodes) {\n      map.set(node.id, getActiveNodes(node.id));\n    }\n    return map;\n  }\n\n  /**\n   * Returns all the node IDs in a stack when a specific nodeId is at the top of the stack\n   * (i.e. a stack's node ID and the node ID of all of its parents).\n   */\n  _getActiveNodeIds(nodeId: number): number[] {\n    const activeNodeIds = this._activeNodeArraysById.get(nodeId);\n    if (!activeNodeIds) throw new Error(`No such node ID ${nodeId}`);\n    return activeNodeIds;\n  }\n\n  /**\n   * Generates the necessary B/E-style trace events for a single transition from stack A to stack B\n   * at the given timestamp.\n   *\n   * Example:\n   *\n   *    timestamp 1234\n   *    previousNodeIds 1,2,3\n   *    currentNodeIds 1,2,4\n   *\n   *    yields [end 3 at ts 1234, begin 4 at ts 1234]\n   *\n   * @param {number} timestamp\n   * @param {Array<number>} previousNodeIds\n   * @param {Array<number>} currentNodeIds\n   * @returns {Array<DurationEvent>}\n   */\n  _createStartEndEventsForTransition(\n    timestamp: number,\n    previousNodeIds: number[],\n    currentNodeIds: number[]\n  ): DurationEvent[] {\n    // Start nodes are the nodes which are present only in the currentNodeIds and not in PreviousNodeIds\n    const startNodes: CPUProfileChunkNode[] = currentNodeIds\n      .filter(id => !previousNodeIds.includes(id))\n      .map(id => this._nodesById.get(id)!);\n    // End nodes are the nodes which are present only in the PreviousNodeIds and not in CurrentNodeIds\n    const endNodes: CPUProfileChunkNode[] = previousNodeIds\n      .filter(id => !currentNodeIds.includes(id))\n      .map(id => this._nodesById.get(id)!);\n\n    /**\n     * The name needs to be modified if `http://` is present as this directs us to bundle files which does not add any information for the end user\n     * @param name\n     */\n    const removeLinksIfExist = (name: string): string => {\n      // If the name includes `http://`, we can filter the name\n      if (name.includes('http://')) {\n        name = name.substring(0, name.lastIndexOf('('));\n      }\n      return name || 'anonymous';\n    };\n\n    /**\n     * Create a Duration Event from CPUProfileChunkNodes.\n     * @param {CPUProfileChunkNode} node\n     * @return {DurationEvent} */\n    const createEvent = (node: CPUProfileChunkNode): DurationEvent => ({\n      ts: timestamp,\n      pid: this._profile.pid,\n      tid: Number(this._profile.tid),\n      ph: EventsPhase.DURATION_EVENTS_BEGIN,\n      name: removeLinksIfExist(node.callFrame.name),\n      cat: node.callFrame.category,\n      args: { ...node.callFrame },\n    });\n\n    const startEvents: DurationEvent[] = startNodes\n      .map(createEvent)\n      .map(evt => ({ ...evt, ph: EventsPhase.DURATION_EVENTS_BEGIN }));\n    const endEvents: DurationEvent[] = endNodes\n      .map(createEvent)\n      .map(evt => ({ ...evt, ph: EventsPhase.DURATION_EVENTS_END }));\n    return [...endEvents.reverse(), ...startEvents];\n  }\n\n  /**\n   * Creates B/E-style trace events from a CpuProfile object created by `collectProfileEvents()`\n   * @return {DurationEvent}\n   * @throws If the length of timeDeltas array or the samples array does not match with the length of samples in Hermes Profile\n   */\n  createStartEndEvents(): DurationEvent[] {\n    const profile = this._profile;\n    const length = profile.samples.length;\n    if (\n      profile.timeDeltas.length !== length ||\n      profile.samples.length !== length\n    )\n      throw new Error(`Invalid CPU profile length`);\n\n    const events: DurationEvent[] = [];\n\n    let timestamp = profile.startTime;\n    let lastActiveNodeIds: number[] = [];\n    for (let i = 0; i < profile.samples.length; i++) {\n      const nodeId = profile.samples[i];\n      const timeDelta = Math.max(profile.timeDeltas[i], 0);\n      const node = this._nodesById.get(nodeId);\n      if (!node) throw new Error(`Missing node ${nodeId}`);\n\n      timestamp += timeDelta;\n      const activeNodeIds = this._getActiveNodeIds(nodeId);\n      events.push(\n        ...this._createStartEndEventsForTransition(\n          timestamp,\n          lastActiveNodeIds,\n          activeNodeIds\n        )\n      );\n      lastActiveNodeIds = activeNodeIds;\n    }\n\n    events.push(\n      ...this._createStartEndEventsForTransition(\n        timestamp,\n        lastActiveNodeIds,\n        []\n      )\n    );\n\n    return events;\n  }\n\n  /**\n   * Creates B/E-style trace events from a CpuProfile object created by `collectProfileEvents()`\n   * @param {CPUProfileChunk} profile\n   */\n  static createStartEndEvents(profile: CPUProfileChunk) {\n    const model = new CpuProfilerModel(profile);\n    return model.createStartEndEvents();\n  }\n\n  /**\n   * Converts the Hermes Sample into a single CpuProfileChunk object for consumption\n   * by `createStartEndEvents()`.\n   *\n   * @param {HermesCPUProfile} profile\n   * @throws Profile must have at least one sample\n   * @return {CPUProfileChunk}\n   */\n  static collectProfileEvents(profile: HermesCPUProfile): CPUProfileChunk {\n    if (profile.samples.length >= 0) {\n      const { samples, stackFrames } = profile;\n      // Assumption: The sample will have a single process\n      const pid: number = samples[0].pid;\n      // Assumption: Javascript is single threaded, so there should only be one thread throughout\n      const tid: string = samples[0].tid;\n      // TODO: What role does id play in string parsing\n      const id: string = '0x1';\n      const startTime: number = Number(samples[0].ts);\n      const { nodes, sampleNumbers, timeDeltas } = this.constructNodes(\n        samples,\n        stackFrames\n      );\n      return {\n        id,\n        pid,\n        tid,\n        startTime,\n        nodes,\n        samples: sampleNumbers,\n        timeDeltas,\n      };\n    } else {\n      throw new Error('The hermes profile has zero samples');\n    }\n  }\n\n  /**\n   * Constructs CPUProfileChunk Nodes and the resultant samples and time deltas to be inputted into the\n   * CPUProfileChunk object which will be processed to give createStartEndEvents()\n   *\n   * @param {HermesSample} samples\n   * @param {<string, HermesStackFrame>} stackFrames\n   * @return {CPUProfileChunker}\n   */\n  static constructNodes(\n    samples: HermesSample[],\n    stackFrames: { [key in string]: HermesStackFrame }\n  ): CPUProfileChunker {\n    samples = samples.map((sample: HermesSample) => {\n      sample.stackFrameData = stackFrames[sample.sf];\n      return sample;\n    });\n    const stackFrameIds: string[] = Object.keys(stackFrames);\n    const profileNodes: CPUProfileChunkNode[] = stackFrameIds.map(\n      (stackFrameId: string) => {\n        const stackFrame = stackFrames[stackFrameId];\n        return {\n          id: Number(stackFrameId),\n          callFrame: {\n            ...stackFrame,\n            url: stackFrame.name,\n          },\n          parent: stackFrames[stackFrameId].parent,\n        };\n      }\n    );\n    const returnedSamples: number[] = [];\n    const timeDeltas: number[] = [];\n    let lastTimeStamp = Number(samples[0].ts);\n    samples.forEach((sample: HermesSample, idx: number) => {\n      returnedSamples.push(sample.sf);\n      if (idx === 0) {\n        timeDeltas.push(0);\n      } else {\n        const timeDiff = Number(sample.ts) - lastTimeStamp;\n        lastTimeStamp = Number(sample.ts);\n        timeDeltas.push(timeDiff);\n      }\n    });\n\n    return {\n      nodes: profileNodes,\n      sampleNumbers: returnedSamples,\n      timeDeltas,\n    };\n  }\n}\n", "// A type of promise-like that resolves synchronously and supports only one observer\nexport const _Pact = /*#__PURE__*/(function() {\n\tfunction _Pact() {}\n\t_Pact.prototype.then = function(onFulfilled, onRejected) {\n\t\tconst result = new _Pact();\n\t\tconst state = this.s;\n\t\tif (state) {\n\t\t\tconst callback = state & 1 ? onFulfilled : onRejected;\n\t\t\tif (callback) {\n\t\t\t\ttry {\n\t\t\t\t\t_settle(result, 1, callback(this.v));\n\t\t\t\t} catch (e) {\n\t\t\t\t\t_settle(result, 2, e);\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t} else {\n\t\t\t\treturn this;\n\t\t\t}\n\t\t}\n\t\tthis.o = function(_this) {\n\t\t\ttry {\n\t\t\t\tconst value = _this.v;\n\t\t\t\tif (_this.s & 1) {\n\t\t\t\t\t_settle(result, 1, onFulfilled ? onFulfilled(value) : value);\n\t\t\t\t} else if (onRejected) {\n\t\t\t\t\t_settle(result, 1, onRejected(value));\n\t\t\t\t} else {\n\t\t\t\t\t_settle(result, 2, value);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t_settle(result, 2, e);\n\t\t\t}\n\t\t};\n\t\treturn result;\n\t}\n\treturn _Pact;\n})();\n\n// Settles a pact synchronously\nexport function _settle(pact, state, value) {\n\tif (!pact.s) {\n\t\tif (value instanceof _Pact) {\n\t\t\tif (value.s) {\n\t\t\t\tif (state & 1) {\n\t\t\t\t\tstate = value.s;\n\t\t\t\t}\n\t\t\t\tvalue = value.v;\n\t\t\t} else {\n\t\t\t\tvalue.o = _settle.bind(null, pact, state);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\tif (value && value.then) {\n\t\t\tvalue.then(_settle.bind(null, pact, state), _settle.bind(null, pact, 2));\n\t\t\treturn;\n\t\t}\n\t\tpact.s = state;\n\t\tpact.v = value;\n\t\tconst observer = pact.o;\n\t\tif (observer) {\n\t\t\tobserver(pact);\n\t\t}\n\t}\n}\n\nexport function _isSettledPact(thenable) {\n\treturn thenable instanceof _Pact && thenable.s & 1;\n}\n\n// Converts argument to a function that always returns a Promise\nexport function _async(f) {\n\treturn function() {\n\t\tfor (var args = [], i = 0; i < arguments.length; i++) {\n\t\t\targs[i] = arguments[i];\n\t\t}\n\t\ttry {\n\t\t\treturn Promise.resolve(f.apply(this, args));\n\t\t} catch(e) {\n\t\t\treturn Promise.reject(e);\n\t\t}\n\t}\n}\n\n// Awaits on a value that may or may not be a Promise (equivalent to the await keyword in ES2015, with continuations passed explicitly)\nexport function _await(value, then, direct) {\n\tif (direct) {\n\t\treturn then ? then(value) : value;\n\t}\n\tif (!value || !value.then) {\n\t\tvalue = Promise.resolve(value);\n\t}\n\treturn then ? value.then(then) : value;\n}\n\n// Awaits on a value that may or may not be a Promise, then ignores it\nexport function _awaitIgnored(value, direct) {\n\tif (!direct) {\n\t\treturn value && value.then ? value.then(_empty) : Promise.resolve();\n\t}\n}\n\n// Proceeds after a value has resolved, or proceeds immediately if the value is not thenable\nexport function _continue(value, then) {\n\treturn value && value.then ? value.then(then) : then(value);\n}\n\n// Proceeds after a value has resolved, or proceeds immediately if the value is not thenable\nexport function _continueIgnored(value) {\n\tif (value && value.then) {\n\t\treturn value.then(_empty);\n\t}\n}\n\n// Asynchronously iterate through an object that has a length property, passing the index as the first argument to the callback (even as the length property changes)\nexport function _forTo(array, body, check) {\n\tvar i = -1, pact, reject;\n\tfunction _cycle(result) {\n\t\ttry {\n\t\t\twhile (++i < array.length && (!check || !check())) {\n\t\t\t\tresult = body(i);\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.then(_cycle, reject || (reject = _settle.bind(null, pact = new _Pact(), 2)));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (pact) {\n\t\t\t\t_settle(pact, 1, result);\n\t\t\t} else {\n\t\t\t\tpact = result;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\t_settle(pact || (pact = new _Pact()), 2, e);\n\t\t}\n\t}\n\t_cycle();\n\treturn pact;\n}\n\n// Asynchronously iterate through an object's properties (including properties inherited from the prototype)\n// Uses a snapshot of the object's properties\nexport function _forIn(target, body, check) {\n\tvar keys = [];\n\tfor (var key in target) {\n\t\tkeys.push(key);\n\t}\n\treturn _forTo(keys, function(i) { return body(keys[i]); }, check);\n}\n\n// Asynchronously iterate through an object's own properties (excluding properties inherited from the prototype)\n// Uses a snapshot of the object's properties\nexport function _forOwn(target, body, check) {\n\tvar keys = [];\n\tfor (var key in target) {\n\t\tif (Object.prototype.hasOwnProperty.call(target, key)) {\n\t\t\tkeys.push(key);\n\t\t}\n\t}\n\treturn _forTo(keys, function(i) { return body(keys[i]); }, check);\n}\n\nexport const _iteratorSymbol = /*#__PURE__*/ typeof Symbol !== \"undefined\" ? (Symbol.iterator || (Symbol.iterator = Symbol(\"Symbol.iterator\"))) : \"@@iterator\";\n\n// Asynchronously iterate through an object's values\n// Uses for...of if the runtime supports it, otherwise iterates until length on a copy\nexport function _forOf(target, body, check) {\n\tif (typeof target[_iteratorSymbol] === \"function\") {\n\t\tvar iterator = target[_iteratorSymbol](), step, pact, reject;\n\t\tfunction _cycle(result) {\n\t\t\ttry {\n\t\t\t\twhile (!(step = iterator.next()).done && (!check || !check())) {\n\t\t\t\t\tresult = body(step.value);\n\t\t\t\t\tif (result && result.then) {\n\t\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresult.then(_cycle, reject || (reject = _settle.bind(null, pact = new _Pact(), 2)));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (pact) {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t} else {\n\t\t\t\t\tpact = result;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t_settle(pact || (pact = new _Pact()), 2, e);\n\t\t\t}\n\t\t}\n\t\t_cycle();\n\t\tif (iterator.return) {\n\t\t\tvar _fixup = function(value) {\n\t\t\t\ttry {\n\t\t\t\t\tif (!step.done) {\n\t\t\t\t\t\titerator.return();\n\t\t\t\t\t}\n\t\t\t\t} catch(e) {\n\t\t\t\t}\n\t\t\t\treturn value;\n\t\t\t}\n\t\t\tif (pact && pact.then) {\n\t\t\t\treturn pact.then(_fixup, function(e) {\n\t\t\t\t\tthrow _fixup(e);\n\t\t\t\t});\n\t\t\t}\n\t\t\t_fixup();\n\t\t}\n\t\treturn pact;\n\t}\n\t// No support for Symbol.iterator\n\tif (!(\"length\" in target)) {\n\t\tthrow new TypeError(\"Object is not iterable\");\n\t}\n\t// Handle live collections properly\n\tvar values = [];\n\tfor (var i = 0; i < target.length; i++) {\n\t\tvalues.push(target[i]);\n\t}\n\treturn _forTo(values, function(i) { return body(values[i]); }, check);\n}\n\nexport const _asyncIteratorSymbol = /*#__PURE__*/ typeof Symbol !== \"undefined\" ? (Symbol.asyncIterator || (Symbol.asyncIterator = Symbol(\"Symbol.asyncIterator\"))) : \"@@asyncIterator\";\n\n// Asynchronously iterate on a value using it's async iterator if present, or its synchronous iterator if missing\nexport function _forAwaitOf(target, body, check) {\n\tif (typeof target[_asyncIteratorSymbol] === \"function\") {\n\t\tvar pact = new _Pact();\n\t\tvar iterator = target[_asyncIteratorSymbol]();\n\t\titerator.next().then(_resumeAfterNext).then(void 0, _reject);\n\t\treturn pact;\n\t\tfunction _resumeAfterBody(result) {\n\t\t\tif (check && check()) {\n\t\t\t\treturn _settle(pact, 1, iterator.return ? iterator.return().then(function() { return result; }) : result);\n\t\t\t}\n\t\t\titerator.next().then(_resumeAfterNext).then(void 0, _reject);\n\t\t}\n\t\tfunction _resumeAfterNext(step) {\n\t\t\tif (step.done) {\n\t\t\t\t_settle(pact, 1);\n\t\t\t} else {\n\t\t\t\tPromise.resolve(body(step.value)).then(_resumeAfterBody).then(void 0, _reject);\n\t\t\t}\n\t\t}\n\t\tfunction _reject(error) {\n\t\t\t_settle(pact, 2, iterator.return ? iterator.return().then(function() { return error; }) : error);\n\t\t}\n\t}\n\treturn Promise.resolve(_forOf(target, function(value) { return Promise.resolve(value).then(body); }, check));\n}\n\n// Asynchronously implement a generic for loop\nexport function _for(test, update, body) {\n\tvar stage;\n\tfor (;;) {\n\t\tvar shouldContinue = test();\n\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\tshouldContinue = shouldContinue.v;\n\t\t}\n\t\tif (!shouldContinue) {\n\t\t\treturn result;\n\t\t}\n\t\tif (shouldContinue.then) {\n\t\t\tstage = 0;\n\t\t\tbreak;\n\t\t}\n\t\tvar result = body();\n\t\tif (result && result.then) {\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.s;\n\t\t\t} else {\n\t\t\t\tstage = 1;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (update) {\n\t\t\tvar updateValue = update();\n\t\t\tif (updateValue && updateValue.then && !_isSettledPact(updateValue)) {\n\t\t\t\tstage = 2;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\tvar pact = new _Pact();\n\tvar reject = _settle.bind(null, pact, 2);\n\t(stage === 0 ? shouldContinue.then(_resumeAfterTest) : stage === 1 ? result.then(_resumeAfterBody) : updateValue.then(_resumeAfterUpdate)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterBody(value) {\n\t\tresult = value;\n\t\tdo {\n\t\t\tif (update) {\n\t\t\t\tupdateValue = update();\n\t\t\t\tif (updateValue && updateValue.then && !_isSettledPact(updateValue)) {\n\t\t\t\t\tupdateValue.then(_resumeAfterUpdate).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\tshouldContinue = test();\n\t\t\tif (!shouldContinue || (_isSettledPact(shouldContinue) && !shouldContinue.v)) {\n\t\t\t\t_settle(pact, 1, result);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.v;\n\t\t\t}\n\t\t} while (!result || !result.then);\n\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t}\n\tfunction _resumeAfterTest(shouldContinue) {\n\t\tif (shouldContinue) {\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t} else {\n\t\t\t\t_resumeAfterBody(result);\n\t\t\t}\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n\tfunction _resumeAfterUpdate() {\n\t\tif (shouldContinue = test()) {\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t} else {\n\t\t\t\t_resumeAfterTest(shouldContinue);\n\t\t\t}\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n}\n\n// Asynchronously implement a do ... while loop\nexport function _do(body, test) {\n\tvar awaitBody;\n\tdo {\n\t\tvar result = body();\n\t\tif (result && result.then) {\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.v;\n\t\t\t} else {\n\t\t\t\tawaitBody = true;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tvar shouldContinue = test();\n\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\tshouldContinue = shouldContinue.v;\n\t\t}\n\t\tif (!shouldContinue) {\n\t\t\treturn result;\n\t\t}\n\t} while (!shouldContinue.then);\n\tconst pact = new _Pact();\n\tconst reject = _settle.bind(null, pact, 2);\n\t(awaitBody ? result.then(_resumeAfterBody) : shouldContinue.then(_resumeAfterTest)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterBody(value) {\n\t\tresult = value;\n\t\tfor (;;) {\n\t\t\tshouldContinue = test();\n\t\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\t\tshouldContinue = shouldContinue.v;\n\t\t\t}\n\t\t\tif (!shouldContinue) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\tresult = result.v;\n\t\t\t\t} else {\n\t\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t_settle(pact, 1, result);\n\t}\n\tfunction _resumeAfterTest(shouldContinue) {\n\t\tif (shouldContinue) {\n\t\t\tdo {\n\t\t\t\tresult = body();\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tshouldContinue = test();\n\t\t\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\t\t\tshouldContinue = shouldContinue.v;\n\t\t\t\t}\n\t\t\t\tif (!shouldContinue) {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} while (!shouldContinue.then);\n\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n}\n\n// Asynchronously implement a switch statement\nexport function _switch(discriminant, cases) {\n\tvar dispatchIndex = -1;\n\tvar awaitBody;\n\touter: {\n\t\tfor (var i = 0; i < cases.length; i++) {\n\t\t\tvar test = cases[i][0];\n\t\t\tif (test) {\n\t\t\t\tvar testValue = test();\n\t\t\t\tif (testValue && testValue.then) {\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t\tif (testValue === discriminant) {\n\t\t\t\t\tdispatchIndex = i;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Found the default case, set it as the pending dispatch case\n\t\t\t\tdispatchIndex = i;\n\t\t\t}\n\t\t}\n\t\tif (dispatchIndex !== -1) {\n\t\t\tdo {\n\t\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\t\twhile (!body) {\n\t\t\t\t\tdispatchIndex++;\n\t\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t\t}\n\t\t\t\tvar result = body();\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tawaitBody = true;\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\t\tdispatchIndex++;\n\t\t\t} while (fallthroughCheck && !fallthroughCheck());\n\t\t\treturn result;\n\t\t}\n\t}\n\tconst pact = new _Pact();\n\tconst reject = _settle.bind(null, pact, 2);\n\t(awaitBody ? result.then(_resumeAfterBody) : testValue.then(_resumeAfterTest)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterTest(value) {\n\t\tfor (;;) {\n\t\t\tif (value === discriminant) {\n\t\t\t\tdispatchIndex = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif (++i === cases.length) {\n\t\t\t\tif (dispatchIndex !== -1) {\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\ttest = cases[i][0];\n\t\t\tif (test) {\n\t\t\t\tvalue = test();\n\t\t\t\tif (value && value.then) {\n\t\t\t\t\tvalue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tdispatchIndex = i;\n\t\t\t}\n\t\t}\n\t\tdo {\n\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\twhile (!body) {\n\t\t\t\tdispatchIndex++;\n\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t}\n\t\t\tvar result = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\tdispatchIndex++;\n\t\t} while (fallthroughCheck && !fallthroughCheck());\n\t\t_settle(pact, 1, result);\n\t}\n\tfunction _resumeAfterBody(result) {\n\t\tfor (;;) {\n\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\tif (!fallthroughCheck || fallthroughCheck()) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tdispatchIndex++;\n\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\twhile (!body) {\n\t\t\t\tdispatchIndex++;\n\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\t_settle(pact, 1, result);\n\t}\n}\n\n// Asynchronously call a function and pass the result to explicitly passed continuations\nexport function _call(body, then, direct) {\n\tif (direct) {\n\t\treturn then ? then(body()) : body();\n\t}\n\ttry {\n\t\tvar result = Promise.resolve(body());\n\t\treturn then ? result.then(then) : result;\n\t} catch (e) {\n\t\treturn Promise.reject(e);\n\t}\n}\n\n// Asynchronously call a function and swallow the result\nexport function _callIgnored(body, direct) {\n\treturn _call(body, _empty, direct);\n}\n\n// Asynchronously call a function and pass the result to explicitly passed continuations\nexport function _invoke(body, then) {\n\tvar result = body();\n\tif (result && result.then) {\n\t\treturn result.then(then);\n\t}\n\treturn then(result);\n}\n\n// Asynchronously call a function and swallow the result\nexport function _invokeIgnored(body) {\n\tvar result = body();\n\tif (result && result.then) {\n\t\treturn result.then(_empty);\n\t}\n}\n\n// Asynchronously call a function and send errors to recovery continuation\nexport function _catch(body, recover) {\n\ttry {\n\t\tvar result = body();\n\t} catch(e) {\n\t\treturn recover(e);\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(void 0, recover);\n\t}\n\treturn result;\n}\n\n// Asynchronously await a promise and pass the result to a finally continuation\nexport function _finallyRethrows(body, finalizer) {\n\ttry {\n\t\tvar result = body();\n\t} catch (e) {\n\t\treturn finalizer(true, e);\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(finalizer.bind(null, false), finalizer.bind(null, true));\n\t}\n\treturn finalizer(false, result);\n}\n\n// Asynchronously await a promise and invoke a finally continuation that always overrides the result\nexport function _finally(body, finalizer) {\n\ttry {\n\t\tvar result = body();\n\t} catch (e) {\n\t\treturn finalizer();\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(finalizer, finalizer);\n\t}\n\treturn finalizer();\n}\n\n// Rethrow or return a value from a finally continuation\nexport function _rethrow(thrown, value) {\n\tif (thrown)\n\t\tthrow value;\n\treturn value;\n}\n\n// Empty function to implement break and other control flow that ignores asynchronous results\nexport function _empty() {\n}\n\n// Sentinel value for early returns in generators \nexport const _earlyReturn = /*#__PURE__*/ {};\n\n// Asynchronously call a function and send errors to recovery continuation, skipping early returns\nexport function _catchInGenerator(body, recover) {\n\treturn _catch(body, function(e) {\n\t\tif (e === _earlyReturn) {\n\t\t\tthrow e;\n\t\t}\n\t\treturn recover(e);\n\t});\n}\n\n// Asynchronous generator class; accepts the entrypoint of the generator, to which it passes itself when the generator should start\nexport const _AsyncGenerator = /*#__PURE__*/(function() {\n\tfunction _AsyncGenerator(entry) {\n\t\tthis._entry = entry;\n\t\tthis._pact = null;\n\t\tthis._resolve = null;\n\t\tthis._return = null;\n\t\tthis._promise = null;\n\t}\n\n\tfunction _wrapReturnedValue(value) {\n\t\treturn { value: value, done: true };\n\t}\n\tfunction _wrapYieldedValue(value) {\n\t\treturn { value: value, done: false };\n\t}\n\n\t_AsyncGenerator.prototype._yield = function(value) {\n\t\t// Yield the value to the pending next call\n\t\tthis._resolve(value && value.then ? value.then(_wrapYieldedValue) : _wrapYieldedValue(value));\n\t\t// Return a pact for an upcoming next/return/throw call\n\t\treturn this._pact = new _Pact();\n\t};\n\t_AsyncGenerator.prototype.next = function(value) {\n\t\t// Advance the generator, starting it if it has yet to be started\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tconst _entry = _this._entry;\n\t\t\t\tif (_entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the next call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Start the generator\n\t\t\t\t_this._entry = null;\n\t\t\t\t_this._resolve = resolve;\n\t\t\t\tfunction returnValue(value) {\n\t\t\t\t\t_this._resolve(value && value.then ? value.then(_wrapReturnedValue) : _wrapReturnedValue(value));\n\t\t\t\t\t_this._pact = null;\n\t\t\t\t\t_this._resolve = null;\n\t\t\t\t}\n\t\t\t\tvar result = _entry(_this);\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tresult.then(returnValue, function(error) {\n\t\t\t\t\t\tif (error === _earlyReturn) {\n\t\t\t\t\t\t\treturnValue(_this._return);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst pact = new _Pact();\n\t\t\t\t\t\t\t_this._resolve(pact);\n\t\t\t\t\t\t\t_this._pact = null;\n\t\t\t\t\t\t\t_this._resolve = null;\n\t\t\t\t\t\t\t_resolve(pact, 2, error);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\treturnValue(result);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Generator is started and a yield expression is pending, settle it\n\t\t\t\t_this._pact = null;\n\t\t\t\t_this._resolve = resolve;\n\t\t\t\t_settle(_pact, 1, value);\n\t\t\t}\n\t\t});\n\t};\n\t_AsyncGenerator.prototype.return = function(value) {\n\t\t// Early return from the generator if started, otherwise abandons the generator\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tif (_this._entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the return call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Generator is not started, abandon it and return the specified value\n\t\t\t\t_this._entry = null;\n\t\t\t\treturn resolve(value && value.then ? value.then(_wrapReturnedValue) : _wrapReturnedValue(value));\n\t\t\t}\n\t\t\t// Settle the yield expression with a rejected \"early return\" value\n\t\t\t_this._return = value;\n\t\t\t_this._resolve = resolve;\n\t\t\t_this._pact = null;\n\t\t\t_settle(_pact, 2, _earlyReturn);\n\t\t});\n\t};\n\t_AsyncGenerator.prototype.throw = function(error) {\n\t\t// Inject an exception into the pending yield expression\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve, reject) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tif (_this._entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the throw call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Generator is not started, abandon it and return a rejected Promise containing the error\n\t\t\t\t_this._entry = null;\n\t\t\t\treturn reject(error);\n\t\t\t}\n\t\t\t// Settle the yield expression with the value as a rejection\n\t\t\t_this._resolve = resolve;\n\t\t\t_this._pact = null;\n\t\t\t_settle(_pact, 2, error);\n\t\t});\n\t};\n\n\t_AsyncGenerator.prototype[_asyncIteratorSymbol] = function() {\n\t\treturn this;\n\t};\n\t\n\treturn _AsyncGenerator;\n})();\n", "import { readFile } from 'fs';\nimport { promisify } from 'util';\n\nexport const readFileAsync = async (path: string): Promise<any> => {\n  try {\n    const readFileAsync = promisify(readFile);\n    const fileString: string = (await readFileAsync(path, 'utf-8')) as string;\n    if (fileString.length === 0) {\n      throw new Error(`${path} is an empty file`);\n    }\n    const obj = JSON.parse(fileString);\n    return obj;\n  } catch (err) {\n    throw err;\n  }\n};\n", "import path from 'path';\nimport { SourceMapConsumer, RawSourceMap } from 'source-map';\nimport { DurationEvent } from '../types/EventInterfaces';\nimport { SourceMap } from '../types/SourceMap';\n\n/**\n * This function is a helper to the applySourceMapsToEvents. The category allocation logic is implemented here based on the sourcemap url (if available)\n * @param defaultCategory The category the event is of by default without the use of Source maps\n * @param url The URL which can be parsed to interpret the new category of the event (depends on node_modules)\n */\nconst improveCategories = (\n  defaultCategory: string,\n  url: string | null\n): string => {\n  const obtainCategory = (url: string): string => {\n    const dirs = url\n      .substring(url.lastIndexOf(`${path.sep}node_modules${path.sep}`))\n      .split(path.sep);\n    return dirs.length > 2 && dirs[1] === 'node_modules'\n      ? dirs[2]\n      : defaultCategory;\n  };\n  return url ? obtainCategory(url) : defaultCategory;\n};\n\n/**\n * Enhances the function line, column and params information and event categories\n * based on JavaScript source maps to make it easier to associate trace events with\n * the application code\n *\n * Throws error if args not set up in ChromeEvents\n * @param {SourceMap} sourceMap\n * @param {DurationEvent[]} chromeEvents\n * @param {string} indexBundleFileName\n * @throws If `args` for events are not populated\n * @returns {DurationEvent[]}\n */\nconst applySourceMapsToEvents = async (\n  sourceMap: SourceMap,\n  chromeEvents: DurationEvent[],\n  indexBundleFileName: string | undefined\n): Promise<DurationEvent[]> => {\n  // SEE: Should file here be an optional parameter, so take indexBundleFileName as a parameter and use\n  // a default name of `index.bundle`\n  const rawSourceMap: RawSourceMap = {\n    version: Number(sourceMap.version),\n    file: indexBundleFileName || 'index.bundle',\n    sources: sourceMap.sources,\n    mappings: sourceMap.mappings,\n    names: sourceMap.names,\n  };\n\n  const consumer = await new SourceMapConsumer(rawSourceMap);\n  const events = chromeEvents.map((event: DurationEvent) => {\n    if (event.args) {\n      const sm = consumer.originalPositionFor({\n        line: Number(event.args.line),\n        column: Number(event.args.column),\n      });\n      /**\n       * The categories can help us better visualise the profile if we modify the categories.\n       * We change these categories only in the root level and not deeper inside the args, just so we have our\n       * original categories as well as these modified categories (as the modified categories simply help with visualisation)\n       */\n      event.cat = improveCategories(event.cat!, sm.source);\n      event.args = {\n        ...event.args,\n        url: sm.source,\n        line: sm.line,\n        column: sm.column,\n        params: sm.name,\n        allocatedCategory: event.cat,\n        allocatedName: event.name,\n      };\n    } else {\n      throw new Error(\n        `Source maps could not be derived for an event at ${event.ts} and with stackFrame ID ${event.sf}`\n      );\n    }\n    return event;\n  });\n  consumer.destroy();\n  return events;\n};\n\nexport default applySourceMapsToEvents;\n", "import { CpuProfilerModel } from './profiler/cpuProfilerModel';\nimport { DurationEvent } from './types/EventInterfaces';\nimport { readFileAsync } from './utils/fileSystem';\nimport { HermesCPUProfile } from './types/HermesProfile';\nimport applySourceMapsToEvents from './profiler/applySourceMapsToEvents';\nimport { SourceMap } from './types/SourceMap';\n\n/**\n * This transformer can take in the path of the profile, the source map (optional) and the bundle file name (optional)\n * and return a promise which resolves to Chrome Dev Tools compatible events\n * @param profilePath string\n * @param sourceMapPath string\n * @param bundleFileName string\n * @return Promise<DurationEvent[]>\n */\nconst transformer = async (\n  profilePath: string,\n  sourceMapPath: string | undefined,\n  bundleFileName: string | undefined\n): Promise<DurationEvent[]> => {\n  const hermesProfile: HermesCPUProfile = await readFileAsync(profilePath);\n  const profileChunk = CpuProfilerModel.collectProfileEvents(hermesProfile);\n  const profiler = new CpuProfilerModel(profileChunk);\n  const chromeEvents = profiler.createStartEndEvents();\n  if (sourceMapPath) {\n    const sourceMap: SourceMap = await readFileAsync(sourceMapPath);\n    const events = applySourceMapsToEvents(\n      sourceMap,\n      chromeEvents,\n      bundleFileName\n    );\n    return events;\n  }\n  return chromeEvents;\n};\n\nexport default transformer;\nexport { SourceMap } from './types/SourceMap';\n"], "names": ["EventsPhase", "CpuProfilerModel", "profile", "_profile", "_nodesById", "_createNodeMap", "_activeNodeArraysById", "_createActiveNodeArrays", "map", "Map", "nodes", "node", "set", "id", "getActiveNodes", "has", "get", "Error", "parent", "array", "concat", "_getActiveNodeIds", "nodeId", "activeNodeIds", "_createStartEndEventsForTransition", "timestamp", "previousNodeIds", "currentNodeIds", "startNodes", "filter", "includes", "endNodes", "removeLinksIfExist", "name", "substring", "lastIndexOf", "createEvent", "ts", "pid", "tid", "Number", "ph", "DURATION_EVENTS_BEGIN", "callFrame", "cat", "category", "args", "startEvents", "evt", "endEvents", "DURATION_EVENTS_END", "reverse", "createStartEndEvents", "length", "samples", "timeDeltas", "events", "startTime", "lastActiveNodeIds", "i", "<PERSON><PERSON><PERSON><PERSON>", "Math", "max", "push", "model", "collectProfileEvents", "stackFrames", "constructNodes", "sampleNumbers", "sample", "stackFrameData", "sf", "stackFrameIds", "Object", "keys", "profileNodes", "stackFrameId", "stackFrame", "url", "returnedSamples", "lastTimeStamp", "for<PERSON>ach", "idx", "timeDiff", "readFileAsync", "path", "promisify", "readFile", "fileString", "obj", "JSON", "parse", "err", "improveCategories", "defaultCategory", "obtainCategory", "dirs", "sep", "split", "applySourceMapsToEvents", "sourceMap", "chromeEvents", "indexBundleFileName", "rawSourceMap", "version", "file", "sources", "mappings", "names", "SourceMapConsumer", "consumer", "event", "sm", "originalPositionFor", "line", "column", "source", "params", "allocatedCategory", "allocatedName", "destroy", "transformer", "profilePath", "sourceMapPath", "bundleFileName", "hermesProfile", "profileChunk", "profiler"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAYA,WAAZ;;AAAA,WAAYA;AACVA,EAAAA,oCAAA,MAAA;AACAA,EAAAA,kCAAA,MAAA;AACAA,EAAAA,8BAAA,MAAA;AACAA,EAAAA,6BAAA,MAAA;AACAA,EAAAA,6BAAA,MAAA;AACAA,EAAAA,0CAAA,MAAA;AACAA,EAAAA,4CAAA,MAAA;AACAA,EAAAA,wCAAA,MAAA;AACAA,EAAAA,gCAAA,MAAA;AACAA,EAAAA,+BAAA,MAAA;AACAA,EAAAA,8BAAA,MAAA;AACAA,EAAAA,4BAAA,MAAA;AACAA,EAAAA,oCAAA,MAAA;AACAA,EAAAA,qCAAA,MAAA;AACAA,EAAAA,sCAAA,MAAA;AACAA,EAAAA,8BAAA,MAAA;AACAA,EAAAA,wCAAA,MAAA;AACAA,EAAAA,yCAAA,MAAA;AACAA,EAAAA,0BAAA,MAAA;AACAA,EAAAA,gCAAA,MAAA;AACAA,EAAAA,mCAAA,MAAA;AACAA,EAAAA,mCAAA,MAAA;;AAEAA,EAAAA,iCAAA,MAAA;AACAA,EAAAA,qCAAA,MAAA;AACAA,EAAAA,qCAAA,MAAA;AACAA,EAAAA,+BAAA,MAAA;AACAA,EAAAA,+BAAA,MAAA;AACD,CA7BD,EAAYA,WAAW,KAAXA,WAAW,KAAA,CAAvB;;ICiDaC,gBAAb;AAKE,4BAAYC,OAAZ;AACE,SAAKC,QAAL,GAAgBD,OAAhB;AACA,SAAKE,UAAL,GAAkB,KAAKC,cAAL,EAAlB;AACA,SAAKC,qBAAL,GAA6B,KAAKC,uBAAL,EAA7B;AACD;AAED;;;;;;AAXF;;AAAA,SAeEF,cAfF,GAeE;AACE;AACA,QAAMG,GAAG,GAAqC,IAAIC,GAAJ,EAA9C;;AAIA,yDAAmB,KAAKN,QAAL,CAAcO,KAAjC,wCAAwC;AAAA,UAA7BC,IAA6B;AACtCH,MAAAA,GAAG,CAACI,GAAJ,CAAQD,IAAI,CAACE,EAAb,EAAiBF,IAAjB;AACD;;AAED,WAAOH,GAAP;AACD;AAED;;;;AA5BF;;AAAA,SAgCED,uBAhCF,GAgCE;;;AACE,QAAMC,GAAG,GAA0B,IAAIC,GAAJ,EAAnC;AAEA;;;;;AAIA,QAAMK,cAAc,GAAG,SAAjBA,cAAiB,CAACD,EAAD;AACrB,UAAIL,GAAG,CAACO,GAAJ,CAAQF,EAAR,CAAJ,EAAiB,OAAOL,GAAG,CAACQ,GAAJ,CAAQH,EAAR,KAAe,EAAtB;;AAEjB,UAAMF,IAAI,GAAG,KAAI,CAACP,UAAL,CAAgBY,GAAhB,CAAoBH,EAApB,CAAb;;AACA,UAAI,CAACF,IAAL,EAAW,MAAM,IAAIM,KAAJ,mBAA0BJ,EAA1B,CAAN;;AACX,UAAIF,IAAI,CAACO,MAAT,EAAiB;AACf,YAAMC,KAAK,GAAGL,cAAc,CAACH,IAAI,CAACO,MAAN,CAAd,CAA4BE,MAA5B,CAAmC,CAACP,EAAD,CAAnC,CAAd;AACAL,QAAAA,GAAG,CAACI,GAAJ,CAAQC,EAAR,EAAYM,KAAZ;AACA,eAAOA,KAAP;AACD,OAJD,MAIO;AACL,eAAO,CAACN,EAAD,CAAP;AACD;AACF,KAZD;;AAcA,0DAAmB,KAAKV,QAAL,CAAcO,KAAjC,2CAAwC;AAAA,UAA7BC,IAA6B;AACtCH,MAAAA,GAAG,CAACI,GAAJ,CAAQD,IAAI,CAACE,EAAb,EAAiBC,cAAc,CAACH,IAAI,CAACE,EAAN,CAA/B;AACD;;AACD,WAAOL,GAAP;AACD;AAED;;;;AA3DF;;AAAA,SA+DEa,iBA/DF,GA+DE,2BAAkBC,MAAlB;AACE,QAAMC,aAAa,GAAG,KAAKjB,qBAAL,CAA2BU,GAA3B,CAA+BM,MAA/B,CAAtB;;AACA,QAAI,CAACC,aAAL,EAAoB,MAAM,IAAIN,KAAJ,sBAA6BK,MAA7B,CAAN;AACpB,WAAOC,aAAP;AACD;AAED;;;;;;;;;;;;;;;;;AArEF;;AAAA,SAsFEC,kCAtFF,GAsFE,4CACEC,SADF,EAEEC,eAFF,EAGEC,cAHF;;;AAKE;AACA,QAAMC,UAAU,GAA0BD,cAAc,CACrDE,MADuC,CAChC,UAAAhB,EAAE;AAAA,aAAI,CAACa,eAAe,CAACI,QAAhB,CAAyBjB,EAAzB,CAAL;AAAA,KAD8B,EAEvCL,GAFuC,CAEnC,UAAAK,EAAE;AAAA,aAAI,MAAI,CAACT,UAAL,CAAgBY,GAAhB,CAAoBH,EAApB,CAAJ;AAAA,KAFiC,CAA1C;;AAIA,QAAMkB,QAAQ,GAA0BL,eAAe,CACpDG,MADqC,CAC9B,UAAAhB,EAAE;AAAA,aAAI,CAACc,cAAc,CAACG,QAAf,CAAwBjB,EAAxB,CAAL;AAAA,KAD4B,EAErCL,GAFqC,CAEjC,UAAAK,EAAE;AAAA,aAAI,MAAI,CAACT,UAAL,CAAgBY,GAAhB,CAAoBH,EAApB,CAAJ;AAAA,KAF+B,CAAxC;AAIA;;;;;AAIA,QAAMmB,kBAAkB,GAAG,SAArBA,kBAAqB,CAACC,IAAD;AACzB;AACA,UAAIA,IAAI,CAACH,QAAL,CAAc,SAAd,CAAJ,EAA8B;AAC5BG,QAAAA,IAAI,GAAGA,IAAI,CAACC,SAAL,CAAe,CAAf,EAAkBD,IAAI,CAACE,WAAL,CAAiB,GAAjB,CAAlB,CAAP;AACD;;AACD,aAAOF,IAAI,IAAI,WAAf;AACD,KAND;AAQA;;;;;;AAIA,QAAMG,WAAW,GAAG,SAAdA,WAAc,CAACzB,IAAD;AAAA,aAA+C;AACjE0B,QAAAA,EAAE,EAAEZ,SAD6D;AAEjEa,QAAAA,GAAG,EAAE,MAAI,CAACnC,QAAL,CAAcmC,GAF8C;AAGjEC,QAAAA,GAAG,EAAEC,MAAM,CAAC,MAAI,CAACrC,QAAL,CAAcoC,GAAf,CAHsD;AAIjEE,QAAAA,EAAE,EAAEzC,WAAW,CAAC0C,qBAJiD;AAKjET,QAAAA,IAAI,EAAED,kBAAkB,CAACrB,IAAI,CAACgC,SAAL,CAAeV,IAAhB,CALyC;AAMjEW,QAAAA,GAAG,EAAEjC,IAAI,CAACgC,SAAL,CAAeE,QAN6C;AAOjEC,QAAAA,IAAI,eAAOnC,IAAI,CAACgC,SAAZ;AAP6D,OAA/C;AAAA,KAApB;;AAUA,QAAMI,WAAW,GAAoBnB,UAAU,CAC5CpB,GADkC,CAC9B4B,WAD8B,EAElC5B,GAFkC,CAE9B,UAAAwC,GAAG;AAAA,0BAAUA,GAAV;AAAeP,QAAAA,EAAE,EAAEzC,WAAW,CAAC0C;AAA/B;AAAA,KAF2B,CAArC;AAGA,QAAMO,SAAS,GAAoBlB,QAAQ,CACxCvB,GADgC,CAC5B4B,WAD4B,EAEhC5B,GAFgC,CAE5B,UAAAwC,GAAG;AAAA,0BAAUA,GAAV;AAAeP,QAAAA,EAAE,EAAEzC,WAAW,CAACkD;AAA/B;AAAA,KAFyB,CAAnC;AAGA,qBAAWD,SAAS,CAACE,OAAV,EAAX,EAAmCJ,WAAnC;AACD;AAED;;;;;AAvIF;;AAAA,SA4IEK,oBA5IF,GA4IE;AACE,QAAMlD,OAAO,GAAG,KAAKC,QAArB;AACA,QAAMkD,MAAM,GAAGnD,OAAO,CAACoD,OAAR,CAAgBD,MAA/B;AACA,QACEnD,OAAO,CAACqD,UAAR,CAAmBF,MAAnB,KAA8BA,MAA9B,IACAnD,OAAO,CAACoD,OAAR,CAAgBD,MAAhB,KAA2BA,MAF7B,EAIE,MAAM,IAAIpC,KAAJ,8BAAN;AAEF,QAAMuC,MAAM,GAAoB,EAAhC;AAEA,QAAI/B,SAAS,GAAGvB,OAAO,CAACuD,SAAxB;AACA,QAAIC,iBAAiB,GAAa,EAAlC;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzD,OAAO,CAACoD,OAAR,CAAgBD,MAApC,EAA4CM,CAAC,EAA7C,EAAiD;AAC/C,UAAMrC,MAAM,GAAGpB,OAAO,CAACoD,OAAR,CAAgBK,CAAhB,CAAf;AACA,UAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAAS5D,OAAO,CAACqD,UAAR,CAAmBI,CAAnB,CAAT,EAAgC,CAAhC,CAAlB;;AACA,UAAMhD,IAAI,GAAG,KAAKP,UAAL,CAAgBY,GAAhB,CAAoBM,MAApB,CAAb;;AACA,UAAI,CAACX,IAAL,EAAW,MAAM,IAAIM,KAAJ,mBAA0BK,MAA1B,CAAN;AAEXG,MAAAA,SAAS,IAAImC,SAAb;;AACA,UAAMrC,aAAa,GAAG,KAAKF,iBAAL,CAAuBC,MAAvB,CAAtB;;AACAkC,MAAAA,MAAM,CAACO,IAAP,OAAAP,MAAM,EACD,KAAKhC,kCAAL,CACDC,SADC,EAEDiC,iBAFC,EAGDnC,aAHC,CADC,CAAN;AAOAmC,MAAAA,iBAAiB,GAAGnC,aAApB;AACD;;AAEDiC,IAAAA,MAAM,CAACO,IAAP,OAAAP,MAAM,EACD,KAAKhC,kCAAL,CACDC,SADC,EAEDiC,iBAFC,EAGD,EAHC,CADC,CAAN;AAQA,WAAOF,MAAP;AACD;AAED;;;;AAtLF;;AAAA,mBA0LSJ,oBA1LT,GA0LE,8BAA4BlD,OAA5B;AACE,QAAM8D,KAAK,GAAG,IAAI/D,gBAAJ,CAAqBC,OAArB,CAAd;AACA,WAAO8D,KAAK,CAACZ,oBAAN,EAAP;AACD;AAED;;;;;;;;AA/LF;;AAAA,mBAuMSa,oBAvMT,GAuME,8BAA4B/D,OAA5B;AACE,QAAIA,OAAO,CAACoD,OAAR,CAAgBD,MAAhB,IAA0B,CAA9B,EAAiC;AAAA,UACvBC,OADuB,GACEpD,OADF,CACvBoD,OADuB;AAAA,UACdY,WADc,GACEhE,OADF,CACdgE,WADc;;AAG/B,UAAM5B,GAAG,GAAWgB,OAAO,CAAC,CAAD,CAAP,CAAWhB,GAA/B,CAH+B;;AAK/B,UAAMC,GAAG,GAAWe,OAAO,CAAC,CAAD,CAAP,CAAWf,GAA/B,CAL+B;;AAO/B,UAAM1B,EAAE,GAAW,KAAnB;AACA,UAAM4C,SAAS,GAAWjB,MAAM,CAACc,OAAO,CAAC,CAAD,CAAP,CAAWjB,EAAZ,CAAhC;;AAR+B,iCASc,KAAK8B,cAAL,CAC3Cb,OAD2C,EAE3CY,WAF2C,CATd;AAAA,UASvBxD,KATuB,wBASvBA,KATuB;AAAA,UAShB0D,aATgB,wBAShBA,aATgB;AAAA,UASDb,UATC,wBASDA,UATC;;AAa/B,aAAO;AACL1C,QAAAA,EAAE,EAAFA,EADK;AAELyB,QAAAA,GAAG,EAAHA,GAFK;AAGLC,QAAAA,GAAG,EAAHA,GAHK;AAILkB,QAAAA,SAAS,EAATA,SAJK;AAKL/C,QAAAA,KAAK,EAALA,KALK;AAML4C,QAAAA,OAAO,EAAEc,aANJ;AAOLb,QAAAA,UAAU,EAAVA;AAPK,OAAP;AASD,KAtBD,MAsBO;AACL,YAAM,IAAItC,KAAJ,CAAU,qCAAV,CAAN;AACD;AACF;AAED;;;;;;;;AAnOF;;AAAA,mBA2OSkD,cA3OT,GA2OE,wBACEb,OADF,EAEEY,WAFF;AAIEZ,IAAAA,OAAO,GAAGA,OAAO,CAAC9C,GAAR,CAAY,UAAC6D,MAAD;AACpBA,MAAAA,MAAM,CAACC,cAAP,GAAwBJ,WAAW,CAACG,MAAM,CAACE,EAAR,CAAnC;AACA,aAAOF,MAAP;AACD,KAHS,CAAV;AAIA,QAAMG,aAAa,GAAaC,MAAM,CAACC,IAAP,CAAYR,WAAZ,CAAhC;AACA,QAAMS,YAAY,GAA0BH,aAAa,CAAChE,GAAd,CAC1C,UAACoE,YAAD;AACE,UAAMC,UAAU,GAAGX,WAAW,CAACU,YAAD,CAA9B;AACA,aAAO;AACL/D,QAAAA,EAAE,EAAE2B,MAAM,CAACoC,YAAD,CADL;AAELjC,QAAAA,SAAS,eACJkC,UADI;AAEPC,UAAAA,GAAG,EAAED,UAAU,CAAC5C;AAFT,UAFJ;AAMLf,QAAAA,MAAM,EAAEgD,WAAW,CAACU,YAAD,CAAX,CAA0B1D;AAN7B,OAAP;AAQD,KAXyC,CAA5C;AAaA,QAAM6D,eAAe,GAAa,EAAlC;AACA,QAAMxB,UAAU,GAAa,EAA7B;AACA,QAAIyB,aAAa,GAAGxC,MAAM,CAACc,OAAO,CAAC,CAAD,CAAP,CAAWjB,EAAZ,CAA1B;AACAiB,IAAAA,OAAO,CAAC2B,OAAR,CAAgB,UAACZ,MAAD,EAAuBa,GAAvB;AACdH,MAAAA,eAAe,CAAChB,IAAhB,CAAqBM,MAAM,CAACE,EAA5B;;AACA,UAAIW,GAAG,KAAK,CAAZ,EAAe;AACb3B,QAAAA,UAAU,CAACQ,IAAX,CAAgB,CAAhB;AACD,OAFD,MAEO;AACL,YAAMoB,QAAQ,GAAG3C,MAAM,CAAC6B,MAAM,CAAChC,EAAR,CAAN,GAAoB2C,aAArC;AACAA,QAAAA,aAAa,GAAGxC,MAAM,CAAC6B,MAAM,CAAChC,EAAR,CAAtB;AACAkB,QAAAA,UAAU,CAACQ,IAAX,CAAgBoB,QAAhB;AACD;AACF,KATD;AAWA,WAAO;AACLzE,MAAAA,KAAK,EAAEiE,YADF;AAELP,MAAAA,aAAa,EAAEW,eAFV;AAGLxB,MAAAA,UAAU,EAAVA;AAHK,KAAP;AAKD,GApRH;;AAAA;AAAA;;ACjDA;AACA,AAkKA;AACA,AAAO,MAAM,eAAe,iBAAiB,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC,IAAI,YAAY,CAAC;AAC/J,AA2DA;AACA,AAAO,MAAM,oBAAoB,iBAAiB,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,iBAAiB,CAAC;AACxL,AA8UA;AACA;AACA,AAAO,SAAS,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE;AACtC,CAAC,IAAI;AACL,EAAE,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC;AACtB,EAAE,CAAC,MAAM,CAAC,EAAE;AACZ,EAAE,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE;AACF,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE;AAC5B,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;AACtC,EAAE;AACF,CAAC,OAAO,MAAM,CAAC;AACf,CAAC;;ACzjBM,IAAM6B,aAAa,YAAbA,aAAa,CAAUC,IAAV;AAAA;8CACpB;AACF,UAAMD,aAAa,GAAGE,cAAS,CAACC,WAAD,CAA/B;AADE,6BAEgCH,aAAa,CAACC,IAAD,EAAO,OAAP,CAF7C,iBAEIG,UAFJ;AAGF,YAAIA,UAAU,CAACnC,MAAX,KAAsB,CAA1B,EAA6B;AAC3B,gBAAM,IAAIpC,KAAJ,CAAaoE,IAAb,uBAAN;AACD;;AACD,YAAMI,GAAG,GAAGC,IAAI,CAACC,KAAL,CAAWH,UAAX,CAAZ;AACA,eAAOC,GAAP;AAPE;AAQH,iBAAQG,KAAK;AACZ,YAAMA,GAAN;AACD;AACF,GAZyB;AAAA;AAAA;AAAA,CAAnB;;ACEP;;;;;;AAKA,IAAMC,iBAAiB,GAAG,SAApBA,iBAAoB,CACxBC,eADwB,EAExBhB,GAFwB;AAIxB,MAAMiB,cAAc,GAAG,SAAjBA,cAAiB,CAACjB,GAAD;AACrB,QAAMkB,IAAI,GAAGlB,GAAG,CACb5C,SADU,CACA4C,GAAG,CAAC3C,WAAJ,CAAmBkD,IAAI,CAACY,GAAxB,oBAA0CZ,IAAI,CAACY,GAA/C,CADA,EAEVC,KAFU,CAEJb,IAAI,CAACY,GAFD,CAAb;AAGA,WAAOD,IAAI,CAAC3C,MAAL,GAAc,CAAd,IAAmB2C,IAAI,CAAC,CAAD,CAAJ,KAAY,cAA/B,GACHA,IAAI,CAAC,CAAD,CADD,GAEHF,eAFJ;AAGD,GAPD;;AAQA,SAAOhB,GAAG,GAAGiB,cAAc,CAACjB,GAAD,CAAjB,GAAyBgB,eAAnC;AACD,CAbD;AAeA;;;;;;;;;;;;;;AAYA,IAAMK,uBAAuB,YAAvBA,uBAAuB,CAC3BC,WAD2B,EAE3BC,YAF2B,EAG3BC,mBAH2B;AAAA;AAK3B;AACA;AACA,QAAMC,YAAY,GAAiB;AACjCC,MAAAA,OAAO,EAAEhE,MAAM,CAAC4D,WAAS,CAACI,OAAX,CADkB;AAEjCC,MAAAA,IAAI,EAAEH,mBAAmB,IAAI,cAFI;AAGjCI,MAAAA,OAAO,EAAEN,WAAS,CAACM,OAHc;AAIjCC,MAAAA,QAAQ,EAAEP,WAAS,CAACO,QAJa;AAKjCC,MAAAA,KAAK,EAAER,WAAS,CAACQ;AALgB,KAAnC;2BAQuB,IAAIC,2BAAJ,CAAsBN,YAAtB,kBAAjBO;AACN,UAAMtD,MAAM,GAAG6C,YAAY,CAAC7F,GAAb,CAAiB,UAACuG,KAAD;AAC9B,YAAIA,KAAK,CAACjE,IAAV,EAAgB;AACd,cAAMkE,EAAE,GAAGF,QAAQ,CAACG,mBAAT,CAA6B;AACtCC,YAAAA,IAAI,EAAE1E,MAAM,CAACuE,KAAK,CAACjE,IAAN,CAAWoE,IAAZ,CAD0B;AAEtCC,YAAAA,MAAM,EAAE3E,MAAM,CAACuE,KAAK,CAACjE,IAAN,CAAWqE,MAAZ;AAFwB,WAA7B,CAAX;AAIA;;;;;;AAKAJ,UAAAA,KAAK,CAACnE,GAAN,GAAYiD,iBAAiB,CAACkB,KAAK,CAACnE,GAAP,EAAaoE,EAAE,CAACI,MAAhB,CAA7B;AACAL,UAAAA,KAAK,CAACjE,IAAN,gBACKiE,KAAK,CAACjE,IADX;AAEEgC,YAAAA,GAAG,EAAEkC,EAAE,CAACI,MAFV;AAGEF,YAAAA,IAAI,EAAEF,EAAE,CAACE,IAHX;AAIEC,YAAAA,MAAM,EAAEH,EAAE,CAACG,MAJb;AAKEE,YAAAA,MAAM,EAAEL,EAAE,CAAC/E,IALb;AAMEqF,YAAAA,iBAAiB,EAAEP,KAAK,CAACnE,GAN3B;AAOE2E,YAAAA,aAAa,EAAER,KAAK,CAAC9E;AAPvB;AASD,SApBD,MAoBO;AACL,gBAAM,IAAIhB,KAAJ,uDACgD8F,KAAK,CAAC1E,EADtD,gCACmF0E,KAAK,CAACxC,EADzF,CAAN;AAGD;;AACD,eAAOwC,KAAP;AACD,OA3Bc,CAAf;AA4BAD,MAAAA,QAAQ,CAACU,OAAT;AACA,aAAOhE,MAAP;;AACD,GA9C4B;AAAA;AAAA;AAAA,CAA7B;;AC9BA;;;;;;;;;AAQA,IAAMiE,WAAW,YAAXA,WAAW,CACfC,WADe,EAEfC,aAFe,EAGfC,cAHe;AAAA;2BAK+BxC,aAAa,CAACsC,WAAD,kBAArDG;;AACN,UAAMC,YAAY,GAAG7H,gBAAgB,CAACgE,oBAAjB,CAAsC4D,aAAtC,CAArB;AACA,UAAME,QAAQ,GAAG,IAAI9H,gBAAJ,CAAqB6H,YAArB,CAAjB;AACA,UAAMzB,YAAY,GAAG0B,QAAQ,CAAC3E,oBAAT,EAArB;;;YACIuE;iCACiCvC,aAAa,CAACuC,aAAD,kBAA1CvB;AACN,gBAAM5C,MAAM,GAAG2C,uBAAuB,CACpCC,SADoC,EAEpCC,YAFoC,EAGpCuB,cAHoC,CAAtC;;mBAKOpE;;;;;;iCAEF6C;2BAAAA;;AACR,GAnBgB;AAAA;AAAA;AAAA,CAAjB;;;;"}