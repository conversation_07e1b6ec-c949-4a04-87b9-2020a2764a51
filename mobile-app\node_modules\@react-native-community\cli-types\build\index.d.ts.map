{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACf,MAAM,OAAO,CAAC;AACf,OAAO,EACL,oBAAoB,EACpB,oBAAoB,EACpB,uBAAuB,EACvB,uBAAuB,EACxB,MAAM,WAAW,CAAC;AAEnB,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC;AAEzB,MAAM,MAAM,eAAe,CAAC,IAAI,GAAG,MAAM,IAAI,CAC3C,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EACnB,GAAG,EAAE,MAAM,EACX,IAAI,EAAE,IAAI,KACP,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAE1B,MAAM,MAAM,WAAW,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,CAAC;AAEpD,MAAM,MAAM,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK,WAAW,IAAI;IAC5D,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,GAAG,CAAC;IAC7B,OAAO,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,uBAAuB,CAAC,IAAI,GAAG,MAAM,IAAI,CACnD,IAAI,EAAE,MAAM,EAAE,EACd,IAAI,EAAE,IAAI,EACV,GAAG,EAAE,MAAM,KACR,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAE1B,MAAM,MAAM,OAAO,CAAC,UAAU,SAAS,OAAO,GAAG,KAAK,IAAI;IACxD,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,UAAU,CAAC;IACtB,QAAQ,CAAC,EAAE,KAAK,CAAC;QACf,IAAI,EAAE,MAAM,CAAC;QACb,GAAG,EAAE,MAAM,CAAC;KACb,CAAC,CAAC;IACH,GAAG,CAAC,EAAE;QACJ,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;IACF,IAAI,EAAE,UAAU,SAAS,IAAI,GACzB,uBAAuB,CAAC,MAAM,CAAC,GAC/B,eAAe,CAAC,MAAM,CAAC,CAAC;IAC5B,OAAO,CAAC,EAAE,KAAK,CACb,aAAa,CACX,UAAU,SAAS,IAAI,GAAG,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK,WAAW,CAC3E,CACF,CAAC;CACH,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE5C,UAAU,cAAc,CACtB,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,gBAAgB;IAEhB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,aAAa,EAAE,CACb,WAAW,EAAE,MAAM,EACnB,aAAa,EAAE,aAAa,GAAG,IAAI,KAChC,aAAa,GAAG,IAAI,CAAC;IAC1B,gBAAgB,EAAE,CAChB,UAAU,EAAE,MAAM,EAClB,MAAM,EAAE,gBAAgB,KACrB,gBAAgB,GAAG,IAAI,CAAC;CAC9B;AAED,KAAK,qBAAqB,GAAG,cAAc,CACzC,oBAAoB,EACpB,oBAAoB,EACpB,uBAAuB,EACvB,uBAAuB,CACxB,CAAC;AAEF,KAAK,iBAAiB,GAAG,cAAc,CACrC,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,CACpB,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG;IAC1B,OAAO,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAC5E,GAAG,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACpE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;CACpB,CAAC;AAEF,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE;QACT,OAAO,CAAC,EAAE,OAAO,CACf,UAAU,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC,EACrD,IAAI,CACL,CAAC;QACF,GAAG,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACvE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;KACpB,CAAC;CACH;AAED,MAAM,WAAW,MAAM;IACrB,IAAI,EAAE,MAAM,CAAC;IACb,eAAe,EAAE,MAAM,CAAC;IACxB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,OAAO,EAAE,aAAa,CAAC;IACvB,YAAY,EAAE;QACZ,CAAC,GAAG,EAAE,MAAM,GAAG,gBAAgB,CAAC;KACjC,CAAC;IACF,SAAS,EAAE;QACT,OAAO,EAAE,qBAAqB,CAAC;QAC/B,GAAG,EAAE,iBAAiB,CAAC;QACvB,CAAC,IAAI,EAAE,MAAM,GAAG,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;KACpD,CAAC;IACF,QAAQ,EAAE,OAAO,EAAE,CAAC;IAEpB,YAAY,EAAE,EAAE,CAAC;CAClB;AAED,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG;IAC9C,eAAe,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B,OAAO,EAAE;QACP,OAAO,CAAC,EAAE,oBAAoB,CAAC;QAC/B,GAAG,CAAC,EAAE,gBAAgB,CAAC;QACvB,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;KACpB,CAAC;CACH,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IAEjC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;IAEpD,QAAQ,EAAE,OAAO,EAAE,CAAC;IAEpB,SAAS,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;IAE/B,YAAY,EAAE,EAAE,CAAC;CAClB,CAAC;AAEF,OAAO,EACL,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,GACf,CAAC;AAEF,OAAO,EACL,oBAAoB,EACpB,oBAAoB,EACpB,uBAAuB,EACvB,uBAAuB,GACxB,CAAC"}