{"version": 3, "names": ["React", "Keyboard", "TextInput", "useKeyboardManager", "isEnabled", "previouslyFocusedTextInputRef", "useRef", "undefined", "startTimestampRef", "keyboardTimeoutRef", "clearKeyboardTimeout", "useCallback", "current", "clearTimeout", "onPageChangeStart", "input", "State", "currentlyFocusedInput", "blur", "Date", "now", "onPageChangeConfirm", "force", "dismiss", "onPageChangeCancel", "setTimeout", "focus", "useEffect"], "sourceRoot": "../../../src", "sources": ["utils/useKeyboardManager.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAwBC,QAAQ,EAAEC,SAAS,QAAQ,cAAc;AAIjE,eAAe,SAASC,kBAAkB,CAACC,SAAwB,EAAE;EACnE;EACA;EACA,MAAMC,6BAA6B,GAAGL,KAAK,CAACM,MAAM,CAAWC,SAAS,CAAC;EACvE,MAAMC,iBAAiB,GAAGR,KAAK,CAACM,MAAM,CAAS,CAAC,CAAC;EACjD,MAAMG,kBAAkB,GAAGT,KAAK,CAACM,MAAM,EAAO;EAE9C,MAAMI,oBAAoB,GAAGV,KAAK,CAACW,WAAW,CAAC,MAAM;IACnD,IAAIF,kBAAkB,CAACG,OAAO,KAAKL,SAAS,EAAE;MAC5CM,YAAY,CAACJ,kBAAkB,CAACG,OAAO,CAAC;MACxCH,kBAAkB,CAACG,OAAO,GAAGL,SAAS;IACxC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,iBAAiB,GAAGd,KAAK,CAACW,WAAW,CAAC,MAAM;IAChD,IAAI,CAACP,SAAS,EAAE,EAAE;MAChB;IACF;IAEAM,oBAAoB,EAAE;IAEtB,MAAMK,KAAe,GAAGb,SAAS,CAACc,KAAK,CAACC,qBAAqB,EAAE;;IAE/D;IACAF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,IAAI,EAAE;;IAEb;IACAb,6BAA6B,CAACO,OAAO,GAAGG,KAAK;;IAE7C;IACAP,iBAAiB,CAACI,OAAO,GAAGO,IAAI,CAACC,GAAG,EAAE;EACxC,CAAC,EAAE,CAACV,oBAAoB,EAAEN,SAAS,CAAC,CAAC;EAErC,MAAMiB,mBAAmB,GAAGrB,KAAK,CAACW,WAAW,CAC1CW,KAAc,IAAK;IAClB,IAAI,CAAClB,SAAS,EAAE,EAAE;MAChB;IACF;IAEAM,oBAAoB,EAAE;IAEtB,IAAIY,KAAK,EAAE;MACT;MACA;MACA;MACArB,QAAQ,CAACsB,OAAO,EAAE;IACpB,CAAC,MAAM;MACL,MAAMR,KAAK,GAAGV,6BAA6B,CAACO,OAAO;;MAEnD;MACA;MACAG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,IAAI,EAAE;IACf;;IAEA;IACAb,6BAA6B,CAACO,OAAO,GAAGL,SAAS;EACnD,CAAC,EACD,CAACG,oBAAoB,EAAEN,SAAS,CAAC,CAClC;EAED,MAAMoB,kBAAkB,GAAGxB,KAAK,CAACW,WAAW,CAAC,MAAM;IACjD,IAAI,CAACP,SAAS,EAAE,EAAE;MAChB;IACF;IAEAM,oBAAoB,EAAE;;IAEtB;IACA,MAAMK,KAAK,GAAGV,6BAA6B,CAACO,OAAO;IAEnD,IAAIG,KAAK,EAAE;MACT;;MAEA;MACA;MACA;MACA;MACA;MACA,IAAII,IAAI,CAACC,GAAG,EAAE,GAAGZ,iBAAiB,CAACI,OAAO,GAAG,GAAG,EAAE;QAChDH,kBAAkB,CAACG,OAAO,GAAGa,UAAU,CAAC,MAAM;UAC5CV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,KAAK,EAAE;UACdrB,6BAA6B,CAACO,OAAO,GAAGL,SAAS;QACnD,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLQ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,KAAK,EAAE;QACdrB,6BAA6B,CAACO,OAAO,GAAGL,SAAS;MACnD;IACF;EACF,CAAC,EAAE,CAACG,oBAAoB,EAAEN,SAAS,CAAC,CAAC;EAErCJ,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,OAAO,MAAMjB,oBAAoB,EAAE;EACrC,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAE1B,OAAO;IACLI,iBAAiB;IACjBO,mBAAmB;IACnBG;EACF,CAAC;AACH"}