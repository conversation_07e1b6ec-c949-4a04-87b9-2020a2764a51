# is-generator-function <sup>[![Version Badge][2]][1]</sup>

[![github actions][actions-image]][actions-url]
[![coverage][codecov-image]][codecov-url]
[![dependency status][5]][6]
[![dev dependency status][7]][8]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]

[![npm badge][11]][1]

Is this a native generator function?

## Example

```js
var isGeneratorFunction = require('is-generator-function');
assert(!isGeneratorFunction(function () {}));
assert(!isGeneratorFunction(null));
assert(isGeneratorFunction(function* () { yield 42; return Infinity; }));
```

## Tests
Simply clone the repo, `npm install`, and run `npm test`

[1]: https://npmjs.org/package/is-generator-function
[2]: https://versionbadg.es/inspect-js/is-generator-function.svg
[5]: https://david-dm.org/inspect-js/is-generator-function.svg
[6]: https://david-dm.org/inspect-js/is-generator-function
[7]: https://david-dm.org/inspect-js/is-generator-function/dev-status.svg
[8]: https://david-dm.org/inspect-js/is-generator-function#info=devDependencies
[11]: https://nodei.co/npm/is-generator-function.png?downloads=true&stars=true
[license-image]: https://img.shields.io/npm/l/is-generator-function.svg
[license-url]: LICENSE
[downloads-image]: https://img.shields.io/npm/dm/is-generator-function.svg
[downloads-url]: https://npm-stat.com/charts.html?package=is-generator-function
[codecov-image]: https://codecov.io/gh/inspect-js/is-generator-function/branch/main/graphs/badge.svg
[codecov-url]: https://app.codecov.io/gh/inspect-js/is-generator-function/
[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-generator-function
[actions-url]: https://github.com/inspect-js/is-generator-function/actions
