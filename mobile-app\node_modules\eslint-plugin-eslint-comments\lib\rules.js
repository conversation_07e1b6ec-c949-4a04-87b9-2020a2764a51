/** DON'T EDIT THIS FILE; was created by scripts. */
"use strict"

module.exports = {
    "disable-enable-pair": require("./rules/disable-enable-pair"),
    "no-aggregating-enable": require("./rules/no-aggregating-enable"),
    "no-duplicate-disable": require("./rules/no-duplicate-disable"),
    "no-restricted-disable": require("./rules/no-restricted-disable"),
    "no-unlimited-disable": require("./rules/no-unlimited-disable"),
    "no-unused-disable": require("./rules/no-unused-disable"),
    "no-unused-enable": require("./rules/no-unused-enable"),
    "no-use": require("./rules/no-use"),
    "require-description": require("./rules/require-description"),
}
