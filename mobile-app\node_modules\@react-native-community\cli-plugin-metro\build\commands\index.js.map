{"version": 3, "names": ["bundleCommand", "ramBundleCommand", "startCommand"], "sources": ["../../src/commands/index.ts"], "sourcesContent": ["import {bundleCommand, ramBundleCommand} from './bundle';\nimport startCommand from './start';\n\nexport default [bundleCommand, ramBundleCommand, startCommand];\nexport {buildBundleWithConfig} from './bundle';\nexport type {CommandLineArgs} from './bundle';\n"], "mappings": ";;;;;;;;;;;;AAAA;AACA;AAAmC;AAAA,eAEpB,CAACA,qBAAa,EAAEC,wBAAgB,EAAEC,cAAY,CAAC;AAAA"}