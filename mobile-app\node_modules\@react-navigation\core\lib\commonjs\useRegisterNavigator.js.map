{"version": 3, "names": ["useRegisterNavigator", "key", "React", "useState", "nanoid", "container", "useContext", "SingleNavigatorContext", "undefined", "Error", "useEffect", "register", "unregister"], "sourceRoot": "../../src", "sources": ["useRegisterNavigator.tsx"], "mappings": ";;;;;;AAAA;AACA;AAEA;AAAiE;AAAA;AAEjE;AACA;AACA;AACA;AACe,SAASA,oBAAoB,GAAG;EAC7C,MAAM,CAACC,GAAG,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAAC,MAAM,IAAAC,iBAAM,GAAE,CAAC;EAC5C,MAAMC,SAAS,GAAGH,KAAK,CAACI,UAAU,CAACC,6CAAsB,CAAC;EAE1D,IAAIF,SAAS,KAAKG,SAAS,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,wLAAwL,CACzL;EACH;EAEAP,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,MAAM;MAAEC,QAAQ;MAAEC;IAAW,CAAC,GAAGP,SAAS;IAE1CM,QAAQ,CAACV,GAAG,CAAC;IAEb,OAAO,MAAMW,UAAU,CAACX,GAAG,CAAC;EAC9B,CAAC,EAAE,CAACI,SAAS,EAAEJ,GAAG,CAAC,CAAC;EAEpB,OAAOA,GAAG;AACZ"}