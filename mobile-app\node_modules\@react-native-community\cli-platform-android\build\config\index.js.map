{"version": 3, "names": ["projectConfig", "root", "userConfig", "src", "sourceDir", "findAndroidDir", "path", "join", "appName", "getAppName", "manifestPath", "findManifest", "buildGradlePath", "findBuildGradle", "packageName", "getPackageName", "CLIError", "dependencyConfiguration", "unstable_reactLegacyComponentNames", "userConfigAppName", "fs", "existsSync", "dependencyConfig", "packageClassName", "findPackageClassName", "packageImportPath", "packageInstance", "buildTypes", "libraryName", "findLibraryName", "componentDescriptors", "findComponentDescriptors", "cmakeListsPath", "process", "platform", "replace"], "sources": ["../../src/config/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport fs from 'fs';\nimport findAndroidDir from './findAndroidDir';\nimport findManifest from './findManifest';\nimport findPackageClassName from './findPackageClassName';\nimport {\n  AndroidProjectParams,\n  AndroidProjectConfig,\n  AndroidDependencyParams,\n  AndroidDependencyConfig,\n} from '@react-native-community/cli-types';\nimport {getPackageName} from './getAndroidProject';\nimport {findLibraryName} from './findLibraryName';\nimport {findComponentDescriptors} from './findComponentDescriptors';\nimport {findBuildGradle} from './findBuildGradle';\nimport {CLIError} from '@react-native-community/cli-tools';\n\n/**\n * Gets android project config by analyzing given folder and taking some\n * defaults specified by user into consideration\n */\nexport function projectConfig(\n  root: string,\n  userConfig: AndroidProjectParams = {},\n): AndroidProjectConfig | null {\n  const src = userConfig.sourceDir || findAndroidDir(root);\n\n  if (!src) {\n    return null;\n  }\n\n  const sourceDir = path.join(root, src);\n\n  const appName = getAppName(sourceDir, userConfig.appName);\n\n  const manifestPath = userConfig.manifestPath\n    ? path.join(sourceDir, userConfig.manifestPath)\n    : findManifest(path.join(sourceDir, appName));\n  const buildGradlePath = findBuildGradle(sourceDir, false);\n\n  if (!manifestPath && !buildGradlePath) {\n    return null;\n  }\n\n  const packageName =\n    userConfig.packageName || getPackageName(manifestPath, buildGradlePath);\n\n  if (!packageName) {\n    throw new CLIError(\n      `Package name not found in neither ${manifestPath} nor ${buildGradlePath}`,\n    );\n  }\n\n  return {\n    sourceDir,\n    appName,\n    packageName,\n    dependencyConfiguration: userConfig.dependencyConfiguration,\n    unstable_reactLegacyComponentNames:\n      userConfig.unstable_reactLegacyComponentNames,\n  };\n}\n\nfunction getAppName(sourceDir: string, userConfigAppName: string | undefined) {\n  let appName = '';\n  if (\n    typeof userConfigAppName === 'string' &&\n    fs.existsSync(path.join(sourceDir, userConfigAppName))\n  ) {\n    appName = userConfigAppName;\n  } else if (fs.existsSync(path.join(sourceDir, 'app'))) {\n    appName = 'app';\n  }\n  return appName;\n}\n\n/**\n * Same as projectConfigAndroid except it returns\n * different config that applies to packages only\n */\nexport function dependencyConfig(\n  root: string,\n  userConfig: AndroidDependencyParams | null = {},\n): AndroidDependencyConfig | null {\n  if (userConfig === null) {\n    return null;\n  }\n\n  const src = userConfig.sourceDir || findAndroidDir(root);\n\n  if (!src) {\n    return null;\n  }\n\n  const sourceDir = path.join(root, src);\n  const manifestPath = userConfig.manifestPath\n    ? path.join(sourceDir, userConfig.manifestPath)\n    : findManifest(sourceDir);\n  const buildGradlePath = findBuildGradle(sourceDir, true);\n\n  if (!manifestPath && !buildGradlePath) {\n    return null;\n  }\n\n  const packageName =\n    userConfig.packageName || getPackageName(manifestPath, buildGradlePath);\n  const packageClassName = findPackageClassName(sourceDir);\n\n  /**\n   * This module has no package to export\n   */\n  if (!packageClassName) {\n    return null;\n  }\n\n  const packageImportPath =\n    userConfig.packageImportPath ||\n    `import ${packageName}.${packageClassName};`;\n\n  const packageInstance =\n    userConfig.packageInstance || `new ${packageClassName}()`;\n\n  const buildTypes = userConfig.buildTypes || [];\n  const dependencyConfiguration = userConfig.dependencyConfiguration;\n  const libraryName =\n    userConfig.libraryName || findLibraryName(root, sourceDir);\n  const componentDescriptors =\n    userConfig.componentDescriptors || findComponentDescriptors(root);\n  let cmakeListsPath = userConfig.cmakeListsPath\n    ? path.join(sourceDir, userConfig.cmakeListsPath)\n    : path.join(sourceDir, 'build/generated/source/codegen/jni/CMakeLists.txt');\n  if (process.platform === 'win32') {\n    cmakeListsPath = cmakeListsPath.replace(/\\\\/g, '/');\n  }\n  return {\n    sourceDir,\n    packageImportPath,\n    packageInstance,\n    buildTypes,\n    dependencyConfiguration,\n    libraryName,\n    componentDescriptors,\n    cmakeListsPath,\n  };\n}\n"], "mappings": ";;;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA2D;AAvB3D;AACA;AACA;AACA;AACA;AACA;AACA;;AAmBA;AACA;AACA;AACA;AACO,SAASA,aAAa,CAC3BC,IAAY,EACZC,UAAgC,GAAG,CAAC,CAAC,EACR;EAC7B,MAAMC,GAAG,GAAGD,UAAU,CAACE,SAAS,IAAI,IAAAC,uBAAc,EAACJ,IAAI,CAAC;EAExD,IAAI,CAACE,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EAEA,MAAMC,SAAS,GAAGE,eAAI,CAACC,IAAI,CAACN,IAAI,EAAEE,GAAG,CAAC;EAEtC,MAAMK,OAAO,GAAGC,UAAU,CAACL,SAAS,EAAEF,UAAU,CAACM,OAAO,CAAC;EAEzD,MAAME,YAAY,GAAGR,UAAU,CAACQ,YAAY,GACxCJ,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEF,UAAU,CAACQ,YAAY,CAAC,GAC7C,IAAAC,qBAAY,EAACL,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEI,OAAO,CAAC,CAAC;EAC/C,MAAMI,eAAe,GAAG,IAAAC,gCAAe,EAACT,SAAS,EAAE,KAAK,CAAC;EAEzD,IAAI,CAACM,YAAY,IAAI,CAACE,eAAe,EAAE;IACrC,OAAO,IAAI;EACb;EAEA,MAAME,WAAW,GACfZ,UAAU,CAACY,WAAW,IAAI,IAAAC,iCAAc,EAACL,YAAY,EAAEE,eAAe,CAAC;EAEzE,IAAI,CAACE,WAAW,EAAE;IAChB,MAAM,KAAIE,oBAAQ,EACf,qCAAoCN,YAAa,QAAOE,eAAgB,EAAC,CAC3E;EACH;EAEA,OAAO;IACLR,SAAS;IACTI,OAAO;IACPM,WAAW;IACXG,uBAAuB,EAAEf,UAAU,CAACe,uBAAuB;IAC3DC,kCAAkC,EAChChB,UAAU,CAACgB;EACf,CAAC;AACH;AAEA,SAAST,UAAU,CAACL,SAAiB,EAAEe,iBAAqC,EAAE;EAC5E,IAAIX,OAAO,GAAG,EAAE;EAChB,IACE,OAAOW,iBAAiB,KAAK,QAAQ,IACrCC,aAAE,CAACC,UAAU,CAACf,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEe,iBAAiB,CAAC,CAAC,EACtD;IACAX,OAAO,GAAGW,iBAAiB;EAC7B,CAAC,MAAM,IAAIC,aAAE,CAACC,UAAU,CAACf,eAAI,CAACC,IAAI,CAACH,SAAS,EAAE,KAAK,CAAC,CAAC,EAAE;IACrDI,OAAO,GAAG,KAAK;EACjB;EACA,OAAOA,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACO,SAASc,gBAAgB,CAC9BrB,IAAY,EACZC,UAA0C,GAAG,CAAC,CAAC,EACf;EAChC,IAAIA,UAAU,KAAK,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,MAAMC,GAAG,GAAGD,UAAU,CAACE,SAAS,IAAI,IAAAC,uBAAc,EAACJ,IAAI,CAAC;EAExD,IAAI,CAACE,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EAEA,MAAMC,SAAS,GAAGE,eAAI,CAACC,IAAI,CAACN,IAAI,EAAEE,GAAG,CAAC;EACtC,MAAMO,YAAY,GAAGR,UAAU,CAACQ,YAAY,GACxCJ,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEF,UAAU,CAACQ,YAAY,CAAC,GAC7C,IAAAC,qBAAY,EAACP,SAAS,CAAC;EAC3B,MAAMQ,eAAe,GAAG,IAAAC,gCAAe,EAACT,SAAS,EAAE,IAAI,CAAC;EAExD,IAAI,CAACM,YAAY,IAAI,CAACE,eAAe,EAAE;IACrC,OAAO,IAAI;EACb;EAEA,MAAME,WAAW,GACfZ,UAAU,CAACY,WAAW,IAAI,IAAAC,iCAAc,EAACL,YAAY,EAAEE,eAAe,CAAC;EACzE,MAAMW,gBAAgB,GAAG,IAAAC,6BAAoB,EAACpB,SAAS,CAAC;;EAExD;AACF;AACA;EACE,IAAI,CAACmB,gBAAgB,EAAE;IACrB,OAAO,IAAI;EACb;EAEA,MAAME,iBAAiB,GACrBvB,UAAU,CAACuB,iBAAiB,IAC3B,UAASX,WAAY,IAAGS,gBAAiB,GAAE;EAE9C,MAAMG,eAAe,GACnBxB,UAAU,CAACwB,eAAe,IAAK,OAAMH,gBAAiB,IAAG;EAE3D,MAAMI,UAAU,GAAGzB,UAAU,CAACyB,UAAU,IAAI,EAAE;EAC9C,MAAMV,uBAAuB,GAAGf,UAAU,CAACe,uBAAuB;EAClE,MAAMW,WAAW,GACf1B,UAAU,CAAC0B,WAAW,IAAI,IAAAC,gCAAe,EAAC5B,IAAI,EAAEG,SAAS,CAAC;EAC5D,MAAM0B,oBAAoB,GACxB5B,UAAU,CAAC4B,oBAAoB,IAAI,IAAAC,kDAAwB,EAAC9B,IAAI,CAAC;EACnE,IAAI+B,cAAc,GAAG9B,UAAU,CAAC8B,cAAc,GAC1C1B,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEF,UAAU,CAAC8B,cAAc,CAAC,GAC/C1B,eAAI,CAACC,IAAI,CAACH,SAAS,EAAE,mDAAmD,CAAC;EAC7E,IAAI6B,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;IAChCF,cAAc,GAAGA,cAAc,CAACG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EACrD;EACA,OAAO;IACL/B,SAAS;IACTqB,iBAAiB;IACjBC,eAAe;IACfC,UAAU;IACVV,uBAAuB;IACvBW,WAAW;IACXE,oBAAoB;IACpBE;EACF,CAAC;AACH"}