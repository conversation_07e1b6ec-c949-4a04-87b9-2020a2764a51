"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ESLintScopeVariable = void 0;
const VariableBase_1 = require("./VariableBase");
/**
 * ESLint defines global variables using the eslint-scope Variable class
 * This is declared her for consumers to use
 */
class ESLintScopeVariable extends VariableBase_1.VariableBase {
}
exports.ESLintScopeVariable = ESLintScopeVariable;
//# sourceMappingURL=ESLintScopeVariable.js.map