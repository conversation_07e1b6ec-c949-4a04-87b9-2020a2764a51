{"version": 3, "names": ["getTempFilePath", "filename", "path", "join", "os", "tmpdir", "writeJsonSync", "targetPath", "data", "json", "JSON", "stringify", "e", "CLIError", "fs", "writeFileSync", "getSourcemapFromServer", "port", "logger", "debug", "DEBUG_SERVER_PORT", "IP_ADDRESS", "ip", "address", "PLATFORM", "requestURL", "fetch", "undefined", "generateSourcemap", "sourceMapPath", "sourceMapResult", "error", "findSourcemap", "ctx", "intermediateBuildPath", "root", "generatedBuildPath", "existsSync"], "sources": ["../../src/profileHermes/sourcemapUtils.ts"], "sourcesContent": ["import {Config} from '@react-native-community/cli-types';\nimport {logger, CLIError, fetch} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport path from 'path';\nimport os from 'os';\nimport {SourceMap} from 'hermes-profile-transformer';\nimport ip from 'ip';\n\nfunction getTempFilePath(filename: string) {\n  return path.join(os.tmpdir(), filename);\n}\n\nfunction writeJsonSync(targetPath: string, data: any) {\n  let json;\n  try {\n    json = JSON.stringify(data);\n  } catch (e) {\n    throw new CLIError(\n      `Failed to serialize data to json before writing to ${targetPath}`,\n      e as Error,\n    );\n  }\n\n  try {\n    fs.writeFileSync(targetPath, json, 'utf-8');\n  } catch (e) {\n    throw new CLIError(`Failed to write json to ${targetPath}`, e as Error);\n  }\n}\n\nasync function getSourcemapFromServer(\n  port?: string,\n): Promise<SourceMap | undefined> {\n  logger.debug('Getting source maps from Metro packager server');\n  const DEBUG_SERVER_PORT = port || '8081';\n  const IP_ADDRESS = ip.address();\n  const PLATFORM = 'android';\n\n  const requestURL = `http://${IP_ADDRESS}:${DEBUG_SERVER_PORT}/index.map?platform=${PLATFORM}&dev=true`;\n  try {\n    const {data} = await fetch(requestURL);\n    return data as SourceMap;\n  } catch (e) {\n    logger.debug(`Failed to fetch source map from \"${requestURL}\"`);\n    return undefined;\n  }\n}\n\n/**\n * Generate a sourcemap by fetching it from a running metro server\n */\nexport async function generateSourcemap(\n  port?: string,\n): Promise<string | undefined> {\n  // Fetch the source map to a temp directory\n  const sourceMapPath = getTempFilePath('index.map');\n  const sourceMapResult = await getSourcemapFromServer(port);\n\n  if (sourceMapResult) {\n    logger.debug('Using source maps from Metro packager server');\n    writeJsonSync(sourceMapPath, sourceMapResult);\n    logger.debug(\n      `Successfully obtained the source map and stored it in ${sourceMapPath}`,\n    );\n    return sourceMapPath;\n  } else {\n    logger.error('Cannot obtain source maps from Metro packager server');\n    return undefined;\n  }\n}\n\n/**\n *\n * @param ctx\n */\nexport async function findSourcemap(\n  ctx: Config,\n  port?: string,\n): Promise<string | undefined> {\n  const intermediateBuildPath = path.join(\n    ctx.root,\n    'android',\n    'app',\n    'build',\n    'intermediates',\n    'sourcemaps',\n    'react',\n    'debug',\n    'index.android.bundle.packager.map',\n  );\n\n  const generatedBuildPath = path.join(\n    ctx.root,\n    'android',\n    'app',\n    'build',\n    'generated',\n    'sourcemaps',\n    'react',\n    'debug',\n    'index.android.bundle.map',\n  );\n\n  if (fs.existsSync(generatedBuildPath)) {\n    logger.debug(`Getting the source map from ${generateSourcemap}`);\n    return generatedBuildPath;\n  } else if (fs.existsSync(intermediateBuildPath)) {\n    logger.debug(`Getting the source map from ${intermediateBuildPath}`);\n    return intermediateBuildPath;\n  } else {\n    return generateSourcemap(port);\n  }\n}\n"], "mappings": ";;;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAoB;AAEpB,SAASA,eAAe,CAACC,QAAgB,EAAE;EACzC,OAAOC,eAAI,CAACC,IAAI,CAACC,aAAE,CAACC,MAAM,EAAE,EAAEJ,QAAQ,CAAC;AACzC;AAEA,SAASK,aAAa,CAACC,UAAkB,EAAEC,IAAS,EAAE;EACpD,IAAIC,IAAI;EACR,IAAI;IACFA,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;EAC7B,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,MAAM,KAAIC,oBAAQ,EACf,sDAAqDN,UAAW,EAAC,EAClEK,CAAC,CACF;EACH;EAEA,IAAI;IACFE,aAAE,CAACC,aAAa,CAACR,UAAU,EAAEE,IAAI,EAAE,OAAO,CAAC;EAC7C,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,MAAM,KAAIC,oBAAQ,EAAE,2BAA0BN,UAAW,EAAC,EAAEK,CAAC,CAAU;EACzE;AACF;AAEA,eAAeI,sBAAsB,CACnCC,IAAa,EACmB;EAChCC,kBAAM,CAACC,KAAK,CAAC,gDAAgD,CAAC;EAC9D,MAAMC,iBAAiB,GAAGH,IAAI,IAAI,MAAM;EACxC,MAAMI,UAAU,GAAGC,aAAE,CAACC,OAAO,EAAE;EAC/B,MAAMC,QAAQ,GAAG,SAAS;EAE1B,MAAMC,UAAU,GAAI,UAASJ,UAAW,IAAGD,iBAAkB,uBAAsBI,QAAS,WAAU;EACtG,IAAI;IACF,MAAM;MAAChB;IAAI,CAAC,GAAG,MAAM,IAAAkB,iBAAK,EAACD,UAAU,CAAC;IACtC,OAAOjB,IAAI;EACb,CAAC,CAAC,OAAOI,CAAC,EAAE;IACVM,kBAAM,CAACC,KAAK,CAAE,oCAAmCM,UAAW,GAAE,CAAC;IAC/D,OAAOE,SAAS;EAClB;AACF;;AAEA;AACA;AACA;AACO,eAAeC,iBAAiB,CACrCX,IAAa,EACgB;EAC7B;EACA,MAAMY,aAAa,GAAG7B,eAAe,CAAC,WAAW,CAAC;EAClD,MAAM8B,eAAe,GAAG,MAAMd,sBAAsB,CAACC,IAAI,CAAC;EAE1D,IAAIa,eAAe,EAAE;IACnBZ,kBAAM,CAACC,KAAK,CAAC,8CAA8C,CAAC;IAC5Db,aAAa,CAACuB,aAAa,EAAEC,eAAe,CAAC;IAC7CZ,kBAAM,CAACC,KAAK,CACT,yDAAwDU,aAAc,EAAC,CACzE;IACD,OAAOA,aAAa;EACtB,CAAC,MAAM;IACLX,kBAAM,CAACa,KAAK,CAAC,sDAAsD,CAAC;IACpE,OAAOJ,SAAS;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACO,eAAeK,aAAa,CACjCC,GAAW,EACXhB,IAAa,EACgB;EAC7B,MAAMiB,qBAAqB,GAAGhC,eAAI,CAACC,IAAI,CACrC8B,GAAG,CAACE,IAAI,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,eAAe,EACf,YAAY,EACZ,OAAO,EACP,OAAO,EACP,mCAAmC,CACpC;EAED,MAAMC,kBAAkB,GAAGlC,eAAI,CAACC,IAAI,CAClC8B,GAAG,CAACE,IAAI,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,WAAW,EACX,YAAY,EACZ,OAAO,EACP,OAAO,EACP,0BAA0B,CAC3B;EAED,IAAIrB,aAAE,CAACuB,UAAU,CAACD,kBAAkB,CAAC,EAAE;IACrClB,kBAAM,CAACC,KAAK,CAAE,+BAA8BS,iBAAkB,EAAC,CAAC;IAChE,OAAOQ,kBAAkB;EAC3B,CAAC,MAAM,IAAItB,aAAE,CAACuB,UAAU,CAACH,qBAAqB,CAAC,EAAE;IAC/ChB,kBAAM,CAACC,KAAK,CAAE,+BAA8Be,qBAAsB,EAAC,CAAC;IACpE,OAAOA,qBAAqB;EAC9B,CAAC,MAAM;IACL,OAAON,iBAAiB,CAACX,IAAI,CAAC;EAChC;AACF"}