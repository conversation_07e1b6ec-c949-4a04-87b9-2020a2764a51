// Type definitions for use-sync-external-store 0.0
// Project: https://github.com/facebook/react#readme
// Definitions by: eps1lon <https://github.com/eps1lon>
//                 <PERSON> <https://github.com/marker<PERSON>son>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped

export function useSyncExternalStore<Snapshot>(
    subscribe: (onStoreChange: () => void) => () => void,
    getSnapshot: () => Snapshot,
    getServerSnapshot?: () => Snapshot,
): Snapshot;
