{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_api$assumption2", "assertVersion", "ignoreToPrimitiveHint", "assumption", "loose", "mutableTemplateObject", "helper<PERSON><PERSON>", "buildConcatCallExpressions", "items", "avail", "reduce", "left", "right", "canBeInserted", "t", "isLiteral", "isCallExpression", "arguments", "push", "callExpression", "memberExpression", "identifier", "name", "visitor", "TaggedTemplateExpression", "path", "node", "quasi", "strings", "raws", "isStringsRawEqual", "elem", "quasis", "raw", "cooked", "value", "scope", "buildUndefinedNode", "stringLiteral", "helper<PERSON><PERSON><PERSON>", "arrayExpression", "tmp", "generateUidIdentifier", "getProgramParent", "id", "cloneNode", "replaceWith", "tag", "template", "expression", "ast", "addHelper", "expressions", "TemplateLiteral", "parent", "type", "nodes", "get", "index", "length", "expr", "isStringLiteral", "unshift", "root", "i", "binaryExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { template, types as t, type NodePath } from \"@babel/core\";\n\nexport interface Options {\n  loose?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const ignoreToPrimitiveHint =\n    api.assumption(\"ignoreToPrimitiveHint\") ?? options.loose;\n  const mutableTemplateObject =\n    api.assumption(\"mutableTemplateObject\") ?? options.loose;\n\n  let helperName = \"taggedTemplateLiteral\";\n  if (mutableTemplateObject) helperName += \"Loose\";\n\n  /**\n   * This function groups the objects into multiple calls to `.concat()` in\n   * order to preserve execution order of the primitive conversion, e.g.\n   *\n   *   \"\".concat(obj.foo, \"foo\", obj2.foo, \"foo2\")\n   *\n   * would evaluate both member expressions _first_ then, `concat` will\n   * convert each one to a primitive, whereas\n   *\n   *   \"\".concat(obj.foo, \"foo\").concat(obj2.foo, \"foo2\")\n   *\n   * would evaluate the member, then convert it to a primitive, then evaluate\n   * the second member and convert that one, which reflects the spec behavior\n   * of template literals.\n   */\n  function buildConcatCallExpressions(items: t.Expression[]): t.CallExpression {\n    let avail = true;\n    // @ts-expect-error items must not be empty\n    return items.reduce(function (left, right) {\n      let canBeInserted = t.isLiteral(right);\n\n      if (!canBeInserted && avail) {\n        canBeInserted = true;\n        avail = false;\n      }\n      if (canBeInserted && t.isCallExpression(left)) {\n        left.arguments.push(right);\n        return left;\n      }\n      return t.callExpression(\n        t.memberExpression(left, t.identifier(\"concat\")),\n        [right],\n      );\n    });\n  }\n\n  return {\n    name: \"transform-template-literals\",\n\n    visitor: {\n      TaggedTemplateExpression(path) {\n        const { node } = path;\n        const { quasi } = node;\n\n        const strings = [];\n        const raws = [];\n\n        // Flag variable to check if contents of strings and raw are equal\n        let isStringsRawEqual = true;\n\n        for (const elem of quasi.quasis) {\n          const { raw, cooked } = elem.value;\n          const value =\n            cooked == null\n              ? path.scope.buildUndefinedNode()\n              : t.stringLiteral(cooked);\n\n          strings.push(value);\n          raws.push(t.stringLiteral(raw));\n\n          if (raw !== cooked) {\n            // false even if one of raw and cooked are not equal\n            isStringsRawEqual = false;\n          }\n        }\n\n        const helperArgs = [t.arrayExpression(strings)];\n        // only add raw arrayExpression if there is any difference between raws and strings\n        if (!isStringsRawEqual) {\n          helperArgs.push(t.arrayExpression(raws));\n        }\n\n        const tmp = path.scope.generateUidIdentifier(\"templateObject\");\n        path.scope.getProgramParent().push({ id: t.cloneNode(tmp) });\n\n        path.replaceWith(\n          t.callExpression(node.tag, [\n            template.expression.ast`\n              ${t.cloneNode(tmp)} || (\n                ${tmp} = ${this.addHelper(helperName)}(${helperArgs})\n              )\n            `,\n            // @ts-expect-error Fixme: quasi.expressions may contain TSAnyKeyword\n            ...quasi.expressions,\n          ]),\n        );\n      },\n\n      TemplateLiteral(path) {\n        if (path.parent.type === \"TSLiteralType\") {\n          // Skip TemplateLiteral in TSLiteralType\n          return;\n        }\n        const nodes: t.Expression[] = [];\n        const expressions = path.get(\"expressions\") as NodePath<t.Expression>[];\n\n        let index = 0;\n        for (const elem of path.node.quasis) {\n          if (elem.value.cooked) {\n            nodes.push(t.stringLiteral(elem.value.cooked));\n          }\n\n          if (index < expressions.length) {\n            const expr = expressions[index++];\n            const node = expr.node;\n            if (!t.isStringLiteral(node, { value: \"\" })) {\n              nodes.push(node);\n            }\n          }\n        }\n\n        // since `+` is left-to-right associative\n        // ensure the first node is a string if first/second isn't\n        if (\n          !t.isStringLiteral(nodes[0]) &&\n          !(ignoreToPrimitiveHint && t.isStringLiteral(nodes[1]))\n        ) {\n          nodes.unshift(t.stringLiteral(\"\"));\n        }\n        let root = nodes[0];\n\n        if (ignoreToPrimitiveHint) {\n          for (let i = 1; i < nodes.length; i++) {\n            root = t.binaryExpression(\"+\", root, nodes[i]);\n          }\n        } else if (nodes.length > 1) {\n          root = buildConcatCallExpressions(nodes);\n        }\n\n        path.replaceWith(root);\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAkE,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMnD,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EAChDH,GAAG,CAACI,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,qBAAqB,IAAAH,eAAA,GACzBF,GAAG,CAACM,UAAU,CAAC,uBAAuB,CAAC,YAAAJ,eAAA,GAAID,OAAO,CAACM,KAAK;EAC1D,MAAMC,qBAAqB,IAAAL,gBAAA,GACzBH,GAAG,CAACM,UAAU,CAAC,uBAAuB,CAAC,YAAAH,gBAAA,GAAIF,OAAO,CAACM,KAAK;EAE1D,IAAIE,UAAU,GAAG,uBAAuB;EACxC,IAAID,qBAAqB,EAAEC,UAAU,IAAI,OAAO;EAiBhD,SAASC,0BAA0BA,CAACC,KAAqB,EAAoB;IAC3E,IAAIC,KAAK,GAAG,IAAI;IAEhB,OAAOD,KAAK,CAACE,MAAM,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;MACzC,IAAIC,aAAa,GAAGC,WAAC,CAACC,SAAS,CAACH,KAAK,CAAC;MAEtC,IAAI,CAACC,aAAa,IAAIJ,KAAK,EAAE;QAC3BI,aAAa,GAAG,IAAI;QACpBJ,KAAK,GAAG,KAAK;MACf;MACA,IAAII,aAAa,IAAIC,WAAC,CAACE,gBAAgB,CAACL,IAAI,CAAC,EAAE;QAC7CA,IAAI,CAACM,SAAS,CAACC,IAAI,CAACN,KAAK,CAAC;QAC1B,OAAOD,IAAI;MACb;MACA,OAAOG,WAAC,CAACK,cAAc,CACrBL,WAAC,CAACM,gBAAgB,CAACT,IAAI,EAAEG,WAAC,CAACO,UAAU,CAAC,QAAQ,CAAC,CAAC,EAChD,CAACT,KAAK,CACR,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,OAAO;IACLU,IAAI,EAAE,6BAA6B;IAEnCC,OAAO,EAAE;MACPC,wBAAwBA,CAACC,IAAI,EAAE;QAC7B,MAAM;UAAEC;QAAK,CAAC,GAAGD,IAAI;QACrB,MAAM;UAAEE;QAAM,CAAC,GAAGD,IAAI;QAEtB,MAAME,OAAO,GAAG,EAAE;QAClB,MAAMC,IAAI,GAAG,EAAE;QAGf,IAAIC,iBAAiB,GAAG,IAAI;QAE5B,KAAK,MAAMC,IAAI,IAAIJ,KAAK,CAACK,MAAM,EAAE;UAC/B,MAAM;YAAEC,GAAG;YAAEC;UAAO,CAAC,GAAGH,IAAI,CAACI,KAAK;UAClC,MAAMA,KAAK,GACTD,MAAM,IAAI,IAAI,GACVT,IAAI,CAACW,KAAK,CAACC,kBAAkB,CAAC,CAAC,GAC/BvB,WAAC,CAACwB,aAAa,CAACJ,MAAM,CAAC;UAE7BN,OAAO,CAACV,IAAI,CAACiB,KAAK,CAAC;UACnBN,IAAI,CAACX,IAAI,CAACJ,WAAC,CAACwB,aAAa,CAACL,GAAG,CAAC,CAAC;UAE/B,IAAIA,GAAG,KAAKC,MAAM,EAAE;YAElBJ,iBAAiB,GAAG,KAAK;UAC3B;QACF;QAEA,MAAMS,UAAU,GAAG,CAACzB,WAAC,CAAC0B,eAAe,CAACZ,OAAO,CAAC,CAAC;QAE/C,IAAI,CAACE,iBAAiB,EAAE;UACtBS,UAAU,CAACrB,IAAI,CAACJ,WAAC,CAAC0B,eAAe,CAACX,IAAI,CAAC,CAAC;QAC1C;QAEA,MAAMY,GAAG,GAAGhB,IAAI,CAACW,KAAK,CAACM,qBAAqB,CAAC,gBAAgB,CAAC;QAC9DjB,IAAI,CAACW,KAAK,CAACO,gBAAgB,CAAC,CAAC,CAACzB,IAAI,CAAC;UAAE0B,EAAE,EAAE9B,WAAC,CAAC+B,SAAS,CAACJ,GAAG;QAAE,CAAC,CAAC;QAE5DhB,IAAI,CAACqB,WAAW,CACdhC,WAAC,CAACK,cAAc,CAACO,IAAI,CAACqB,GAAG,EAAE,CACzBC,cAAQ,CAACC,UAAU,CAACC,GAAG;AACnC,gBAAgBpC,WAAC,CAAC+B,SAAS,CAACJ,GAAG,CAAC;AAChC,kBAAkBA,GAAG,MAAM,IAAI,CAACU,SAAS,CAAC7C,UAAU,CAAC,IAAIiC,UAAU;AACnE;AACA,aAAa,EAED,GAAGZ,KAAK,CAACyB,WAAW,CACrB,CACH,CAAC;MACH,CAAC;MAEDC,eAAeA,CAAC5B,IAAI,EAAE;QACpB,IAAIA,IAAI,CAAC6B,MAAM,CAACC,IAAI,KAAK,eAAe,EAAE;UAExC;QACF;QACA,MAAMC,KAAqB,GAAG,EAAE;QAChC,MAAMJ,WAAW,GAAG3B,IAAI,CAACgC,GAAG,CAAC,aAAa,CAA6B;QAEvE,IAAIC,KAAK,GAAG,CAAC;QACb,KAAK,MAAM3B,IAAI,IAAIN,IAAI,CAACC,IAAI,CAACM,MAAM,EAAE;UACnC,IAAID,IAAI,CAACI,KAAK,CAACD,MAAM,EAAE;YACrBsB,KAAK,CAACtC,IAAI,CAACJ,WAAC,CAACwB,aAAa,CAACP,IAAI,CAACI,KAAK,CAACD,MAAM,CAAC,CAAC;UAChD;UAEA,IAAIwB,KAAK,GAAGN,WAAW,CAACO,MAAM,EAAE;YAC9B,MAAMC,IAAI,GAAGR,WAAW,CAACM,KAAK,EAAE,CAAC;YACjC,MAAMhC,IAAI,GAAGkC,IAAI,CAAClC,IAAI;YACtB,IAAI,CAACZ,WAAC,CAAC+C,eAAe,CAACnC,IAAI,EAAE;cAAES,KAAK,EAAE;YAAG,CAAC,CAAC,EAAE;cAC3CqB,KAAK,CAACtC,IAAI,CAACQ,IAAI,CAAC;YAClB;UACF;QACF;QAIA,IACE,CAACZ,WAAC,CAAC+C,eAAe,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC,IAC5B,EAAEtD,qBAAqB,IAAIY,WAAC,CAAC+C,eAAe,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EACvD;UACAA,KAAK,CAACM,OAAO,CAAChD,WAAC,CAACwB,aAAa,CAAC,EAAE,CAAC,CAAC;QACpC;QACA,IAAIyB,IAAI,GAAGP,KAAK,CAAC,CAAC,CAAC;QAEnB,IAAItD,qBAAqB,EAAE;UACzB,KAAK,IAAI8D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,CAACG,MAAM,EAAEK,CAAC,EAAE,EAAE;YACrCD,IAAI,GAAGjD,WAAC,CAACmD,gBAAgB,CAAC,GAAG,EAAEF,IAAI,EAAEP,KAAK,CAACQ,CAAC,CAAC,CAAC;UAChD;QACF,CAAC,MAAM,IAAIR,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;UAC3BI,IAAI,GAAGxD,0BAA0B,CAACiD,KAAK,CAAC;QAC1C;QAEA/B,IAAI,CAACqB,WAAW,CAACiB,IAAI,CAAC;MACxB;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}