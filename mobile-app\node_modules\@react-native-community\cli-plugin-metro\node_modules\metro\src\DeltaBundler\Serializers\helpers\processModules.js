/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

"use strict";

const { isJsModule, wrapModule } = require("./js");
function processModules(
  modules,
  {
    filter = () => true,
    createModuleId,
    dev,
    includeAsyncPaths,
    projectRoot,
    serverRoot,
    sourceUrl,
  }
) {
  return [...modules]
    .filter(isJsModule)
    .filter(filter)
    .map((module) => [
      module,
      wrapModule(module, {
        createModuleId,
        dev,
        includeAsyncPaths,
        projectRoot,
        serverRoot,
        sourceUrl,
      }),
    ]);
}
module.exports = processModules;
