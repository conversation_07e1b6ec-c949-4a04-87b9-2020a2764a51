{"version": 3, "names": ["tryLaunchAppOnDevice", "device", "packageName", "adbPath", "args", "appId", "appIdSuffix", "packageNameWithSuffix", "filter", "Boolean", "join", "activityToLaunch", "mainActivity", "includes", "adbArgs", "unshift", "logger", "info", "debug", "execa", "sync", "stdio", "error", "CLIError"], "sources": ["../../../src/commands/runAndroid/tryLaunchAppOnDevice.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport execa from 'execa';\nimport {Flags} from '.';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\n\nfunction tryLaunchAppOnDevice(\n  device: string | void,\n  packageName: string,\n  adbPath: string,\n  args: Flags,\n) {\n  const {appId, appIdSuffix} = args;\n  const packageNameWithSuffix = [appId || packageName, appIdSuffix]\n    .filter(Boolean)\n    .join('.');\n\n  const activityToLaunch = args.mainActivity.includes('.')\n    ? args.mainActivity\n    : [packageName, args.mainActivity].filter(Boolean).join('.');\n\n  try {\n    // Here we're using the same flags as Android Studio to launch the app\n    const adbArgs = [\n      'shell',\n      'am',\n      'start',\n      '-n',\n      `${packageNameWithSuffix}/${activityToLaunch}`,\n      '-a',\n      'android.intent.action.MAIN',\n      '-c',\n      'android.intent.category.LAUNCHER',\n    ];\n\n    if (device) {\n      adbArgs.unshift('-s', device);\n      logger.info(`Starting the app on \"${device}\"...`);\n    } else {\n      logger.info('Starting the app...');\n    }\n    logger.debug(`Running command \"${adbPath} ${adbArgs.join(' ')}\"`);\n    execa.sync(adbPath, adbArgs, {stdio: 'inherit'});\n  } catch (error) {\n    throw new CLIError('Failed to start the app.', error as any);\n  }\n}\n\nexport default tryLaunchAppOnDevice;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAmE;AAVnE;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,SAASA,oBAAoB,CAC3BC,MAAqB,EACrBC,WAAmB,EACnBC,OAAe,EACfC,IAAW,EACX;EACA,MAAM;IAACC,KAAK;IAAEC;EAAW,CAAC,GAAGF,IAAI;EACjC,MAAMG,qBAAqB,GAAG,CAACF,KAAK,IAAIH,WAAW,EAAEI,WAAW,CAAC,CAC9DE,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC;EAEZ,MAAMC,gBAAgB,GAAGP,IAAI,CAACQ,YAAY,CAACC,QAAQ,CAAC,GAAG,CAAC,GACpDT,IAAI,CAACQ,YAAY,GACjB,CAACV,WAAW,EAAEE,IAAI,CAACQ,YAAY,CAAC,CAACJ,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE9D,IAAI;IACF;IACA,MAAMI,OAAO,GAAG,CACd,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACH,GAAEP,qBAAsB,IAAGI,gBAAiB,EAAC,EAC9C,IAAI,EACJ,4BAA4B,EAC5B,IAAI,EACJ,kCAAkC,CACnC;IAED,IAAIV,MAAM,EAAE;MACVa,OAAO,CAACC,OAAO,CAAC,IAAI,EAAEd,MAAM,CAAC;MAC7Be,kBAAM,CAACC,IAAI,CAAE,wBAAuBhB,MAAO,MAAK,CAAC;IACnD,CAAC,MAAM;MACLe,kBAAM,CAACC,IAAI,CAAC,qBAAqB,CAAC;IACpC;IACAD,kBAAM,CAACE,KAAK,CAAE,oBAAmBf,OAAQ,IAAGW,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAE,GAAE,CAAC;IACjES,gBAAK,CAACC,IAAI,CAACjB,OAAO,EAAEW,OAAO,EAAE;MAACO,KAAK,EAAE;IAAS,CAAC,CAAC;EAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAM,KAAIC,oBAAQ,EAAC,0BAA0B,EAAED,KAAK,CAAQ;EAC9D;AACF;AAAC,eAEctB,oBAAoB;AAAA"}