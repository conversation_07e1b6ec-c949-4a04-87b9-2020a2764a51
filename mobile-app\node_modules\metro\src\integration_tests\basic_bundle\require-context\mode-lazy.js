"use strict";

var _utils = require("./utils");
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 *
 */

function main() {
  return (0, _utils.awaitProperties)(
    (0, _utils.copyContextToObject)(
      require.context("./subdir", undefined, undefined, "lazy")
    )
  );
}
module.exports = main();
