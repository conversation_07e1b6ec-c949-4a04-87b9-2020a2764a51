# Installation
> `npm install --save @types/yargs`

# Summary
This package contains type definitions for yargs (https://github.com/chevex/yargs).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs/v16.

### Additional Details
 * Last updated: Mon, 20 Nov 2023 23:36:24 GMT
 * Dependencies: [@types/yargs-parser](https://npmjs.com/package/@types/yargs-parser)

# Credits
These definitions were written by [<PERSON>](https://github.com/poelstra), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/mizunashi-mana), [<PERSON><PERSON>](https://github.com/pushplay), [<PERSON><PERSON> (<PERSON><PERSON>) <PERSON>id<PERSON>](https://github.com/JimiC), [<PERSON><PERSON><PERSON> Viken Valvåg](https://github.com/steffenvv), [<PERSON>](https://github.com/forivall), [<PERSON>E Boss](https://github.com/ExE-Boss), and [<PERSON><PERSON><PERSON><PERSON>](https://github.com/Aankhen).
