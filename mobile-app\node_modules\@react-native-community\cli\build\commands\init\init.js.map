{"version": 3, "names": ["DEFAULT_VERSION", "doesDirectoryExist", "dir", "fs", "existsSync", "setProjectDirectory", "directory", "DirectoryAlreadyExistsError", "mkdirSync", "recursive", "process", "chdir", "error", "CLIError", "cwd", "getTemplateName", "name", "Object", "keys", "JSON", "parse", "readFileSync", "path", "join", "dependencies", "createFromTemplate", "projectName", "templateUri", "npm", "projectTitle", "<PERSON><PERSON><PERSON><PERSON>", "packageName", "logger", "debug", "log", "banner", "projectDirectory", "loader", "<PERSON><PERSON><PERSON><PERSON>", "text", "templateSourceDir", "mkdtempSync", "os", "tmpdir", "start", "installTemplatePackage", "succeed", "templateName", "templateConfig", "getTemplateConfig", "copyTemplate", "templateDir", "changePlaceholderInTemplate", "placeholder<PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON>", "titlePlaceholder", "postInitScript", "info", "executePostInitScript", "installDependencies", "root", "e", "fail", "removeSync", "PackageManager", "installAll", "<PERSON><PERSON><PERSON><PERSON>", "silent", "platform", "installPods", "createTemplateUri", "options", "version", "isTypescriptTemplate", "template", "warn", "createProject", "title", "initialize", "validateProjectName", "TemplateAndVersionError", "directoryName", "relative", "projectFolder", "printRunInstructions"], "sources": ["../../../src/commands/init/init.ts"], "sourcesContent": ["import os from 'os';\nimport path from 'path';\nimport fs from 'fs-extra';\nimport {validateProjectName} from './validate';\nimport DirectoryAlreadyExistsError from './errors/DirectoryAlreadyExistsError';\nimport printRunInstructions from './printRunInstructions';\nimport {\n  CLIError,\n  logger,\n  getLoader,\n  Loader,\n} from '@react-native-community/cli-tools';\nimport {\n  installTemplatePackage,\n  getTemplateConfig,\n  copyTemplate,\n  executePostInitScript,\n} from './template';\nimport {changePlaceholderInTemplate} from './editTemplate';\nimport * as PackageManager from '../../tools/packageManager';\nimport {installPods} from '@react-native-community/cli-doctor';\nimport banner from './banner';\nimport TemplateAndVersionError from './errors/TemplateAndVersionError';\n\nconst DEFAULT_VERSION = 'latest';\n\ntype Options = {\n  template?: string;\n  npm?: boolean;\n  directory?: string;\n  displayName?: string;\n  title?: string;\n  skipInstall?: boolean;\n  version?: string;\n  packageName?: string;\n};\n\ninterface TemplateOptions {\n  projectName: string;\n  templateUri: string;\n  npm?: boolean;\n  directory: string;\n  projectTitle?: string;\n  skipInstall?: boolean;\n  packageName?: string;\n}\n\nfunction doesDirectoryExist(dir: string) {\n  return fs.existsSync(dir);\n}\n\nasync function setProjectDirectory(directory: string) {\n  if (doesDirectoryExist(directory)) {\n    throw new DirectoryAlreadyExistsError(directory);\n  }\n\n  try {\n    fs.mkdirSync(directory, {recursive: true});\n    process.chdir(directory);\n  } catch (error) {\n    throw new CLIError(\n      'Error occurred while trying to create project directory.',\n      error as Error,\n    );\n  }\n\n  return process.cwd();\n}\n\nfunction getTemplateName(cwd: string) {\n  // We use package manager to infer the name of the template module for us.\n  // That's why we get it from temporary package.json, where the name is the\n  // first and only dependency (hence 0).\n  const name = Object.keys(\n    JSON.parse(fs.readFileSync(path.join(cwd, './package.json'), 'utf8'))\n      .dependencies,\n  )[0];\n  return name;\n}\n\nasync function createFromTemplate({\n  projectName,\n  templateUri,\n  npm,\n  directory,\n  projectTitle,\n  skipInstall,\n  packageName,\n}: TemplateOptions) {\n  logger.debug('Initializing new project');\n  logger.log(banner);\n\n  const projectDirectory = await setProjectDirectory(directory);\n\n  const loader = getLoader({text: 'Downloading template'});\n  const templateSourceDir = fs.mkdtempSync(\n    path.join(os.tmpdir(), 'rncli-init-template-'),\n  );\n\n  try {\n    loader.start();\n\n    await installTemplatePackage(templateUri, templateSourceDir, npm);\n\n    loader.succeed();\n    loader.start('Copying template');\n\n    const templateName = getTemplateName(templateSourceDir);\n    const templateConfig = getTemplateConfig(templateName, templateSourceDir);\n    await copyTemplate(\n      templateName,\n      templateConfig.templateDir,\n      templateSourceDir,\n    );\n\n    loader.succeed();\n    loader.start('Processing template');\n\n    await changePlaceholderInTemplate({\n      projectName,\n      projectTitle,\n      placeholderName: templateConfig.placeholderName,\n      placeholderTitle: templateConfig.titlePlaceholder,\n      packageName,\n    });\n\n    loader.succeed();\n    const {postInitScript} = templateConfig;\n    if (postInitScript) {\n      loader.info('Executing post init script ');\n      await executePostInitScript(\n        templateName,\n        postInitScript,\n        templateSourceDir,\n      );\n    }\n\n    if (!skipInstall) {\n      await installDependencies({\n        npm,\n        loader,\n        root: projectDirectory,\n        directory,\n      });\n    } else {\n      loader.succeed('Dependencies installation skipped');\n    }\n  } catch (e) {\n    loader.fail();\n    throw e;\n  } finally {\n    fs.removeSync(templateSourceDir);\n  }\n}\n\nasync function installDependencies({\n  directory,\n  npm,\n  loader,\n  root,\n}: {\n  directory: string;\n  npm?: boolean;\n  loader: Loader;\n  root: string;\n}) {\n  loader.start('Installing dependencies');\n\n  await PackageManager.installAll({\n    preferYarn: !npm,\n    silent: true,\n    root,\n  });\n\n  if (process.platform === 'darwin') {\n    await installPods({directory, loader});\n  }\n\n  loader.succeed();\n}\n\nfunction createTemplateUri(options: Options, version: string): string {\n  const isTypescriptTemplate =\n    options.template === 'react-native-template-typescript';\n\n  if (isTypescriptTemplate) {\n    logger.warn(\n      \"Ignoring custom template: 'react-native-template-typescript'. Starting from React Native v0.71 TypeScript is used by default.\",\n    );\n    return 'react-native';\n  }\n\n  return options.template || `react-native@${version}`;\n}\n\nasync function createProject(\n  projectName: string,\n  directory: string,\n  version: string,\n  options: Options,\n) {\n  const templateUri = createTemplateUri(options, version);\n\n  return createFromTemplate({\n    projectName,\n    templateUri,\n    npm: options.npm,\n    directory,\n    projectTitle: options.title,\n    skipInstall: options.skipInstall,\n    packageName: options.packageName,\n  });\n}\n\nexport default (async function initialize(\n  [projectName]: Array<string>,\n  options: Options,\n) {\n  validateProjectName(projectName);\n\n  if (!!options.template && !!options.version) {\n    throw new TemplateAndVersionError(options.template);\n  }\n\n  const root = process.cwd();\n  const version = options.version || DEFAULT_VERSION;\n  const directoryName = path.relative(root, options.directory || projectName);\n\n  await createProject(projectName, directoryName, version, options);\n\n  const projectFolder = path.join(root, directoryName);\n  printRunInstructions(projectFolder, projectName);\n});\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;AAMA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAAuE;AAAA;AAAA;AAEvE,MAAMA,eAAe,GAAG,QAAQ;AAuBhC,SAASC,kBAAkB,CAACC,GAAW,EAAE;EACvC,OAAOC,kBAAE,CAACC,UAAU,CAACF,GAAG,CAAC;AAC3B;AAEA,eAAeG,mBAAmB,CAACC,SAAiB,EAAE;EACpD,IAAIL,kBAAkB,CAACK,SAAS,CAAC,EAAE;IACjC,MAAM,IAAIC,oCAA2B,CAACD,SAAS,CAAC;EAClD;EAEA,IAAI;IACFH,kBAAE,CAACK,SAAS,CAACF,SAAS,EAAE;MAACG,SAAS,EAAE;IAAI,CAAC,CAAC;IAC1CC,OAAO,CAACC,KAAK,CAACL,SAAS,CAAC;EAC1B,CAAC,CAAC,OAAOM,KAAK,EAAE;IACd,MAAM,KAAIC,oBAAQ,EAChB,0DAA0D,EAC1DD,KAAK,CACN;EACH;EAEA,OAAOF,OAAO,CAACI,GAAG,EAAE;AACtB;AAEA,SAASC,eAAe,CAACD,GAAW,EAAE;EACpC;EACA;EACA;EACA,MAAME,IAAI,GAAGC,MAAM,CAACC,IAAI,CACtBC,IAAI,CAACC,KAAK,CAACjB,kBAAE,CAACkB,YAAY,CAACC,eAAI,CAACC,IAAI,CAACT,GAAG,EAAE,gBAAgB,CAAC,EAAE,MAAM,CAAC,CAAC,CAClEU,YAAY,CAChB,CAAC,CAAC,CAAC;EACJ,OAAOR,IAAI;AACb;AAEA,eAAeS,kBAAkB,CAAC;EAChCC,WAAW;EACXC,WAAW;EACXC,GAAG;EACHtB,SAAS;EACTuB,YAAY;EACZC,WAAW;EACXC;AACe,CAAC,EAAE;EAClBC,kBAAM,CAACC,KAAK,CAAC,0BAA0B,CAAC;EACxCD,kBAAM,CAACE,GAAG,CAACC,eAAM,CAAC;EAElB,MAAMC,gBAAgB,GAAG,MAAM/B,mBAAmB,CAACC,SAAS,CAAC;EAE7D,MAAM+B,MAAM,GAAG,IAAAC,qBAAS,EAAC;IAACC,IAAI,EAAE;EAAsB,CAAC,CAAC;EACxD,MAAMC,iBAAiB,GAAGrC,kBAAE,CAACsC,WAAW,CACtCnB,eAAI,CAACC,IAAI,CAACmB,aAAE,CAACC,MAAM,EAAE,EAAE,sBAAsB,CAAC,CAC/C;EAED,IAAI;IACFN,MAAM,CAACO,KAAK,EAAE;IAEd,MAAM,IAAAC,gCAAsB,EAAClB,WAAW,EAAEa,iBAAiB,EAAEZ,GAAG,CAAC;IAEjES,MAAM,CAACS,OAAO,EAAE;IAChBT,MAAM,CAACO,KAAK,CAAC,kBAAkB,CAAC;IAEhC,MAAMG,YAAY,GAAGhC,eAAe,CAACyB,iBAAiB,CAAC;IACvD,MAAMQ,cAAc,GAAG,IAAAC,2BAAiB,EAACF,YAAY,EAAEP,iBAAiB,CAAC;IACzE,MAAM,IAAAU,sBAAY,EAChBH,YAAY,EACZC,cAAc,CAACG,WAAW,EAC1BX,iBAAiB,CAClB;IAEDH,MAAM,CAACS,OAAO,EAAE;IAChBT,MAAM,CAACO,KAAK,CAAC,qBAAqB,CAAC;IAEnC,MAAM,IAAAQ,yCAA2B,EAAC;MAChC1B,WAAW;MACXG,YAAY;MACZwB,eAAe,EAAEL,cAAc,CAACK,eAAe;MAC/CC,gBAAgB,EAAEN,cAAc,CAACO,gBAAgB;MACjDxB;IACF,CAAC,CAAC;IAEFM,MAAM,CAACS,OAAO,EAAE;IAChB,MAAM;MAACU;IAAc,CAAC,GAAGR,cAAc;IACvC,IAAIQ,cAAc,EAAE;MAClBnB,MAAM,CAACoB,IAAI,CAAC,6BAA6B,CAAC;MAC1C,MAAM,IAAAC,+BAAqB,EACzBX,YAAY,EACZS,cAAc,EACdhB,iBAAiB,CAClB;IACH;IAEA,IAAI,CAACV,WAAW,EAAE;MAChB,MAAM6B,mBAAmB,CAAC;QACxB/B,GAAG;QACHS,MAAM;QACNuB,IAAI,EAAExB,gBAAgB;QACtB9B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL+B,MAAM,CAACS,OAAO,CAAC,mCAAmC,CAAC;IACrD;EACF,CAAC,CAAC,OAAOe,CAAC,EAAE;IACVxB,MAAM,CAACyB,IAAI,EAAE;IACb,MAAMD,CAAC;EACT,CAAC,SAAS;IACR1D,kBAAE,CAAC4D,UAAU,CAACvB,iBAAiB,CAAC;EAClC;AACF;AAEA,eAAemB,mBAAmB,CAAC;EACjCrD,SAAS;EACTsB,GAAG;EACHS,MAAM;EACNuB;AAMF,CAAC,EAAE;EACDvB,MAAM,CAACO,KAAK,CAAC,yBAAyB,CAAC;EAEvC,MAAMoB,cAAc,CAACC,UAAU,CAAC;IAC9BC,UAAU,EAAE,CAACtC,GAAG;IAChBuC,MAAM,EAAE,IAAI;IACZP;EACF,CAAC,CAAC;EAEF,IAAIlD,OAAO,CAAC0D,QAAQ,KAAK,QAAQ,EAAE;IACjC,MAAM,IAAAC,wBAAW,EAAC;MAAC/D,SAAS;MAAE+B;IAAM,CAAC,CAAC;EACxC;EAEAA,MAAM,CAACS,OAAO,EAAE;AAClB;AAEA,SAASwB,iBAAiB,CAACC,OAAgB,EAAEC,OAAe,EAAU;EACpE,MAAMC,oBAAoB,GACxBF,OAAO,CAACG,QAAQ,KAAK,kCAAkC;EAEzD,IAAID,oBAAoB,EAAE;IACxBzC,kBAAM,CAAC2C,IAAI,CACT,+HAA+H,CAChI;IACD,OAAO,cAAc;EACvB;EAEA,OAAOJ,OAAO,CAACG,QAAQ,IAAK,gBAAeF,OAAQ,EAAC;AACtD;AAEA,eAAeI,aAAa,CAC1BlD,WAAmB,EACnBpB,SAAiB,EACjBkE,OAAe,EACfD,OAAgB,EAChB;EACA,MAAM5C,WAAW,GAAG2C,iBAAiB,CAACC,OAAO,EAAEC,OAAO,CAAC;EAEvD,OAAO/C,kBAAkB,CAAC;IACxBC,WAAW;IACXC,WAAW;IACXC,GAAG,EAAE2C,OAAO,CAAC3C,GAAG;IAChBtB,SAAS;IACTuB,YAAY,EAAE0C,OAAO,CAACM,KAAK;IAC3B/C,WAAW,EAAEyC,OAAO,CAACzC,WAAW;IAChCC,WAAW,EAAEwC,OAAO,CAACxC;EACvB,CAAC,CAAC;AACJ;AAAC,IAE8B+C,UAAU,GAAzB,eAAeA,UAAU,CACvC,CAACpD,WAAW,CAAgB,EAC5B6C,OAAgB,EAChB;EACA,IAAAQ,6BAAmB,EAACrD,WAAW,CAAC;EAEhC,IAAI,CAAC,CAAC6C,OAAO,CAACG,QAAQ,IAAI,CAAC,CAACH,OAAO,CAACC,OAAO,EAAE;IAC3C,MAAM,IAAIQ,gCAAuB,CAACT,OAAO,CAACG,QAAQ,CAAC;EACrD;EAEA,MAAMd,IAAI,GAAGlD,OAAO,CAACI,GAAG,EAAE;EAC1B,MAAM0D,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAIxE,eAAe;EAClD,MAAMiF,aAAa,GAAG3D,eAAI,CAAC4D,QAAQ,CAACtB,IAAI,EAAEW,OAAO,CAACjE,SAAS,IAAIoB,WAAW,CAAC;EAE3E,MAAMkD,aAAa,CAAClD,WAAW,EAAEuD,aAAa,EAAET,OAAO,EAAED,OAAO,CAAC;EAEjE,MAAMY,aAAa,GAAG7D,eAAI,CAACC,IAAI,CAACqC,IAAI,EAAEqB,aAAa,CAAC;EACpD,IAAAG,6BAAoB,EAACD,aAAa,EAAEzD,WAAW,CAAC;AAClD,CAAC;AAAA"}