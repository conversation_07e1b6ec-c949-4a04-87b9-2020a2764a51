{"version": 3, "names": ["copyFiles", "srcPath", "destPath", "options", "Promise", "all", "walk", "map", "absoluteSrcFilePath", "exclude", "some", "p", "test", "relativeFilePath", "path", "relative", "copyFile", "resolve", "fs", "lstatSync", "isDirectory", "existsSync", "mkdirSync", "reject", "copyBinaryFile", "err", "cb", "cbCalled", "mode", "statSync", "readStream", "createReadStream", "writeStream", "createWriteStream", "on", "done", "chmodSync", "pipe"], "sources": ["../../src/tools/copyFiles.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport fs from 'fs';\nimport path from 'path';\nimport walk from './walk';\n\ntype Options = {\n  exclude?: Array<RegExp>;\n};\n\n/**\n * Copy files (binary included) recursively.\n */\nasync function copyFiles(\n  srcPath: string,\n  destPath: string,\n  options: Options = {},\n) {\n  return Promise.all(\n    walk(srcPath).map(async (absoluteSrcFilePath: string) => {\n      const exclude = options.exclude;\n      if (exclude && exclude.some((p) => p.test(absoluteSrcFilePath))) {\n        return;\n      }\n      const relativeFilePath = path.relative(srcPath, absoluteSrcFilePath);\n      await copyFile(\n        absoluteSrcFilePath,\n        path.resolve(destPath, relativeFilePath),\n      );\n    }),\n  );\n}\n\n/**\n * Copy a file to given destination.\n */\nfunction copyFile(srcPath: string, destPath: string) {\n  if (fs.lstatSync(srcPath).isDirectory()) {\n    if (!fs.existsSync(destPath)) {\n      fs.mkdirSync(destPath);\n    }\n    // Not recursive\n    return;\n  }\n\n  return new Promise((resolve, reject) => {\n    copyBinaryFile(srcPath, destPath, (err) => {\n      if (err) {\n        reject(err);\n      }\n      resolve(destPath);\n    });\n  });\n}\n\n/**\n * Same as 'cp' on Unix. Don't do any replacements.\n */\nfunction copyBinaryFile(\n  srcPath: string,\n  destPath: string,\n  cb: (err?: Error) => void,\n) {\n  let cbCalled = false;\n  const {mode} = fs.statSync(srcPath);\n  const readStream = fs.createReadStream(srcPath);\n  const writeStream = fs.createWriteStream(destPath);\n  readStream.on('error', (err) => {\n    done(err);\n  });\n  writeStream.on('error', (err) => {\n    done(err);\n  });\n  readStream.on('close', () => {\n    done();\n    fs.chmodSync(destPath, mode);\n  });\n  readStream.pipe(writeStream);\n  function done(err?: Error) {\n    if (!cbCalled) {\n      cb(err);\n      cbCalled = true;\n    }\n  }\n}\n\nexport default copyFiles;\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA0B;AAT1B;AACA;AACA;AACA;AACA;AACA;;AAUA;AACA;AACA;AACA,eAAeA,SAAS,CACtBC,OAAe,EACfC,QAAgB,EAChBC,OAAgB,GAAG,CAAC,CAAC,EACrB;EACA,OAAOC,OAAO,CAACC,GAAG,CAChB,IAAAC,aAAI,EAACL,OAAO,CAAC,CAACM,GAAG,CAAC,MAAOC,mBAA2B,IAAK;IACvD,MAAMC,OAAO,GAAGN,OAAO,CAACM,OAAO;IAC/B,IAAIA,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,CAACJ,mBAAmB,CAAC,CAAC,EAAE;MAC/D;IACF;IACA,MAAMK,gBAAgB,GAAGC,eAAI,CAACC,QAAQ,CAACd,OAAO,EAAEO,mBAAmB,CAAC;IACpE,MAAMQ,QAAQ,CACZR,mBAAmB,EACnBM,eAAI,CAACG,OAAO,CAACf,QAAQ,EAAEW,gBAAgB,CAAC,CACzC;EACH,CAAC,CAAC,CACH;AACH;;AAEA;AACA;AACA;AACA,SAASG,QAAQ,CAACf,OAAe,EAAEC,QAAgB,EAAE;EACnD,IAAIgB,aAAE,CAACC,SAAS,CAAClB,OAAO,CAAC,CAACmB,WAAW,EAAE,EAAE;IACvC,IAAI,CAACF,aAAE,CAACG,UAAU,CAACnB,QAAQ,CAAC,EAAE;MAC5BgB,aAAE,CAACI,SAAS,CAACpB,QAAQ,CAAC;IACxB;IACA;IACA;EACF;EAEA,OAAO,IAAIE,OAAO,CAAC,CAACa,OAAO,EAAEM,MAAM,KAAK;IACtCC,cAAc,CAACvB,OAAO,EAAEC,QAAQ,EAAGuB,GAAG,IAAK;MACzC,IAAIA,GAAG,EAAE;QACPF,MAAM,CAACE,GAAG,CAAC;MACb;MACAR,OAAO,CAACf,QAAQ,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,SAASsB,cAAc,CACrBvB,OAAe,EACfC,QAAgB,EAChBwB,EAAyB,EACzB;EACA,IAAIC,QAAQ,GAAG,KAAK;EACpB,MAAM;IAACC;EAAI,CAAC,GAAGV,aAAE,CAACW,QAAQ,CAAC5B,OAAO,CAAC;EACnC,MAAM6B,UAAU,GAAGZ,aAAE,CAACa,gBAAgB,CAAC9B,OAAO,CAAC;EAC/C,MAAM+B,WAAW,GAAGd,aAAE,CAACe,iBAAiB,CAAC/B,QAAQ,CAAC;EAClD4B,UAAU,CAACI,EAAE,CAAC,OAAO,EAAGT,GAAG,IAAK;IAC9BU,IAAI,CAACV,GAAG,CAAC;EACX,CAAC,CAAC;EACFO,WAAW,CAACE,EAAE,CAAC,OAAO,EAAGT,GAAG,IAAK;IAC/BU,IAAI,CAACV,GAAG,CAAC;EACX,CAAC,CAAC;EACFK,UAAU,CAACI,EAAE,CAAC,OAAO,EAAE,MAAM;IAC3BC,IAAI,EAAE;IACNjB,aAAE,CAACkB,SAAS,CAAClC,QAAQ,EAAE0B,IAAI,CAAC;EAC9B,CAAC,CAAC;EACFE,UAAU,CAACO,IAAI,CAACL,WAAW,CAAC;EAC5B,SAASG,IAAI,CAACV,GAAW,EAAE;IACzB,IAAI,CAACE,QAAQ,EAAE;MACbD,EAAE,CAACD,GAAG,CAAC;MACPE,QAAQ,GAAG,IAAI;IACjB;EACF;AACF;AAAC,eAEc3B,SAAS;AAAA"}