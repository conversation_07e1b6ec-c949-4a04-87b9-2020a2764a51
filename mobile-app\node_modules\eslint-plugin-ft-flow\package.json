{"name": "eslint-plugin-ft-flow", "description": "Flowtype linting rules for ESLint by flow-typed", "version": "2.0.3", "license": "MIT", "main": "./dist/index.js", "repository": {"type": "git", "url": "https://github.com/flow-typed/eslint-plugin-ft-flow"}, "engines": {"node": ">=12.22.0"}, "scripts": {"build:flow": "flow-copy-source --ignore '*.spec.*' src dist", "build": "rimraf ./dist && NODE_ENV=production babel ./src --out-dir ./dist --copy-files && yarn build:flow", "check-docs": "babel-node ./scripts/checkDocs", "check-tests": "babel-node ./scripts/checkTests", "create-readme": "node ./scripts/genReadme && babel-node ./scripts/addAssertions", "lint": "eslint ./src ./tests", "test:rules": "mocha --require @babel/register ./tests/rules/index.js", "test": "yarn jest && yarn test:rules", "prepublishOnly": "yarn create-readme && yarn build"}, "dependencies": {"lodash": "^4.17.21", "string-natural-compare": "^3.0.1"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.16.5", "@babel/eslint-parser": "^7.16.5", "@babel/node": "^7.15.8", "@babel/plugin-syntax-flow": "^7.16.5", "@babel/plugin-transform-react-jsx": "^7.16.5", "@babel/preset-env": "^7.16.5", "@babel/preset-flow": "^7.16.0", "@babel/preset-react": "^7.16.7", "@babel/register": "^7.15.3", "ajv": "^8.6.3", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-transform-flow-enums": "^0.0.2", "eslint": "^8.4.1", "eslint-config-airbnb": "^19.0.2", "eslint-config-bzc": "^1.0.5", "eslint-plugin-fb-flow": "^0.0.4", "eslint-plugin-ft-flow": "^1.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jest": "^25.3.2", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-testing-library": "^5.0.1", "flow-bin": "^0.167.1", "flow-copy-source": "^2.0.9", "glob": "^7.2.0", "husky": "^7.0.4", "jest": "^27.4.5", "lint-staged": "^12.1.2", "mocha": "^10.1.0", "rimraf": "^3.0.2"}, "peerDependencies": {"@babel/eslint-parser": "^7.12.0", "eslint": "^8.1.0"}, "keywords": ["eslint", "eslintplugin", "plugin", "flow", "flow-bin", "flowtype", "flow-typed"], "lint-staged": {"**/*.js": "eslint"}}