{"version": 3, "names": ["getYarnVersionIfAvailable", "yarnVersion", "execSync", "stdio", "toString", "trim", "error", "semver", "gte", "logger", "isProjectUsingYarn", "cwd", "findUp", "sync"], "sources": ["../../src/tools/yarn.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {execSync} from 'child_process';\nimport semver from 'semver';\nimport {logger} from '@react-native-community/cli-tools';\nimport findUp from 'find-up';\n\n/**\n * Use Yarn if available, it's much faster than the npm client.\n * Return the version of yarn installed on the system, null if yarn is not available.\n */\nexport function getYarnVersionIfAvailable() {\n  let yarnVersion;\n  try {\n    // execSync returns a Buffer -> convert to string\n    yarnVersion = (\n      execSync('yarn --version', {\n        stdio: [0, 'pipe', 'ignore'],\n      }).toString() || ''\n    ).trim();\n  } catch (error) {\n    return null;\n  }\n  // yarn < 0.16 has a 'missing manifest' bug\n  try {\n    if (semver.gte(yarnVersion, '0.16.0')) {\n      return yarnVersion;\n    }\n    return null;\n  } catch (error) {\n    logger.error(`Cannot parse yarn version: ${yarnVersion}`);\n    return null;\n  }\n}\n\n/**\n * Check if project is using Yarn (has `yarn.lock` in the tree)\n */\nexport function isProjectUsingYarn(cwd: string) {\n  return findUp.sync('yarn.lock', {cwd});\n}\n"], "mappings": ";;;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA6B;AAX7B;AACA;AACA;AACA;AACA;AACA;AACA;;AAOA;AACA;AACA;AACA;AACO,SAASA,yBAAyB,GAAG;EAC1C,IAAIC,WAAW;EACf,IAAI;IACF;IACAA,WAAW,GAAG,CACZ,IAAAC,yBAAQ,EAAC,gBAAgB,EAAE;MACzBC,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ;IAC7B,CAAC,CAAC,CAACC,QAAQ,EAAE,IAAI,EAAE,EACnBC,IAAI,EAAE;EACV,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAO,IAAI;EACb;EACA;EACA,IAAI;IACF,IAAIC,iBAAM,CAACC,GAAG,CAACP,WAAW,EAAE,QAAQ,CAAC,EAAE;MACrC,OAAOA,WAAW;IACpB;IACA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdG,kBAAM,CAACH,KAAK,CAAE,8BAA6BL,WAAY,EAAC,CAAC;IACzD,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACO,SAASS,kBAAkB,CAACC,GAAW,EAAE;EAC9C,OAAOC,iBAAM,CAACC,IAAI,CAAC,WAAW,EAAE;IAACF;EAAG,CAAC,CAAC;AACxC"}