{"version": 3, "names": ["findXcodeProject", "files", "sortedFiles", "sort", "i", "length", "fileName", "ext", "path", "extname", "name", "isWorkspace"], "sources": ["../../src/config/findXcodeProject.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport {IOSProjectInfo} from '@react-native-community/cli-types';\n\nfunction findXcodeProject(files: Array<string>): IOSProjectInfo | null {\n  const sortedFiles = files.sort();\n\n  for (let i = sortedFiles.length - 1; i >= 0; i--) {\n    const fileName = files[i];\n    const ext = path.extname(fileName);\n\n    if (ext === '.xcworkspace') {\n      return {\n        name: fileName,\n        isWorkspace: true,\n      };\n    }\n    if (ext === '.xcodeproj') {\n      return {\n        name: fileName,\n        isWorkspace: false,\n      };\n    }\n  }\n\n  return null;\n}\n\nexport default findXcodeProject;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AARxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,SAASA,gBAAgB,CAACC,KAAoB,EAAyB;EACrE,MAAMC,WAAW,GAAGD,KAAK,CAACE,IAAI,EAAE;EAEhC,KAAK,IAAIC,CAAC,GAAGF,WAAW,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAChD,MAAME,QAAQ,GAAGL,KAAK,CAACG,CAAC,CAAC;IACzB,MAAMG,GAAG,GAAGC,eAAI,CAACC,OAAO,CAACH,QAAQ,CAAC;IAElC,IAAIC,GAAG,KAAK,cAAc,EAAE;MAC1B,OAAO;QACLG,IAAI,EAAEJ,QAAQ;QACdK,WAAW,EAAE;MACf,CAAC;IACH;IACA,IAAIJ,GAAG,KAAK,YAAY,EAAE;MACxB,OAAO;QACLG,IAAI,EAAEJ,QAAQ;QACdK,WAAW,EAAE;MACf,CAAC;IACH;EACF;EAEA,OAAO,IAAI;AACb;AAAC,eAEcX,gBAAgB;AAAA"}