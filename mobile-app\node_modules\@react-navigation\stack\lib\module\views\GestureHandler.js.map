{"version": 3, "names": ["React", "View", "Dummy", "children", "PanGestureHandler", "GestureHandlerRootView", "GestureState", "UNDETERMINED", "FAILED", "BEGAN", "CANCELLED", "ACTIVE", "END"], "sourceRoot": "../../../src", "sources": ["views/GestureHandler.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,cAAc;AAGnC,MAAMC,KAAU,GAAG;EAAA,IAAC;IAAEC;EAAwC,CAAC;EAAA,oBAC7D,0CAAGA,QAAQ,CAAI;AAAA,CAChB;AAED,OAAO,MAAMC,iBAAiB,GAC5BF,KAAyD;AAE3D,OAAO,MAAMG,sBAAsB,GAAGJ,IAAI;AAE1C,OAAO,MAAMK,YAAY,GAAG;EAC1BC,YAAY,EAAE,CAAC;EACfC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,CAAC;EACZC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE;AACP,CAAC"}