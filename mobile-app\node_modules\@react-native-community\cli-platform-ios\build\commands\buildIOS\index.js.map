{"version": 3, "names": ["buildIOS", "_", "ctx", "args", "project", "ios", "CLIError", "xcodeProject", "sourceDir", "process", "chdir", "configuration", "logger", "warn", "mode", "projectInfo", "getProjectInfo", "checkIfConfigurationExists", "inferredSchemeName", "path", "basename", "name", "extname", "scheme", "interactive", "selection", "selectFromInteractiveMode", "modifiedArgs", "getConfigurationScheme", "info", "isWorkspace", "chalk", "bold", "extendedArgs", "packager", "device", "udid", "simulator", "buildProject", "undefined", "fallbackSimulators", "selectedSimulator", "getDestinationSimulator", "error", "devices", "listIOSDevices", "find", "d", "printFoundDevices", "physicalDevices", "filter", "type", "matchingDevice", "deviceName", "firstIOSDevice", "deviceByName", "formattedDeviceName", "String", "version", "map", "join", "iosBuildOptions", "description", "default", "env", "RCT_METRO_PORT", "parse", "Number", "getDefaultUserTerminal", "val", "split", "func", "examples", "desc", "cmd", "options"], "sources": ["../../../src/commands/buildIOS/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport chalk from 'chalk';\nimport {Config} from '@react-native-community/cli-types';\nimport {\n  logger,\n  CLIError,\n  getDefaultUserTerminal,\n} from '@react-native-community/cli-tools';\nimport {Device} from '../../types';\nimport {BuildFlags, buildProject} from './buildProject';\nimport {getDestinationSimulator} from '../../tools/getDestinationSimulator';\nimport {selectFromInteractiveMode} from '../../tools/selectFromInteractiveMode';\nimport {getProjectInfo} from '../../tools/getProjectInfo';\nimport {checkIfConfigurationExists} from '../../tools/checkIfConfigurationExists';\nimport {getConfigurationScheme} from '../../tools/getConfigurationScheme';\nimport listIOSDevices from '../../tools/listIOSDevices';\n\nexport interface FlagsT extends BuildFlags {\n  configuration?: string;\n  simulator?: string;\n  device?: string | true;\n  udid?: string;\n  scheme?: string;\n}\n\nasync function buildIOS(_: Array<string>, ctx: Config, args: FlagsT) {\n  if (!ctx.project.ios) {\n    throw new CLIError(\n      'iOS project folder not found. Are you sure this is a React Native project?',\n    );\n  }\n\n  const {xcodeProject, sourceDir} = ctx.project.ios;\n\n  if (!xcodeProject) {\n    throw new CLIError(\n      `Could not find Xcode project files in \"${sourceDir}\" folder`,\n    );\n  }\n\n  process.chdir(sourceDir);\n\n  if (args.configuration) {\n    logger.warn('--configuration has been deprecated. Use --mode instead.');\n    logger.warn(\n      'Parameters were automatically reassigned to --mode on this run.',\n    );\n    args.mode = args.configuration;\n  }\n\n  const projectInfo = getProjectInfo();\n\n  if (args.mode) {\n    checkIfConfigurationExists(projectInfo, args.mode);\n  }\n\n  const inferredSchemeName = path.basename(\n    xcodeProject.name,\n    path.extname(xcodeProject.name),\n  );\n\n  let scheme = args.scheme || inferredSchemeName;\n  let mode = args.mode;\n\n  if (args.interactive) {\n    const selection = await selectFromInteractiveMode({scheme, mode});\n\n    if (selection.scheme) {\n      scheme = selection.scheme;\n    }\n\n    if (selection.mode) {\n      mode = selection.mode;\n    }\n  }\n\n  const modifiedArgs = {...args, scheme, mode};\n\n  args.mode = getConfigurationScheme(\n    {scheme: args.scheme, mode: args.mode},\n    sourceDir,\n  );\n\n  logger.info(\n    `Found Xcode ${\n      xcodeProject.isWorkspace ? 'workspace' : 'project'\n    } \"${chalk.bold(xcodeProject.name)}\"`,\n  );\n\n  const extendedArgs = {\n    ...modifiedArgs,\n    packager: false,\n  };\n\n  // // No need to load all available devices\n  if (!args.device && !args.udid) {\n    if (!args.simulator) {\n      return buildProject(xcodeProject, undefined, scheme, extendedArgs);\n    }\n\n    /**\n     * If provided simulator does not exist, try simulators in following order\n     * - iPhone 14\n     * - iPhone 13\n     * - iPhone 12\n     * - iPhone 11\n     */\n    const fallbackSimulators = [\n      'iPhone 14',\n      'iPhone 13',\n      'iPhone 12',\n      'iPhone 11',\n    ];\n\n    const selectedSimulator = getDestinationSimulator(args, fallbackSimulators);\n\n    return buildProject(\n      xcodeProject,\n      selectedSimulator.udid,\n      scheme,\n      extendedArgs,\n    );\n  }\n\n  if (args.device && args.udid) {\n    return logger.error(\n      'The `device` and `udid` options are mutually exclusive.',\n    );\n  }\n\n  const devices = await listIOSDevices();\n\n  if (args.udid) {\n    const device = devices.find((d) => d.udid === args.udid);\n    if (!device) {\n      return logger.error(\n        `Could not find a device with udid: \"${chalk.bold(\n          args.udid,\n        )}\". ${printFoundDevices(devices)}`,\n      );\n    }\n\n    return buildProject(xcodeProject, device.udid, scheme, extendedArgs);\n  } else {\n    const physicalDevices = devices.filter((d) => d.type !== 'simulator');\n    const device = matchingDevice(physicalDevices, args.device);\n    if (device) {\n      return buildProject(xcodeProject, device.udid, scheme, extendedArgs);\n    }\n  }\n}\n\nfunction matchingDevice(\n  devices: Array<Device>,\n  deviceName: string | true | undefined,\n) {\n  if (deviceName === true) {\n    const firstIOSDevice = devices.find((d) => d.type === 'device')!;\n    if (firstIOSDevice) {\n      logger.info(\n        `Using first available device named \"${chalk.bold(\n          firstIOSDevice.name,\n        )}\" due to lack of name supplied.`,\n      );\n      return firstIOSDevice;\n    } else {\n      logger.error('No iOS devices connected.');\n      return undefined;\n    }\n  }\n  const deviceByName = devices.find(\n    (device) =>\n      device.name === deviceName || formattedDeviceName(device) === deviceName,\n  );\n  if (!deviceByName) {\n    logger.error(\n      `Could not find a device named: \"${chalk.bold(\n        String(deviceName),\n      )}\". ${printFoundDevices(devices)}`,\n    );\n  }\n  return deviceByName;\n}\n\nfunction formattedDeviceName(simulator: Device) {\n  return simulator.version\n    ? `${simulator.name} (${simulator.version})`\n    : simulator.name;\n}\n\nfunction printFoundDevices(devices: Array<Device>) {\n  return [\n    'Available devices:',\n    ...devices.map((device) => `  - ${device.name} (${device.udid})`),\n  ].join('\\n');\n}\n\nexport const iosBuildOptions = [\n  {\n    name: '--simulator <string>',\n    description:\n      'Explicitly set simulator to use. Optionally include iOS version between ' +\n      'parenthesis at the end to match an exact version: \"iPhone 6 (10.0)\"',\n  },\n  {\n    name: '--mode <string>',\n    description:\n      'Explicitly set the scheme configuration to use. This option is case sensitive.',\n  },\n  {\n    name: '--configuration <string>',\n    description: '[Deprecated] Explicitly set the scheme configuration to use',\n  },\n  {\n    name: '--scheme <string>',\n    description: 'Explicitly set Xcode scheme to use',\n  },\n  {\n    name: '--device [string]',\n    description:\n      'Explicitly set device to use by name.  The value is not required if you have a single device connected.',\n  },\n  {\n    name: '--destination <string>',\n    description: 'Explicitly extend distination e.g. \"arch=x86_64\"',\n  },\n  {\n    name: '--udid <string>',\n    description: 'Explicitly set device to use by udid',\n  },\n  {\n    name: '--verbose',\n    description: 'Do not use xcbeautify or xcpretty even if installed',\n  },\n  {\n    name: '--port <number>',\n    default: process.env.RCT_METRO_PORT || 8081,\n    parse: Number,\n  },\n  {\n    name: '--terminal <string>',\n    description:\n      'Launches the Metro Bundler in a new window using the specified terminal path.',\n    default: getDefaultUserTerminal(),\n  },\n  {\n    name: '--xcconfig [string]',\n    description: 'Explicitly set xcconfig to use',\n  },\n  {\n    name: '--buildFolder <string>',\n    description:\n      'Location for iOS build artifacts. Corresponds to Xcode\\'s \"-derivedDataPath\".',\n  },\n  {\n    name: '--extra-params <string>',\n    description: 'Custom params that will be passed to xcodebuild command.',\n    parse: (val: string) => val.split(' '),\n  },\n];\n\nexport default {\n  name: 'build-ios',\n  description: 'builds your app on iOS simulator',\n  func: buildIOS,\n  examples: [\n    {\n      desc: 'Build the app for the IOS simulator',\n      cmd: 'react-native build-ios',\n    },\n    {\n      desc: 'Build the app for all IOS devices',\n      cmd: 'react-native build-ios --mode \"Release\"',\n    },\n    {\n      desc: 'Build the app for a specific IOS device',\n      cmd: 'react-native build-ios --simulator \"IPhone 11\"',\n    },\n  ],\n  options: [\n    ...iosBuildOptions,\n    {\n      name: '--interactive',\n      description:\n        'Explicitly select which scheme and configuration to use before running a build',\n    },\n  ],\n};\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AAAwD;AAvBxD;AACA;AACA;AACA;AACA;AACA;AACA;;AA2BA,eAAeA,QAAQ,CAACC,CAAgB,EAAEC,GAAW,EAAEC,IAAY,EAAE;EACnE,IAAI,CAACD,GAAG,CAACE,OAAO,CAACC,GAAG,EAAE;IACpB,MAAM,KAAIC,oBAAQ,EAChB,4EAA4E,CAC7E;EACH;EAEA,MAAM;IAACC,YAAY;IAAEC;EAAS,CAAC,GAAGN,GAAG,CAACE,OAAO,CAACC,GAAG;EAEjD,IAAI,CAACE,YAAY,EAAE;IACjB,MAAM,KAAID,oBAAQ,EACf,0CAAyCE,SAAU,UAAS,CAC9D;EACH;EAEAC,OAAO,CAACC,KAAK,CAACF,SAAS,CAAC;EAExB,IAAIL,IAAI,CAACQ,aAAa,EAAE;IACtBC,kBAAM,CAACC,IAAI,CAAC,0DAA0D,CAAC;IACvED,kBAAM,CAACC,IAAI,CACT,iEAAiE,CAClE;IACDV,IAAI,CAACW,IAAI,GAAGX,IAAI,CAACQ,aAAa;EAChC;EAEA,MAAMI,WAAW,GAAG,IAAAC,8BAAc,GAAE;EAEpC,IAAIb,IAAI,CAACW,IAAI,EAAE;IACb,IAAAG,sDAA0B,EAACF,WAAW,EAAEZ,IAAI,CAACW,IAAI,CAAC;EACpD;EAEA,MAAMI,kBAAkB,GAAGC,eAAI,CAACC,QAAQ,CACtCb,YAAY,CAACc,IAAI,EACjBF,eAAI,CAACG,OAAO,CAACf,YAAY,CAACc,IAAI,CAAC,CAChC;EAED,IAAIE,MAAM,GAAGpB,IAAI,CAACoB,MAAM,IAAIL,kBAAkB;EAC9C,IAAIJ,IAAI,GAAGX,IAAI,CAACW,IAAI;EAEpB,IAAIX,IAAI,CAACqB,WAAW,EAAE;IACpB,MAAMC,SAAS,GAAG,MAAM,IAAAC,oDAAyB,EAAC;MAACH,MAAM;MAAET;IAAI,CAAC,CAAC;IAEjE,IAAIW,SAAS,CAACF,MAAM,EAAE;MACpBA,MAAM,GAAGE,SAAS,CAACF,MAAM;IAC3B;IAEA,IAAIE,SAAS,CAACX,IAAI,EAAE;MAClBA,IAAI,GAAGW,SAAS,CAACX,IAAI;IACvB;EACF;EAEA,MAAMa,YAAY,GAAG;IAAC,GAAGxB,IAAI;IAAEoB,MAAM;IAAET;EAAI,CAAC;EAE5CX,IAAI,CAACW,IAAI,GAAG,IAAAc,8CAAsB,EAChC;IAACL,MAAM,EAAEpB,IAAI,CAACoB,MAAM;IAAET,IAAI,EAAEX,IAAI,CAACW;EAAI,CAAC,EACtCN,SAAS,CACV;EAEDI,kBAAM,CAACiB,IAAI,CACR,eACCtB,YAAY,CAACuB,WAAW,GAAG,WAAW,GAAG,SAC1C,KAAIC,gBAAK,CAACC,IAAI,CAACzB,YAAY,CAACc,IAAI,CAAE,GAAE,CACtC;EAED,MAAMY,YAAY,GAAG;IACnB,GAAGN,YAAY;IACfO,QAAQ,EAAE;EACZ,CAAC;;EAED;EACA,IAAI,CAAC/B,IAAI,CAACgC,MAAM,IAAI,CAAChC,IAAI,CAACiC,IAAI,EAAE;IAC9B,IAAI,CAACjC,IAAI,CAACkC,SAAS,EAAE;MACnB,OAAO,IAAAC,0BAAY,EAAC/B,YAAY,EAAEgC,SAAS,EAAEhB,MAAM,EAAEU,YAAY,CAAC;IACpE;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,MAAMO,kBAAkB,GAAG,CACzB,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,CACZ;IAED,MAAMC,iBAAiB,GAAG,IAAAC,gDAAuB,EAACvC,IAAI,EAAEqC,kBAAkB,CAAC;IAE3E,OAAO,IAAAF,0BAAY,EACjB/B,YAAY,EACZkC,iBAAiB,CAACL,IAAI,EACtBb,MAAM,EACNU,YAAY,CACb;EACH;EAEA,IAAI9B,IAAI,CAACgC,MAAM,IAAIhC,IAAI,CAACiC,IAAI,EAAE;IAC5B,OAAOxB,kBAAM,CAAC+B,KAAK,CACjB,yDAAyD,CAC1D;EACH;EAEA,MAAMC,OAAO,GAAG,MAAM,IAAAC,uBAAc,GAAE;EAEtC,IAAI1C,IAAI,CAACiC,IAAI,EAAE;IACb,MAAMD,MAAM,GAAGS,OAAO,CAACE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACX,IAAI,KAAKjC,IAAI,CAACiC,IAAI,CAAC;IACxD,IAAI,CAACD,MAAM,EAAE;MACX,OAAOvB,kBAAM,CAAC+B,KAAK,CAChB,uCAAsCZ,gBAAK,CAACC,IAAI,CAC/C7B,IAAI,CAACiC,IAAI,CACT,MAAKY,iBAAiB,CAACJ,OAAO,CAAE,EAAC,CACpC;IACH;IAEA,OAAO,IAAAN,0BAAY,EAAC/B,YAAY,EAAE4B,MAAM,CAACC,IAAI,EAAEb,MAAM,EAAEU,YAAY,CAAC;EACtE,CAAC,MAAM;IACL,MAAMgB,eAAe,GAAGL,OAAO,CAACM,MAAM,CAAEH,CAAC,IAAKA,CAAC,CAACI,IAAI,KAAK,WAAW,CAAC;IACrE,MAAMhB,MAAM,GAAGiB,cAAc,CAACH,eAAe,EAAE9C,IAAI,CAACgC,MAAM,CAAC;IAC3D,IAAIA,MAAM,EAAE;MACV,OAAO,IAAAG,0BAAY,EAAC/B,YAAY,EAAE4B,MAAM,CAACC,IAAI,EAAEb,MAAM,EAAEU,YAAY,CAAC;IACtE;EACF;AACF;AAEA,SAASmB,cAAc,CACrBR,OAAsB,EACtBS,UAAqC,EACrC;EACA,IAAIA,UAAU,KAAK,IAAI,EAAE;IACvB,MAAMC,cAAc,GAAGV,OAAO,CAACE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,IAAI,KAAK,QAAQ,CAAE;IAChE,IAAIG,cAAc,EAAE;MAClB1C,kBAAM,CAACiB,IAAI,CACR,uCAAsCE,gBAAK,CAACC,IAAI,CAC/CsB,cAAc,CAACjC,IAAI,CACnB,iCAAgC,CACnC;MACD,OAAOiC,cAAc;IACvB,CAAC,MAAM;MACL1C,kBAAM,CAAC+B,KAAK,CAAC,2BAA2B,CAAC;MACzC,OAAOJ,SAAS;IAClB;EACF;EACA,MAAMgB,YAAY,GAAGX,OAAO,CAACE,IAAI,CAC9BX,MAAM,IACLA,MAAM,CAACd,IAAI,KAAKgC,UAAU,IAAIG,mBAAmB,CAACrB,MAAM,CAAC,KAAKkB,UAAU,CAC3E;EACD,IAAI,CAACE,YAAY,EAAE;IACjB3C,kBAAM,CAAC+B,KAAK,CACT,mCAAkCZ,gBAAK,CAACC,IAAI,CAC3CyB,MAAM,CAACJ,UAAU,CAAC,CAClB,MAAKL,iBAAiB,CAACJ,OAAO,CAAE,EAAC,CACpC;EACH;EACA,OAAOW,YAAY;AACrB;AAEA,SAASC,mBAAmB,CAACnB,SAAiB,EAAE;EAC9C,OAAOA,SAAS,CAACqB,OAAO,GACnB,GAAErB,SAAS,CAAChB,IAAK,KAAIgB,SAAS,CAACqB,OAAQ,GAAE,GAC1CrB,SAAS,CAAChB,IAAI;AACpB;AAEA,SAAS2B,iBAAiB,CAACJ,OAAsB,EAAE;EACjD,OAAO,CACL,oBAAoB,EACpB,GAAGA,OAAO,CAACe,GAAG,CAAExB,MAAM,IAAM,OAAMA,MAAM,CAACd,IAAK,KAAIc,MAAM,CAACC,IAAK,GAAE,CAAC,CAClE,CAACwB,IAAI,CAAC,IAAI,CAAC;AACd;AAEO,MAAMC,eAAe,GAAG,CAC7B;EACExC,IAAI,EAAE,sBAAsB;EAC5ByC,WAAW,EACT,0EAA0E,GAC1E;AACJ,CAAC,EACD;EACEzC,IAAI,EAAE,iBAAiB;EACvByC,WAAW,EACT;AACJ,CAAC,EACD;EACEzC,IAAI,EAAE,0BAA0B;EAChCyC,WAAW,EAAE;AACf,CAAC,EACD;EACEzC,IAAI,EAAE,mBAAmB;EACzByC,WAAW,EAAE;AACf,CAAC,EACD;EACEzC,IAAI,EAAE,mBAAmB;EACzByC,WAAW,EACT;AACJ,CAAC,EACD;EACEzC,IAAI,EAAE,wBAAwB;EAC9ByC,WAAW,EAAE;AACf,CAAC,EACD;EACEzC,IAAI,EAAE,iBAAiB;EACvByC,WAAW,EAAE;AACf,CAAC,EACD;EACEzC,IAAI,EAAE,WAAW;EACjByC,WAAW,EAAE;AACf,CAAC,EACD;EACEzC,IAAI,EAAE,iBAAiB;EACvB0C,OAAO,EAAEtD,OAAO,CAACuD,GAAG,CAACC,cAAc,IAAI,IAAI;EAC3CC,KAAK,EAAEC;AACT,CAAC,EACD;EACE9C,IAAI,EAAE,qBAAqB;EAC3ByC,WAAW,EACT,+EAA+E;EACjFC,OAAO,EAAE,IAAAK,kCAAsB;AACjC,CAAC,EACD;EACE/C,IAAI,EAAE,qBAAqB;EAC3ByC,WAAW,EAAE;AACf,CAAC,EACD;EACEzC,IAAI,EAAE,wBAAwB;EAC9ByC,WAAW,EACT;AACJ,CAAC,EACD;EACEzC,IAAI,EAAE,yBAAyB;EAC/ByC,WAAW,EAAE,0DAA0D;EACvEI,KAAK,EAAGG,GAAW,IAAKA,GAAG,CAACC,KAAK,CAAC,GAAG;AACvC,CAAC,CACF;AAAC;AAAA,eAEa;EACbjD,IAAI,EAAE,WAAW;EACjByC,WAAW,EAAE,kCAAkC;EAC/CS,IAAI,EAAEvE,QAAQ;EACdwE,QAAQ,EAAE,CACR;IACEC,IAAI,EAAE,qCAAqC;IAC3CC,GAAG,EAAE;EACP,CAAC,EACD;IACED,IAAI,EAAE,mCAAmC;IACzCC,GAAG,EAAE;EACP,CAAC,EACD;IACED,IAAI,EAAE,yCAAyC;IAC/CC,GAAG,EAAE;EACP,CAAC,CACF;EACDC,OAAO,EAAE,CACP,GAAGd,eAAe,EAClB;IACExC,IAAI,EAAE,eAAe;IACrByC,WAAW,EACT;EACJ,CAAC;AAEL,CAAC;AAAA"}