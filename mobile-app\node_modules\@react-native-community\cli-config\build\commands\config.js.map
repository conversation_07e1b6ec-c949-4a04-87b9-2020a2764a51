{"version": 3, "names": ["isValidRNDependency", "config", "Object", "keys", "platforms", "filter", "key", "Boolean", "length", "filterConfig", "filtered", "dependencies", "for<PERSON>ach", "item", "name", "description", "func", "_argv", "ctx", "console", "log", "JSON", "stringify"], "sources": ["../../src/commands/config.ts"], "sourcesContent": ["import {Config, DependencyConfig} from '@react-native-community/cli-types';\n\nfunction isValidRNDependency(config: DependencyConfig) {\n  return (\n    Object.keys(config.platforms).filter((key) =>\n      Boolean(config.platforms[key]),\n    ).length !== 0\n  );\n}\n\nfunction filterConfig(config: Config) {\n  const filtered = {...config};\n  Object.keys(filtered.dependencies).forEach((item) => {\n    if (!isValidRNDependency(filtered.dependencies[item])) {\n      delete filtered.dependencies[item];\n    }\n  });\n  return filtered;\n}\n\nexport default {\n  name: 'config',\n  description: 'Print CLI configuration',\n  func: async (_argv: string[], ctx: Config) => {\n    console.log(JSON.stringify(filterConfig(ctx), null, 2));\n  },\n};\n"], "mappings": ";;;;;;AAEA,SAASA,mBAAmB,CAACC,MAAwB,EAAE;EACrD,OACEC,MAAM,CAACC,IAAI,CAACF,MAAM,CAACG,SAAS,CAAC,CAACC,MAAM,CAAEC,GAAG,IACvCC,OAAO,CAACN,MAAM,CAACG,SAAS,CAACE,GAAG,CAAC,CAAC,CAC/B,CAACE,MAAM,KAAK,CAAC;AAElB;AAEA,SAASC,YAAY,CAACR,MAAc,EAAE;EACpC,MAAMS,QAAQ,GAAG;IAAC,GAAGT;EAAM,CAAC;EAC5BC,MAAM,CAACC,IAAI,CAACO,QAAQ,CAACC,YAAY,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;IACnD,IAAI,CAACb,mBAAmB,CAACU,QAAQ,CAACC,YAAY,CAACE,IAAI,CAAC,CAAC,EAAE;MACrD,OAAOH,QAAQ,CAACC,YAAY,CAACE,IAAI,CAAC;IACpC;EACF,CAAC,CAAC;EACF,OAAOH,QAAQ;AACjB;AAAC,eAEc;EACbI,IAAI,EAAE,QAAQ;EACdC,WAAW,EAAE,yBAAyB;EACtCC,IAAI,EAAE,OAAOC,KAAe,EAAEC,GAAW,KAAK;IAC5CC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACb,YAAY,CAACS,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACzD;AACF,CAAC;AAAA"}