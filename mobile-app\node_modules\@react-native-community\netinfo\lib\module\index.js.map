{"version": 3, "sources": ["index.ts"], "names": ["useState", "useEffect", "Platform", "DEFAULT_CONFIGURATION", "NativeInterface", "State", "Types", "_configuration", "_state", "createState", "configure", "configuration", "tearDown", "OS", "fetch", "requestedInterface", "latest", "refresh", "_fetchCurrentState", "addEventListener", "listener", "add", "remove", "useNetInfo", "netInfo", "setNetInfo", "type", "NetInfoStateType", "unknown", "isConnected", "isInternetReachable", "details"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,SAAQA,QAAR,EAAkBC,SAAlB,QAAkC,OAAlC;AACA,SAAQC,QAAR,QAAuB,cAAvB;AACA,OAAOC,qBAAP,MAAkC,iCAAlC;AACA,OAAOC,eAAP,MAA4B,4BAA5B;AACA,OAAOC,KAAP,MAAkB,kBAAlB;AACA,OAAO,KAAKC,KAAZ,MAAuB,kBAAvB,C,CAEA;;AACA,IAAIC,cAAc,GAAGJ,qBAArB,C,CAEA;;AACA,IAAIK,MAAoB,GAAG,IAA3B;;AACA,MAAMC,WAAW,GAAG,MAAa;AAC/B,SAAO,IAAIJ,KAAJ,CAAUE,cAAV,CAAP;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,OAAO,SAASG,SAAT,CACLC,aADK,EAEC;AACNJ,EAAAA,cAAc,GAAG,EACf,GAAGJ,qBADY;AAEf,OAAGQ;AAFY,GAAjB;;AAKA,MAAIH,MAAJ,EAAY;AACVA,IAAAA,MAAM,CAACI,QAAP;;AACAJ,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AAED,MAAIP,QAAQ,CAACW,EAAT,KAAgB,KAApB,EAA2B;AACzBT,IAAAA,eAAe,CAACM,SAAhB,CAA0BC,aAA1B;AACD;AACF;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASG,KAAT,CACLC,kBADK,EAEwB;AAC7B,MAAI,CAACP,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AACD,SAAOD,MAAM,CAACQ,MAAP,CAAcD,kBAAd,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASE,OAAT,GAAgD;AACrD,MAAI,CAACT,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AACD,SAAOD,MAAM,CAACU,kBAAP,EAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,gBAAT,CACLC,QADK,EAEsB;AAC3B,MAAI,CAACZ,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AAEDD,EAAAA,MAAM,CAACa,GAAP,CAAWD,QAAX;;AACA,SAAO,MAAY;AACjBZ,IAAAA,MAAM,IAAIA,MAAM,CAACc,MAAP,CAAcF,QAAd,CAAV;AACD,GAFD;AAGD;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASG,UAAT,CACLZ,aADK,EAEe;AACpB,MAAIA,aAAJ,EAAmB;AACjBD,IAAAA,SAAS,CAACC,aAAD,CAAT;AACD;;AAED,QAAM,CAACa,OAAD,EAAUC,UAAV,IAAwBzB,QAAQ,CAAqB;AACzD0B,IAAAA,IAAI,EAAEpB,KAAK,CAACqB,gBAAN,CAAuBC,OAD4B;AAEzDC,IAAAA,WAAW,EAAE,IAF4C;AAGzDC,IAAAA,mBAAmB,EAAE,IAHoC;AAIzDC,IAAAA,OAAO,EAAE;AAJgD,GAArB,CAAtC;AAOA9B,EAAAA,SAAS,CAAC,MAAoB;AAC5B,WAAOkB,gBAAgB,CAACM,UAAD,CAAvB;AACD,GAFQ,EAEN,EAFM,CAAT;AAIA,SAAOD,OAAP;AACD;AAED,cAAc,kBAAd;AAEA,eAAe;AACbd,EAAAA,SADa;AAEbI,EAAAA,KAFa;AAGbG,EAAAA,OAHa;AAIbE,EAAAA,gBAJa;AAKbI,EAAAA;AALa,CAAf", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {useState, useEffect} from 'react';\nimport {Platform} from 'react-native';\nimport DEFAULT_CONFIGURATION from './internal/defaultConfiguration';\nimport NativeInterface from './internal/nativeInterface';\nimport State from './internal/state';\nimport * as Types from './internal/types';\n\n// Stores the currently used configuration\nlet _configuration = DEFAULT_CONFIGURATION;\n\n// Stores the singleton reference to the state manager\nlet _state: State | null = null;\nconst createState = (): State => {\n  return new State(_configuration);\n};\n\n/**\n * Configures the library with the given configuration. Note that calling this will stop all\n * previously added listeners from being called again. It is best to call this right when your\n * application is started to avoid issues.\n *\n * @param configuration The new configuration to set.\n */\nexport function configure(\n  configuration: Partial<Types.NetInfoConfiguration>,\n): void {\n  _configuration = {\n    ...DEFAULT_CONFIGURATION,\n    ...configuration,\n  };\n\n  if (_state) {\n    _state.tearDown();\n    _state = createState();\n  }\n\n  if (Platform.OS === 'ios') {\n    NativeInterface.configure(configuration);\n  }\n}\n\n/**\n * Returns a `Promise` that resolves to a `NetInfoState` object.\n *\n * @param [requestedInterface] interface from which to obtain the information\n *\n * @returns A Promise which contains the current connection state.\n */\nexport function fetch(\n  requestedInterface?: string,\n): Promise<Types.NetInfoState> {\n  if (!_state) {\n    _state = createState();\n  }\n  return _state.latest(requestedInterface);\n}\n\n/**\n * Force-refreshes the internal state of the NetInfo library.\n *\n * @returns A Promise which contains the updated connection state.\n */\nexport function refresh(): Promise<Types.NetInfoState> {\n  if (!_state) {\n    _state = createState();\n  }\n  return _state._fetchCurrentState();\n}\n\n/**\n * Subscribe to connection information. The callback is called with a parameter of type\n * [`NetInfoState`](README.md#netinfostate) whenever the connection state changes. Your listener\n * will be called with the latest information soon after you subscribe and then with any\n * subsequent changes afterwards. You should not assume that the listener is called in the same\n * way across devices or platforms.\n *\n * @param listener The listener which is called when the network state changes.\n *\n * @returns A function which can be called to unsubscribe.\n */\nexport function addEventListener(\n  listener: Types.NetInfoChangeHandler,\n): Types.NetInfoSubscription {\n  if (!_state) {\n    _state = createState();\n  }\n\n  _state.add(listener);\n  return (): void => {\n    _state && _state.remove(listener);\n  };\n}\n\n/**\n * A React Hook which updates when the connection state changes.\n *\n * @returns The connection state.\n */\nexport function useNetInfo(\n  configuration?: Partial<Types.NetInfoConfiguration>,\n): Types.NetInfoState {\n  if (configuration) {\n    configure(configuration);\n  }\n\n  const [netInfo, setNetInfo] = useState<Types.NetInfoState>({\n    type: Types.NetInfoStateType.unknown,\n    isConnected: null,\n    isInternetReachable: null,\n    details: null,\n  });\n\n  useEffect((): (() => void) => {\n    return addEventListener(setNetInfo);\n  }, []);\n\n  return netInfo;\n}\n\nexport * from './internal/types';\n\nexport default {\n  configure,\n  fetch,\n  refresh,\n  addEventListener,\n  useNetInfo,\n};\n"]}