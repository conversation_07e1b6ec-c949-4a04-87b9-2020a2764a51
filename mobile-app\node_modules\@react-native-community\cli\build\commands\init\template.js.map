{"version": 3, "names": ["installTemplatePackage", "templateName", "root", "npm", "logger", "debug", "PackageManager", "init", "<PERSON><PERSON><PERSON><PERSON>", "silent", "install", "getTemplateConfig", "templateSourceDir", "config<PERSON><PERSON><PERSON><PERSON>", "path", "resolve", "fs", "existsSync", "CLIError", "chalk", "underline", "dim", "require", "copyTemplate", "templateDir", "templatePath", "regexStr", "copyFiles", "process", "cwd", "exclude", "RegExp", "replacePathSepForRegex", "executePostInitScript", "postInitScript", "script<PERSON>ath", "execa", "stdio"], "sources": ["../../../src/commands/init/template.ts"], "sourcesContent": ["import execa from 'execa';\nimport path from 'path';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\nimport * as PackageManager from '../../tools/packageManager';\nimport copyFiles from '../../tools/copyFiles';\nimport replacePathSepForRegex from '../../tools/replacePathSepForRegex';\nimport fs from 'fs';\nimport chalk from 'chalk';\n\nexport type TemplateConfig = {\n  placeholderName: string;\n  templateDir: string;\n  postInitScript?: string;\n  titlePlaceholder?: string;\n};\n\nexport async function installTemplatePackage(\n  templateName: string,\n  root: string,\n  npm?: boolean,\n) {\n  logger.debug(`Installing template from ${templateName}`);\n\n  await PackageManager.init({\n    preferYarn: !npm,\n    silent: true,\n    root,\n  });\n\n  return PackageManager.install([templateName], {\n    preferYarn: !npm,\n    silent: true,\n    root,\n  });\n}\n\nexport function getTemplateConfig(\n  templateName: string,\n  templateSourceDir: string,\n): TemplateConfig {\n  const configFilePath = path.resolve(\n    templateSourceDir,\n    'node_modules',\n    templateName,\n    'template.config.js',\n  );\n\n  logger.debug(`Getting config from ${configFilePath}`);\n  if (!fs.existsSync(configFilePath)) {\n    throw new CLIError(\n      `Couldn't find the \"${configFilePath} file inside \"${templateName}\" template. Please make sure the template is valid.\n      Read more: ${chalk.underline.dim(\n        'https://github.com/react-native-community/cli/blob/master/docs/init.md#creating-custom-template',\n      )}`,\n    );\n  }\n  return require(configFilePath);\n}\n\nexport async function copyTemplate(\n  templateName: string,\n  templateDir: string,\n  templateSourceDir: string,\n) {\n  const templatePath = path.resolve(\n    templateSourceDir,\n    'node_modules',\n    templateName,\n    templateDir,\n  );\n\n  logger.debug(`Copying template from ${templatePath}`);\n  let regexStr = path.resolve(templatePath, 'node_modules');\n  await copyFiles(templatePath, process.cwd(), {\n    exclude: [new RegExp(replacePathSepForRegex(regexStr))],\n  });\n}\n\nexport function executePostInitScript(\n  templateName: string,\n  postInitScript: string,\n  templateSourceDir: string,\n) {\n  const scriptPath = path.resolve(\n    templateSourceDir,\n    'node_modules',\n    templateName,\n    postInitScript,\n  );\n\n  logger.debug(`Executing post init script located ${scriptPath}`);\n\n  return execa(scriptPath, {stdio: 'inherit'});\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAAA;AAAA;AASnB,eAAeA,sBAAsB,CAC1CC,YAAoB,EACpBC,IAAY,EACZC,GAAa,EACb;EACAC,kBAAM,CAACC,KAAK,CAAE,4BAA2BJ,YAAa,EAAC,CAAC;EAExD,MAAMK,cAAc,CAACC,IAAI,CAAC;IACxBC,UAAU,EAAE,CAACL,GAAG;IAChBM,MAAM,EAAE,IAAI;IACZP;EACF,CAAC,CAAC;EAEF,OAAOI,cAAc,CAACI,OAAO,CAAC,CAACT,YAAY,CAAC,EAAE;IAC5CO,UAAU,EAAE,CAACL,GAAG;IAChBM,MAAM,EAAE,IAAI;IACZP;EACF,CAAC,CAAC;AACJ;AAEO,SAASS,iBAAiB,CAC/BV,YAAoB,EACpBW,iBAAyB,EACT;EAChB,MAAMC,cAAc,GAAGC,eAAI,CAACC,OAAO,CACjCH,iBAAiB,EACjB,cAAc,EACdX,YAAY,EACZ,oBAAoB,CACrB;EAEDG,kBAAM,CAACC,KAAK,CAAE,uBAAsBQ,cAAe,EAAC,CAAC;EACrD,IAAI,CAACG,aAAE,CAACC,UAAU,CAACJ,cAAc,CAAC,EAAE;IAClC,MAAM,KAAIK,oBAAQ,EACf,sBAAqBL,cAAe,iBAAgBZ,YAAa;AACxE,mBAAmBkB,gBAAK,CAACC,SAAS,CAACC,GAAG,CAC9B,iGAAiG,CACjG,EAAC,CACJ;EACH;EACA,OAAOC,OAAO,CAACT,cAAc,CAAC;AAChC;AAEO,eAAeU,YAAY,CAChCtB,YAAoB,EACpBuB,WAAmB,EACnBZ,iBAAyB,EACzB;EACA,MAAMa,YAAY,GAAGX,eAAI,CAACC,OAAO,CAC/BH,iBAAiB,EACjB,cAAc,EACdX,YAAY,EACZuB,WAAW,CACZ;EAEDpB,kBAAM,CAACC,KAAK,CAAE,yBAAwBoB,YAAa,EAAC,CAAC;EACrD,IAAIC,QAAQ,GAAGZ,eAAI,CAACC,OAAO,CAACU,YAAY,EAAE,cAAc,CAAC;EACzD,MAAM,IAAAE,kBAAS,EAACF,YAAY,EAAEG,OAAO,CAACC,GAAG,EAAE,EAAE;IAC3CC,OAAO,EAAE,CAAC,IAAIC,MAAM,CAAC,IAAAC,+BAAsB,EAACN,QAAQ,CAAC,CAAC;EACxD,CAAC,CAAC;AACJ;AAEO,SAASO,qBAAqB,CACnChC,YAAoB,EACpBiC,cAAsB,EACtBtB,iBAAyB,EACzB;EACA,MAAMuB,UAAU,GAAGrB,eAAI,CAACC,OAAO,CAC7BH,iBAAiB,EACjB,cAAc,EACdX,YAAY,EACZiC,cAAc,CACf;EAED9B,kBAAM,CAACC,KAAK,CAAE,sCAAqC8B,UAAW,EAAC,CAAC;EAEhE,OAAO,IAAAC,gBAAK,EAACD,UAAU,EAAE;IAACE,KAAK,EAAE;EAAS,CAAC,CAAC;AAC9C"}