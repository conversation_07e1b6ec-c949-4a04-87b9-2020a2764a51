{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/index.ts"], "sourcesContent": ["import { types as t, type NodePath, type Visitor } from \"@babel/core\";\n\nfunction isNameOrLength(key: t.Node): boolean {\n  if (t.isIdentifier(key)) {\n    return key.name === \"name\" || key.name === \"length\";\n  }\n  if (t.isStringLiteral(key)) {\n    return key.value === \"name\" || key.value === \"length\";\n  }\n  return false;\n}\n\nfunction isStaticFieldWithValue(\n  node: t.Node,\n): node is t.ClassProperty | t.ClassPrivateProperty {\n  return (\n    (t.isClassProperty(node) || t.isClassPrivateProperty(node)) &&\n    node.static &&\n    !!node.value\n  );\n}\n\nconst hasReferenceVisitor: Visitor<{ name: string; ref: () => void }> = {\n  ReferencedIdentifier(path, state) {\n    if (path.node.name === state.name) {\n      state.ref();\n      path.stop();\n    }\n  },\n  Scope(path, { name }) {\n    if (path.scope.hasOwnBinding(name)) {\n      path.skip();\n    }\n  },\n};\n\nfunction isReferenceOrThis(node: t.Node, name?: string) {\n  return t.isThisExpression(node) || (name && t.isIdentifier(node, { name }));\n}\n\nconst hasReferenceOrThisVisitor: Visitor<{ name?: string; ref: () => void }> = {\n  \"ThisExpression|ReferencedIdentifier\"(path, state) {\n    if (isReferenceOrThis(path.node, state.name)) {\n      state.ref();\n      path.stop();\n    }\n  },\n  FunctionParent(path, state) {\n    if (path.isArrowFunctionExpression()) return;\n    if (state.name && !path.scope.hasOwnBinding(state.name)) {\n      path.traverse(hasReferenceVisitor, state);\n    }\n    path.skip();\n    if (path.isMethod()) {\n      if (\n        process.env.BABEL_8_BREAKING ||\n        USE_ESM ||\n        IS_STANDALONE ||\n        path.requeueComputedKeyAndDecorators\n      ) {\n        path.requeueComputedKeyAndDecorators();\n      } else {\n        // eslint-disable-next-line no-restricted-globals\n        require(\"@babel/traverse\").NodePath.prototype.requeueComputedKeyAndDecorators.call(\n          path,\n        );\n      }\n    }\n  },\n};\n\ntype ClassElementWithComputedKeySupport = Extract<\n  t.ClassBody[\"body\"][number],\n  { computed?: boolean }\n>;\n\n/**\n * This function returns an array containing the indexes of class elements\n * that might be affected by https://crbug.com/v8/12421 bug.\n *\n * This bug affects public static class fields that have the same name as an\n * existing non-writable property with the same name. This usually happens when\n * the static field is named 'length' or 'name', since it clashes with the\n * predefined fn.length and fn.name properties. We must also compile static\n * fields with computed key, because they might end up being named 'length' or\n * 'name'.\n *\n * However, this bug can potentially affect public static fields with any name.\n * Consider this example:\n *\n *     class A {\n *       static {\n *         Object.defineProperty(A, \"readonly\", {\n *           value: 1,\n *           writable: false,\n *           configurable: true\n *         })\n *       }\n *\n *       static readonly = 2;\n *     }\n *\n * When initializing the 'static readonly' field, the class already has a\n * non-writable property named 'readonly' and thus V8 9.7 incorrectly throws.\n *\n * To avoid unconditionally compiling every public static field, we track how\n * the class is referenced during definition & static evaluation: any side\n * effect after a reference to the class can potentially define a non-writable\n * conficting property, so subsequent public static fields must be compiled.\n * The class could be referenced using the class name in computed keys, which\n * run before static fields, or using either the class name or 'this' in static\n * fields (both public and private) and static blocks.\n *\n * We don't need to check if computed keys referencing the class have any side\n * effect, because during the computed keys evaluation the internal class\n * binding is in TDZ. However, the first side effect in a static field/block\n * could have access to a function defined in a computed key that modifies the\n * class.\n *\n * This logic is already quite complex, so we assume that static blocks always\n * have side effects and reference the class (the reason to use them is to\n * perform additional initialization logic on the class anyway), so that we do\n * not have to check their contents.\n */\nexport function getPotentiallyBuggyFieldsIndexes(path: NodePath<t.Class>) {\n  const buggyPublicStaticFieldsIndexes: number[] = [];\n\n  let classReferenced = false;\n  const className = path.node.id?.name;\n\n  const hasReferenceState = {\n    name: className,\n    ref: () => (classReferenced = true),\n  };\n\n  if (className) {\n    for (const el of path.get(\"body.body\")) {\n      if ((el.node as ClassElementWithComputedKeySupport).computed) {\n        // Since .traverse skips the top-level node, it doesn't detect\n        // a reference happening immediately:\n        //     class A { [A]() {} }\n        // However, it's a TDZ error so it's ok not to consider this case.\n        (el as NodePath<ClassElementWithComputedKeySupport>)\n          .get(\"key\")\n          .traverse(hasReferenceVisitor, hasReferenceState);\n\n        if (classReferenced) break;\n      }\n    }\n  }\n\n  let nextPotentiallyBuggy = false;\n\n  const { body } = path.node.body;\n  for (let i = 0; i < body.length; i++) {\n    const node = body[i];\n\n    if (!nextPotentiallyBuggy) {\n      if (t.isStaticBlock(node)) {\n        classReferenced = true;\n        nextPotentiallyBuggy = true;\n      } else if (isStaticFieldWithValue(node)) {\n        if (!classReferenced) {\n          if (isReferenceOrThis(node.value, className)) {\n            classReferenced = true;\n          } else {\n            (\n              path.get(`body.body.${i}.value`) as NodePath<t.Expression>\n            ).traverse(hasReferenceOrThisVisitor, hasReferenceState);\n          }\n        }\n\n        if (classReferenced) {\n          nextPotentiallyBuggy = !path.scope.isPure(node.value);\n        }\n      }\n    }\n\n    if (\n      t.isClassProperty(node, { static: true }) &&\n      (nextPotentiallyBuggy || node.computed || isNameOrLength(node.key))\n    ) {\n      buggyPublicStaticFieldsIndexes.push(i);\n    }\n  }\n\n  return buggyPublicStaticFieldsIndexes;\n}\n\nexport function getNameOrLengthStaticFieldsIndexes(path: NodePath<t.Class>) {\n  const indexes: number[] = [];\n\n  const { body } = path.node.body;\n  for (let i = 0; i < body.length; i++) {\n    const node = body[i];\n    if (\n      t.isClassProperty(node, { static: true, computed: false }) &&\n      isNameOrLength(node.key)\n    ) {\n      indexes.push(i);\n    }\n  }\n\n  return indexes;\n}\n\ntype Range = [start: number, end: number];\n\n/**\n * Converts a sorted list of numbers into a list of (inclusive-exclusive)\n * ranges representing the same numbers.\n *\n * @example toRanges([1, 3, 4, 5, 8, 9]) -> [[1, 2], [3, 6], [8, 10]]\n */\nexport function toRanges(nums: number[]): Range[] {\n  const ranges: Range[] = [];\n\n  if (nums.length === 0) return ranges;\n\n  let start = nums[0];\n  let end = start + 1;\n  for (let i = 1; i < nums.length; i++) {\n    if (nums[i] <= nums[i - 1]) {\n      throw new Error(\"Internal Babel error: nums must be in ascending order\");\n    }\n    if (nums[i] === end) {\n      end++;\n    } else {\n      ranges.push([start, end]);\n      start = nums[i];\n      end = start + 1;\n    }\n  }\n  ranges.push([start, end]);\n\n  return ranges;\n}\n", "import type { Node<PERSON><PERSON>, Scope, PluginPass, File } from \"@babel/core\";\nimport { types as t } from \"@babel/core\";\nimport { declare } from \"@babel/helper-plugin-utils\";\n\nimport {\n  getPotentiallyBuggyFieldsIndexes,\n  getNameOrLengthStaticFieldsIndexes,\n  toRanges,\n} from \"./util.ts\";\n\nfunction buildFieldsReplacement(\n  fields: t.ClassProperty[],\n  scope: Scope,\n  file: File,\n) {\n  return t.staticBlock(\n    fields.map(field => {\n      const key =\n        field.computed || !t.isIdentifier(field.key)\n          ? field.key\n          : t.stringLiteral(field.key.name);\n\n      return t.expressionStatement(\n        t.callExpression(file.addHelper(\"defineProperty\"), [\n          t.thisExpression(),\n          key,\n          field.value || scope.buildUndefinedNode(),\n        ]),\n      );\n    }),\n  );\n}\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const setPublicClassFields = api.assumption(\"setPublicClassFields\");\n\n  return {\n    name: \"bugfix-v8-static-class-fields-redefine-readonly\",\n\n    visitor: {\n      Class(this: PluginPass, path: NodePath<t.Class>) {\n        const ranges = toRanges(\n          setPublicClassFields\n            ? getNameOrLengthStaticFieldsIndexes(path)\n            : getPotentiallyBuggyFieldsIndexes(path),\n        );\n\n        for (let i = ranges.length - 1; i >= 0; i--) {\n          const [start, end] = ranges[i];\n\n          const startPath = path.get(\"body.body\")[start];\n\n          startPath.replaceWith(\n            buildFieldsReplacement(\n              path.node.body.body.slice(start, end) as t.ClassProperty[],\n              path.scope,\n              this.file,\n            ),\n          );\n\n          for (let j = end - 1; j > start; j--) {\n            path.get(\"body.body\")[j].remove();\n          }\n        }\n      },\n    },\n  };\n});\n"], "names": ["isNameOrLength", "key", "t", "isIdentifier", "name", "isStringLiteral", "value", "isStaticFieldWithValue", "node", "isClassProperty", "isClassPrivateProperty", "static", "hasReferenceVisitor", "ReferencedIdentifier", "path", "state", "ref", "stop", "<PERSON><PERSON>", "scope", "hasOwnBinding", "skip", "isReferenceOrThis", "isThisExpression", "hasReferenceOrThisVisitor", "ThisExpression|ReferencedIdentifier", "FunctionParent", "isArrowFunctionExpression", "traverse", "isMethod", "requeueComputedKeyAndDecorators", "require", "NodePath", "prototype", "call", "getPotentiallyBuggyFieldsIndexes", "_path$node$id", "buggyPublicStaticFieldsIndexes", "classReferenced", "className", "id", "hasReferenceState", "el", "get", "computed", "nextPotentiallyBuggy", "body", "i", "length", "isStaticBlock", "isPure", "push", "getNameOrLengthStaticFieldsIndexes", "indexes", "to<PERSON><PERSON><PERSON>", "nums", "ranges", "start", "end", "Error", "buildFieldsReplacement", "fields", "file", "staticBlock", "map", "field", "stringLiteral", "expressionStatement", "callExpression", "addHelper", "thisExpression", "buildUndefinedNode", "declare", "api", "assertVersion", "setPublicClassFields", "assumption", "visitor", "Class", "startPath", "replaceWith", "slice", "j", "remove"], "mappings": ";;;;;;;AAEA,SAASA,cAAcA,CAACC,GAAW,EAAW;AAC5C,EAAA,IAAIC,UAAC,CAACC,YAAY,CAACF,GAAG,CAAC,EAAE;IACvB,OAAOA,GAAG,CAACG,IAAI,KAAK,MAAM,IAAIH,GAAG,CAACG,IAAI,KAAK,QAAQ,CAAA;AACrD,GAAA;AACA,EAAA,IAAIF,UAAC,CAACG,eAAe,CAACJ,GAAG,CAAC,EAAE;IAC1B,OAAOA,GAAG,CAACK,KAAK,KAAK,MAAM,IAAIL,GAAG,CAACK,KAAK,KAAK,QAAQ,CAAA;AACvD,GAAA;AACA,EAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAEA,SAASC,sBAAsBA,CAC7BC,IAAY,EACsC;EAClD,OACE,CAACN,UAAC,CAACO,eAAe,CAACD,IAAI,CAAC,IAAIN,UAAC,CAACQ,sBAAsB,CAACF,IAAI,CAAC,KAC1DA,IAAI,CAACG,MAAM,IACX,CAAC,CAACH,IAAI,CAACF,KAAK,CAAA;AAEhB,CAAA;AAEA,MAAMM,mBAA+D,GAAG;AACtEC,EAAAA,oBAAoBA,CAACC,IAAI,EAAEC,KAAK,EAAE;IAChC,IAAID,IAAI,CAACN,IAAI,CAACJ,IAAI,KAAKW,KAAK,CAACX,IAAI,EAAE;MACjCW,KAAK,CAACC,GAAG,EAAE,CAAA;MACXF,IAAI,CAACG,IAAI,EAAE,CAAA;AACb,KAAA;GACD;EACDC,KAAKA,CAACJ,IAAI,EAAE;AAAEV,IAAAA,IAAAA;AAAK,GAAC,EAAE;IACpB,IAAIU,IAAI,CAACK,KAAK,CAACC,aAAa,CAAChB,IAAI,CAAC,EAAE;MAClCU,IAAI,CAACO,IAAI,EAAE,CAAA;AACb,KAAA;AACF,GAAA;AACF,CAAC,CAAA;AAED,SAASC,iBAAiBA,CAACd,IAAY,EAAEJ,IAAa,EAAE;AACtD,EAAA,OAAOF,UAAC,CAACqB,gBAAgB,CAACf,IAAI,CAAC,IAAKJ,IAAI,IAAIF,UAAC,CAACC,YAAY,CAACK,IAAI,EAAE;AAAEJ,IAAAA,IAAAA;AAAK,GAAC,CAAE,CAAA;AAC7E,CAAA;AAEA,MAAMoB,yBAAsE,GAAG;AAC7E,EAAA,qCAAqCC,CAACX,IAAI,EAAEC,KAAK,EAAE;IACjD,IAAIO,iBAAiB,CAACR,IAAI,CAACN,IAAI,EAAEO,KAAK,CAACX,IAAI,CAAC,EAAE;MAC5CW,KAAK,CAACC,GAAG,EAAE,CAAA;MACXF,IAAI,CAACG,IAAI,EAAE,CAAA;AACb,KAAA;GACD;AACDS,EAAAA,cAAcA,CAACZ,IAAI,EAAEC,KAAK,EAAE;AAC1B,IAAA,IAAID,IAAI,CAACa,yBAAyB,EAAE,EAAE,OAAA;AACtC,IAAA,IAAIZ,KAAK,CAACX,IAAI,IAAI,CAACU,IAAI,CAACK,KAAK,CAACC,aAAa,CAACL,KAAK,CAACX,IAAI,CAAC,EAAE;AACvDU,MAAAA,IAAI,CAACc,QAAQ,CAAChB,mBAAmB,EAAEG,KAAK,CAAC,CAAA;AAC3C,KAAA;IACAD,IAAI,CAACO,IAAI,EAAE,CAAA;AACX,IAAA,IAAIP,IAAI,CAACe,QAAQ,EAAE,EAAE;MACnB,IAIEf,IAAI,CAACgB,+BAA+B,EACpC;QACAhB,IAAI,CAACgB,+BAA+B,EAAE,CAAA;AACxC,OAAC,MAAM;AAELC,QAAAA,OAAO,CAAC,iBAAiB,CAAC,CAACC,QAAQ,CAACC,SAAS,CAACH,+BAA+B,CAACI,IAAI,CAChFpB,IACF,CAAC,CAAA;AACH,OAAA;AACF,KAAA;AACF,GAAA;AACF,CAAC,CAAA;AAuDM,SAASqB,gCAAgCA,CAACrB,IAAuB,EAAE;AAAA,EAAA,IAAAsB,aAAA,CAAA;EACxE,MAAMC,8BAAwC,GAAG,EAAE,CAAA;EAEnD,IAAIC,eAAe,GAAG,KAAK,CAAA;AAC3B,EAAA,MAAMC,SAAS,GAAA,CAAAH,aAAA,GAAGtB,IAAI,CAACN,IAAI,CAACgC,EAAE,KAAA,IAAA,GAAA,KAAA,CAAA,GAAZJ,aAAA,CAAchC,IAAI,CAAA;AAEpC,EAAA,MAAMqC,iBAAiB,GAAG;AACxBrC,IAAAA,IAAI,EAAEmC,SAAS;AACfvB,IAAAA,GAAG,EAAEA,MAAOsB,eAAe,GAAG,IAAA;GAC/B,CAAA;AAED,EAAA,IAAIC,SAAS,EAAE;IACb,KAAK,MAAMG,EAAE,IAAI5B,IAAI,CAAC6B,GAAG,CAAC,WAAW,CAAC,EAAE;AACtC,MAAA,IAAKD,EAAE,CAAClC,IAAI,CAAwCoC,QAAQ,EAAE;QAK3DF,EAAE,CACAC,GAAG,CAAC,KAAK,CAAC,CACVf,QAAQ,CAAChB,mBAAmB,EAAE6B,iBAAiB,CAAC,CAAA;AAEnD,QAAA,IAAIH,eAAe,EAAE,MAAA;AACvB,OAAA;AACF,KAAA;AACF,GAAA;EAEA,IAAIO,oBAAoB,GAAG,KAAK,CAAA;EAEhC,MAAM;AAAEC,IAAAA,IAAAA;AAAK,GAAC,GAAGhC,IAAI,CAACN,IAAI,CAACsC,IAAI,CAAA;AAC/B,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;AACpC,IAAA,MAAMvC,IAAI,GAAGsC,IAAI,CAACC,CAAC,CAAC,CAAA;IAEpB,IAAI,CAACF,oBAAoB,EAAE;AACzB,MAAA,IAAI3C,UAAC,CAAC+C,aAAa,CAACzC,IAAI,CAAC,EAAE;AACzB8B,QAAAA,eAAe,GAAG,IAAI,CAAA;AACtBO,QAAAA,oBAAoB,GAAG,IAAI,CAAA;AAC7B,OAAC,MAAM,IAAItC,sBAAsB,CAACC,IAAI,CAAC,EAAE;QACvC,IAAI,CAAC8B,eAAe,EAAE;UACpB,IAAIhB,iBAAiB,CAACd,IAAI,CAACF,KAAK,EAAEiC,SAAS,CAAC,EAAE;AAC5CD,YAAAA,eAAe,GAAG,IAAI,CAAA;AACxB,WAAC,MAAM;AAEHxB,YAAAA,IAAI,CAAC6B,GAAG,CAAC,CAAA,UAAA,EAAaI,CAAC,CAAA,MAAA,CAAQ,CAAC,CAChCnB,QAAQ,CAACJ,yBAAyB,EAAEiB,iBAAiB,CAAC,CAAA;AAC1D,WAAA;AACF,SAAA;AAEA,QAAA,IAAIH,eAAe,EAAE;UACnBO,oBAAoB,GAAG,CAAC/B,IAAI,CAACK,KAAK,CAAC+B,MAAM,CAAC1C,IAAI,CAACF,KAAK,CAAC,CAAA;AACvD,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,IACEJ,UAAC,CAACO,eAAe,CAACD,IAAI,EAAE;AAAEG,MAAAA,MAAM,EAAE,IAAA;AAAK,KAAC,CAAC,KACxCkC,oBAAoB,IAAIrC,IAAI,CAACoC,QAAQ,IAAI5C,cAAc,CAACQ,IAAI,CAACP,GAAG,CAAC,CAAC,EACnE;AACAoC,MAAAA,8BAA8B,CAACc,IAAI,CAACJ,CAAC,CAAC,CAAA;AACxC,KAAA;AACF,GAAA;AAEA,EAAA,OAAOV,8BAA8B,CAAA;AACvC,CAAA;AAEO,SAASe,kCAAkCA,CAACtC,IAAuB,EAAE;EAC1E,MAAMuC,OAAiB,GAAG,EAAE,CAAA;EAE5B,MAAM;AAAEP,IAAAA,IAAAA;AAAK,GAAC,GAAGhC,IAAI,CAACN,IAAI,CAACsC,IAAI,CAAA;AAC/B,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;AACpC,IAAA,MAAMvC,IAAI,GAAGsC,IAAI,CAACC,CAAC,CAAC,CAAA;AACpB,IAAA,IACE7C,UAAC,CAACO,eAAe,CAACD,IAAI,EAAE;AAAEG,MAAAA,MAAM,EAAE,IAAI;AAAEiC,MAAAA,QAAQ,EAAE,KAAA;KAAO,CAAC,IAC1D5C,cAAc,CAACQ,IAAI,CAACP,GAAG,CAAC,EACxB;AACAoD,MAAAA,OAAO,CAACF,IAAI,CAACJ,CAAC,CAAC,CAAA;AACjB,KAAA;AACF,GAAA;AAEA,EAAA,OAAOM,OAAO,CAAA;AAChB,CAAA;AAUO,SAASC,QAAQA,CAACC,IAAc,EAAW;EAChD,MAAMC,MAAe,GAAG,EAAE,CAAA;AAE1B,EAAA,IAAID,IAAI,CAACP,MAAM,KAAK,CAAC,EAAE,OAAOQ,MAAM,CAAA;AAEpC,EAAA,IAAIC,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,CAAA;AACnB,EAAA,IAAIG,GAAG,GAAGD,KAAK,GAAG,CAAC,CAAA;AACnB,EAAA,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,IAAI,CAACP,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIQ,IAAI,CAACR,CAAC,CAAC,IAAIQ,IAAI,CAACR,CAAC,GAAG,CAAC,CAAC,EAAE;AAC1B,MAAA,MAAM,IAAIY,KAAK,CAAC,uDAAuD,CAAC,CAAA;AAC1E,KAAA;AACA,IAAA,IAAIJ,IAAI,CAACR,CAAC,CAAC,KAAKW,GAAG,EAAE;AACnBA,MAAAA,GAAG,EAAE,CAAA;AACP,KAAC,MAAM;MACLF,MAAM,CAACL,IAAI,CAAC,CAACM,KAAK,EAAEC,GAAG,CAAC,CAAC,CAAA;AACzBD,MAAAA,KAAK,GAAGF,IAAI,CAACR,CAAC,CAAC,CAAA;MACfW,GAAG,GAAGD,KAAK,GAAG,CAAC,CAAA;AACjB,KAAA;AACF,GAAA;EACAD,MAAM,CAACL,IAAI,CAAC,CAACM,KAAK,EAAEC,GAAG,CAAC,CAAC,CAAA;AAEzB,EAAA,OAAOF,MAAM,CAAA;AACf;;AClOA,SAASI,sBAAsBA,CAC7BC,MAAyB,EACzB1C,KAAY,EACZ2C,IAAU,EACV;EACA,OAAO5D,UAAC,CAAC6D,WAAW,CAClBF,MAAM,CAACG,GAAG,CAACC,KAAK,IAAI;AAClB,IAAA,MAAMhE,GAAG,GACPgE,KAAK,CAACrB,QAAQ,IAAI,CAAC1C,UAAC,CAACC,YAAY,CAAC8D,KAAK,CAAChE,GAAG,CAAC,GACxCgE,KAAK,CAAChE,GAAG,GACTC,UAAC,CAACgE,aAAa,CAACD,KAAK,CAAChE,GAAG,CAACG,IAAI,CAAC,CAAA;AAErC,IAAA,OAAOF,UAAC,CAACiE,mBAAmB,CAC1BjE,UAAC,CAACkE,cAAc,CAACN,IAAI,CAACO,SAAS,CAAC,gBAAgB,CAAC,EAAE,CACjDnE,UAAC,CAACoE,cAAc,EAAE,EAClBrE,GAAG,EACHgE,KAAK,CAAC3D,KAAK,IAAIa,KAAK,CAACoD,kBAAkB,EAAE,CAC1C,CACH,CAAC,CAAA;AACH,GAAC,CACH,CAAC,CAAA;AACH,CAAA;AAEA,YAAeC,yBAAO,CAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAA,sCAAoB,CAAC,CAAA;AAEtC,EAAA,MAAMC,oBAAoB,GAAGF,GAAG,CAACG,UAAU,CAAC,sBAAsB,CAAC,CAAA;EAEnE,OAAO;AACLxE,IAAAA,IAAI,EAAE,iDAAiD;AAEvDyE,IAAAA,OAAO,EAAE;MACPC,KAAKA,CAAmBhE,IAAuB,EAAE;AAC/C,QAAA,MAAM0C,MAAM,GAAGF,QAAQ,CACrBqB,oBAAoB,GAChBvB,kCAAkC,CAACtC,IAAI,CAAC,GACxCqB,gCAAgC,CAACrB,IAAI,CAC3C,CAAC,CAAA;AAED,QAAA,KAAK,IAAIiC,CAAC,GAAGS,MAAM,CAACR,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3C,MAAM,CAACU,KAAK,EAAEC,GAAG,CAAC,GAAGF,MAAM,CAACT,CAAC,CAAC,CAAA;UAE9B,MAAMgC,SAAS,GAAGjE,IAAI,CAAC6B,GAAG,CAAC,WAAW,CAAC,CAACc,KAAK,CAAC,CAAA;AAE9CsB,UAAAA,SAAS,CAACC,WAAW,CACnBpB,sBAAsB,CACpB9C,IAAI,CAACN,IAAI,CAACsC,IAAI,CAACA,IAAI,CAACmC,KAAK,CAACxB,KAAK,EAAEC,GAAG,CAAC,EACrC5C,IAAI,CAACK,KAAK,EACV,IAAI,CAAC2C,IACP,CACF,CAAC,CAAA;AAED,UAAA,KAAK,IAAIoB,CAAC,GAAGxB,GAAG,GAAG,CAAC,EAAEwB,CAAC,GAAGzB,KAAK,EAAEyB,CAAC,EAAE,EAAE;YACpCpE,IAAI,CAAC6B,GAAG,CAAC,WAAW,CAAC,CAACuC,CAAC,CAAC,CAACC,MAAM,EAAE,CAAA;AACnC,WAAA;AACF,SAAA;AACF,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;"}