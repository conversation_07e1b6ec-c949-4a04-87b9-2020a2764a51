{"version": 3, "sources": ["../src/errors.ts"], "names": ["CodeError", "Error", "constructor", "code", "message", "ERR_ANDROID_UNPROCESSABLE_PID", "ERR_ANDROID_CANNOT_GET_APP_PID", "ERR_ANDROID_CANNOT_CLEAN_LOGCAT_BUFFER", "ERR_ANDROID_CANNOT_START_LOGCAT", "ERR_IOS_CANNOT_LIST_SIMULATORS", "ERR_IOS_NO_SIMULATORS_BOOTED", "ERR_IOS_CANNOT_START_SYSLOG"], "mappings": ";;;;;;;;;AAAO,MAAMA,SAAN,SAAwBC,KAAxB,CAA8B;AAGnCC,EAAAA,WAAW,CAACC,IAAD,EAAeC,OAAf,EAAiC;AAC1C,UAAMA,OAAN;;AAD0C;;AAE1C,SAAKD,IAAL,GAAYA,IAAZ;AACD;;AANkC;;;AAS9B,MAAME,6BAA6B,GAAG,+BAAtC;;AACA,MAAMC,8BAA8B,GAAG,gCAAvC;;AACA,MAAMC,sCAAsC,GACjD,wCADK;;AAEA,MAAMC,+BAA+B,GAC1C,iCADK;;AAGA,MAAMC,8BAA8B,GAAG,gCAAvC;;AACA,MAAMC,4BAA4B,GAAG,8BAArC;;AACA,MAAMC,2BAA2B,GAAG,6BAApC", "sourcesContent": ["export class CodeError extends Error {\n  code: string;\n\n  constructor(code: string, message?: string) {\n    super(message);\n    this.code = code;\n  }\n}\n\nexport const ERR_ANDROID_UNPROCESSABLE_PID = 'ERR_ANDROID_UNPROCESSABLE_PID';\nexport const ERR_ANDROID_CANNOT_GET_APP_PID = 'ERR_ANDROID_CANNOT_GET_APP_PID';\nexport const ERR_ANDROID_CANNOT_CLEAN_LOGCAT_BUFFER =\n  'ERR_ANDROID_CANNOT_CLEAN_LOGCAT_BUFFER';\nexport const ERR_ANDROID_CANNOT_START_LOGCAT =\n  'ERR_ANDROID_CANNOT_START_LOGCAT';\n\nexport const ERR_IOS_CANNOT_LIST_SIMULATORS = 'ERR_IOS_CANNOT_LIST_SIMULATORS';\nexport const ERR_IOS_NO_SIMULATORS_BOOTED = 'ERR_IOS_NO_SIMULATORS_BOOTED';\nexport const ERR_IOS_CANNOT_START_SYSLOG = 'ERR_IOS_CANNOT_START_SYSLOG';\n"], "file": "errors.js"}