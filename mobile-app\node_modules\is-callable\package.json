{"name": "is-callable", "version": "1.2.7", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Is this JS value callable? Works with Functions and GeneratorFunctions, despite ES6 @@toStringTag.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "test": "npm run tests-only --", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs ."}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-callable.git"}, "keywords": ["Function", "function", "callable", "generator", "generator function", "arrow", "arrow function", "ES6", "toStringTag", "@@toStringTag"], "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "auto-changelog": "^2.4.0", "available-typed-arrays": "^1.0.5", "eclint": "^2.8.1", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-tostringtag": "^1.0.0", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.2", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0", "tape": "^5.6.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "v1.2.5"}, "publishConfig": {"ignore": [".github/workflows"]}}