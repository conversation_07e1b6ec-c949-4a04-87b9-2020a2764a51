var e,t,n=Object.defineProperty,r=Object.defineProperties,i=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,u=(e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var n in t||(t={}))o.call(t,n)&&u(e,n,t[n]);if(a)for(var n of a(t))s.call(t,n)&&u(e,n,t[n]);return e},d=(e,t)=>r(e,i(t)),l=(e,t)=>{var n={};for(var r in e)o.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&a)for(var r of a(e))t.indexOf(r)<0&&s.call(e,r)&&(n[r]=e[r]);return n};(t=e||(e={})).uninitialized="uninitialized",t.pending="pending",t.fulfilled="fulfilled",t.rejected="rejected";var p=e=>[].concat(...e);import{isPlainObject as f}from"@reduxjs/toolkit";var y=f;function m(e,t){if(e===t||!(y(e)&&y(t)||Array.isArray(e)&&Array.isArray(t)))return t;const n=Object.keys(t),r=Object.keys(e);let i=n.length===r.length;const a=Array.isArray(t)?[]:{};for(const r of n)a[r]=m(e[r],t[r]),i&&(i=e[r]===a[r]);return i?e:a}import{isPlainObject as h}from"@reduxjs/toolkit";var g=(...e)=>fetch(...e),v=e=>e.status>=200&&e.status<=299,b=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function q(e){if(!h(e))return e;const t=c({},e);for(const[e,n]of Object.entries(t))void 0===n&&delete t[e];return t}function S(e={}){var t=e,{baseUrl:n,prepareHeaders:r=(e=>e),fetchFn:i=g,paramsSerializer:a,isJsonContentType:o=b,jsonContentType:s="application/json",jsonReplacer:u,timeout:p,responseHandler:f,validateStatus:y}=t,m=l(t,["baseUrl","prepareHeaders","fetchFn","paramsSerializer","isJsonContentType","jsonContentType","jsonReplacer","timeout","responseHandler","validateStatus"]);return"undefined"==typeof fetch&&i===g&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(e,t)=>{const{signal:g,getState:b,extra:R,endpoint:O,forced:T,type:w}=t;let j,A="string"==typeof e?{url:e}:e,{url:k,headers:x=new Headers(m.headers),params:Q,responseHandler:I=(null!=f?f:"json"),validateStatus:C=(null!=y?y:v),timeout:P=p}=A,M=l(A,["url","headers","params","responseHandler","validateStatus","timeout"]),D=c(d(c({},m),{signal:g}),M);x=new Headers(q(x)),D.headers=await r(x,{getState:b,extra:R,endpoint:O,forced:T,type:w})||x;const N=e=>"object"==typeof e&&(h(e)||Array.isArray(e)||"function"==typeof e.toJSON);if(!D.headers.has("content-type")&&N(D.body)&&D.headers.set("content-type",s),N(D.body)&&o(D.headers)&&(D.body=JSON.stringify(D.body,u)),Q){const e=~k.indexOf("?")?"&":"?";k+=e+(a?a(Q):new URLSearchParams(q(Q)))}k=function(e,t){if(!e)return t;if(!t)return e;if(function(e){return new RegExp("(^|:)//").test(e)}(t))return t;const n=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=(e=>e.replace(/\/$/,""))(e),`${e}${n}${t=(e=>e.replace(/^\//,""))(t)}`}(n,k);const K=new Request(k,D);j={request:new Request(k,D)};let E,F=!1,z=P&&setTimeout((()=>{F=!0,t.abort()}),P);try{E=await i(K)}catch(e){return{error:{status:F?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(e)},meta:j}}finally{z&&clearTimeout(z)}const _=E.clone();let $;j.response=_;let U="";try{let e;if(await Promise.all([S(E,I).then((e=>$=e),(t=>e=t)),_.text().then((e=>U=e),(()=>{}))]),e)throw e}catch(e){return{error:{status:"PARSING_ERROR",originalStatus:E.status,data:U,error:String(e)},meta:j}}return C(E,$)?{data:$,meta:j}:{error:{status:E.status,data:$},meta:j}};async function S(e,t){if("function"==typeof t)return t(e);if("content-type"===t&&(t=o(e.headers)?"json":"text"),"json"===t){const t=await e.text();return t.length?JSON.parse(t):null}return e.text()}}var R=class{constructor(e,t){this.value=e,this.meta=t}};async function O(e=0,t=5){const n=Math.min(e,t),r=~~((Math.random()+.4)*(300<<n));await new Promise((e=>setTimeout((t=>e(t)),r)))}var T={},w=Object.assign(((e,t)=>async(n,r,i)=>{const a=[5,(t||T).maxRetries,(i||T).maxRetries].filter((e=>void 0!==e)),[o]=a.slice(-1),s=c(c({maxRetries:o,backoff:O,retryCondition:(e,t,{attempt:n})=>n<=o},t),i);let u=0;for(;;)try{const t=await e(n,r,i);if(t.error)throw new R(t);return t}catch(e){if(u++,e.throwImmediately){if(e instanceof R)return e.value;throw e}if(e instanceof R&&!s.retryCondition(e.value.error,n,{attempt:u,baseQueryApi:r,extraOptions:i}))return e.value;await s.backoff(u,s.maxRetries)}}),{fail:function(e){throw Object.assign(new R({error:e}),{throwImmediately:!0})}});import{createAction as j}from"@reduxjs/toolkit";var A,k,x=j("__rtkq/focused"),Q=j("__rtkq/unfocused"),I=j("__rtkq/online"),C=j("__rtkq/offline"),P=!1;function M(e,t){return t?t(e,{onFocus:x,onFocusLost:Q,onOffline:C,onOnline:I}):function(){const t=()=>e(x()),n=()=>e(I()),r=()=>e(C()),i=()=>{"visible"===window.document.visibilityState?t():e(Q())};return P||"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("visibilitychange",i,!1),window.addEventListener("focus",t,!1),window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),P=!0),()=>{window.removeEventListener("focus",t),window.removeEventListener("visibilitychange",i),window.removeEventListener("online",n),window.removeEventListener("offline",r),P=!1}}()}import{createNextState as D,createSelector as N}from"@reduxjs/toolkit";function K(e){return e.type===A.query}function E(e,t,n,r,i,a){return"function"==typeof e?e(t,n,r,i).map(F).map(a):Array.isArray(e)?e.map(F).map(a):[]}function F(e){return"string"==typeof e?{type:e}:e}(k=A||(A={})).query="query",k.mutation="mutation";import{combineReducers as z,createAction as _,createSlice as $,isAnyOf as U,isFulfilled as L,isRejectedWithValue as W,createNextState as B,prepareAutoBatched as J}from"@reduxjs/toolkit";function H(e){return null!=e}var V=Symbol("forceQueryFn"),G=e=>"function"==typeof e[V];import{isAllOf as Y,isFulfilled as X,isPending as Z,isRejected as ee,isRejectedWithValue as te}from"@reduxjs/toolkit";import{isDraftable as ne,produceWithPatches as re}from"immer";import{createAsyncThunk as ie,SHOULD_AUTOBATCH as ae}from"@reduxjs/toolkit";function oe(e){return e}function se(e,t,n,r){return E(n[e.meta.arg.endpointName][t],X(e)?e.payload:void 0,te(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,r)}import{isDraft as ue}from"immer";import{applyPatches as ce,original as de}from"immer";function le(e,t,n){const r=e[t];r&&n(r)}function pe(e){var t;return null!=(t="arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)?t:e.requestId}function fe(e,t,n){const r=e[pe(t)];r&&n(r)}var ye={},me=Symbol.for("RTKQ/skipToken"),he=me,ge={status:e.uninitialized},ve=D(ge,(()=>{})),be=D(ge,(()=>{}));import{isPlainObject as qe}from"@reduxjs/toolkit";var Se=WeakMap?new WeakMap:void 0,Re=({endpointName:e,queryArgs:t})=>{let n="";const r=null==Se?void 0:Se.get(t);if("string"==typeof r)n=r;else{const e=JSON.stringify(t,((e,t)=>qe(t)?Object.keys(t).sort().reduce(((e,n)=>(e[n]=t[n],e)),{}):t));qe(t)&&(null==Se||Se.set(t,e)),n=e}return`${e}(${n})`};import{nanoid as Oe}from"@reduxjs/toolkit";import{defaultMemoize as Te}from"reselect";function we(...e){return function(t){const n=Te((e=>{var n,r;return null==(r=t.extractRehydrationInfo)?void 0:r.call(t,e,{reducerPath:null!=(n=t.reducerPath)?n:"api"})})),r=d(c({reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1},t),{extractRehydrationInfo:n,serializeQueryArgs(e){let n=Re;if("serializeQueryArgs"in e.endpointDefinition){const t=e.endpointDefinition.serializeQueryArgs;n=e=>{const n=t(e);return"string"==typeof n?n:Re(d(c({},e),{queryArgs:n}))}}else t.serializeQueryArgs&&(n=t.serializeQueryArgs);return n(e)},tagTypes:[...t.tagTypes||[]]}),i={endpointDefinitions:{},batch(e){e()},apiUid:Oe(),extractRehydrationInfo:n,hasRehydrationInfo:Te((e=>null!=n(e)))},a={injectEndpoints:function(e){const t=e.endpoints({query:e=>d(c({},e),{type:A.query}),mutation:e=>d(c({},e),{type:A.mutation})});for(const[n,r]of Object.entries(t))if(e.overrideExisting||!(n in i.endpointDefinitions)){i.endpointDefinitions[n]=r;for(const e of o)e.injectEndpoint(n,r)}return a},enhanceEndpoints({addTagTypes:e,endpoints:t}){if(e)for(const t of e)r.tagTypes.includes(t)||r.tagTypes.push(t);if(t)for(const[e,n]of Object.entries(t))"function"==typeof n?n(i.endpointDefinitions[e]):Object.assign(i.endpointDefinitions[e]||{},n);return a}},o=e.map((e=>e.init(a,r,i)));return a.injectEndpoints({endpoints:t.endpoints})}}function je(){return function(){throw new Error("When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.")}}import{createAction as Ae}from"@reduxjs/toolkit";var ke=({reducerPath:e,api:t,context:n,internalState:r})=>{const{removeQueryResult:i,unsubscribeQueryResult:a}=t.internalActions;function o(e){const t=r.currentSubscriptions[e];return!!t&&!function(e){for(let t in e)return!1;return!0}(t)}const s={};function u(e,t,r,a){var u;const c=n.endpointDefinitions[t],d=null!=(u=null==c?void 0:c.keepUnusedDataFor)?u:a.keepUnusedDataFor;if(Infinity===d)return;const l=Math.max(0,Math.min(d,2147482.647));if(!o(e)){const t=s[e];t&&clearTimeout(t),s[e]=setTimeout((()=>{o(e)||r.dispatch(i({queryCacheKey:e})),delete s[e]}),1e3*l)}}return(r,i,o)=>{var c;if(a.match(r)){const t=i.getState()[e],{queryCacheKey:n}=r.payload;u(n,null==(c=t.queries[n])?void 0:c.endpointName,i,t.config)}if(t.util.resetApiState.match(r))for(const[e,t]of Object.entries(s))t&&clearTimeout(t),delete s[e];if(n.hasRehydrationInfo(r)){const t=i.getState()[e],{queries:a}=n.extractRehydrationInfo(r);for(const[e,n]of Object.entries(a))u(e,null==n?void 0:n.endpointName,i,t.config)}}};import{isAnyOf as xe,isFulfilled as Qe,isRejectedWithValue as Ie}from"@reduxjs/toolkit";var Ce=({reducerPath:t,context:n,context:{endpointDefinitions:r},mutationThunk:i,api:a,assertTagType:o,refetchQuery:s})=>{const{removeQueryResult:u}=a.internalActions,c=xe(Qe(i),Ie(i));function d(r,i){const o=i.getState(),c=o[t],d=a.util.selectInvalidatedBy(o,r);n.batch((()=>{var t;const n=Array.from(d.values());for(const{queryCacheKey:r}of n){const n=c.queries[r],a=null!=(t=c.subscriptions[r])?t:{};n&&(0===Object.keys(a).length?i.dispatch(u({queryCacheKey:r})):n.status!==e.uninitialized&&i.dispatch(s(n,r)))}}))}return(e,t)=>{c(e)&&d(se(e,"invalidatesTags",r,o),t),a.util.invalidateTags.match(e)&&d(E(e.payload,void 0,void 0,void 0,void 0,o),t)}},Pe=({reducerPath:t,queryThunk:n,api:r,refetchQuery:i,internalState:a})=>{const o={};function s({queryCacheKey:n},r){const s=r.getState()[t].queries[n];if(!s||s.status===e.uninitialized)return;const u=d(a.currentSubscriptions[n]);if(!Number.isFinite(u))return;const c=o[n];(null==c?void 0:c.timeout)&&(clearTimeout(c.timeout),c.timeout=void 0);const l=Date.now()+u,p=o[n]={nextPollTimestamp:l,pollingInterval:u,timeout:setTimeout((()=>{p.timeout=void 0,r.dispatch(i(s,n))}),u)}}function u({queryCacheKey:n},r){const i=r.getState()[t].queries[n];if(!i||i.status===e.uninitialized)return;const u=d(a.currentSubscriptions[n]);if(!Number.isFinite(u))return void c(n);const l=o[n],p=Date.now()+u;(!l||p<l.nextPollTimestamp)&&s({queryCacheKey:n},r)}function c(e){const t=o[e];(null==t?void 0:t.timeout)&&clearTimeout(t.timeout),delete o[e]}function d(e={}){let t=Number.POSITIVE_INFINITY;for(let n in e)e[n].pollingInterval&&(t=Math.min(e[n].pollingInterval,t));return t}return(e,t)=>{(r.internalActions.updateSubscriptionOptions.match(e)||r.internalActions.unsubscribeQueryResult.match(e))&&u(e.payload,t),(n.pending.match(e)||n.rejected.match(e)&&e.meta.condition)&&u(e.meta.arg,t),(n.fulfilled.match(e)||n.rejected.match(e)&&!e.meta.condition)&&s(e.meta.arg,t),r.util.resetApiState.match(e)&&function(){for(const e of Object.keys(o))c(e)}()}};import{isAsyncThunkAction as Me,isFulfilled as De}from"@reduxjs/toolkit";var Ne=new Error("Promise never resolved before cacheEntryRemoved."),Ke=({api:e,reducerPath:t,context:n,queryThunk:r,mutationThunk:i})=>{const a=Me(r),o=Me(i),s=De(r,i),u={};function l(t,r,i,a,o){const s=n.endpointDefinitions[t],l=null==s?void 0:s.onCacheEntryAdded;if(!l)return;let p={};const f=new Promise((e=>{p.cacheEntryRemoved=e})),y=Promise.race([new Promise((e=>{p.valueResolved=e})),f.then((()=>{throw Ne}))]);y.catch((()=>{})),u[i]=p;const m=e.endpoints[t].select(s.type===A.query?r:i),h=a.dispatch(((e,t,n)=>n)),g=d(c({},a),{getCacheEntry:()=>m(a.getState()),requestId:o,extra:h,updateCachedData:s.type===A.query?n=>a.dispatch(e.util.updateQueryData(t,r,n)):void 0,cacheDataLoaded:y,cacheEntryRemoved:f}),v=l(r,g);Promise.resolve(v).catch((e=>{if(e!==Ne)throw e}))}return(n,c,d)=>{const p=function(t){return a(t)?t.meta.arg.queryCacheKey:o(t)?t.meta.requestId:e.internalActions.removeQueryResult.match(t)?t.payload.queryCacheKey:e.internalActions.removeMutationResult.match(t)?pe(t.payload):""}(n);if(r.pending.match(n)){const e=d[t].queries[p],r=c.getState()[t].queries[p];!e&&r&&l(n.meta.arg.endpointName,n.meta.arg.originalArgs,p,c,n.meta.requestId)}else if(i.pending.match(n))c.getState()[t].mutations[p]&&l(n.meta.arg.endpointName,n.meta.arg.originalArgs,p,c,n.meta.requestId);else if(s(n)){const e=u[p];(null==e?void 0:e.valueResolved)&&(e.valueResolved({data:n.payload,meta:n.meta.baseQueryMeta}),delete e.valueResolved)}else if(e.internalActions.removeQueryResult.match(n)||e.internalActions.removeMutationResult.match(n)){const e=u[p];e&&(delete u[p],e.cacheEntryRemoved())}else if(e.util.resetApiState.match(n))for(const[e,t]of Object.entries(u))delete u[e],t.cacheEntryRemoved()}};import{isPending as Ee,isRejected as Fe,isFulfilled as ze}from"@reduxjs/toolkit";var _e,$e=({api:e,context:t,queryThunk:n,mutationThunk:r})=>{const i=Ee(n,r),a=Fe(n,r),o=ze(n,r),s={};return(n,r)=>{var u,l,p;if(i(n)){const{requestId:i,arg:{endpointName:a,originalArgs:o}}=n.meta,u=t.endpointDefinitions[a],l=null==u?void 0:u.onQueryStarted;if(l){const t={},n=new Promise(((e,n)=>{t.resolve=e,t.reject=n}));n.catch((()=>{})),s[i]=t;const p=e.endpoints[a].select(u.type===A.query?o:i),f=r.dispatch(((e,t,n)=>n)),y=d(c({},r),{getCacheEntry:()=>p(r.getState()),requestId:i,extra:f,updateCachedData:u.type===A.query?t=>r.dispatch(e.util.updateQueryData(a,o,t)):void 0,queryFulfilled:n});l(o,y)}}else if(o(n)){const{requestId:e,baseQueryMeta:t}=n.meta;null==(u=s[e])||u.resolve({data:n.payload,meta:t}),delete s[e]}else if(a(n)){const{requestId:e,rejectedWithValue:t,baseQueryMeta:r}=n.meta;null==(p=s[e])||p.reject({error:null!=(l=n.payload)?l:n.error,isUnhandledError:!t,meta:r}),delete s[e]}}},Ue=({api:e,context:{apiUid:t}})=>(n,r)=>{e.util.resetApiState.match(n)&&r.dispatch(e.internalActions.middlewareRegistered(t))};import{produceWithPatches as Le}from"immer";var We="function"==typeof queueMicrotask?queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis):e=>(_e||(_e=Promise.resolve())).then(e).catch((e=>setTimeout((()=>{throw e}),0)));function Be(e,...t){Object.assign(e,...t)}import{enablePatches as Je}from"immer";var He=Symbol(),Ve=()=>({name:He,init(t,{baseQuery:n,reducerPath:r,serializeQueryArgs:i,keepUnusedDataFor:a,refetchOnMountOrArgChange:o,refetchOnFocus:s,refetchOnReconnect:u},l){Je();const f=e=>e;Object.assign(t,{reducerPath:r,endpoints:{},internalActions:{onOnline:I,onOffline:C,onFocus:x,onFocusLost:Q},util:{}});const{queryThunk:y,mutationThunk:h,patchQueryData:g,updateQueryData:v,upsertQueryData:b,prefetch:q,buildMatchThunkActions:S}=function({reducerPath:t,baseQuery:n,context:{endpointDefinitions:r},serializeQueryArgs:i,api:a,assertTagType:o}){const s=async(e,{signal:t,abort:i,rejectWithValue:a,fulfillWithValue:o,dispatch:s,getState:c,extra:d})=>{const l=r[e.endpointName];try{let r,a=oe;const p={signal:t,abort:i,dispatch:s,getState:c,extra:d,endpoint:e.endpointName,type:e.type,forced:"query"===e.type?u(e,c()):void 0},f="query"===e.type?e[V]:void 0;if(f?r=f():l.query?(r=await n(l.query(e.originalArgs),p,l.extraOptions),l.transformResponse&&(a=l.transformResponse)):r=await l.queryFn(e.originalArgs,p,l.extraOptions,(e=>n(e,p,l.extraOptions))),r.error)throw new R(r.error,r.meta);return o(await a(r.data,r.meta,e.originalArgs),{fulfilledTimeStamp:Date.now(),baseQueryMeta:r.meta,[ae]:!0})}catch(t){let n=t;if(n instanceof R){let t=oe;l.query&&l.transformErrorResponse&&(t=l.transformErrorResponse);try{return a(await t(n.value,n.meta,e.originalArgs),{baseQueryMeta:n.meta,[ae]:!0})}catch(e){n=e}}throw console.error(n),n}};function u(e,n){var r,i,a,o;const s=null==(i=null==(r=n[t])?void 0:r.queries)?void 0:i[e.queryCacheKey],u=null==(a=n[t])?void 0:a.config.refetchOnMountOrArgChange,c=null==s?void 0:s.fulfilledTimeStamp,d=null!=(o=e.forceRefetch)?o:e.subscribe&&u;return!!d&&(!0===d||(Number(new Date)-Number(c))/1e3>=d)}function c(e){return t=>{var n,r;return(null==(r=null==(n=null==t?void 0:t.meta)?void 0:n.arg)?void 0:r.endpointName)===e}}return{queryThunk:ie(`${t}/executeQuery`,s,{getPendingMeta:()=>({startedTimeStamp:Date.now(),[ae]:!0}),condition(e,{getState:n}){var i,a,o;const s=n(),c=null==(a=null==(i=s[t])?void 0:i.queries)?void 0:a[e.queryCacheKey],d=null==c?void 0:c.fulfilledTimeStamp,l=e.originalArgs,p=null==c?void 0:c.originalArgs,f=r[e.endpointName];return!(!G(e)&&("pending"===(null==c?void 0:c.status)||!u(e,s)&&(!K(f)||!(null==(o=null==f?void 0:f.forceRefetch)?void 0:o.call(f,{currentArg:l,previousArg:p,endpointState:c,state:s})))&&d))},dispatchConditionRejection:!0}),mutationThunk:ie(`${t}/executeMutation`,s,{getPendingMeta:()=>({startedTimeStamp:Date.now(),[ae]:!0})}),prefetch:(e,t,n)=>(r,i)=>{const o=(e=>"force"in e)(n)&&n.force,s=(e=>"ifOlderThan"in e)(n)&&n.ifOlderThan,u=(n=!0)=>a.endpoints[e].initiate(t,{forceRefetch:n}),c=a.endpoints[e].select(t)(i());if(o)r(u());else if(s){const e=null==c?void 0:c.fulfilledTimeStamp;if(!e)return void r(u());(Number(new Date)-Number(new Date(e)))/1e3>=s&&r(u())}else r(u(!1))},updateQueryData:(t,n,r,i=!0)=>(o,s)=>{const u=a.endpoints[t].select(n)(s());let c,d={patches:[],inversePatches:[],undo:()=>o(a.util.patchQueryData(t,n,d.inversePatches,i))};if(u.status===e.uninitialized)return d;if("data"in u)if(ne(u.data)){const[e,t,n]=re(u.data,r);d.patches.push(...t),d.inversePatches.push(...n),c=e}else c=r(u.data),d.patches.push({op:"replace",path:[],value:c}),d.inversePatches.push({op:"replace",path:[],value:u.data});return o(a.util.patchQueryData(t,n,d.patches,i)),d},upsertQueryData:(e,t,n)=>r=>r(a.endpoints[e].initiate(t,{subscribe:!1,forceRefetch:!0,[V]:()=>({data:n})})),patchQueryData:(e,t,n,s)=>(u,c)=>{const d=r[e],l=i({queryArgs:t,endpointDefinition:d,endpointName:e});if(u(a.internalActions.queryResultPatched({queryCacheKey:l,patches:n})),!s)return;const p=a.endpoints[e].select(t)(c()),f=E(d.providesTags,p.data,void 0,t,{},o);u(a.internalActions.updateProvidedBy({queryCacheKey:l,providedTags:f}))},buildMatchThunkActions:function(e,t){return{matchPending:Y(Z(e),c(t)),matchFulfilled:Y(X(e),c(t)),matchRejected:Y(ee(e),c(t))}}}}({baseQuery:n,reducerPath:r,context:l,api:t,serializeQueryArgs:i,assertTagType:f}),{reducer:O,actions:T}=function({reducerPath:t,queryThunk:n,mutationThunk:r,context:{endpointDefinitions:i,apiUid:a,extractRehydrationInfo:o,hasRehydrationInfo:s},assertTagType:u,config:l}){const p=_(`${t}/resetApiState`),f=$({name:`${t}/queries`,initialState:ye,reducers:{removeQueryResult:{reducer(e,{payload:{queryCacheKey:t}}){delete e[t]},prepare:J()},queryResultPatched:{reducer(e,{payload:{queryCacheKey:t,patches:n}}){le(e,t,(e=>{e.data=ce(e.data,n.concat())}))},prepare:J()}},extraReducers(t){t.addCase(n.pending,((t,{meta:n,meta:{arg:r}})=>{var i;const a=G(r);(r.subscribe||a)&&(null!=t[i=r.queryCacheKey]||(t[i]={status:e.uninitialized,endpointName:r.endpointName})),le(t,r.queryCacheKey,(t=>{t.status=e.pending,t.requestId=a&&t.requestId?t.requestId:n.requestId,void 0!==r.originalArgs&&(t.originalArgs=r.originalArgs),t.startedTimeStamp=n.startedTimeStamp}))})).addCase(n.fulfilled,((t,{meta:n,payload:r})=>{le(t,n.arg.queryCacheKey,(t=>{var a;if(t.requestId!==n.requestId&&!G(n.arg))return;const{merge:o}=i[n.arg.endpointName];if(t.status=e.fulfilled,o)if(void 0!==t.data){const{fulfilledTimeStamp:e,arg:i,baseQueryMeta:a,requestId:s}=n;let u=B(t.data,(t=>o(t,r,{arg:i.originalArgs,baseQueryMeta:a,fulfilledTimeStamp:e,requestId:s})));t.data=u}else t.data=r;else t.data=null==(a=i[n.arg.endpointName].structuralSharing)||a?m(ue(t.data)?de(t.data):t.data,r):r;delete t.error,t.fulfilledTimeStamp=n.fulfilledTimeStamp}))})).addCase(n.rejected,((t,{meta:{condition:n,arg:r,requestId:i},error:a,payload:o})=>{le(t,r.queryCacheKey,(t=>{if(n);else{if(t.requestId!==i)return;t.status=e.rejected,t.error=null!=o?o:a}}))})).addMatcher(s,((t,n)=>{const{queries:r}=o(n);for(const[n,i]of Object.entries(r))(null==i?void 0:i.status)!==e.fulfilled&&(null==i?void 0:i.status)!==e.rejected||(t[n]=i)}))}}),y=$({name:`${t}/mutations`,initialState:ye,reducers:{removeMutationResult:{reducer(e,{payload:t}){const n=pe(t);n in e&&delete e[n]},prepare:J()}},extraReducers(t){t.addCase(r.pending,((t,{meta:n,meta:{requestId:r,arg:i,startedTimeStamp:a}})=>{i.track&&(t[pe(n)]={requestId:r,status:e.pending,endpointName:i.endpointName,startedTimeStamp:a})})).addCase(r.fulfilled,((t,{payload:n,meta:r})=>{r.arg.track&&fe(t,r,(t=>{t.requestId===r.requestId&&(t.status=e.fulfilled,t.data=n,t.fulfilledTimeStamp=r.fulfilledTimeStamp)}))})).addCase(r.rejected,((t,{payload:n,error:r,meta:i})=>{i.arg.track&&fe(t,i,(t=>{t.requestId===i.requestId&&(t.status=e.rejected,t.error=null!=n?n:r)}))})).addMatcher(s,((t,n)=>{const{mutations:r}=o(n);for(const[n,i]of Object.entries(r))(null==i?void 0:i.status)!==e.fulfilled&&(null==i?void 0:i.status)!==e.rejected||n===(null==i?void 0:i.requestId)||(t[n]=i)}))}}),h=$({name:`${t}/invalidation`,initialState:ye,reducers:{updateProvidedBy:{reducer(e,t){var n,r,i,a;const{queryCacheKey:o,providedTags:s}=t.payload;for(const t of Object.values(e))for(const e of Object.values(t)){const t=e.indexOf(o);-1!==t&&e.splice(t,1)}for(const{type:t,id:u}of s){const s=null!=(a=(r=null!=(n=e[t])?n:e[t]={})[i=u||"__internal_without_id"])?a:r[i]=[];s.includes(o)||s.push(o)}},prepare:J()}},extraReducers(e){e.addCase(f.actions.removeQueryResult,((e,{payload:{queryCacheKey:t}})=>{for(const n of Object.values(e))for(const e of Object.values(n)){const n=e.indexOf(t);-1!==n&&e.splice(n,1)}})).addMatcher(s,((e,t)=>{var n,r,i,a;const{provided:s}=o(t);for(const[t,o]of Object.entries(s))for(const[s,u]of Object.entries(o)){const o=null!=(a=(r=null!=(n=e[t])?n:e[t]={})[i=s||"__internal_without_id"])?a:r[i]=[];for(const e of u)o.includes(e)||o.push(e)}})).addMatcher(U(L(n),W(n)),((e,t)=>{const n=se(t,"providesTags",i,u),{queryCacheKey:r}=t.meta.arg;h.caseReducers.updateProvidedBy(e,h.actions.updateProvidedBy({queryCacheKey:r,providedTags:n}))}))}}),g=$({name:`${t}/subscriptions`,initialState:ye,reducers:{updateSubscriptionOptions(e,t){},unsubscribeQueryResult(e,t){},internal_probeSubscription(e,t){}}}),v=$({name:`${t}/internalSubscriptions`,initialState:ye,reducers:{subscriptionsUpdated:{reducer:(e,t)=>ce(e,t.payload),prepare:J()}}}),b=$({name:`${t}/config`,initialState:c({online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine,focused:"undefined"==typeof document||"hidden"!==document.visibilityState,middlewareRegistered:!1},l),reducers:{middlewareRegistered(e,{payload:t}){e.middlewareRegistered="conflict"!==e.middlewareRegistered&&a===t||"conflict"}},extraReducers:e=>{e.addCase(I,(e=>{e.online=!0})).addCase(C,(e=>{e.online=!1})).addCase(x,(e=>{e.focused=!0})).addCase(Q,(e=>{e.focused=!1})).addMatcher(s,(e=>c({},e)))}}),q=z({queries:f.reducer,mutations:y.reducer,provided:h.reducer,subscriptions:v.reducer,config:b.reducer});return{reducer:(e,t)=>q(p.match(t)?void 0:e,t),actions:d(c(c(c(c(c(c({},b.actions),f.actions),g.actions),v.actions),y.actions),h.actions),{unsubscribeMutationResult:y.actions.removeMutationResult,resetApiState:p})}}({context:l,queryThunk:y,mutationThunk:h,reducerPath:r,assertTagType:f,config:{refetchOnFocus:s,refetchOnReconnect:u,refetchOnMountOrArgChange:o,keepUnusedDataFor:a,reducerPath:r}});Be(t.util,{patchQueryData:g,updateQueryData:v,upsertQueryData:b,prefetch:q,resetApiState:T.resetApiState}),Be(t.internalActions,T);const{middleware:w,actions:j}=function(t){const{reducerPath:n,queryThunk:r,api:i,context:a}=t,{apiUid:o}=a,s={invalidateTags:Ae(`${n}/invalidateTags`)},u=[Ue,ke,Ce,Pe,Ke,$e];return{middleware:r=>{let s=!1;const p=d(c({},t),{internalState:{currentSubscriptions:{}},refetchQuery:l}),f=u.map((e=>e(p))),y=(({api:e,queryThunk:t,internalState:n})=>{const r=`${e.reducerPath}/subscriptions`;let i=null,a=!1;const{updateSubscriptionOptions:o,unsubscribeQueryResult:s}=e.internalActions;return(u,c)=>{var d,l;if(i||(i=JSON.parse(JSON.stringify(n.currentSubscriptions))),e.util.resetApiState.match(u))return i=n.currentSubscriptions={},[!0,!1];if(e.internalActions.internal_probeSubscription.match(u)){const{queryCacheKey:e,requestId:t}=u.payload;return[!1,!!(null==(d=n.currentSubscriptions[e])?void 0:d[t])]}const p=((n,r)=>{var i,a,u,c,d,l,p,f,y;if(o.match(r)){const{queryCacheKey:e,requestId:t,options:a}=r.payload;return(null==(i=null==n?void 0:n[e])?void 0:i[t])&&(n[e][t]=a),!0}if(s.match(r)){const{queryCacheKey:e,requestId:t}=r.payload;return n[e]&&delete n[e][t],!0}if(e.internalActions.removeQueryResult.match(r))return delete n[r.payload.queryCacheKey],!0;if(t.pending.match(r)){const{meta:{arg:e,requestId:t}}=r;if(e.subscribe){const r=null!=(u=n[a=e.queryCacheKey])?u:n[a]={};return r[t]=null!=(d=null!=(c=e.subscriptionOptions)?c:r[t])?d:{},!0}}if(t.rejected.match(r)){const{meta:{condition:e,arg:t,requestId:i}}=r;if(e&&t.subscribe){const e=null!=(p=n[l=t.queryCacheKey])?p:n[l]={};return e[i]=null!=(y=null!=(f=t.subscriptionOptions)?f:e[i])?y:{},!0}}return!1})(n.currentSubscriptions,u);if(p){a||(We((()=>{const t=JSON.parse(JSON.stringify(n.currentSubscriptions)),[,r]=Le(i,(()=>t));c.next(e.internalActions.subscriptionsUpdated(r)),i=t,a=!1})),a=!0);const o=!!(null==(l=u.type)?void 0:l.startsWith(r)),s=t.rejected.match(u)&&u.meta.condition&&!!u.meta.arg.subscribe;return[!o&&!s,!1]}return[!0,!1]}})(p),m=(({reducerPath:t,context:n,api:r,refetchQuery:i,internalState:a})=>{const{removeQueryResult:o}=r.internalActions;function s(r,s){const u=r.getState()[t],c=u.queries,d=a.currentSubscriptions;n.batch((()=>{for(const t of Object.keys(d)){const n=c[t],a=d[t];a&&n&&(Object.values(a).some((e=>!0===e[s]))||Object.values(a).every((e=>void 0===e[s]))&&u.config[s])&&(0===Object.keys(a).length?r.dispatch(o({queryCacheKey:t})):n.status!==e.uninitialized&&r.dispatch(i(n,t)))}}))}return(e,t)=>{x.match(e)&&s(t,"refetchOnFocus"),I.match(e)&&s(t,"refetchOnReconnect")}})(p);return e=>t=>{s||(s=!0,r.dispatch(i.internalActions.middlewareRegistered(o)));const u=d(c({},r),{next:e}),l=r.getState(),[p,h]=y(t,u,l);let g;if(g=p?e(t):h,r.getState()[n]&&(m(t,u,l),(e=>!!e&&"string"==typeof e.type&&e.type.startsWith(`${n}/`))(t)||a.hasRehydrationInfo(t)))for(let e of f)e(t,u,l);return g}},actions:s};function l(e,t,n={}){return r(c({type:"query",endpointName:e.endpointName,originalArgs:e.originalArgs,subscribe:!1,forceRefetch:!0,queryCacheKey:t},n))}}({reducerPath:r,context:l,queryThunk:y,mutationThunk:h,api:t,assertTagType:f});Be(t.util,j),Be(t,{reducer:O,middleware:w});const{buildQuerySelector:k,buildMutationSelector:P,selectInvalidatedBy:M}=function({serializeQueryArgs:t,reducerPath:n}){const r=e=>ve,i=e=>be;return{buildQuerySelector:function(e,n){return i=>{const s=t({queryArgs:i,endpointDefinition:n,endpointName:e});return N(i===me?r:e=>{var t,n,r;return null!=(r=null==(n=null==(t=o(e))?void 0:t.queries)?void 0:n[s])?r:ve},a)}},buildMutationSelector:function(){return e=>{var t;let n;return n="object"==typeof e?null!=(t=pe(e))?t:me:e,N(n===me?i:e=>{var t,r,i;return null!=(i=null==(r=null==(t=o(e))?void 0:t.mutations)?void 0:r[n])?i:be},a)}},selectInvalidatedBy:function(e,t){var r;const i=e[n],a=new Set;for(const e of t.map(F)){const t=i.provided[e.type];if(!t)continue;let n=null!=(r=void 0!==e.id?t[e.id]:p(Object.values(t)))?r:[];for(const e of n)a.add(e)}return p(Array.from(a.values()).map((e=>{const t=i.queries[e];return t?[{queryCacheKey:e,endpointName:t.endpointName,originalArgs:t.originalArgs}]:[]})))}};function a(t){return c(c({},t),{status:n=t.status,isUninitialized:n===e.uninitialized,isLoading:n===e.pending,isSuccess:n===e.fulfilled,isError:n===e.rejected});var n}function o(e){return e[n]}}({serializeQueryArgs:i,reducerPath:r});Be(t.util,{selectInvalidatedBy:M});const{buildInitiateQuery:D,buildInitiateMutation:te,getRunningMutationThunk:he,getRunningMutationsThunk:ge,getRunningQueriesThunk:qe,getRunningQueryThunk:Se,getRunningOperationPromises:Re,removalWarning:Oe}=function({serializeQueryArgs:e,queryThunk:t,mutationThunk:n,api:r,context:i}){const a=new Map,o=new Map,{unsubscribeQueryResult:s,removeMutationResult:u,updateSubscriptionOptions:c}=r.internalActions;return{buildInitiateQuery:function(n,i){const o=(u,{subscribe:d=!0,forceRefetch:l,subscriptionOptions:p,[V]:f}={})=>(y,m)=>{var h;const g=e({queryArgs:u,endpointDefinition:i,endpointName:n}),v=t({type:"query",subscribe:d,forceRefetch:l,subscriptionOptions:p,endpointName:n,originalArgs:u,queryCacheKey:g,[V]:f}),b=r.endpoints[n].select(u),q=y(v),S=b(m()),{requestId:R,abort:O}=q,T=S.requestId!==R,w=null==(h=a.get(y))?void 0:h[g],j=()=>b(m()),A=Object.assign(f?q.then(j):T&&!w?Promise.resolve(S):Promise.all([w,q]).then(j),{arg:u,requestId:R,subscriptionOptions:p,queryCacheKey:g,abort:O,async unwrap(){const e=await A;if(e.isError)throw e.error;return e.data},refetch:()=>y(o(u,{subscribe:!1,forceRefetch:!0})),unsubscribe(){d&&y(s({queryCacheKey:g,requestId:R}))},updateSubscriptionOptions(e){A.subscriptionOptions=e,y(c({endpointName:n,requestId:R,queryCacheKey:g,options:e}))}});if(!w&&!T&&!f){const e=a.get(y)||{};e[g]=A,a.set(y,e),A.then((()=>{delete e[g],Object.keys(e).length||a.delete(y)}))}return A};return o},buildInitiateMutation:function(e){return(t,{track:r=!0,fixedCacheKey:i}={})=>(a,s)=>{const c=n({type:"mutation",endpointName:e,originalArgs:t,track:r,fixedCacheKey:i}),d=a(c),{requestId:l,abort:p,unwrap:f}=d,y=d.unwrap().then((e=>({data:e}))).catch((e=>({error:e}))),m=()=>{a(u({requestId:l,fixedCacheKey:i}))},h=Object.assign(y,{arg:d.arg,requestId:l,abort:p,unwrap:f,unsubscribe:m,reset:m}),g=o.get(a)||{};return o.set(a,g),g[l]=h,h.then((()=>{delete g[l],Object.keys(g).length||o.delete(a)})),i&&(g[i]=h,h.then((()=>{g[i]===h&&(delete g[i],Object.keys(g).length||o.delete(a))}))),h}},getRunningQueryThunk:function(t,n){return r=>{var o;const s=e({queryArgs:n,endpointDefinition:i.endpointDefinitions[t],endpointName:t});return null==(o=a.get(r))?void 0:o[s]}},getRunningMutationThunk:function(e,t){return e=>{var n;return null==(n=o.get(e))?void 0:n[t]}},getRunningQueriesThunk:function(){return e=>Object.values(a.get(e)||{}).filter(H)},getRunningMutationsThunk:function(){return e=>Object.values(o.get(e)||{}).filter(H)},getRunningOperationPromises:function(){{const e=e=>Array.from(e.values()).flatMap((e=>e?Object.values(e):[]));return[...e(a),...e(o)].filter(H)}},removalWarning:function(){throw new Error("This method had to be removed due to a conceptual bug in RTK.\n       Please see https://github.com/reduxjs/redux-toolkit/pull/2481 for details.\n       See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for new guidance on SSR.")}}}({queryThunk:y,mutationThunk:h,api:t,serializeQueryArgs:i,context:l});return Be(t.util,{getRunningOperationPromises:Re,getRunningOperationPromise:Oe,getRunningMutationThunk:he,getRunningMutationsThunk:ge,getRunningQueryThunk:Se,getRunningQueriesThunk:qe}),{name:He,injectEndpoint(e,n){var r;const i=t;null!=(r=i.endpoints)[e]||(r[e]={}),K(n)?Be(i.endpoints[e],{name:e,select:k(e,n),initiate:D(e,n)},S(y,e)):n.type===A.mutation&&Be(i.endpoints[e],{name:e,select:P(),initiate:te(e)},S(h,e))}}}}),Ge=we(Ve());export{e as QueryStatus,we as buildCreateApi,m as copyWithStructuralSharing,Ve as coreModule,He as coreModuleName,Ge as createApi,Re as defaultSerializeQueryArgs,je as fakeBaseQuery,S as fetchBaseQuery,w as retry,M as setupListeners,he as skipSelector,me as skipToken};
//# sourceMappingURL=rtk-query.modern.production.min.js.map