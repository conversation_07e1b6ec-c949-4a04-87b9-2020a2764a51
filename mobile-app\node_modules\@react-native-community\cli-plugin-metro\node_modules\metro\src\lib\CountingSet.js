"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true,
});
exports.default = void 0;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

/**
 * A Set that only deletes a given item when the number of delete(item) calls
 * matches the number of add(item) calls. Iteration and `size` are in terms of
 * *unique* items.
 */
class CountingSet {
  #map = new Map();
  constructor(items) {
    if (items) {
      if (items instanceof CountingSet) {
        this.#map = new Map(items.#map);
      } else {
        for (const item of items) {
          this.add(item);
        }
      }
    }
  }
  has(item) {
    return this.#map.has(item);
  }
  add(item) {
    const newCount = this.count(item) + 1;
    this.#map.set(item, newCount);
  }
  delete(item) {
    const newCount = this.count(item) - 1;
    if (newCount <= 0) {
      this.#map.delete(item);
    } else {
      this.#map.set(item, newCount);
    }
  }
  keys() {
    return this.#map.keys();
  }
  values() {
    return this.#map.keys();
  }
  *entries() {
    for (const item of this) {
      yield [item, item];
    }
  }

  // Iterate over unique entries
  // $FlowIssue[unsupported-syntax]
  [Symbol.iterator]() {
    return this.values();
  }

  /*::
  // For Flow's benefit
  @@iterator(): Iterator<T> {
    return this.values();
  }
  */

  // Number of unique entries
  // $FlowIssue[unsafe-getters-setters]
  get size() {
    return this.#map.size;
  }
  count(item) {
    return this.#map.get(item) ?? 0;
  }
  clear() {
    this.#map.clear();
  }
  forEach(callbackFn, thisArg) {
    for (const item of this) {
      callbackFn.call(thisArg, item, item, this);
    }
  }

  // For Jest purposes. Ideally a custom serializer would be enough, but in
  // practice there is hardcoded magic for Set in toEqual (etc) that we cannot
  // extend to custom collection classes. Instead let's assume values are
  // sortable ( = strings) and make this look like an array with some stable
  // order.
  toJSON() {
    return [...this].sort();
  }
}
exports.default = CountingSet;
