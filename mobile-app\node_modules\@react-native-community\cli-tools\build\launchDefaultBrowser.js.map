{"version": 3, "names": ["launchDefaultBrowser", "url", "throwIfNonAllowedProtocol", "open", "err", "Error", "logger", "error", "message"], "sources": ["../src/launchDefaultBrowser.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport open from 'open';\nimport throwIfNonAllowedProtocol from './throwIfNonAllowedProtocol';\nimport logger from './logger';\n\nasync function launchDefaultBrowser(url: string) {\n  try {\n    throwIfNonAllowedProtocol(url);\n\n    await open(url);\n  } catch (err) {\n    if (err instanceof Error) {\n      logger.error('<PERSON><PERSON><PERSON> exited with error:', err.message);\n    }\n  }\n}\n\nexport default launchDefaultBrowser;\n"], "mappings": ";;;;;;AASA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAA8B;AAX9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,eAAeA,oBAAoB,CAACC,GAAW,EAAE;EAC/C,IAAI;IACF,IAAAC,kCAAyB,EAACD,GAAG,CAAC;IAE9B,MAAM,IAAAE,eAAI,EAACF,GAAG,CAAC;EACjB,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,IAAIA,GAAG,YAAYC,KAAK,EAAE;MACxBC,eAAM,CAACC,KAAK,CAAC,4BAA4B,EAAEH,GAAG,CAACI,OAAO,CAAC;IACzD;EACF;AACF;AAAC,eAEcR,oBAAoB;AAAA"}