{"author": "<PERSON> <<EMAIL>>", "name": "ast-types", "version": "0.15.2", "description": "Esprima-compatible implementation of the Mozilla JS Parser API", "keywords": ["ast", "abstract syntax tree", "hierarchy", "mozilla", "spidermonkey", "parser api", "esprima", "types", "type system", "type checking", "dynamic types", "parsing", "transformation", "syntax"], "homepage": "http://github.com/benjamn/ast-types", "repository": {"type": "git", "url": "git://github.com/benjamn/ast-types.git"}, "license": "MIT", "main": "main.js", "types": "main.d.ts", "scripts": {"gen": "ts-node --transpile-only script/gen-types.ts", "mocha": "test/run.sh", "test": "npm run gen && npm run build && npm run mocha", "clean": "ts-emit-clean", "build": "tsc && ts-add-module-exports", "prepare": "npm run clean && npm run gen && npm run build", "postpack": "npm run clean"}, "dependencies": {"tslib": "^2.0.1"}, "devDependencies": {"@babel/parser": "7.16.4", "@babel/types": "7.16.0", "@types/esprima": "4.0.3", "@types/glob": "7.2.0", "@types/mocha": "9.0.0", "@types/node": "16.11.10", "espree": "9.1.0", "esprima": "4.0.1", "esprima-fb": "15001.1001.0-dev-harmony-fb", "flow-parser": "0.166.0", "glob": "7.2.0", "mocha": "^9.1.3", "recast": "0.20.5", "reify": "0.20.12", "ts-add-module-exports": "1.0.0", "ts-emit-clean": "1.0.0", "ts-node": "10.4.0", "typescript": "4.5.2"}, "engines": {"node": ">=4"}}