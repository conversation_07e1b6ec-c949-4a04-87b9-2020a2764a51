{"version": 3, "file": "prefer-as-const.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-as-const.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,iDAAiD;YAC9D,WAAW,EAAE,OAAO;SACrB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,oBAAoB,EAClB,yDAAyD;YAC3D,sBAAsB,EACpB,oEAAoE;YACtE,eAAe,EAAE,uDAAuD;SACzE;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,SAAS,YAAY,CACnB,SAA8B,EAC9B,QAA2B,EAC3B,MAAe;YAEf,IACE,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;gBACzC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;gBAC9C,KAAK,IAAI,QAAQ,CAAC,OAAO;gBACzB,SAAS,CAAC,GAAG,KAAK,QAAQ,CAAC,OAAO,CAAC,GAAG,EACtC;gBACA,IAAI,MAAM,EAAE;oBACV,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,QAAQ;wBACd,SAAS,EAAE,sBAAsB;wBACjC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC;qBACnD,CAAC,CAAC;iBACJ;qBAAM;oBACL,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,QAAQ;wBACd,SAAS,EAAE,wBAAwB;wBACnC,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,iBAAiB;gCAC5B,GAAG,EAAE,CAAC,KAAK,EAAsB,EAAE,CAAC;oCAClC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAO,CAAC;oCAC9B,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,WAAW,CAAC;iCAC9C;6BACF;yBACF;qBACF,CAAC,CAAC;iBACJ;aACF;QACH,CAAC;QAED,OAAO;YACL,cAAc,CAAC,IAAI;gBACjB,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YAC3D,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YAC3D,CAAC;YACD,kBAAkB,CAAC,IAAI;gBACrB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;iBACrE;YACH,CAAC;YACD,kBAAkB,CAAC,IAAI;gBACrB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE;oBACvC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;iBACvE;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}