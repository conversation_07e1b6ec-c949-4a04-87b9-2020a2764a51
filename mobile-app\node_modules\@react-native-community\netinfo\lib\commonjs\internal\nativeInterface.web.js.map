{"version": 3, "sources": ["nativeInterface.web.ts"], "names": ["nativeEventEmitter", "NativeEventEmitter", "RNCNetInfo", "addListener", "DEVICE_CONNECTIVITY_EVENT", "event", "emit", "eventEmitter"], "mappings": ";;;;;;;AASA;;AACA;;AACA;;;;AAXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA,MAAMA,kBAAkB,GAAG,IAAIC,+BAAJ,EAA3B,C,CAEA;;AACAC,sBAAWC,WAAX,CACEC,uCADF,EAEGC,KAAD,IAAiB;AACfL,EAAAA,kBAAkB,CAACM,IAAnB,CAAwBF,uCAAxB,EAAmDC,KAAnD;AACD,CAJH;;eAOe,EACb,GAAGH,qBADU;AAEbK,EAAAA,YAAY,EAAEP;AAFD,C", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {NativeEventEmitter} from 'react-native';\nimport RNCNetInfo from './nativeModule';\nimport {DEVICE_CONNECTIVITY_EVENT} from './privateTypes';\n\nconst nativeEventEmitter = new NativeEventEmitter();\n\n// Listen to connectivity events\nRNCNetInfo.addListener(\n  DEVICE_CONNECTIVITY_EVENT,\n  (event): void => {\n    nativeEventEmitter.emit(DEVICE_CONNECTIVITY_EVENT, event);\n  },\n);\n\nexport default {\n  ...RNCNetInfo,\n  eventEmitter: nativeEventEmitter,\n};\n"]}