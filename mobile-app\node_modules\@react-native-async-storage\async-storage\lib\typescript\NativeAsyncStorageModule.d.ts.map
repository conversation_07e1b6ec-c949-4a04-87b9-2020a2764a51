{"version": 3, "file": "NativeAsyncStorageModule.d.ts", "sourceRoot": "", "sources": ["../../src/NativeAsyncStorageModule.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAEhD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAEzC,MAAM,WAAW,IAAK,SAAQ,WAAW;IACvC,QAAQ,EAAE,CACR,IAAI,EAAE,MAAM,EAAE,EACd,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,IAAI,KACjE,IAAI,CAAC;IACV,QAAQ,EAAE,CACR,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,EAC3B,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,IAAI,KACpC,IAAI,CAAC;IACV,WAAW,EAAE,CACX,IAAI,EAAE,SAAS,MAAM,EAAE,EACvB,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,IAAI,KACpC,IAAI,CAAC;IACV,UAAU,EAAE,CACV,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,EAC3B,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,IAAI,KACpC,IAAI,CAAC;IACV,UAAU,EAAE,CACV,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,IAAI,KACjE,IAAI,CAAC;IACV,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,IAAI,KAAK,IAAI,CAAC;CAC1D;;AAED,wBAAgE"}