{"version": 3, "names": ["React", "NavigationBuilderContext", "useFocusedListenersChildrenAdapter", "navigation", "focusedListeners", "addListener", "useContext", "listener", "useCallback", "callback", "isFocused", "handled", "result", "useEffect"], "sourceRoot": "../../src", "sources": ["useFocusedListenersChildrenAdapter.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB,MAGxB,4BAA4B;AAQnC;AACA;AACA;AACA,eAAe,SAASC,kCAAkC,OAG9C;EAAA,IAH+C;IACzDC,UAAU;IACVC;EACO,CAAC;EACR,MAAM;IAAEC;EAAY,CAAC,GAAGL,KAAK,CAACM,UAAU,CAACL,wBAAwB,CAAC;EAElE,MAAMM,QAAQ,GAAGP,KAAK,CAACQ,WAAW,CAC/BC,QAAwC,IAAK;IAC5C,IAAIN,UAAU,CAACO,SAAS,EAAE,EAAE;MAC1B,KAAK,MAAMH,QAAQ,IAAIH,gBAAgB,EAAE;QACvC,MAAM;UAAEO,OAAO;UAAEC;QAAO,CAAC,GAAGL,QAAQ,CAACE,QAAQ,CAAC;QAE9C,IAAIE,OAAO,EAAE;UACX,OAAO;YAAEA,OAAO;YAAEC;UAAO,CAAC;QAC5B;MACF;MAEA,OAAO;QAAED,OAAO,EAAE,IAAI;QAAEC,MAAM,EAAEH,QAAQ,CAACN,UAAU;MAAE,CAAC;IACxD,CAAC,MAAM;MACL,OAAO;QAAEQ,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAK,CAAC;IACzC;EACF,CAAC,EACD,CAACR,gBAAgB,EAAED,UAAU,CAAC,CAC/B;EAEDH,KAAK,CAACa,SAAS,CACb,MAAMR,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAG,OAAO,EAAEE,QAAQ,CAAC,EACtC,CAACF,WAAW,EAAEE,QAAQ,CAAC,CACxB;AACH"}