{"version": 3, "names": ["CHILD_STATE", "getFocusedRouteNameFromRoute", "route", "state", "params", "routeName", "routes", "index", "type", "length", "name", "screen", "undefined"], "sourceRoot": "../../src", "sources": ["getFocusedRouteNameFromRoute.tsx"], "mappings": "AAEA,SAASA,WAAW,QAAQ,iBAAiB;AAE7C,eAAe,SAASC,4BAA4B,CAClDC,KAA6B,EACT;EACpB;EACA,MAAMC,KAAK,GAAGD,KAAK,CAACF,WAAW,CAAC,IAAIE,KAAK,CAACC,KAAK;EAC/C,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAA0C;EAE/D,MAAMC,SAAS,GAAGF,KAAK;EACnB;EACAA,KAAK,CAACG,MAAM;EACV;EACA;EACAH,KAAK,CAACI,KAAK,KACR,OAAOJ,KAAK,CAACK,IAAI,KAAK,QAAQ,IAAIL,KAAK,CAACK,IAAI,KAAK,OAAO,GACrD,CAAC,GACDL,KAAK,CAACG,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAC/B,CAACC,IAAI;EACN;EACF,QAAON,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,MAAM,MAAK,QAAQ,GAChCP,MAAM,CAACO,MAAM,GACbC,SAAS;EAEb,OAAOP,SAAS;AAClB"}