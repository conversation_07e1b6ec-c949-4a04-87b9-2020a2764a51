{"version": 3, "names": ["getOpenStackFrameInEditorMiddleware", "watchFolders", "req", "res", "next", "rawBody", "Error", "frame", "JSON", "parse", "launchEditor", "file", "lineNumber", "end", "options", "connect", "use", "rawBodyMiddleware"], "sources": ["../src/openStackFrameInEditorMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport http from 'http';\nimport {launchEditor} from '@react-native-community/cli-tools';\nimport connect from 'connect';\nimport rawBodyMiddleware from './rawBodyMiddleware';\n\ntype Options = {\n  watchFolders: ReadonlyArray<string>;\n};\n\nfunction getOpenStackFrameInEditorMiddleware({watchFolders}: Options) {\n  return (\n    req: http.IncomingMessage & {rawBody?: string},\n    res: http.ServerResponse,\n    next: (err?: any) => void,\n  ) => {\n    if (!req.rawBody) {\n      return next(new Error('missing request body'));\n    }\n    const frame = JSON.parse(req.rawBody);\n    launchEditor(frame.file, frame.lineNumber, watchFolders);\n    res.end('OK');\n  };\n}\n\nexport default (options: Options) => {\n  return connect()\n    .use(rawBodyMiddleware)\n    .use(getOpenStackFrameInEditorMiddleware(options));\n};\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAoD;AATpD;AACA;AACA;AACA;AACA;AACA;;AAUA,SAASA,mCAAmC,CAAC;EAACC;AAAqB,CAAC,EAAE;EACpE,OAAO,CACLC,GAA8C,EAC9CC,GAAwB,EACxBC,IAAyB,KACtB;IACH,IAAI,CAACF,GAAG,CAACG,OAAO,EAAE;MAChB,OAAOD,IAAI,CAAC,IAAIE,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAChD;IACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACP,GAAG,CAACG,OAAO,CAAC;IACrC,IAAAK,wBAAY,EAACH,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACK,UAAU,EAAEX,YAAY,CAAC;IACxDE,GAAG,CAACU,GAAG,CAAC,IAAI,CAAC;EACf,CAAC;AACH;AAAC,eAEeC,OAAgB,IAAK;EACnC,OAAO,IAAAC,kBAAO,GAAE,CACbC,GAAG,CAACC,0BAAiB,CAAC,CACtBD,GAAG,CAAChB,mCAAmC,CAACc,OAAO,CAAC,CAAC;AACtD,CAAC;AAAA"}