{"version": 3, "names": ["logMessage", "message", "indentation", "logger", "log", "messageByLine", "split", "join", "addBlankLine", "logManualInstallation", "healthcheck", "url", "command", "chalk", "dim", "underline", "bold", "logError", "loader", "error", "fail", "calculateMessageSize", "Math", "max", "ceil", "wcwidth", "stripAnsi", "process", "stdout", "columns", "removeMessage", "readline", "moveCursor", "clearScreenDown", "inline", "strings", "values", "zipped", "map", "str", "i", "trim", "filter", "line", "test"], "sources": ["../../../src/tools/healthchecks/common.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport readline from 'readline';\nimport wcwidth from 'wcwidth';\nimport stripAnsi from 'strip-ansi';\nimport {logger} from '@react-native-community/cli-tools';\nimport {Loader} from '../../types';\n\n// Space is necessary to keep correct ordering on screen\nconst logMessage = (message?: string) => {\n  const indentation = '   ';\n\n  if (typeof message !== 'string') {\n    logger.log();\n\n    return;\n  }\n\n  const messageByLine = message.split('\\n');\n\n  return logger.log(`${indentation}${messageByLine.join(`\\n${indentation}`)}`);\n};\n\nconst addBlankLine = () => logMessage();\n\nconst logManualInstallation = ({\n  healthcheck,\n  url,\n  command,\n  message,\n}: {\n  healthcheck?: string;\n  url?: string;\n  command?: string;\n  message?: string;\n}) => {\n  if (message) {\n    return logMessage(message);\n  }\n\n  if (url) {\n    logMessage(\n      `Read more about how to download ${healthcheck} at ${chalk.dim.underline(\n        url,\n      )}`,\n    );\n\n    return;\n  }\n\n  if (command) {\n    logMessage(\n      `Please install ${healthcheck} by running ${chalk.bold(command)}`,\n    );\n  }\n};\n\nconst logError = ({\n  healthcheck,\n  loader,\n  error,\n  message,\n  command,\n}: {\n  healthcheck: string;\n  loader?: Loader;\n  error: Error;\n  message?: string;\n  command: string;\n}) => {\n  if (loader) {\n    loader.fail();\n  }\n\n  addBlankLine();\n\n  logMessage(chalk.dim(error.message));\n\n  if (message) {\n    logMessage(message);\n    addBlankLine();\n\n    return;\n  }\n\n  logMessage(\n    `The error above occured while trying to install ${healthcheck}. Please try again manually: ${chalk.bold(\n      command,\n    )}`,\n  );\n  addBlankLine();\n};\n\n// Calculate the size of a message on terminal based on rows\nfunction calculateMessageSize(message: string) {\n  return Math.max(\n    1,\n    Math.ceil(wcwidth(stripAnsi(message)) / (process.stdout.columns || 80)),\n  );\n}\n\n// Clear the message from the terminal\nfunction removeMessage(message: string) {\n  readline.moveCursor(process.stdout, 0, -calculateMessageSize(message));\n  readline.clearScreenDown(process.stdout);\n}\n\n/**\n * Inline a series of Ruby statements:\n *\n * In:\n *  puts \"a\"\n *  puts \"b\"\n *\n * Out:\n *  puts \"a\"; puts \"b\";\n */\nfunction inline(\n  strings: TemplateStringsArray,\n  ...values: {toString(): string}[]\n) {\n  const zipped = strings.map((str, i) => `${str}${values[i] ?? ''}`).join('');\n\n  return zipped\n    .trim()\n    .split('\\n')\n    .filter((line) => !/^\\W*$/.test(line))\n    .map((line) => line.trim())\n    .join('; ');\n}\n\nexport {logMessage, logManualInstallation, logError, removeMessage, inline};\n"], "mappings": ";;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAyD;AAGzD;AACA,MAAMA,UAAU,GAAIC,OAAgB,IAAK;EACvC,MAAMC,WAAW,GAAG,KAAK;EAEzB,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;IAC/BE,kBAAM,CAACC,GAAG,EAAE;IAEZ;EACF;EAEA,MAAMC,aAAa,GAAGJ,OAAO,CAACK,KAAK,CAAC,IAAI,CAAC;EAEzC,OAAOH,kBAAM,CAACC,GAAG,CAAE,GAAEF,WAAY,GAAEG,aAAa,CAACE,IAAI,CAAE,KAAIL,WAAY,EAAC,CAAE,EAAC,CAAC;AAC9E,CAAC;AAAC;AAEF,MAAMM,YAAY,GAAG,MAAMR,UAAU,EAAE;AAEvC,MAAMS,qBAAqB,GAAG,CAAC;EAC7BC,WAAW;EACXC,GAAG;EACHC,OAAO;EACPX;AAMF,CAAC,KAAK;EACJ,IAAIA,OAAO,EAAE;IACX,OAAOD,UAAU,CAACC,OAAO,CAAC;EAC5B;EAEA,IAAIU,GAAG,EAAE;IACPX,UAAU,CACP,mCAAkCU,WAAY,OAAMG,gBAAK,CAACC,GAAG,CAACC,SAAS,CACtEJ,GAAG,CACH,EAAC,CACJ;IAED;EACF;EAEA,IAAIC,OAAO,EAAE;IACXZ,UAAU,CACP,kBAAiBU,WAAY,eAAcG,gBAAK,CAACG,IAAI,CAACJ,OAAO,CAAE,EAAC,CAClE;EACH;AACF,CAAC;AAAC;AAEF,MAAMK,QAAQ,GAAG,CAAC;EAChBP,WAAW;EACXQ,MAAM;EACNC,KAAK;EACLlB,OAAO;EACPW;AAOF,CAAC,KAAK;EACJ,IAAIM,MAAM,EAAE;IACVA,MAAM,CAACE,IAAI,EAAE;EACf;EAEAZ,YAAY,EAAE;EAEdR,UAAU,CAACa,gBAAK,CAACC,GAAG,CAACK,KAAK,CAAClB,OAAO,CAAC,CAAC;EAEpC,IAAIA,OAAO,EAAE;IACXD,UAAU,CAACC,OAAO,CAAC;IACnBO,YAAY,EAAE;IAEd;EACF;EAEAR,UAAU,CACP,mDAAkDU,WAAY,gCAA+BG,gBAAK,CAACG,IAAI,CACtGJ,OAAO,CACP,EAAC,CACJ;EACDJ,YAAY,EAAE;AAChB,CAAC;;AAED;AAAA;AACA,SAASa,oBAAoB,CAACpB,OAAe,EAAE;EAC7C,OAAOqB,IAAI,CAACC,GAAG,CACb,CAAC,EACDD,IAAI,CAACE,IAAI,CAAC,IAAAC,kBAAO,EAAC,IAAAC,oBAAS,EAACzB,OAAO,CAAC,CAAC,IAAI0B,OAAO,CAACC,MAAM,CAACC,OAAO,IAAI,EAAE,CAAC,CAAC,CACxE;AACH;;AAEA;AACA,SAASC,aAAa,CAAC7B,OAAe,EAAE;EACtC8B,mBAAQ,CAACC,UAAU,CAACL,OAAO,CAACC,MAAM,EAAE,CAAC,EAAE,CAACP,oBAAoB,CAACpB,OAAO,CAAC,CAAC;EACtE8B,mBAAQ,CAACE,eAAe,CAACN,OAAO,CAACC,MAAM,CAAC;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,MAAM,CACbC,OAA6B,EAC7B,GAAGC,MAA8B,EACjC;EACA,MAAMC,MAAM,GAAGF,OAAO,CAACG,GAAG,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAM,GAAED,GAAI,GAAEH,MAAM,CAACI,CAAC,CAAC,IAAI,EAAG,EAAC,CAAC,CAACjC,IAAI,CAAC,EAAE,CAAC;EAE3E,OAAO8B,MAAM,CACVI,IAAI,EAAE,CACNnC,KAAK,CAAC,IAAI,CAAC,CACXoC,MAAM,CAAEC,IAAI,IAAK,CAAC,OAAO,CAACC,IAAI,CAACD,IAAI,CAAC,CAAC,CACrCL,GAAG,CAAEK,IAAI,IAAKA,IAAI,CAACF,IAAI,EAAE,CAAC,CAC1BlC,IAAI,CAAC,IAAI,CAAC;AACf"}