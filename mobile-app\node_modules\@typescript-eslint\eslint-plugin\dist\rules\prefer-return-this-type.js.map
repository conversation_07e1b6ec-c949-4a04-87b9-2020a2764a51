{"version": 3, "file": "prefer-return-this-type.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-return-this-type.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,kCAAgF;AAUhF,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,yBAAyB;IAC/B,cAAc,EAAE,EAAE;IAElB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,+DAA+D;YACjE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,0BAA0B;SACxC;QACD,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,MAAM;KAChB;IAED,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAExD,SAAS,gBAAgB,CACvB,IAAY,EACZ,QAA2B;YAE3B,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAChD,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACpD,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI,EAC/B;gBACA,OAAO,QAAQ,CAAC;aACjB;YAED,IAAI,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE;gBAChD,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;oBACjC,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC3C,IAAI,KAAK,EAAE;wBACT,OAAO,KAAK,CAAC;qBACd;iBACF;aACF;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,2BAA2B,CAAC,YAA0B;YAC7D,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACxC,OAAO,CACL,QAAQ;gBACR,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC3C,QAAQ,CAAC,IAAI,KAAK,MAAM,CACzB,CAAC;QACJ,CAAC;QAED,SAAS,uBAAuB,CAC9B,YAA0B,EAC1B,aAAmC;YAEnC,IAAI,2BAA2B,CAAC,YAAY,CAAC,EAAE;gBAC7C,OAAO,KAAK,CAAC;aACd;YAED,MAAM,IAAI,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAEpE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACd,OAAO,KAAK,CAAC;aACd;YAED,MAAM,SAAS,GAAG,OAAO,CAAC,iBAAiB,CACzC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,CACpC,CAAC;YAEtB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE;gBAC1C,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClD,OAAO,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC;aACpC;YAED,IAAI,aAAa,GAAG,KAAK,CAAC;YAC1B,IAAI,kBAAkB,GAAG,KAAK,CAAC;YAE/B,IAAA,6BAAsB,EAAC,IAAI,CAAC,IAAgB,EAAE,IAAI,CAAC,EAAE;gBACnD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;gBAC7B,IAAI,CAAC,IAAI,EAAE;oBACT,OAAO;iBACR;gBAED,aAAa;gBACb,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE;oBAC3C,aAAa,GAAG,IAAI,CAAC;oBACrB,OAAO;iBACR;gBAED,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAC7C,IAAI,SAAS,KAAK,IAAI,EAAE;oBACtB,kBAAkB,GAAG,IAAI,CAAC;oBAC1B,OAAO,IAAI,CAAC;iBACb;gBAED,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE;oBAC/B,aAAa,GAAG,IAAI,CAAC;oBACrB,OAAO;iBACR;gBAED,OAAO;YACT,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,kBAAkB,IAAI,aAAa,CAAC;QAC9C,CAAC;QAED,SAAS,aAAa,CACpB,YAA0B,EAC1B,aAAmC;;YAEnC,MAAM,SAAS,GAAG,MAAA,aAAa,CAAC,EAAE,0CAAE,IAAI,CAAC;YACzC,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;gBAC1C,OAAO;aACR;YAED,MAAM,IAAI,GAAG,gBAAgB,CAC3B,SAAS,EACT,YAAY,CAAC,UAAU,CAAC,cAAc,CACvC,CAAC;YACF,IAAI,CAAC,IAAI,EAAE;gBACT,OAAO;aACR;YAED,IAAI,uBAAuB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;gBACxD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,aAAa;oBACxB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC;iBAC9C,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,8BAA8B,CAAC,IAA+B;gBAC5D,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAO,CAAC,MAA8B,CAAC,CAAC;YACzE,CAAC;YACD,gCAAgC,CAC9B,IAAiC;;gBAEjC,IACE,CAAC,CACC,CAAA,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,MAAK,sBAAc,CAAC,kBAAkB;oBACtD,CAAA,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,MAAK,sBAAc,CAAC,uBAAuB,CAC5D,EACD;oBACA,OAAO;iBACR;gBAED,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAO,CAAC,MAA8B,CAAC,CAAC;YACzE,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}