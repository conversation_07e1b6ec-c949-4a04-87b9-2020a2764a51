{"name": "colorette", "version": "1.4.0", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set the text color and style in the terminal.", "repository": "jorgebucaran/colorette", "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "files": ["*.*(c)[tj]s*"], "author": "<PERSON>", "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "node -e \"fs.writeFileSync('index.cjs', fs.readFileSync('index.js', 'utf8').replace(/export const /g, 'exports.').replace(/import \\* as ([^ ]+) from \\\"(.+)\\\"/, 'const \\$1 = require(\\\"\\$2\\\")'), 'utf8')\"", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}}