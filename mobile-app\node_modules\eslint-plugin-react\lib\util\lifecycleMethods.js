/**
 * @fileoverview lifecycle methods
 * <AUTHOR>
 */

'use strict';

module.exports = {
  instance: [
    'getDefaultProps',
    'getInitialState',
    'getChildContext',
    'componentWillMount',
    'UNSAFE_componentWillMount',
    'componentDidMount',
    'componentWillReceiveProps',
    'UNSAFE_componentWillReceiveProps',
    'shouldComponentUpdate',
    'componentWillUpdate',
    'UNSAFE_componentWillUpdate',
    'getSnapshotBeforeUpdate',
    'componentDidUpdate',
    'componentDidCatch',
    'componentWillUnmount',
    'render',
  ],
  static: [
    'getDerivedStateFromProps',
  ],
};
