{"name": "metro-file-map", "version": "0.76.7", "description": "[Experimental] - 🚇 File crawling, watching and mapping for Metro", "main": "src/index.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "license": "MIT", "dependencies": {"anymatch": "^3.0.3", "debug": "^2.2.0", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.4", "invariant": "^2.2.4", "jest-regex-util": "^27.0.6", "jest-util": "^27.2.0", "jest-worker": "^27.2.0", "micromatch": "^4.0.4", "node-abort-controller": "^3.1.1", "nullthrows": "^1.1.1", "walker": "^1.0.7"}, "devDependencies": {"slash": "^3.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}, "engines": {"node": ">=16"}}