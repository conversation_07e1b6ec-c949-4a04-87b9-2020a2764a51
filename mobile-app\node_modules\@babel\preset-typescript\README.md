# @babel/preset-typescript

> Babel preset for TypeScript.

See our website [@babel/preset-typescript](https://babeljs.io/docs/babel-preset-typescript) for more information or the [issues](https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen) associated with this package.

## Install

Using npm:

```sh
npm install --save-dev @babel/preset-typescript
```

or using yarn:

```sh
yarn add @babel/preset-typescript --dev
```
