!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).hoistNonReactStatics=t()}(this,(function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function t(e,t){return e(t={exports:{}},t.exports),t.exports}var r=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&Symbol.for,o=r?Symbol.for("react.element"):60103,n=r?Symbol.for("react.portal"):60106,c=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,f=r?Symbol.for("react.profiler"):60114,s=r?Symbol.for("react.provider"):60109,i=r?Symbol.for("react.context"):60110,u=r?Symbol.for("react.async_mode"):60111,y=r?Symbol.for("react.concurrent_mode"):60111,l=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,m=r?Symbol.for("react.suspense_list"):60120,d=r?Symbol.for("react.memo"):60115,b=r?Symbol.for("react.lazy"):60116,S=r?Symbol.for("react.fundamental"):60117,$=r?Symbol.for("react.responder"):60118,v=r?Symbol.for("react.scope"):60119;function g(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case o:switch(e=e.type){case u:case y:case c:case f:case a:case p:return e;default:switch(e=e&&e.$$typeof){case i:case l:case b:case d:case s:return e;default:return t}}case n:return t}}}function P(e){return g(e)===y}t.typeOf=g,t.AsyncMode=u,t.ConcurrentMode=y,t.ContextConsumer=i,t.ContextProvider=s,t.Element=o,t.ForwardRef=l,t.Fragment=c,t.Lazy=b,t.Memo=d,t.Portal=n,t.Profiler=f,t.StrictMode=a,t.Suspense=p,t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===c||e===y||e===f||e===a||e===p||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===b||e.$$typeof===d||e.$$typeof===s||e.$$typeof===i||e.$$typeof===l||e.$$typeof===S||e.$$typeof===$||e.$$typeof===v)},t.isAsyncMode=function(e){return P(e)||g(e)===u},t.isConcurrentMode=P,t.isContextConsumer=function(e){return g(e)===i},t.isContextProvider=function(e){return g(e)===s},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===o},t.isForwardRef=function(e){return g(e)===l},t.isFragment=function(e){return g(e)===c},t.isLazy=function(e){return g(e)===b},t.isMemo=function(e){return g(e)===d},t.isPortal=function(e){return g(e)===n},t.isProfiler=function(e){return g(e)===f},t.isStrictMode=function(e){return g(e)===a},t.isSuspense=function(e){return g(e)===p}}));e(r);var o=t((function(e,t){"production"!==process.env.NODE_ENV&&function(){Object.defineProperty(t,"__esModule",{value:!0});var e="function"==typeof Symbol&&Symbol.for,r=e?Symbol.for("react.element"):60103,o=e?Symbol.for("react.portal"):60106,n=e?Symbol.for("react.fragment"):60107,c=e?Symbol.for("react.strict_mode"):60108,a=e?Symbol.for("react.profiler"):60114,f=e?Symbol.for("react.provider"):60109,s=e?Symbol.for("react.context"):60110,i=e?Symbol.for("react.async_mode"):60111,u=e?Symbol.for("react.concurrent_mode"):60111,y=e?Symbol.for("react.forward_ref"):60112,l=e?Symbol.for("react.suspense"):60113,p=e?Symbol.for("react.suspense_list"):60120,m=e?Symbol.for("react.memo"):60115,d=e?Symbol.for("react.lazy"):60116,b=e?Symbol.for("react.fundamental"):60117,S=e?Symbol.for("react.responder"):60118,$=e?Symbol.for("react.scope"):60119;var v=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),o=1;t>o;o++)r[o-1]=arguments[o];var n=0,c="Warning: "+e.replace(/%s/g,(function(){return r[n++]}));void 0!==console&&console.warn(c);try{throw Error(c)}catch(e){}},g=function(e,t){if(void 0===t)throw Error("`lowPriorityWarningWithoutStack(condition, format, ...args)` requires a warning message argument");if(!e){for(var r=arguments.length,o=Array(r>2?r-2:0),n=2;r>n;n++)o[n-2]=arguments[n];v.apply(void 0,[t].concat(o))}};function P(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:var p=e.type;switch(p){case i:case u:case n:case a:case c:case l:return p;default:var b=p&&p.$$typeof;switch(b){case s:case y:case d:case m:case f:return b;default:return t}}case o:return t}}}var h=i,w=u,M=s,x=f,C=r,O=y,_=n,j=d,E=m,F=o,N=a,R=c,T=l,A=!1;function z(e){return P(e)===u}t.typeOf=P,t.AsyncMode=h,t.ConcurrentMode=w,t.ContextConsumer=M,t.ContextProvider=x,t.Element=C,t.ForwardRef=O,t.Fragment=_,t.Lazy=j,t.Memo=E,t.Portal=F,t.Profiler=N,t.StrictMode=R,t.Suspense=T,t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===u||e===a||e===c||e===l||e===p||"object"==typeof e&&null!==e&&(e.$$typeof===d||e.$$typeof===m||e.$$typeof===f||e.$$typeof===s||e.$$typeof===y||e.$$typeof===b||e.$$typeof===S||e.$$typeof===$)},t.isAsyncMode=function(e){return A||(A=!0,g(!1,"The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),z(e)||P(e)===i},t.isConcurrentMode=z,t.isContextConsumer=function(e){return P(e)===s},t.isContextProvider=function(e){return P(e)===f},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return P(e)===y},t.isFragment=function(e){return P(e)===n},t.isLazy=function(e){return P(e)===d},t.isMemo=function(e){return P(e)===m},t.isPortal=function(e){return P(e)===o},t.isProfiler=function(e){return P(e)===a},t.isStrictMode=function(e){return P(e)===c},t.isSuspense=function(e){return P(e)===l}}()}));e(o);var n=t((function(e){e.exports="production"===process.env.NODE_ENV?r:o})),c=n.Memo,a=n.isMemo,f={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},s={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},u={};function y(e){return a(e)?i:u[e.$$typeof]||f}u[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u[c]=i;var l=Object.defineProperty,p=Object.getOwnPropertyNames,m=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,b=Object.getPrototypeOf,S=Object.prototype;return function e(t,r,o){if("string"!=typeof r){if(S){var n=b(r);n&&n!==S&&e(t,n,o)}var c=p(r);m&&(c=c.concat(m(r)));for(var a=y(t),f=y(r),i=0;c.length>i;++i){var u=c[i];if(!(s[u]||o&&o[u]||f&&f[u]||a&&a[u])){var $=d(r,u);try{l(t,u,$)}catch(e){}}}}return t}}));
