/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

"use strict";

// Implements an API-compatible subset of source-map's `SourceMapConsumer`.
const DelegatingConsumer = require("./DelegatingConsumer");
module.exports = DelegatingConsumer;
