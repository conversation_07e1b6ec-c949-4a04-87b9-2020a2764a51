{"version": 3, "names": ["getBuildToolsVersion", "projectRoot", "findProjectRoot", "logger", "log", "warn", "gradleBuildFilePath", "path", "join", "buildToolsVersionEntry", "fs", "existsSync", "gradleBuildFile", "readFileSync", "buildToolsVersionIndex", "indexOf", "buildToolsVersion", "substring", "split", "match", "installMessage", "chalk", "dim", "isSDKInstalled", "environmentInfo", "version", "SDKs", "label", "description", "getDiagnostics", "config", "requiredVersion", "root", "buildTools", "isAndroidSDKInstalled", "Array", "isArray", "isRequiredVersionInstalled", "includes", "versions", "versionRange", "needsToBeFixed", "win32AutomaticFix", "loader", "cliToolsUrl", "systemImage", "componentsToInstall", "androidSDKRoot", "getAndroidSdkRootInstallation", "fail", "downloadAndUnzip", "downloadUrl", "component", "installPath", "text", "installComponent", "e", "setEnvironment", "updateEnvironment", "hypervisor", "installed", "getBestHypervisor", "enableAMDH", "enableHAXM", "enableWHPX", "createAVD", "succeed", "runAutomaticFix", "logManualInstallation", "message", "healthcheck", "url", "link", "docs", "hash", "guide", "platform"], "sources": ["../../../src/tools/healthchecks/androidSDK.ts"], "sourcesContent": ["import {findProjectRoot, logger, link} from '@react-native-community/cli-tools';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\nimport {EnvironmentInfo, HealthCheckInterface} from '../../types';\nimport {downloadAndUnzip} from '../downloadAndUnzip';\nimport {\n  createAVD,\n  enableAMDH,\n  enableHAXM,\n  enableWHPX,\n  getAndroidSdkRootInstallation,\n  getBestHypervisor,\n  installComponent,\n} from '../windows/androidWinHelpers';\nimport {\n  setEnvironment,\n  updateEnvironment,\n} from '../windows/environmentVariables';\n\nconst getBuildToolsVersion = (projectRoot = ''): string => {\n  try {\n    // doctor is a detached command, so we may not be in a RN project.\n    projectRoot = projectRoot || findProjectRoot();\n  } catch {\n    logger.log(); // for extra space\n    logger.warn(\n      \"We couldn't find a package.json in this directory. Android SDK checks may fail. <PERSON> works best in a React Native project root.\",\n    );\n  }\n\n  const gradleBuildFilePath = path.join(projectRoot, 'android/build.gradle');\n\n  const buildToolsVersionEntry = 'buildToolsVersion';\n\n  if (!fs.existsSync(gradleBuildFilePath)) {\n    return 'Not Found';\n  }\n\n  // Read the content of the `build.gradle` file\n  const gradleBuildFile = fs.readFileSync(gradleBuildFilePath, 'utf-8');\n\n  const buildToolsVersionIndex = gradleBuildFile.indexOf(\n    buildToolsVersionEntry,\n  );\n\n  const buildToolsVersion = (\n    gradleBuildFile\n      // Get only the portion of the declaration of `buildToolsVersion`\n      .substring(buildToolsVersionIndex)\n      .split('\\n')[0]\n      // Get only the the value of `buildToolsVersion`\n      .match(/\\d|\\../g) || []\n  ).join('');\n\n  return buildToolsVersion || 'Not Found';\n};\n\nconst installMessage = `Read more about how to update Android SDK at ${chalk.dim(\n  'https://developer.android.com/studio',\n)}`;\n\nconst isSDKInstalled = (environmentInfo: EnvironmentInfo) => {\n  const version = environmentInfo.SDKs['Android SDK'];\n  return version !== 'Not Found';\n};\n\nexport default {\n  label: 'Android SDK',\n  description: 'Required for building and installing your app on Android',\n  getDiagnostics: async ({SDKs}, config) => {\n    const requiredVersion = getBuildToolsVersion(config?.root);\n    const buildTools =\n      typeof SDKs['Android SDK'] === 'string'\n        ? SDKs['Android SDK']\n        : SDKs['Android SDK']['Build Tools'];\n\n    const isAndroidSDKInstalled = Array.isArray(buildTools);\n\n    const isRequiredVersionInstalled = isAndroidSDKInstalled\n      ? buildTools.includes(requiredVersion)\n      : false;\n\n    return {\n      versions: isAndroidSDKInstalled ? buildTools : SDKs['Android SDK'],\n      versionRange: requiredVersion,\n      needsToBeFixed: !isRequiredVersionInstalled,\n    };\n  },\n  win32AutomaticFix: async ({loader}) => {\n    // Need a GitHub action to update automatically. See #1180\n    const cliToolsUrl =\n      'https://dl.google.com/android/repository/commandlinetools-win-8512546_latest.zip';\n\n    const systemImage = 'system-images;android-31;google_apis;x86_64';\n    // Installing 29 as well so Android Studio does not complain on first boot\n    const componentsToInstall = [\n      'platform-tools',\n      'build-tools;31.0.0',\n      'platforms;android-31',\n      // Is 28 still needed?\n      'build-tools;28.0.3',\n      'platforms;android-28',\n      'emulator',\n      systemImage,\n      '--licenses', // Accept any pending licenses at the end\n    ];\n\n    const androidSDKRoot = getAndroidSdkRootInstallation();\n\n    if (androidSDKRoot === '') {\n      loader.fail('There was an error finding the Android SDK root');\n\n      return;\n    }\n\n    await downloadAndUnzip({\n      loader,\n      downloadUrl: cliToolsUrl,\n      component: 'Android Command Line Tools',\n      installPath: androidSDKRoot,\n    });\n\n    for (const component of componentsToInstall) {\n      loader.text = `Installing \"${component}\" (this may take a few minutes)`;\n\n      try {\n        await installComponent(component, androidSDKRoot);\n      } catch (e) {\n        // Is there a way to persist a line in loader and continue the execution?\n      }\n    }\n\n    loader.text = 'Updating environment variables';\n\n    // Required for the emulator to work from the CLI\n    await setEnvironment('ANDROID_SDK_ROOT', androidSDKRoot);\n    await setEnvironment('ANDROID_HOME', androidSDKRoot);\n    await updateEnvironment('PATH', path.join(androidSDKRoot, 'tools'));\n    await updateEnvironment(\n      'PATH',\n      path.join(androidSDKRoot, 'platform-tools'),\n    );\n\n    loader.text =\n      'Configuring Hypervisor for faster emulation, this might prompt UAC';\n\n    const {hypervisor, installed} = await getBestHypervisor(androidSDKRoot);\n\n    if (!installed) {\n      if (hypervisor === 'none') {\n        loader.warn(\n          'Android SDK configured but virtualization could not be enabled.',\n        );\n        return;\n      }\n\n      if (hypervisor === 'AMDH') {\n        await enableAMDH(androidSDKRoot);\n      } else if (hypervisor === 'HAXM') {\n        await enableHAXM(androidSDKRoot);\n      } else if (hypervisor === 'WHPX') {\n        await enableWHPX();\n      }\n    }\n\n    loader.text = 'Creating AVD';\n    await createAVD(androidSDKRoot, 'pixel_9.0', 'pixel', systemImage);\n\n    loader.succeed(\n      'Android SDK configured. You might need to restart your PC for all changes to take effect.',\n    );\n  },\n  runAutomaticFix: async ({loader, logManualInstallation, environmentInfo}) => {\n    loader.fail();\n\n    if (isSDKInstalled(environmentInfo)) {\n      return logManualInstallation({\n        message: installMessage,\n      });\n    }\n\n    return logManualInstallation({\n      healthcheck: 'Android SDK',\n      url: link.docs('environment-setup', {\n        hash: 'android-sdk',\n        guide: 'native',\n        platform: 'android',\n      }),\n    });\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AASA;AAGyC;AAEzC,MAAMA,oBAAoB,GAAG,CAACC,WAAW,GAAG,EAAE,KAAa;EACzD,IAAI;IACF;IACAA,WAAW,GAAGA,WAAW,IAAI,IAAAC,2BAAe,GAAE;EAChD,CAAC,CAAC,MAAM;IACNC,kBAAM,CAACC,GAAG,EAAE,CAAC,CAAC;IACdD,kBAAM,CAACE,IAAI,CACT,mIAAmI,CACpI;EACH;EAEA,MAAMC,mBAAmB,GAAGC,eAAI,CAACC,IAAI,CAACP,WAAW,EAAE,sBAAsB,CAAC;EAE1E,MAAMQ,sBAAsB,GAAG,mBAAmB;EAElD,IAAI,CAACC,aAAE,CAACC,UAAU,CAACL,mBAAmB,CAAC,EAAE;IACvC,OAAO,WAAW;EACpB;;EAEA;EACA,MAAMM,eAAe,GAAGF,aAAE,CAACG,YAAY,CAACP,mBAAmB,EAAE,OAAO,CAAC;EAErE,MAAMQ,sBAAsB,GAAGF,eAAe,CAACG,OAAO,CACpDN,sBAAsB,CACvB;EAED,MAAMO,iBAAiB,GAAG,CACxBJ;EACE;EAAA,CACCK,SAAS,CAACH,sBAAsB,CAAC,CACjCI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;EACd;EAAA,CACCC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,EACzBX,IAAI,CAAC,EAAE,CAAC;EAEV,OAAOQ,iBAAiB,IAAI,WAAW;AACzC,CAAC;AAED,MAAMI,cAAc,GAAI,gDAA+CC,gBAAK,CAACC,GAAG,CAC9E,sCAAsC,CACtC,EAAC;AAEH,MAAMC,cAAc,GAAIC,eAAgC,IAAK;EAC3D,MAAMC,OAAO,GAAGD,eAAe,CAACE,IAAI,CAAC,aAAa,CAAC;EACnD,OAAOD,OAAO,KAAK,WAAW;AAChC,CAAC;AAAC,eAEa;EACbE,KAAK,EAAE,aAAa;EACpBC,WAAW,EAAE,0DAA0D;EACvEC,cAAc,EAAE,OAAO;IAACH;EAAI,CAAC,EAAEI,MAAM,KAAK;IACxC,MAAMC,eAAe,GAAG/B,oBAAoB,CAAC8B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,IAAI,CAAC;IAC1D,MAAMC,UAAU,GACd,OAAOP,IAAI,CAAC,aAAa,CAAC,KAAK,QAAQ,GACnCA,IAAI,CAAC,aAAa,CAAC,GACnBA,IAAI,CAAC,aAAa,CAAC,CAAC,aAAa,CAAC;IAExC,MAAMQ,qBAAqB,GAAGC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC;IAEvD,MAAMI,0BAA0B,GAAGH,qBAAqB,GACpDD,UAAU,CAACK,QAAQ,CAACP,eAAe,CAAC,GACpC,KAAK;IAET,OAAO;MACLQ,QAAQ,EAAEL,qBAAqB,GAAGD,UAAU,GAAGP,IAAI,CAAC,aAAa,CAAC;MAClEc,YAAY,EAAET,eAAe;MAC7BU,cAAc,EAAE,CAACJ;IACnB,CAAC;EACH,CAAC;EACDK,iBAAiB,EAAE,OAAO;IAACC;EAAM,CAAC,KAAK;IACrC;IACA,MAAMC,WAAW,GACf,kFAAkF;IAEpF,MAAMC,WAAW,GAAG,6CAA6C;IACjE;IACA,MAAMC,mBAAmB,GAAG,CAC1B,gBAAgB,EAChB,oBAAoB,EACpB,sBAAsB;IACtB;IACA,oBAAoB,EACpB,sBAAsB,EACtB,UAAU,EACVD,WAAW,EACX,YAAY,CAAE;IAAA,CACf;;IAED,MAAME,cAAc,GAAG,IAAAC,gDAA6B,GAAE;IAEtD,IAAID,cAAc,KAAK,EAAE,EAAE;MACzBJ,MAAM,CAACM,IAAI,CAAC,iDAAiD,CAAC;MAE9D;IACF;IAEA,MAAM,IAAAC,kCAAgB,EAAC;MACrBP,MAAM;MACNQ,WAAW,EAAEP,WAAW;MACxBQ,SAAS,EAAE,4BAA4B;MACvCC,WAAW,EAAEN;IACf,CAAC,CAAC;IAEF,KAAK,MAAMK,SAAS,IAAIN,mBAAmB,EAAE;MAC3CH,MAAM,CAACW,IAAI,GAAI,eAAcF,SAAU,iCAAgC;MAEvE,IAAI;QACF,MAAM,IAAAG,mCAAgB,EAACH,SAAS,EAAEL,cAAc,CAAC;MACnD,CAAC,CAAC,OAAOS,CAAC,EAAE;QACV;MAAA;IAEJ;IAEAb,MAAM,CAACW,IAAI,GAAG,gCAAgC;;IAE9C;IACA,MAAM,IAAAG,oCAAc,EAAC,kBAAkB,EAAEV,cAAc,CAAC;IACxD,MAAM,IAAAU,oCAAc,EAAC,cAAc,EAAEV,cAAc,CAAC;IACpD,MAAM,IAAAW,uCAAiB,EAAC,MAAM,EAAEnD,eAAI,CAACC,IAAI,CAACuC,cAAc,EAAE,OAAO,CAAC,CAAC;IACnE,MAAM,IAAAW,uCAAiB,EACrB,MAAM,EACNnD,eAAI,CAACC,IAAI,CAACuC,cAAc,EAAE,gBAAgB,CAAC,CAC5C;IAEDJ,MAAM,CAACW,IAAI,GACT,oEAAoE;IAEtE,MAAM;MAACK,UAAU;MAAEC;IAAS,CAAC,GAAG,MAAM,IAAAC,oCAAiB,EAACd,cAAc,CAAC;IAEvE,IAAI,CAACa,SAAS,EAAE;MACd,IAAID,UAAU,KAAK,MAAM,EAAE;QACzBhB,MAAM,CAACtC,IAAI,CACT,iEAAiE,CAClE;QACD;MACF;MAEA,IAAIsD,UAAU,KAAK,MAAM,EAAE;QACzB,MAAM,IAAAG,6BAAU,EAACf,cAAc,CAAC;MAClC,CAAC,MAAM,IAAIY,UAAU,KAAK,MAAM,EAAE;QAChC,MAAM,IAAAI,6BAAU,EAAChB,cAAc,CAAC;MAClC,CAAC,MAAM,IAAIY,UAAU,KAAK,MAAM,EAAE;QAChC,MAAM,IAAAK,6BAAU,GAAE;MACpB;IACF;IAEArB,MAAM,CAACW,IAAI,GAAG,cAAc;IAC5B,MAAM,IAAAW,4BAAS,EAAClB,cAAc,EAAE,WAAW,EAAE,OAAO,EAAEF,WAAW,CAAC;IAElEF,MAAM,CAACuB,OAAO,CACZ,2FAA2F,CAC5F;EACH,CAAC;EACDC,eAAe,EAAE,OAAO;IAACxB,MAAM;IAAEyB,qBAAqB;IAAE5C;EAAe,CAAC,KAAK;IAC3EmB,MAAM,CAACM,IAAI,EAAE;IAEb,IAAI1B,cAAc,CAACC,eAAe,CAAC,EAAE;MACnC,OAAO4C,qBAAqB,CAAC;QAC3BC,OAAO,EAAEjD;MACX,CAAC,CAAC;IACJ;IAEA,OAAOgD,qBAAqB,CAAC;MAC3BE,WAAW,EAAE,aAAa;MAC1BC,GAAG,EAAEC,gBAAI,CAACC,IAAI,CAAC,mBAAmB,EAAE;QAClCC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,QAAQ;QACfC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC;AAAA"}