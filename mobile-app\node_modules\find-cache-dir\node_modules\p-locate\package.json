{"name": "p-locate", "version": "3.0.0", "description": "Get the first fulfilled promise that satisfies the provided testing function", "license": "MIT", "repository": "sindresorhus/p-locate", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "locate", "find", "finder", "search", "searcher", "test", "array", "collection", "iterable", "iterator", "race", "fulfilled", "fastest", "async", "await", "promises", "bluebird"], "dependencies": {"p-limit": "^2.0.0"}, "devDependencies": {"ava": "*", "delay": "^3.0.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "xo": "*"}}