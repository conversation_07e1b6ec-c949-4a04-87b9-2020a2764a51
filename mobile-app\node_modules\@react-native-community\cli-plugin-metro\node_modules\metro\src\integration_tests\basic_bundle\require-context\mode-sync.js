"use strict";

var _utils = require("./utils");
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 *
 */

function main() {
  return (0, _utils.copyContextToObject)(
    // $FlowFixMe[underconstrained-implicit-instantiation]
    require.context("./subdir", undefined, undefined, "sync")
  );
}
module.exports = main();
