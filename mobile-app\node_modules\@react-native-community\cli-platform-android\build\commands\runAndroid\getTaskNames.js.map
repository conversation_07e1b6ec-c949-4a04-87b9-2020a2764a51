{"version": 3, "names": ["getTaskNames", "appName", "mode", "tasks", "taskPrefix", "sourceDir", "appTasks", "length", "toPascalCase", "actionableInstallTasks", "getGradleTasks", "find", "t", "task", "includes", "installTasksForMode", "filter", "toLowerCase", "CLIError", "map", "taskName", "replace", "join", "logger", "warn", "command"], "sources": ["../../../src/commands/runAndroid/getTaskNames.ts"], "sourcesContent": ["import {toPascalCase} from './toPascalCase';\nimport type {BuildFlags} from '../buildAndroid';\nimport {getGradleTasks} from './listAndroidTasks';\nimport {CLIError, logger} from '@react-native-community/cli-tools';\n\nexport function getTaskNames(\n  appName: string,\n  mode: BuildFlags['mode'] = 'debug',\n  tasks: BuildFlags['tasks'],\n  taskPrefix: 'assemble' | 'install' | 'bundle',\n  sourceDir: string,\n): Array<string> {\n  let appTasks =\n    tasks && tasks.length ? tasks : [taskPrefix + toPascalCase(mode)];\n\n  // Check against build flavors for \"install\" task (\"assemble\" don't care about it so much and will build all)\n  if (!tasks?.length && taskPrefix === 'install') {\n    const actionableInstallTasks = getGradleTasks('install', sourceDir);\n    if (!actionableInstallTasks.find((t) => t.task.includes(appTasks[0]))) {\n      const installTasksForMode = actionableInstallTasks.filter((t) =>\n        t.task.toLowerCase().includes(mode),\n      );\n      if (!installTasksForMode.length) {\n        throw new CLIError(\n          `Couldn't find \"${appTasks\n            .map((taskName) => taskName.replace(taskPrefix, ''))\n            .join(\n              ', ',\n            )}\" build variant. Available variants are: ${actionableInstallTasks\n            .map((t) => `\"${t.task.replace(taskPrefix, '')}\"`)\n            .join(', ')}.`,\n        );\n      }\n      logger.warn(\n        `Found multiple tasks for \"install\" command: ${installTasksForMode\n          .map((t) => t.task)\n          .join(', ')}.\\nSelecting first available: ${\n          installTasksForMode[0].task\n        }.`,\n      );\n      appTasks = [installTasksForMode[0].task];\n    }\n  }\n\n  return appName\n    ? appTasks.map((command) => `${appName}:${command}`)\n    : appTasks;\n}\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEO,SAASA,YAAY,CAC1BC,OAAe,EACfC,IAAwB,GAAG,OAAO,EAClCC,KAA0B,EAC1BC,UAA6C,EAC7CC,SAAiB,EACF;EACf,IAAIC,QAAQ,GACVH,KAAK,IAAIA,KAAK,CAACI,MAAM,GAAGJ,KAAK,GAAG,CAACC,UAAU,GAAG,IAAAI,0BAAY,EAACN,IAAI,CAAC,CAAC;;EAEnE;EACA,IAAI,EAACC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,MAAM,KAAIH,UAAU,KAAK,SAAS,EAAE;IAC9C,MAAMK,sBAAsB,GAAG,IAAAC,gCAAc,EAAC,SAAS,EAAEL,SAAS,CAAC;IACnE,IAAI,CAACI,sBAAsB,CAACE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,CAACC,QAAQ,CAACR,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrE,MAAMS,mBAAmB,GAAGN,sBAAsB,CAACO,MAAM,CAAEJ,CAAC,IAC1DA,CAAC,CAACC,IAAI,CAACI,WAAW,EAAE,CAACH,QAAQ,CAACZ,IAAI,CAAC,CACpC;MACD,IAAI,CAACa,mBAAmB,CAACR,MAAM,EAAE;QAC/B,MAAM,KAAIW,oBAAQ,EACf,kBAAiBZ,QAAQ,CACvBa,GAAG,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,OAAO,CAACjB,UAAU,EAAE,EAAE,CAAC,CAAC,CACnDkB,IAAI,CACH,IAAI,CACJ,4CAA2Cb,sBAAsB,CAClEU,GAAG,CAAEP,CAAC,IAAM,IAAGA,CAAC,CAACC,IAAI,CAACQ,OAAO,CAACjB,UAAU,EAAE,EAAE,CAAE,GAAE,CAAC,CACjDkB,IAAI,CAAC,IAAI,CAAE,GAAE,CACjB;MACH;MACAC,kBAAM,CAACC,IAAI,CACR,+CAA8CT,mBAAmB,CAC/DI,GAAG,CAAEP,CAAC,IAAKA,CAAC,CAACC,IAAI,CAAC,CAClBS,IAAI,CAAC,IAAI,CAAE,iCACZP,mBAAmB,CAAC,CAAC,CAAC,CAACF,IACxB,GAAE,CACJ;MACDP,QAAQ,GAAG,CAACS,mBAAmB,CAAC,CAAC,CAAC,CAACF,IAAI,CAAC;IAC1C;EACF;EAEA,OAAOZ,OAAO,GACVK,QAAQ,CAACa,GAAG,CAAEM,OAAO,IAAM,GAAExB,OAAQ,IAAGwB,OAAQ,EAAC,CAAC,GAClDnB,QAAQ;AACd"}