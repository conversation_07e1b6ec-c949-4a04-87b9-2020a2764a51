{"version": 3, "names": ["commands", "info", "doctor"], "sources": ["../src/index.ts"], "sourcesContent": ["import doctor from './commands/doctor';\nimport info from './commands/info';\n\nexport const commands = {info, doctor};\n\n/**\n * @todo\n * We should not rely on this file from other packages, e.g. CLI. We probably need to\n * refactor the init in order to remove that connection.\n */\nexport {default as versionRanges} from './tools/versionRanges';\nexport {default as installPods} from './tools/installPods';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AASA;AACA;AAA2D;AARpD,MAAMA,QAAQ,GAAG;EAACC,IAAI,EAAJA,aAAI;EAAEC,MAAM,EAANA;AAAM,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AAJA"}