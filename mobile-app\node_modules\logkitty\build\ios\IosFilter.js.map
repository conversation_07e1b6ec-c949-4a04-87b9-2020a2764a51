{"version": 3, "sources": ["../../src/ios/IosFilter.ts"], "names": ["<PERSON>os<PERSON><PERSON><PERSON>", "constructor", "minPriority", "filter", "entry", "priority", "setFilterByTag", "tags", "Boolean", "tag", "indexOf", "setFilterByMatch", "regexes", "find", "reg", "messages", "message", "test", "shouldInclude"], "mappings": ";;;;;;;;;AAIe,MAAMA,SAAN,CAAmC;AAIhDC,EAAAA,WAAW,CAACC,WAAmB,GAAG,CAAvB,EAA0B;AAAA;;AAAA;;AACnC,SAAKA,WAAL,GAAmBA,WAAnB,CADmC,CAEnC;;AACA,SAAKC,MAAL,GAAeC,KAAD,IAAkB;AAC9B,aAAOA,KAAK,CAACC,QAAN,IAAkB,KAAKH,WAA9B;AACD,KAFD;AAGD;;AAEDI,EAAAA,cAAc,CAACC,IAAD,EAAiB;AAC7B,SAAKJ,MAAL,GAAeC,KAAD,IAAkB;AAC9B,aAAOI,OAAO,CACZJ,KAAK,CAACC,QAAN,IAAkB,KAAKH,WAAvB,IACEE,KAAK,CAACK,GADR,IAEEF,IAAI,CAACG,OAAL,CAAaN,KAAK,CAACK,GAAnB,IAA0B,CAAC,CAHjB,CAAd;AAKD,KAND;AAOD;;AAEDE,EAAAA,gBAAgB,CAACC,OAAD,EAAoB;AAClC,SAAKT,MAAL,GAAeC,KAAD,IAAkB;AAC9B,aACEA,KAAK,CAACC,QAAN,IAAkB,KAAKH,WAAvB,IACAM,OAAO,CACLI,OAAO,CAACC,IAAR,CAAcC,GAAD,IACXN,OAAO,CAACJ,KAAK,CAACW,QAAN,CAAeF,IAAf,CAAqBG,OAAD,IAAqBF,GAAG,CAACG,IAAJ,CAASD,OAAT,CAAzC,CAAD,CADT,CADK,CAFT;AAQD,KATD;AAUD;;AAEDE,EAAAA,aAAa,CAACd,KAAD,EAAe;AAC1B,WAAO,KAAKD,MAAL,CAAYC,KAAZ,CAAP;AACD;;AArC+C", "sourcesContent": ["import { IFilter, Entry } from '../types';\n\ntype Filter = (entry: Entry) => boolean;\n\nexport default class IosFilter implements IFilter {\n  private readonly minPriority: number;\n  private filter: Filter;\n\n  constructor(minPriority: number = 0) {\n    this.minPriority = minPriority;\n    // Default filter by all\n    this.filter = (entry: Entry) => {\n      return entry.priority >= this.minPriority;\n    };\n  }\n\n  setFilterByTag(tags: string[]) {\n    this.filter = (entry: Entry) => {\n      return Boolean(\n        entry.priority >= this.minPriority &&\n          entry.tag &&\n          tags.indexOf(entry.tag) > -1\n      );\n    };\n  }\n\n  setFilterByMatch(regexes: RegExp[]) {\n    this.filter = (entry: Entry) => {\n      return (\n        entry.priority >= this.minPriority &&\n        Boolean(\n          regexes.find((reg: RegExp) =>\n            Boolean(entry.messages.find((message: string) => reg.test(message)))\n          )\n        )\n      );\n    };\n  }\n\n  shouldInclude(entry: Entry) {\n    return this.filter(entry);\n  }\n}\n"], "file": "IosFilter.js"}