/**
 * Pure function - doesn't mutate either parameter!
 * Uses the default options and overrides with the options provided by the user
 * @param defaultOptions the defaults
 * @param userOptions the user opts
 * @returns the options with defaults
 */
declare function applyDefault<TUser extends readonly unknown[], TDefault extends TUser>(defaultOptions: Readonly<TDefault>, userOptions: Readonly<TUser> | null): TDefault;
export { applyDefault };
//# sourceMappingURL=applyDefault.d.ts.map
