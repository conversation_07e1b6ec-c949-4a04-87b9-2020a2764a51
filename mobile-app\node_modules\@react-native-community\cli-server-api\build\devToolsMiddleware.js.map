{"version": 3, "names": ["launchDefaultDebugger", "host", "port", "args", "hostname", "debuggerURL", "logger", "info", "launchDebugger", "escape<PERSON><PERSON>", "pathname", "launchDevTools", "watchFolders", "isDebuggerConnected", "customDebugger", "process", "env", "REACT_DEBUGGER", "startCustomDebugger", "folders", "map", "join", "command", "exec", "error", "stack", "getDevToolsMiddleware", "options", "devToolsMiddleware", "_req", "res", "end"], "sources": ["../src/devToolsMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport http from 'http';\nimport {launchDebugger, logger} from '@react-native-community/cli-tools';\nimport {exec} from 'child_process';\n\nfunction launchDefaultDebugger(\n  host: string | undefined,\n  port: number,\n  args = '',\n) {\n  const hostname = host || 'localhost';\n  const debuggerURL = `http://${hostname}:${port}/debugger-ui${args}`;\n  logger.info('Launching Dev Tools...');\n  launchDebugger(debuggerURL);\n}\n\nfunction escapePath(pathname: string) {\n  // \" Can escape paths with spaces in OS X, Windows, and *nix\n  return `\"${pathname}\"`;\n}\n\ntype LaunchDevToolsOptions = {\n  host?: string;\n  port: number;\n  watchFolders: ReadonlyArray<string>;\n};\n\nfunction launchDevTools(\n  {host, port, watchFolders}: LaunchDevToolsOptions,\n  isDebuggerConnected: () => boolean,\n) {\n  // Explicit config always wins\n  const customDebugger = process.env.REACT_DEBUGGER;\n  if (customDebugger) {\n    startCustomDebugger({watchFolders, customDebugger});\n  } else if (!isDebuggerConnected()) {\n    // Debugger is not yet open; we need to open a session\n    launchDefaultDebugger(host, port);\n  }\n}\n\nfunction startCustomDebugger({\n  watchFolders,\n  customDebugger,\n}: {\n  watchFolders: ReadonlyArray<string>;\n  customDebugger: string;\n}) {\n  const folders = watchFolders.map(escapePath).join(' ');\n  const command = `${customDebugger} ${folders}`;\n  logger.info('Starting custom debugger by executing:', command);\n  exec(command, function (error) {\n    if (error !== null) {\n      logger.error('Error while starting custom debugger:', error.stack || '');\n    }\n  });\n}\n\nexport default function getDevToolsMiddleware(\n  options: LaunchDevToolsOptions,\n  isDebuggerConnected: () => boolean,\n) {\n  return function devToolsMiddleware(\n    _req: http.IncomingMessage,\n    res: http.ServerResponse,\n  ) {\n    launchDevTools(options, isDebuggerConnected);\n    res.end('OK');\n  };\n}\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AARA;AACA;AACA;AACA;AACA;AACA;;AAKA,SAASA,qBAAqB,CAC5BC,IAAwB,EACxBC,IAAY,EACZC,IAAI,GAAG,EAAE,EACT;EACA,MAAMC,QAAQ,GAAGH,IAAI,IAAI,WAAW;EACpC,MAAMI,WAAW,GAAI,UAASD,QAAS,IAAGF,IAAK,eAAcC,IAAK,EAAC;EACnEG,kBAAM,CAACC,IAAI,CAAC,wBAAwB,CAAC;EACrC,IAAAC,0BAAc,EAACH,WAAW,CAAC;AAC7B;AAEA,SAASI,UAAU,CAACC,QAAgB,EAAE;EACpC;EACA,OAAQ,IAAGA,QAAS,GAAE;AACxB;AAQA,SAASC,cAAc,CACrB;EAACV,IAAI;EAAEC,IAAI;EAAEU;AAAmC,CAAC,EACjDC,mBAAkC,EAClC;EACA;EACA,MAAMC,cAAc,GAAGC,OAAO,CAACC,GAAG,CAACC,cAAc;EACjD,IAAIH,cAAc,EAAE;IAClBI,mBAAmB,CAAC;MAACN,YAAY;MAAEE;IAAc,CAAC,CAAC;EACrD,CAAC,MAAM,IAAI,CAACD,mBAAmB,EAAE,EAAE;IACjC;IACAb,qBAAqB,CAACC,IAAI,EAAEC,IAAI,CAAC;EACnC;AACF;AAEA,SAASgB,mBAAmB,CAAC;EAC3BN,YAAY;EACZE;AAIF,CAAC,EAAE;EACD,MAAMK,OAAO,GAAGP,YAAY,CAACQ,GAAG,CAACX,UAAU,CAAC,CAACY,IAAI,CAAC,GAAG,CAAC;EACtD,MAAMC,OAAO,GAAI,GAAER,cAAe,IAAGK,OAAQ,EAAC;EAC9Cb,kBAAM,CAACC,IAAI,CAAC,wCAAwC,EAAEe,OAAO,CAAC;EAC9D,IAAAC,qBAAI,EAACD,OAAO,EAAE,UAAUE,KAAK,EAAE;IAC7B,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClBlB,kBAAM,CAACkB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAACC,KAAK,IAAI,EAAE,CAAC;IAC1E;EACF,CAAC,CAAC;AACJ;AAEe,SAASC,qBAAqB,CAC3CC,OAA8B,EAC9Bd,mBAAkC,EAClC;EACA,OAAO,SAASe,kBAAkB,CAChCC,IAA0B,EAC1BC,GAAwB,EACxB;IACAnB,cAAc,CAACgB,OAAO,EAAEd,mBAAmB,CAAC;IAC5CiB,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC;EACf,CAAC;AACH"}