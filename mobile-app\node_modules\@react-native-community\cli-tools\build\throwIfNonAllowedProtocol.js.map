{"version": 3, "names": ["ALLOWED_PROTOCOLS", "throwIfNonAllowedProtocol", "url", "_url", "URL", "urlProtocol", "protocol", "includes", "Error", "join"], "sources": ["../src/throwIfNonAllowedProtocol.ts"], "sourcesContent": ["/**\n * Check if a url uses an allowed protocol\n */\n\nconst ALLOWED_PROTOCOLS = ['http:', 'https:', 'devtools:', 'flipper:'];\n\nexport default function throwIfNonAllowedProtocol(url: string) {\n  const _url = new URL(url);\n  const urlProtocol = _url.protocol;\n\n  if (!ALLOWED_PROTOCOLS.includes(urlProtocol)) {\n    throw new Error(\n      `Invalid url protocol ${urlProtocol}.\\nAllowed protocols: ${ALLOWED_PROTOCOLS.join(\n        ', ',\n      )}`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;;AAEA,MAAMA,iBAAiB,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;AAEvD,SAASC,yBAAyB,CAACC,GAAW,EAAE;EAC7D,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAACF,GAAG,CAAC;EACzB,MAAMG,WAAW,GAAGF,IAAI,CAACG,QAAQ;EAEjC,IAAI,CAACN,iBAAiB,CAACO,QAAQ,CAACF,WAAW,CAAC,EAAE;IAC5C,MAAM,IAAIG,KAAK,CACZ,wBAAuBH,WAAY,yBAAwBL,iBAAiB,CAACS,IAAI,CAChF,IAAI,CACJ,EAAC,CACJ;EACH;AACF"}