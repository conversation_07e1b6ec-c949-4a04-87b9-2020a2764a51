/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import type { AssetData } from 'metro';
declare function saveAssets(assets: ReadonlyArray<AssetData>, platform: string, assetsDest: string | undefined, assetCatalogDest: string | undefined): Promise<void> | undefined;
export default saveAssets;
//# sourceMappingURL=saveAssets.d.ts.map