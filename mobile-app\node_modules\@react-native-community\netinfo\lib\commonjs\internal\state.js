"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _nativeInterface = _interopRequireDefault(require("./nativeInterface"));

var _internetReachability = _interopRequireDefault(require("./internetReachability"));

var PrivateTypes = _interopRequireWildcard(require("./privateTypes"));

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

class State {
  constructor(configuration) {
    _defineProperty(this, "_nativeEventSubscription", null);

    _defineProperty(this, "_subscriptions", new Set());

    _defineProperty(this, "_latestState", null);

    _defineProperty(this, "_internetReachability", void 0);

    _defineProperty(this, "_handleNativeStateUpdate", state => {
      // Update the internet reachability module
      this._internetReachability.update(state); // Convert the state from native to JS shape


      const convertedState = this._convertState(state); // Update the listeners


      this._latestState = convertedState;

      this._subscriptions.forEach(handler => handler(convertedState));
    });

    _defineProperty(this, "_handleInternetReachabilityUpdate", isInternetReachable => {
      if (!this._latestState) {
        return;
      }

      const nextState = { ...this._latestState,
        isInternetReachable
      };
      this._latestState = nextState;

      this._subscriptions.forEach(handler => handler(nextState));
    });

    _defineProperty(this, "_fetchCurrentState", async requestedInterface => {
      const state = await _nativeInterface.default.getCurrentState(requestedInterface); // Update the internet reachability module

      this._internetReachability.update(state); // Convert and store the new state


      const convertedState = this._convertState(state);

      if (!requestedInterface) {
        this._latestState = convertedState;

        this._subscriptions.forEach(handler => handler(convertedState));
      }

      return convertedState;
    });

    _defineProperty(this, "_convertState", input => {
      if (typeof input.isInternetReachable === 'boolean') {
        return input;
      } else {
        return { ...input,
          isInternetReachable: this._internetReachability.currentState()
        };
      }
    });

    _defineProperty(this, "latest", requestedInterface => {
      if (requestedInterface) {
        return this._fetchCurrentState(requestedInterface);
      } else if (this._latestState) {
        return Promise.resolve(this._latestState);
      } else {
        return this._fetchCurrentState();
      }
    });

    _defineProperty(this, "add", handler => {
      // Add the subscription handler to our set
      this._subscriptions.add(handler); // Send it the latest data we have


      if (this._latestState) {
        handler(this._latestState);
      } else {
        this.latest().then(handler);
      }
    });

    _defineProperty(this, "remove", handler => {
      this._subscriptions.delete(handler);
    });

    _defineProperty(this, "tearDown", () => {
      if (this._internetReachability) {
        this._internetReachability.tearDown();
      }

      if (this._nativeEventSubscription) {
        this._nativeEventSubscription.remove();
      }

      this._subscriptions.clear();
    });

    // Add the listener to the internet connectivity events
    this._internetReachability = new _internetReachability.default(configuration, this._handleInternetReachabilityUpdate); // Add the subscription to the native events

    this._nativeEventSubscription = _nativeInterface.default.eventEmitter.addListener(PrivateTypes.DEVICE_CONNECTIVITY_EVENT, this._handleNativeStateUpdate); // Fetch the current state from the native module

    this._fetchCurrentState();
  }

}

exports.default = State;
//# sourceMappingURL=state.js.map