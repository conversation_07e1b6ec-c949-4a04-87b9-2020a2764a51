{"version": 3, "names": ["replacePathSepForRegex", "string", "path", "sep", "replace", "_match", "_", "p2"], "sources": ["../../src/tools/replacePathSepForRegex.ts"], "sourcesContent": ["import path from 'path';\n\nexport default function replacePathSepForRegex(string: string) {\n  if (path.sep === '\\\\') {\n    return string.replace(\n      /(\\/|(.)?\\\\(?![[\\]{}()*+?.^$|\\\\]))/g,\n      (_match, _, p2) => (p2 && p2 !== '\\\\' ? p2 + '\\\\\\\\' : '\\\\\\\\'),\n    );\n  }\n  return string;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAET,SAASA,sBAAsB,CAACC,MAAc,EAAE;EAC7D,IAAIC,eAAI,CAACC,GAAG,KAAK,IAAI,EAAE;IACrB,OAAOF,MAAM,CAACG,OAAO,CACnB,oCAAoC,EACpC,CAACC,MAAM,EAAEC,CAAC,EAAEC,EAAE,KAAMA,EAAE,IAAIA,EAAE,KAAK,IAAI,GAAGA,EAAE,GAAG,MAAM,GAAG,MAAO,CAC9D;EACH;EACA,OAAON,MAAM;AACf"}