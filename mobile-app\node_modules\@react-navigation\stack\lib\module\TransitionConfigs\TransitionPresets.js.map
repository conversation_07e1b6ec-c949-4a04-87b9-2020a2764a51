{"version": 3, "names": ["Platform", "forBottomSheetAndroid", "forFadeFromBottomAndroid", "forFadeFromCenter", "forFadeCard", "forHorizontalIOS", "forModalPresentationIOS", "forRevealFromBottomAndroid", "forScaleFromCenterAndroid", "forVerticalIOS", "forFade", "BottomSheetSlideInSpec", "BottomSheetSlideOutSpec", "FadeInFromBottomAndroidSpec", "FadeOutToBottomAndroidSpec", "RevealFromBottomAndroidSpec", "ScaleFromCenterAndroidSpec", "TransitionIOSSpec", "ANDROID_VERSION_PIE", "ANDROID_VERSION_10", "SlideFromRightIOS", "gestureDirection", "transitionSpec", "open", "close", "cardStyleInterpolator", "headerStyleInterpolator", "ModalSlideFromBottomIOS", "ModalPresentationIOS", "FadeFromBottomAndroid", "RevealFromBottomAndroid", "ScaleFromCenterAndroid", "BottomSheetAndroid", "ModalFadeTransition", "DefaultTransition", "select", "ios", "android", "Version", "default", "ModalTransition"], "sourceRoot": "../../../src", "sources": ["TransitionConfigs/TransitionPresets.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAGvC,SACEC,qBAAqB,EACrBC,wBAAwB,EACxBC,iBAAiB,IAAIC,WAAW,EAChCC,gBAAgB,EAChBC,uBAAuB,EACvBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,cAAc,QACT,0BAA0B;AACjC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SACEC,sBAAsB,EACtBC,uBAAuB,EACvBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,iBAAiB,QACZ,mBAAmB;AAE1B,MAAMC,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,kBAAkB,GAAG,EAAE;;AAE7B;AACA;AACA;AACA,OAAO,MAAMC,iBAAmC,GAAG;EACjDC,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE;IACdC,IAAI,EAAEN,iBAAiB;IACvBO,KAAK,EAAEP;EACT,CAAC;EACDQ,qBAAqB,EAAEpB,gBAAgB;EACvCqB,uBAAuB,EAAEhB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMiB,uBAAyC,GAAG;EACvDN,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEN,iBAAiB;IACvBO,KAAK,EAAEP;EACT,CAAC;EACDQ,qBAAqB,EAAEhB,cAAc;EACrCiB,uBAAuB,EAAEhB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMkB,oBAAsC,GAAG;EACpDP,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEN,iBAAiB;IACvBO,KAAK,EAAEP;EACT,CAAC;EACDQ,qBAAqB,EAAEnB,uBAAuB;EAC9CoB,uBAAuB,EAAEhB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMmB,qBAAuC,GAAG;EACrDR,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEV,2BAA2B;IACjCW,KAAK,EAAEV;EACT,CAAC;EACDW,qBAAqB,EAAEvB,wBAAwB;EAC/CwB,uBAAuB,EAAEhB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMoB,uBAAyC,GAAG;EACvDT,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAER,2BAA2B;IACjCS,KAAK,EAAET;EACT,CAAC;EACDU,qBAAqB,EAAElB,0BAA0B;EACjDmB,uBAAuB,EAAEhB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMqB,sBAAwC,GAAG;EACtDV,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE;IACdC,IAAI,EAAEP,0BAA0B;IAChCQ,KAAK,EAAER;EACT,CAAC;EACDS,qBAAqB,EAAEjB,yBAAyB;EAChDkB,uBAAuB,EAAEhB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMsB,kBAAoC,GAAG;EAClDX,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEZ,sBAAsB;IAC5Ba,KAAK,EAAEZ;EACT,CAAC;EACDa,qBAAqB,EAAExB,qBAAqB;EAC5CyB,uBAAuB,EAAEhB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMuB,mBAAqC,GAAG;EACnDZ,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEZ,sBAAsB;IAC5Ba,KAAK,EAAEZ;EACT,CAAC;EACDa,qBAAqB,EAAErB,WAAW;EAClCsB,uBAAuB,EAAEhB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMwB,iBAAiB,GAAGlC,QAAQ,CAACmC,MAAM,CAAC;EAC/CC,GAAG,EAAEhB,iBAAiB;EACtBiB,OAAO,EACLrC,QAAQ,CAACsC,OAAO,IAAInB,kBAAkB,GAClCY,sBAAsB,GACtB/B,QAAQ,CAACsC,OAAO,IAAIpB,mBAAmB,GACvCY,uBAAuB,GACvBD,qBAAqB;EAC3BU,OAAO,EAAER;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AACA,OAAO,MAAMS,eAAe,GAAGxC,QAAQ,CAACmC,MAAM,CAAC;EAC7CC,GAAG,EAAER,oBAAoB;EACzBW,OAAO,EAAEP;AACX,CAAC,CAAC"}