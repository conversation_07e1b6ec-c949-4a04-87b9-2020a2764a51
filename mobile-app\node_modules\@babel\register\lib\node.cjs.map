{"version": 3, "names": ["hook", "require", "LocalClient", "client", "register", "opts", "Object", "assign", "module", "exports", "revert", "default"], "sources": ["../src/node.cts"], "sourcesContent": ["// TODO: Remove this file in Babel 8\n\n\"use strict\";\n\nconst hook = require(\"./hook.cjs\");\nconst { LocalClient } = require(\"./worker-client.cjs\");\n\nconst client = new LocalClient();\nfunction register(opts = {}) {\n  return hook.register(client, { ...opts });\n}\n\nmodule.exports = Object.assign(register, {\n  revert: hook.revert,\n  default: register,\n});\n"], "mappings": "AAEA,YAAY;;AAEZ,MAAMA,IAAI,GAAGC,OAAO,CAAC,YAAY,CAAC;AAClC,MAAM;EAAEC;AAAY,CAAC,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAEtD,MAAME,MAAM,GAAG,IAAID,WAAW,CAAC,CAAC;AAChC,SAASE,QAAQA,CAACC,IAAI,GAAG,CAAC,CAAC,EAAE;EAC3B,OAAOL,IAAI,CAACI,QAAQ,CAACD,MAAM,EAAAG,MAAA,CAAAC,MAAA,KAAOF,IAAI,CAAE,CAAC;AAC3C;AAEAG,MAAM,CAACC,OAAO,GAAGH,MAAM,CAACC,MAAM,CAACH,QAAQ,EAAE;EACvCM,MAAM,EAAEV,IAAI,CAACU,MAAM;EACnBC,OAAO,EAAEP;AACX,CAAC,CAAC", "ignoreList": []}