{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../src/parser.ts"], "names": [], "mappings": ";;;;;;AAIA,oEAA2D;AAO3D,4EAG8C;AAC9C,kDAA0B;AAE1B,2CAA0C;AAE1C,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,iCAAiC,CAAC,CAAC;AAarD,SAAS,eAAe,CACtB,KAA0B,EAC1B,QAAQ,GAAG,KAAK;IAEhB,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;QAC9B,OAAO,QAAQ,CAAC;KACjB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,kBAAkB,GAAG,wBAAwB,CAAC;AACpD,SAAS,MAAM,CAAC,eAAgC;;IAC9C,IAAI,eAAe,CAAC,GAAG,EAAE;QACvB,OAAO,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC7C,MAAM,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;YACzD,IAAI,KAAK,EAAE;gBACT,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAQ,CAAC,CAAC;aAC3B;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAW,CAAC,CAAC;KACjB;IAED,MAAM,MAAM,GAAG,MAAA,eAAe,CAAC,MAAM,mCAAI,yBAAY,CAAC,GAAG,CAAC;IAC1D,gIAAgI;IAChI,QAAQ,MAAM,EAAE;QACd,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB;YACE,OAAO,CAAC,KAAK,CAAC,CAAC;KAClB;AACH,CAAC;AAED,SAAS,KAAK,CACZ,IAAY,EACZ,OAAuB;IAEvB,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC;AAC3C,CAAC;AA6FQ,sBAAK;AA3Fd,SAAS,cAAc,CACrB,IAAY,EACZ,OAA8B;IAE9B,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC3C,OAAO,GAAG,EAAE,CAAC;KACd;SAAM;QACL,OAAO,qBAAQ,OAAO,CAAE,CAAC;KAC1B;IACD,2EAA2E;IAC3E,yFAAyF;IACzF,IAAI,OAAO,CAAC,UAAU,KAAK,QAAQ,IAAI,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE;QACtE,OAAO,CAAC,UAAU,GAAG,QAAQ,CAAC;KAC/B;IACD,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,QAAQ,EAAE;QAC5C,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;KAC3B;IAED,MAAM,aAAa,GAAoB,EAAE,CAAC;IAC1C,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE;QACpC,GAAG,EAAE,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;KAC/C,CAAC,CAAC;IACH,MAAM,cAAc,GAAmB;QACrC,WAAW,EAAE,OAAO,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW;QACzE,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,YAAY;QAC/C,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,UAAU,EAAE,OAAO,CAAC,UAAU;KAC/B,CAAC;IAEF;;;OAGG;IACH,MAAM,kCAAkC,GAAG,eAAe,CACxD,OAAO,CAAC,kCAAkC,EAC1C,IAAI,CACL,CAAC;IACF,IAAI,CAAC,kCAAkC,EAAE;QACvC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC;KAChC;IAED,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,IAAA,4CAAwB,EAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACxE,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IAEpC,IAAI,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,KAAK,IAAI,CAAC;IACnE,IAAI,QAAQ,CAAC,sBAAsB,EAAE;QACnC,6DAA6D;QAC7D,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC9D,IAAI,cAAc,CAAC,GAAG,IAAI,IAAI,EAAE;YAC9B,cAAc,CAAC,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAC7C,GAAG,CAAC,gCAAgC,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;SAC3D;QACD,IACE,cAAc,CAAC,SAAS,KAAK,SAAS;YACtC,eAAe,CAAC,UAAU,IAAI,IAAI,EAClC;YACA,2DAA2D;YAC3D,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAChE,cAAc,CAAC,SAAS,GAAG,OAAO,CAAC;YACnC,GAAG,CAAC,qCAAqC,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;SACtE;QACD,IACE,cAAc,CAAC,eAAe,KAAK,SAAS;YAC5C,eAAe,CAAC,kBAAkB,IAAI,IAAI,EAC1C;YACA,kEAAkE;YAClE,MAAM,WAAW,GAAG,eAAe,CAAC,kBAAkB;iBACnD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACb,IAAI,EAAE,CAAC;YACV,cAAc,CAAC,eAAe,GAAG,WAAW,CAAC;YAC7C,GAAG,CACD,2CAA2C,EAC3C,cAAc,CAAC,eAAe,CAC/B,CAAC;SACH;QACD,IAAI,eAAe,CAAC,qBAAqB,KAAK,IAAI,EAAE;YAClD,qBAAqB,GAAG,IAAI,CAAC;SAC9B;KACF;IAED,IAAI,qBAAqB,EAAE;QACzB,cAAc,CAAC,qBAAqB,GAAG,IAAI,CAAC;KAC7C;IAED,MAAM,YAAY,GAAG,IAAA,uBAAO,EAAC,GAAG,EAAE,cAAc,CAAC,CAAC;IAElD,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAX,+BAAW,EAAE,CAAC;AACtD,CAAC;AAEe,wCAAc"}