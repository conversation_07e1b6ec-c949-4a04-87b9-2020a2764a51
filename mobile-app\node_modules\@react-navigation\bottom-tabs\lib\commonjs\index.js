"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "BottomTabBar", {
  enumerable: true,
  get: function () {
    return _BottomTabBar.default;
  }
});
Object.defineProperty(exports, "BottomTabBarHeightCallbackContext", {
  enumerable: true,
  get: function () {
    return _BottomTabBarHeightCallbackContext.default;
  }
});
Object.defineProperty(exports, "BottomTabBarHeightContext", {
  enumerable: true,
  get: function () {
    return _BottomTabBarHeightContext.default;
  }
});
Object.defineProperty(exports, "BottomTabView", {
  enumerable: true,
  get: function () {
    return _BottomTabView.default;
  }
});
Object.defineProperty(exports, "createBottomTabNavigator", {
  enumerable: true,
  get: function () {
    return _createBottomTabNavigator.default;
  }
});
Object.defineProperty(exports, "useBottomTabBarHeight", {
  enumerable: true,
  get: function () {
    return _useBottomTabBarHeight.default;
  }
});
var _createBottomTabNavigator = _interopRequireDefault(require("./navigators/createBottomTabNavigator"));
var _BottomTabBar = _interopRequireDefault(require("./views/BottomTabBar"));
var _BottomTabView = _interopRequireDefault(require("./views/BottomTabView"));
var _BottomTabBarHeightCallbackContext = _interopRequireDefault(require("./utils/BottomTabBarHeightCallbackContext"));
var _BottomTabBarHeightContext = _interopRequireDefault(require("./utils/BottomTabBarHeightContext"));
var _useBottomTabBarHeight = _interopRequireDefault(require("./utils/useBottomTabBarHeight"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
//# sourceMappingURL=index.js.map