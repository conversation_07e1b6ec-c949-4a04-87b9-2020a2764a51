{"version": 3, "file": "no-use-before-define.js", "sourceRoot": "", "sources": ["../../src/rules/no-use-before-define.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oEAAkE;AAElE,oDAAoE;AAEpE,8CAAgC;AAEhC,MAAM,aAAa,GACjB,iIAAiI,CAAC;AAEpI;;GAEG;AACH,SAAS,YAAY,CAAC,OAA+B;IACnD,IAAI,SAAS,GAAG,IAAI,CAAC;IACrB,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,SAAS,GAAG,IAAI,CAAC;IACrB,IAAI,QAAQ,GAAG,IAAI,CAAC;IACpB,IAAI,oBAAoB,GAAG,IAAI,CAAC;IAChC,IAAI,iBAAiB,GAAG,KAAK,CAAC;IAE9B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,SAAS,GAAG,OAAO,KAAK,QAAQ,CAAC;KAClC;SAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,IAAI,IAAI,EAAE;QACzD,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC;QACxC,OAAO,GAAG,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC;QACpC,KAAK,GAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;QAChC,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC;QACxC,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC;QACtC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC;QAC9D,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC;KACzD;IAED,OAAO;QACL,SAAS;QACT,OAAO;QACP,KAAK;QACL,SAAS;QACT,QAAQ;QACR,oBAAoB;QACpB,iBAAiB;KAClB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,UAAU,CAAC,QAAiC;IACnD,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,YAAY,CAAC;AAC/D,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAC,QAAiC;IAClD,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,IAAI,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAClB,QAAiC,EACjC,SAAmC;IAEnC,OAAO,CACL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,UAAU;QACnD,QAAQ,CAAC,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,CAAC,aAAa,CAC9D,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CACnB,QAAiC,EACjC,SAAmC;IAEnC,OAAO,CACL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,SAAS;QAClD,QAAQ,CAAC,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,CAAC,aAAa,CAC9D,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CACtB,QAAiC,EACjC,SAAmC;IAEnC,OAAO,CACL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,QAAQ;QACjD,QAAQ,CAAC,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,CAAC,aAAa,CAC9D,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,SAAmC;;IACzD,MAAM,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC;IACjC,OAAO,CACL,CAAA,MAAA,UAAU,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe;QAC1D,UAAU,CAAC,MAAM,CAAC,KAAK,KAAK,UAAU,CACvC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,0BAA0B,CAAC,IAAmB;IACrD,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,IAAI,CAAC;QAEd,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,UAAU;YAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,OAAO,KAAK,CAAC;aACd;YACD,OAAO,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEjD;YACE,6EAA6E;YAC7E,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,SAAmC;IAC1D,OAAO,CACL,SAAS,CAAC,eAAe;QACzB,0BAA0B,CAAC,SAAS,CAAC,UAAU,CAAC,CACjD,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAChB,IAA4C,EAC5C,QAAgB;IAEhB,OAAO,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC;AAED;;;GAGG;AACH,SAAS,0BAA0B,CACjC,QAAiC,EACjC,SAAmC;IAEnC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,SAAS,EAAE;QACtD,OAAO,KAAK,CAAC;KACd;IAED,IACE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU;QACjC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAC7C;QACA,OAAO,KAAK,CAAC;KACd;IAED,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE;QACnD,IACE,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9C,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAC9C;YACA,OAAO,IAAI,CAAC;SACb;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,eAAe,CACtB,QAAiC,EACjC,SAAmC;;IAEnC,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,CAAC,IAAI,EAAE;QACrC,OAAO,KAAK,CAAC;KACd;IAED,IAAI,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE/C,OAAO,IAAI,EAAE;QACX,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE;YACnD,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;gBAClC,OAAO,IAAI,CAAC;aACb;YACD,IACE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,MAAM;gBACnB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;oBACxD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;gBAC5D,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,EAC7C;gBACA,OAAO,IAAI,CAAC;aACb;YACD,MAAM;SACP;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;YACzD,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;gBACnC,OAAO,IAAI,CAAC;aACb;SACF;aAAM,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACxC,MAAM;SACP;QAED,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;KACpB;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAcD,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,uDAAuD;YACpE,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,4CAA4C;SAChE;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,CAAC,QAAQ,CAAC;qBACjB;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC9B,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC5B,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC1B,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC9B,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC7B,oBAAoB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BACzC,iBAAiB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;yBACvC;wBACD,oBAAoB,EAAE,KAAK;qBAC5B;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,IAAI;YACX,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,IAAI;YACd,oBAAoB,EAAE,IAAI;YAC1B,iBAAiB,EAAE,KAAK;SACzB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,kBAAkB;QAChC,MAAM,OAAO,GAAG,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD;;;;WAIG;QACH,SAAS,WAAW,CAClB,QAAiC,EACjC,SAAmC;YAEnC,IAAI,OAAO,CAAC,oBAAoB,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;gBAC9D,OAAO,KAAK,CAAC;aACd;YACD,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;gBACxB,OAAO,OAAO,CAAC,SAAS,CAAC;aAC1B;YACD,IAAI,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE;gBACrC,OAAO,OAAO,CAAC,OAAO,CAAC;aACxB;YACD,IAAI,eAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE;gBACxC,OAAO,OAAO,CAAC,SAAS,CAAC;aAC1B;YACD,IAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE;gBACpC,OAAO,OAAO,CAAC,KAAK,CAAC;aACtB;YACD,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE;gBACvB,OAAO,OAAO,CAAC,QAAQ,CAAC;aACzB;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,kBAAkB,CACzB,QAAiC,EACjC,SAAmC;YAEnC,OAAO,CACL,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;gBACjE,CAAC,eAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,CACtC,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,oBAAoB,CAAC,KAA2B;YACvD,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACnC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;gBAEpC,SAAS,MAAM;oBACb,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,SAAS,CAAC,UAAU;wBAC1B,SAAS,EAAE,mBAAmB;wBAC9B,IAAI,EAAE;4BACJ,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,IAAI;yBAChC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,+BAA+B;gBAC/B,qBAAqB;gBACrB,wCAAwC;gBACxC,0EAA0E;gBAC1E,+DAA+D;gBAC/D,wBAAwB;gBACxB,IAAI,SAAS,CAAC,IAAI,EAAE;oBAClB,OAAO;iBACR;gBAED,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,cAAc,CAAC,SAAS,CAAC,EAAE;oBAC3D,IAAI,CAAC,QAAQ,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE;wBACzD,MAAM,EAAE,CAAC;qBACV;oBACD,OAAO;iBACR;gBAED,IAAI,CAAC,QAAQ,EAAE;oBACb,OAAO;iBACR;gBAED,IACE,QAAQ,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBACjC,kBAAkB,CAAC,QAAQ,EAAE,SAAS,CAAC;oBACvC,CAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,CAAC;oBACjC,0BAA0B,CAAC,QAAQ,EAAE,SAAS,CAAC;oBAC/C,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,EAC7D;oBACA,OAAO;iBACR;gBAED,WAAW;gBACX,MAAM,EAAE,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAClD,CAAC;QAED,OAAO;YACL,OAAO;gBACL,oBAAoB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3C,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}