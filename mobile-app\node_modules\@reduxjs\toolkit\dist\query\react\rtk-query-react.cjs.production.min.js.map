{"version": 3, "names": ["__markAsModule", "exports", "__export", "A<PERSON><PERSON><PERSON><PERSON>", "createApi", "reactHooksModule", "reactHooksModuleName", "import_query3", "__toModule", "require", "import_toolkit2", "import_react3", "import_query", "import_react_redux2", "import_react", "useStableQueryArgs", "queryArgs", "serialize", "endpointDefinition", "endpointName", "incoming", "useMemo", "serialized", "cache2", "useRef", "useEffect", "current", "UNINITIALIZED_VALUE", "Symbol", "import_react2", "import_react_redux", "useShallowStableValue", "value", "shallowEqual", "DefinitionType", "DefinitionType2", "import_toolkit", "cache", "WeakMap", "defaultSerializeQueryArgs", "_c", "cached", "get", "stringified", "JSON", "stringify", "key", "isPlainObject", "Object", "keys", "sort", "reduce", "acc", "key2", "set", "useIsomorphicLayoutEffect", "window", "document", "createElement", "useLayoutEffect", "defaultMutationStateSelector", "x", "noPendingQueryStateSelector", "selected", "isUninitialized", "__spreadProps", "__spreadValues", "isFetching", "isLoading", "data", "status", "QueryStatus", "pending", "capitalize", "str", "replace", "toUpperCase", "safeAssign", "target", "args", "_i", "arguments", "length", "assign", "apply", "__spread<PERSON><PERSON>y", "import_react_redux3", "_d", "_e", "batch", "_f", "useDispatch", "_g", "useSelector", "_h", "useStore", "_j", "unstable__sideEffectsInRender", "name", "init", "api", "context", "anyApi", "moduleOptions", "serializeQueryArgs", "usePossiblyImmediateEffect", "cb", "buildQueryHooks", "useQuerySubscription", "arg", "refetchOnReconnect", "refetchOnFocus", "refetchOnMountOrArgChange", "skip", "pollingInterval", "initiate", "endpoints", "dispatch", "stableArg", "skipToken", "endpointDefinitions", "stableSubscriptionOptions", "lastRenderHadSubscription", "promiseRef", "query<PERSON><PERSON><PERSON><PERSON>", "requestId", "currentRenderHasSubscription", "returnedValue", "internalActions", "internal_probeSubscription", "subscriptionRemoved", "_a", "lastPromise", "unsubscribe", "lastSubscriptionOptions", "subscriptionOptions", "updateSubscriptionOptions", "promise", "forceRefetch", "refetch", "Error", "useLazyQuerySubscription", "useState", "setArg", "_b", "subscriptionOptionsRef", "trigger", "useCallback", "arg2", "preferCacheValue", "useQueryState", "selectFromResult", "select", "lastValue", "selectDefaultResult", "createSelector", "_", "lastResult", "queryStatePreSelector", "querySelector", "currentState", "state", "store", "newLastValue", "getState", "useLazyQuery", "options", "queryStateResults", "info", "lastArg", "useQuery", "querySubscriptionResults", "useDebugValue", "isSuccess", "isError", "error", "buildMutationHook", "fixedCacheKey", "setPromise", "reset", "triggerMutation", "promise2", "mutationSelector", "originalArgs", "removeMutationResult", "finalState", "usePrefetch", "defaultOptions", "stableDefaultOptions", "util", "prefetch", "hasData", "currentData", "buildHooks", "injectEndpoint", "definition", "type", "query", "mutation", "useMutation", "__reExport", "import_toolkit3", "import_react4", "import_react5", "import_react_redux4", "import_query2", "props", "default", "configureStore", "reducer", "reducerPath", "middleware", "gDM", "concat", "setupListeners", "Provider", "children", "buildCreateApi", "coreModule"], "sources": ["../../../src/query/react/index.ts", "../../../src/query/react/buildHooks.ts", "../../../src/query/react/useSerializedStableValue.ts", "../../../src/query/react/constants.ts", "../../../src/query/react/useShallowStableValue.ts", "../../../src/query/defaultSerializeQueryArgs.ts", "../../../src/query/endpointDefinitions.ts", "../../../src/query/utils/capitalize.ts", "../../../src/query/tsHelpers.ts", "../../../src/query/react/module.ts", "../../../src/query/react/ApiProvider.tsx"], "sourcesContent": ["import { coreModule, buildCreateApi } from '@reduxjs/toolkit/query'\r\nimport { reactHooksModule, reactHooksModuleName } from './module'\r\n\r\nexport * from '@reduxjs/toolkit/query'\r\nexport { ApiProvider } from './ApiProvider'\r\n\r\nconst createApi = /* @__PURE__ */ buildCreateApi(\r\n  coreModule(),\r\n  reactHooksModule()\r\n)\r\n\r\nexport type {\r\n  TypedUseQueryHookResult,\r\n  TypedUseQueryStateResult,\r\n  TypedUseQuerySubscriptionResult,\r\n  TypedUseMutationResult,\r\n} from './buildHooks'\r\nexport { createApi, reactHooksModule, reactHooksModuleName }\r\n", "import type { AnyAction, ThunkAction, ThunkDispatch } from '@reduxjs/toolkit'\r\nimport { createSelector } from '@reduxjs/toolkit'\r\nimport type { Selector } from '@reduxjs/toolkit'\r\nimport type { DependencyList } from 'react'\r\nimport {\r\n  useCallback,\r\n  useDebugValue,\r\n  useEffect,\r\n  useLayoutEffect,\r\n  useMemo,\r\n  useRef,\r\n  useState,\r\n} from 'react'\r\nimport { QueryStatus, skipToken } from '@reduxjs/toolkit/query'\r\nimport type {\r\n  QuerySubState,\r\n  SubscriptionOptions,\r\n  QueryKeys,\r\n  RootState,\r\n} from '@reduxjs/toolkit/query'\r\nimport type {\r\n  EndpointDefinitions,\r\n  MutationDefinition,\r\n  QueryDefinition,\r\n  QueryArgFrom,\r\n  ResultTypeFrom,\r\n} from '@reduxjs/toolkit/query'\r\nimport type {\r\n  QueryResultSelectorResult,\r\n  MutationResultSelectorResult,\r\n  SkipToken,\r\n} from '@reduxjs/toolkit/query'\r\nimport type {\r\n  QueryActionCreatorResult,\r\n  MutationActionCreatorResult,\r\n} from '@reduxjs/toolkit/query'\r\nimport type { SerializeQueryArgs } from '@reduxjs/toolkit/query'\r\nimport { shallowEqual } from 'react-redux'\r\nimport type { Api, ApiContext } from '@reduxjs/toolkit/query'\r\nimport type {\r\n  TSHelpersId,\r\n  TSHelpersNoInfer,\r\n  TSHelpersOverride,\r\n} from '@reduxjs/toolkit/query'\r\nimport type {\r\n  ApiEndpointMutation,\r\n  ApiEndpointQuery,\r\n  CoreModule,\r\n  PrefetchOptions,\r\n} from '@reduxjs/toolkit/query'\r\nimport type { ReactHooksModuleOptions } from './module'\r\nimport { useStableQueryArgs } from './useSerializedStableValue'\r\nimport type { UninitializedValue } from './constants'\r\nimport { UNINITIALIZED_VALUE } from './constants'\r\nimport { useShallowStableValue } from './useShallowStableValue'\r\nimport type { BaseQueryFn } from '../baseQueryTypes'\r\nimport { defaultSerializeQueryArgs } from '../defaultSerializeQueryArgs'\r\n\r\n// Copy-pasted from React-Redux\r\nexport const useIsomorphicLayoutEffect =\r\n  typeof window !== 'undefined' &&\r\n  !!window.document &&\r\n  !!window.document.createElement\r\n    ? useLayoutEffect\r\n    : useEffect\r\n\r\nexport interface QueryHooks<\r\n  Definition extends QueryDefinition<any, any, any, any, any>\r\n> {\r\n  useQuery: UseQuery<Definition>\r\n  useLazyQuery: UseLazyQuery<Definition>\r\n  useQuerySubscription: UseQuerySubscription<Definition>\r\n  useLazyQuerySubscription: UseLazyQuerySubscription<Definition>\r\n  useQueryState: UseQueryState<Definition>\r\n}\r\n\r\nexport interface MutationHooks<\r\n  Definition extends MutationDefinition<any, any, any, any, any>\r\n> {\r\n  useMutation: UseMutation<Definition>\r\n}\r\n\r\n/**\r\n * A React hook that automatically triggers fetches of data from an endpoint, 'subscribes' the component to the cached data, and reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.\r\n *\r\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already, and the hook will return the data for that query arg once it's available.\r\n *\r\n * This hook combines the functionality of both [`useQueryState`](#usequerystate) and [`useQuerySubscription`](#usequerysubscription) together, and is intended to be used in the majority of situations.\r\n *\r\n * #### Features\r\n *\r\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\r\n * - Returns the latest request status and cached data from the Redux store\r\n * - Re-renders as the request status changes and data becomes available\r\n */\r\nexport type UseQuery<D extends QueryDefinition<any, any, any, any>> = <\r\n  R extends Record<string, any> = UseQueryStateDefaultResult<D>\r\n>(\r\n  arg: QueryArgFrom<D> | SkipToken,\r\n  options?: UseQuerySubscriptionOptions & UseQueryStateOptions<D, R>\r\n) => UseQueryHookResult<D, R>\r\n\r\nexport type UseQueryHookResult<\r\n  D extends QueryDefinition<any, any, any, any>,\r\n  R = UseQueryStateDefaultResult<D>\r\n> = UseQueryStateResult<D, R> & UseQuerySubscriptionResult<D>\r\n\r\n/**\r\n * Helper type to manually type the result\r\n * of the `useQuery` hook in userland code.\r\n */\r\nexport type TypedUseQueryHookResult<\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  R = UseQueryStateDefaultResult<\r\n    QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>\r\n  >\r\n> = TypedUseQueryStateResult<ResultType, QueryArg, BaseQuery, R> &\r\n  TypedUseQuerySubscriptionResult<ResultType, QueryArg, BaseQuery>\r\n\r\ninterface UseQuerySubscriptionOptions extends SubscriptionOptions {\r\n  /**\r\n   * Prevents a query from automatically running.\r\n   *\r\n   * @remarks\r\n   * When `skip` is true (or `skipToken` is passed in as `arg`):\r\n   *\r\n   * - **If the query has cached data:**\r\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\r\n   *   * The query will have a status of `uninitialized`\r\n   *   * If `skip: false` is set after the initial load, the cached result will be used\r\n   * - **If the query does not have cached data:**\r\n   *   * The query will have a status of `uninitialized`\r\n   *   * The query will not exist in the state when viewed with the dev tools\r\n   *   * The query will not automatically fetch on mount\r\n   *   * The query will not automatically run when additional components with the same query are added that do run\r\n   *\r\n   * @example\r\n   * ```tsx\r\n   * // codeblock-meta no-transpile title=\"Skip example\"\r\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\r\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\r\n   *     skip,\r\n   *   });\r\n   *\r\n   *   return (\r\n   *     <div>\r\n   *       {name} - {status}\r\n   *     </div>\r\n   *   );\r\n   * };\r\n   * ```\r\n   */\r\n  skip?: boolean\r\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether if a cached result is already available, RTK Query will only serve a cached result, or if it should `refetch` when set to `true` or if an adequate amount of time has passed since the last successful query result.\r\n   * - `false` - Will not cause a query to be performed _unless_ it does not exist yet.\r\n   * - `true` - Will always refetch when a new subscriber to a query is added. Behaves the same as calling the `refetch` callback or passing `forceRefetch: true` in the action creator.\r\n   * - `number` - **Value is in seconds**. If a number is provided and there is an existing query in the cache, it will compare the current time vs the last fulfilled timestamp, and only refetch if enough time has elapsed.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   */\r\n  refetchOnMountOrArgChange?: boolean | number\r\n}\r\n\r\n/**\r\n * A React hook that automatically triggers fetches of data from an endpoint, and 'subscribes' the component to the cached data.\r\n *\r\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already.\r\n *\r\n * Note that this hook does not return a request status or cached data. For that use-case, see [`useQuery`](#usequery) or [`useQueryState`](#usequerystate).\r\n *\r\n * #### Features\r\n *\r\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\r\n */\r\nexport type UseQuerySubscription<\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = (\r\n  arg: QueryArgFrom<D> | SkipToken,\r\n  options?: UseQuerySubscriptionOptions\r\n) => UseQuerySubscriptionResult<D>\r\n\r\nexport type UseQuerySubscriptionResult<\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = Pick<QueryActionCreatorResult<D>, 'refetch'>\r\n\r\n/**\r\n * Helper type to manually type the result\r\n * of the `useQuerySubscription` hook in userland code.\r\n */\r\nexport type TypedUseQuerySubscriptionResult<\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn\r\n> = UseQuerySubscriptionResult<\r\n  QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>\r\n>\r\n\r\nexport type UseLazyQueryLastPromiseInfo<\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = {\r\n  lastArg: QueryArgFrom<D>\r\n}\r\n\r\n/**\r\n * A React hook similar to [`useQuery`](#usequery), but with manual control over when the data fetching occurs.\r\n *\r\n * This hook includes the functionality of [`useLazyQuerySubscription`](#uselazyquerysubscription).\r\n *\r\n * #### Features\r\n *\r\n * - Manual control over firing a request to retrieve data\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Returns the latest request status and cached data from the Redux store\r\n * - Re-renders as the request status changes and data becomes available\r\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met and the fetch has been manually called at least once\r\n *\r\n * #### Note\r\n *\r\n * When the trigger function returned from a LazyQuery is called, it always initiates a new request to the server even if there is cached data. Set `preferCacheValue`(the second argument to the function) as `true` if you want it to immediately return a cached value if one exists.\r\n */\r\nexport type UseLazyQuery<D extends QueryDefinition<any, any, any, any>> = <\r\n  R extends Record<string, any> = UseQueryStateDefaultResult<D>\r\n>(\r\n  options?: SubscriptionOptions & Omit<UseQueryStateOptions<D, R>, 'skip'>\r\n) => [\r\n  LazyQueryTrigger<D>,\r\n  UseQueryStateResult<D, R>,\r\n  UseLazyQueryLastPromiseInfo<D>\r\n]\r\n\r\nexport type LazyQueryTrigger<D extends QueryDefinition<any, any, any, any>> = {\r\n  /**\r\n   * Triggers a lazy query.\r\n   *\r\n   * By default, this will start a new request even if there is already a value in the cache.\r\n   * If you want to use the cache value and only start a request if there is no cache value, set the second argument to `true`.\r\n   *\r\n   * @remarks\r\n   * If you need to access the error or success payload immediately after a lazy query, you can chain .unwrap().\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Using .unwrap with async await\"\r\n   * try {\r\n   *   const payload = await getUserById(1).unwrap();\r\n   *   console.log('fulfilled', payload)\r\n   * } catch (error) {\r\n   *   console.error('rejected', error);\r\n   * }\r\n   * ```\r\n   */\r\n  (\r\n    arg: QueryArgFrom<D>,\r\n    preferCacheValue?: boolean\r\n  ): QueryActionCreatorResult<D>\r\n}\r\n\r\n/**\r\n * A React hook similar to [`useQuerySubscription`](#usequerysubscription), but with manual control over when the data fetching occurs.\r\n *\r\n * Note that this hook does not return a request status or cached data. For that use-case, see [`useLazyQuery`](#uselazyquery).\r\n *\r\n * #### Features\r\n *\r\n * - Manual control over firing a request to retrieve data\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met and the fetch has been manually called at least once\r\n */\r\nexport type UseLazyQuerySubscription<\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = (\r\n  options?: SubscriptionOptions\r\n) => readonly [LazyQueryTrigger<D>, QueryArgFrom<D> | UninitializedValue]\r\n\r\nexport type QueryStateSelector<\r\n  R extends Record<string, any>,\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = (state: UseQueryStateDefaultResult<D>) => R\r\n\r\n/**\r\n * A React hook that reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.\r\n *\r\n * Note that this hook does not trigger fetching new data. For that use-case, see [`useQuery`](#usequery) or [`useQuerySubscription`](#usequerysubscription).\r\n *\r\n * #### Features\r\n *\r\n * - Returns the latest request status and cached data from the Redux store\r\n * - Re-renders as the request status changes and data becomes available\r\n */\r\nexport type UseQueryState<D extends QueryDefinition<any, any, any, any>> = <\r\n  R extends Record<string, any> = UseQueryStateDefaultResult<D>\r\n>(\r\n  arg: QueryArgFrom<D> | SkipToken,\r\n  options?: UseQueryStateOptions<D, R>\r\n) => UseQueryStateResult<D, R>\r\n\r\nexport type UseQueryStateOptions<\r\n  D extends QueryDefinition<any, any, any, any>,\r\n  R extends Record<string, any>\r\n> = {\r\n  /**\r\n   * Prevents a query from automatically running.\r\n   *\r\n   * @remarks\r\n   * When skip is true:\r\n   *\r\n   * - **If the query has cached data:**\r\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\r\n   *   * The query will have a status of `uninitialized`\r\n   *   * If `skip: false` is set after skipping the initial load, the cached result will be used\r\n   * - **If the query does not have cached data:**\r\n   *   * The query will have a status of `uninitialized`\r\n   *   * The query will not exist in the state when viewed with the dev tools\r\n   *   * The query will not automatically fetch on mount\r\n   *   * The query will not automatically run when additional components with the same query are added that do run\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Skip example\"\r\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\r\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\r\n   *     skip,\r\n   *   });\r\n   *\r\n   *   return (\r\n   *     <div>\r\n   *       {name} - {status}\r\n   *     </div>\r\n   *   );\r\n   * };\r\n   * ```\r\n   */\r\n  skip?: boolean\r\n  /**\r\n   * `selectFromResult` allows you to get a specific segment from a query result in a performant manner.\r\n   * When using this feature, the component will not rerender unless the underlying data of the selected item has changed.\r\n   * If the selected item is one element in a larger collection, it will disregard changes to elements in the same collection.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Using selectFromResult to extract a single result\"\r\n   * function PostsList() {\r\n   *   const { data: posts } = api.useGetPostsQuery();\r\n   *\r\n   *   return (\r\n   *     <ul>\r\n   *       {posts?.data?.map((post) => (\r\n   *         <PostById key={post.id} id={post.id} />\r\n   *       ))}\r\n   *     </ul>\r\n   *   );\r\n   * }\r\n   *\r\n   * function PostById({ id }: { id: number }) {\r\n   *   // Will select the post with the given id, and will only rerender if the given posts data changes\r\n   *   const { post } = api.useGetPostsQuery(undefined, {\r\n   *     selectFromResult: ({ data }) => ({ post: data?.find((post) => post.id === id) }),\r\n   *   });\r\n   *\r\n   *   return <li>{post?.name}</li>;\r\n   * }\r\n   * ```\r\n   */\r\n  selectFromResult?: QueryStateSelector<R, D>\r\n}\r\n\r\nexport type UseQueryStateResult<\r\n  _ extends QueryDefinition<any, any, any, any>,\r\n  R\r\n> = TSHelpersNoInfer<R>\r\n\r\n/**\r\n * Helper type to manually type the result\r\n * of the `useQueryState` hook in userland code.\r\n */\r\nexport type TypedUseQueryStateResult<\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  R = UseQueryStateDefaultResult<\r\n    QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>\r\n  >\r\n> = TSHelpersNoInfer<R>\r\n\r\ntype UseQueryStateBaseResult<D extends QueryDefinition<any, any, any, any>> =\r\n  QuerySubState<D> & {\r\n    /**\r\n     * Where `data` tries to hold data as much as possible, also re-using\r\n     * data from the last arguments passed into the hook, this property\r\n     * will always contain the received data from the query, for the current query arguments.\r\n     */\r\n    currentData?: ResultTypeFrom<D>\r\n    /**\r\n     * Query has not started yet.\r\n     */\r\n    isUninitialized: false\r\n    /**\r\n     * Query is currently loading for the first time. No data yet.\r\n     */\r\n    isLoading: false\r\n    /**\r\n     * Query is currently fetching, but might have data from an earlier request.\r\n     */\r\n    isFetching: false\r\n    /**\r\n     * Query has data from a successful load.\r\n     */\r\n    isSuccess: false\r\n    /**\r\n     * Query is currently in \"error\" state.\r\n     */\r\n    isError: false\r\n  }\r\n\r\ntype UseQueryStateDefaultResult<D extends QueryDefinition<any, any, any, any>> =\r\n  TSHelpersId<\r\n    | TSHelpersOverride<\r\n        Extract<\r\n          UseQueryStateBaseResult<D>,\r\n          { status: QueryStatus.uninitialized }\r\n        >,\r\n        { isUninitialized: true }\r\n      >\r\n    | TSHelpersOverride<\r\n        UseQueryStateBaseResult<D>,\r\n        | { isLoading: true; isFetching: boolean; data: undefined }\r\n        | ({\r\n            isSuccess: true\r\n            isFetching: true\r\n            error: undefined\r\n          } & Required<\r\n            Pick<UseQueryStateBaseResult<D>, 'data' | 'fulfilledTimeStamp'>\r\n          >)\r\n        | ({\r\n            isSuccess: true\r\n            isFetching: false\r\n            error: undefined\r\n          } & Required<\r\n            Pick<\r\n              UseQueryStateBaseResult<D>,\r\n              'data' | 'fulfilledTimeStamp' | 'currentData'\r\n            >\r\n          >)\r\n        | ({ isError: true } & Required<\r\n            Pick<UseQueryStateBaseResult<D>, 'error'>\r\n          >)\r\n      >\r\n  > & {\r\n    /**\r\n     * @deprecated will be removed in the next version\r\n     * please use the `isLoading`, `isFetching`, `isSuccess`, `isError`\r\n     * and `isUninitialized` flags instead\r\n     */\r\n    status: QueryStatus\r\n  }\r\n\r\nexport type MutationStateSelector<\r\n  R extends Record<string, any>,\r\n  D extends MutationDefinition<any, any, any, any>\r\n> = (state: MutationResultSelectorResult<D>) => R\r\n\r\nexport type UseMutationStateOptions<\r\n  D extends MutationDefinition<any, any, any, any>,\r\n  R extends Record<string, any>\r\n> = {\r\n  selectFromResult?: MutationStateSelector<R, D>\r\n  fixedCacheKey?: string\r\n}\r\n\r\nexport type UseMutationStateResult<\r\n  D extends MutationDefinition<any, any, any, any>,\r\n  R\r\n> = TSHelpersNoInfer<R> & {\r\n  originalArgs?: QueryArgFrom<D>\r\n  /**\r\n   * Resets the hook state to it's initial `uninitialized` state.\r\n   * This will also remove the last result from the cache.\r\n   */\r\n  reset: () => void\r\n}\r\n\r\n/**\r\n * Helper type to manually type the result\r\n * of the `useMutation` hook in userland code.\r\n */\r\nexport type TypedUseMutationResult<\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  R = MutationResultSelectorResult<\r\n    MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>\r\n  >\r\n> = UseMutationStateResult<\r\n  MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>,\r\n  R\r\n>\r\n\r\n/**\r\n * A React hook that lets you trigger an update request for a given endpoint, and subscribes the component to read the request status from the Redux store. The component will re-render as the loading status changes.\r\n *\r\n * #### Features\r\n *\r\n * - Manual control over firing a request to alter data on the server or possibly invalidate the cache\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Returns the latest request status and cached data from the Redux store\r\n * - Re-renders as the request status changes and data becomes available\r\n */\r\nexport type UseMutation<D extends MutationDefinition<any, any, any, any>> = <\r\n  R extends Record<string, any> = MutationResultSelectorResult<D>\r\n>(\r\n  options?: UseMutationStateOptions<D, R>\r\n) => readonly [MutationTrigger<D>, UseMutationStateResult<D, R>]\r\n\r\nexport type MutationTrigger<D extends MutationDefinition<any, any, any, any>> =\r\n  {\r\n    /**\r\n     * Triggers the mutation and returns a Promise.\r\n     * @remarks\r\n     * If you need to access the error or success payload immediately after a mutation, you can chain .unwrap().\r\n     *\r\n     * @example\r\n     * ```ts\r\n     * // codeblock-meta title=\"Using .unwrap with async await\"\r\n     * try {\r\n     *   const payload = await addPost({ id: 1, name: 'Example' }).unwrap();\r\n     *   console.log('fulfilled', payload)\r\n     * } catch (error) {\r\n     *   console.error('rejected', error);\r\n     * }\r\n     * ```\r\n     */\r\n    (arg: QueryArgFrom<D>): MutationActionCreatorResult<D>\r\n  }\r\n\r\nconst defaultQueryStateSelector: QueryStateSelector<any, any> = (x) => x\r\nconst defaultMutationStateSelector: MutationStateSelector<any, any> = (x) => x\r\n\r\n/**\r\n * Wrapper around `defaultQueryStateSelector` to be used in `useQuery`.\r\n * We want the initial render to already come back with\r\n * `{ isUninitialized: false, isFetching: true, isLoading: true }`\r\n * to prevent that the library user has to do an additional check for `isUninitialized`/\r\n */\r\nconst noPendingQueryStateSelector: QueryStateSelector<any, any> = (\r\n  selected\r\n) => {\r\n  if (selected.isUninitialized) {\r\n    return {\r\n      ...selected,\r\n      isUninitialized: false,\r\n      isFetching: true,\r\n      isLoading: selected.data !== undefined ? false : true,\r\n      status: QueryStatus.pending,\r\n    } as any\r\n  }\r\n  return selected\r\n}\r\n\r\ntype GenericPrefetchThunk = (\r\n  endpointName: any,\r\n  arg: any,\r\n  options: PrefetchOptions\r\n) => ThunkAction<void, any, any, AnyAction>\r\n\r\n/**\r\n *\r\n * @param opts.api - An API with defined endpoints to create hooks for\r\n * @param opts.moduleOptions.batch - The version of the `batchedUpdates` function to be used\r\n * @param opts.moduleOptions.useDispatch - The version of the `useDispatch` hook to be used\r\n * @param opts.moduleOptions.useSelector - The version of the `useSelector` hook to be used\r\n * @returns An object containing functions to generate hooks based on an endpoint\r\n */\r\nexport function buildHooks<Definitions extends EndpointDefinitions>({\r\n  api,\r\n  moduleOptions: {\r\n    batch,\r\n    useDispatch,\r\n    useSelector,\r\n    useStore,\r\n    unstable__sideEffectsInRender,\r\n  },\r\n  serializeQueryArgs,\r\n  context,\r\n}: {\r\n  api: Api<any, Definitions, any, any, CoreModule>\r\n  moduleOptions: Required<ReactHooksModuleOptions>\r\n  serializeQueryArgs: SerializeQueryArgs<any>\r\n  context: ApiContext<Definitions>\r\n}) {\r\n  const usePossiblyImmediateEffect: (\r\n    effect: () => void | undefined,\r\n    deps?: DependencyList\r\n  ) => void = unstable__sideEffectsInRender ? (cb) => cb() : useEffect\r\n\r\n  return { buildQueryHooks, buildMutationHook, usePrefetch }\r\n\r\n  function queryStatePreSelector(\r\n    currentState: QueryResultSelectorResult<any>,\r\n    lastResult: UseQueryStateDefaultResult<any> | undefined,\r\n    queryArgs: any\r\n  ): UseQueryStateDefaultResult<any> {\r\n    // if we had a last result and the current result is uninitialized,\r\n    // we might have called `api.util.resetApiState`\r\n    // in this case, reset the hook\r\n    if (lastResult?.endpointName && currentState.isUninitialized) {\r\n      const { endpointName } = lastResult\r\n      const endpointDefinition = context.endpointDefinitions[endpointName]\r\n      if (\r\n        serializeQueryArgs({\r\n          queryArgs: lastResult.originalArgs,\r\n          endpointDefinition,\r\n          endpointName,\r\n        }) ===\r\n        serializeQueryArgs({\r\n          queryArgs,\r\n          endpointDefinition,\r\n          endpointName,\r\n        })\r\n      )\r\n        lastResult = undefined\r\n    }\r\n\r\n    // data is the last known good request result we have tracked - or if none has been tracked yet the last good result for the current args\r\n    let data = currentState.isSuccess ? currentState.data : lastResult?.data\r\n    if (data === undefined) data = currentState.data\r\n\r\n    const hasData = data !== undefined\r\n\r\n    // isFetching = true any time a request is in flight\r\n    const isFetching = currentState.isLoading\r\n    // isLoading = true only when loading while no data is present yet (initial load with no data in the cache)\r\n    const isLoading = !hasData && isFetching\r\n    // isSuccess = true when data is present\r\n    const isSuccess = currentState.isSuccess || (isFetching && hasData)\r\n\r\n    return {\r\n      ...currentState,\r\n      data,\r\n      currentData: currentState.data,\r\n      isFetching,\r\n      isLoading,\r\n      isSuccess,\r\n    } as UseQueryStateDefaultResult<any>\r\n  }\r\n\r\n  function usePrefetch<EndpointName extends QueryKeys<Definitions>>(\r\n    endpointName: EndpointName,\r\n    defaultOptions?: PrefetchOptions\r\n  ) {\r\n    const dispatch = useDispatch<ThunkDispatch<any, any, AnyAction>>()\r\n    const stableDefaultOptions = useShallowStableValue(defaultOptions)\r\n\r\n    return useCallback(\r\n      (arg: any, options?: PrefetchOptions) =>\r\n        dispatch(\r\n          (api.util.prefetch as GenericPrefetchThunk)(endpointName, arg, {\r\n            ...stableDefaultOptions,\r\n            ...options,\r\n          })\r\n        ),\r\n      [endpointName, dispatch, stableDefaultOptions]\r\n    )\r\n  }\r\n\r\n  function buildQueryHooks(name: string): QueryHooks<any> {\r\n    const useQuerySubscription: UseQuerySubscription<any> = (\r\n      arg: any,\r\n      {\r\n        refetchOnReconnect,\r\n        refetchOnFocus,\r\n        refetchOnMountOrArgChange,\r\n        skip = false,\r\n        pollingInterval = 0,\r\n      } = {}\r\n    ) => {\r\n      const { initiate } = api.endpoints[name] as ApiEndpointQuery<\r\n        QueryDefinition<any, any, any, any, any>,\r\n        Definitions\r\n      >\r\n      const dispatch = useDispatch<ThunkDispatch<any, any, AnyAction>>()\r\n      const stableArg = useStableQueryArgs(\r\n        skip ? skipToken : arg,\r\n        // Even if the user provided a per-endpoint `serializeQueryArgs` with\r\n        // a consistent return value, _here_ we want to use the default behavior\r\n        // so we can tell if _anything_ actually changed. Otherwise, we can end up\r\n        // with a case where the query args did change but the serialization doesn't,\r\n        // and then we never try to initiate a refetch.\r\n        defaultSerializeQueryArgs,\r\n        context.endpointDefinitions[name],\r\n        name\r\n      )\r\n      const stableSubscriptionOptions = useShallowStableValue({\r\n        refetchOnReconnect,\r\n        refetchOnFocus,\r\n        pollingInterval,\r\n      })\r\n\r\n      const lastRenderHadSubscription = useRef(false)\r\n\r\n      const promiseRef = useRef<QueryActionCreatorResult<any>>()\r\n\r\n      let { queryCacheKey, requestId } = promiseRef.current || {}\r\n\r\n      // HACK Because the latest state is in the middleware, we actually\r\n      // dispatch an action that will be intercepted and returned.\r\n      let currentRenderHasSubscription = false\r\n      if (queryCacheKey && requestId) {\r\n        // This _should_ return a boolean, even if the types don't line up\r\n        const returnedValue = dispatch(\r\n          api.internalActions.internal_probeSubscription({\r\n            queryCacheKey,\r\n            requestId,\r\n          })\r\n        )\r\n\r\n        if (process.env.NODE_ENV !== 'production') {\r\n          if (typeof returnedValue !== 'boolean') {\r\n            throw new Error(\r\n              `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\r\n    You must add the middleware for RTK-Query to function correctly!`\r\n            )\r\n          }\r\n        }\r\n\r\n        currentRenderHasSubscription = !!returnedValue\r\n      }\r\n\r\n      const subscriptionRemoved =\r\n        !currentRenderHasSubscription && lastRenderHadSubscription.current\r\n\r\n      usePossiblyImmediateEffect(() => {\r\n        lastRenderHadSubscription.current = currentRenderHasSubscription\r\n      })\r\n\r\n      usePossiblyImmediateEffect((): void | undefined => {\r\n        if (subscriptionRemoved) {\r\n          promiseRef.current = undefined\r\n        }\r\n      }, [subscriptionRemoved])\r\n\r\n      usePossiblyImmediateEffect((): void | undefined => {\r\n        const lastPromise = promiseRef.current\r\n        if (\r\n          typeof process !== 'undefined' &&\r\n          process.env.NODE_ENV === 'removeMeOnCompilation'\r\n        ) {\r\n          // this is only present to enforce the rule of hooks to keep `isSubscribed` in the dependency array\r\n          console.log(subscriptionRemoved)\r\n        }\r\n\r\n        if (stableArg === skipToken) {\r\n          lastPromise?.unsubscribe()\r\n          promiseRef.current = undefined\r\n          return\r\n        }\r\n\r\n        const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions\r\n\r\n        if (!lastPromise || lastPromise.arg !== stableArg) {\r\n          lastPromise?.unsubscribe()\r\n          const promise = dispatch(\r\n            initiate(stableArg, {\r\n              subscriptionOptions: stableSubscriptionOptions,\r\n              forceRefetch: refetchOnMountOrArgChange,\r\n            })\r\n          )\r\n\r\n          promiseRef.current = promise\r\n        } else if (stableSubscriptionOptions !== lastSubscriptionOptions) {\r\n          lastPromise.updateSubscriptionOptions(stableSubscriptionOptions)\r\n        }\r\n      }, [\r\n        dispatch,\r\n        initiate,\r\n        refetchOnMountOrArgChange,\r\n        stableArg,\r\n        stableSubscriptionOptions,\r\n        subscriptionRemoved,\r\n      ])\r\n\r\n      useEffect(() => {\r\n        return () => {\r\n          promiseRef.current?.unsubscribe()\r\n          promiseRef.current = undefined\r\n        }\r\n      }, [])\r\n\r\n      return useMemo(\r\n        () => ({\r\n          /**\r\n           * A method to manually refetch data for the query\r\n           */\r\n          refetch: () => {\r\n            if (!promiseRef.current)\r\n              throw new Error(\r\n                'Cannot refetch a query that has not been started yet.'\r\n              )\r\n            return promiseRef.current?.refetch()\r\n          },\r\n        }),\r\n        []\r\n      )\r\n    }\r\n\r\n    const useLazyQuerySubscription: UseLazyQuerySubscription<any> = ({\r\n      refetchOnReconnect,\r\n      refetchOnFocus,\r\n      pollingInterval = 0,\r\n    } = {}) => {\r\n      const { initiate } = api.endpoints[name] as ApiEndpointQuery<\r\n        QueryDefinition<any, any, any, any, any>,\r\n        Definitions\r\n      >\r\n      const dispatch = useDispatch<ThunkDispatch<any, any, AnyAction>>()\r\n\r\n      const [arg, setArg] = useState<any>(UNINITIALIZED_VALUE)\r\n      const promiseRef = useRef<QueryActionCreatorResult<any> | undefined>()\r\n\r\n      const stableSubscriptionOptions = useShallowStableValue({\r\n        refetchOnReconnect,\r\n        refetchOnFocus,\r\n        pollingInterval,\r\n      })\r\n\r\n      usePossiblyImmediateEffect(() => {\r\n        const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions\r\n\r\n        if (stableSubscriptionOptions !== lastSubscriptionOptions) {\r\n          promiseRef.current?.updateSubscriptionOptions(\r\n            stableSubscriptionOptions\r\n          )\r\n        }\r\n      }, [stableSubscriptionOptions])\r\n\r\n      const subscriptionOptionsRef = useRef(stableSubscriptionOptions)\r\n      usePossiblyImmediateEffect(() => {\r\n        subscriptionOptionsRef.current = stableSubscriptionOptions\r\n      }, [stableSubscriptionOptions])\r\n\r\n      const trigger = useCallback(\r\n        function (arg: any, preferCacheValue = false) {\r\n          let promise: QueryActionCreatorResult<any>\r\n\r\n          batch(() => {\r\n            promiseRef.current?.unsubscribe()\r\n\r\n            promiseRef.current = promise = dispatch(\r\n              initiate(arg, {\r\n                subscriptionOptions: subscriptionOptionsRef.current,\r\n                forceRefetch: !preferCacheValue,\r\n              })\r\n            )\r\n\r\n            setArg(arg)\r\n          })\r\n\r\n          return promise!\r\n        },\r\n        [dispatch, initiate]\r\n      )\r\n\r\n      /* cleanup on unmount */\r\n      useEffect(() => {\r\n        return () => {\r\n          promiseRef?.current?.unsubscribe()\r\n        }\r\n      }, [])\r\n\r\n      /* if \"cleanup on unmount\" was triggered from a fast refresh, we want to reinstate the query */\r\n      useEffect(() => {\r\n        if (arg !== UNINITIALIZED_VALUE && !promiseRef.current) {\r\n          trigger(arg, true)\r\n        }\r\n      }, [arg, trigger])\r\n\r\n      return useMemo(() => [trigger, arg] as const, [trigger, arg])\r\n    }\r\n\r\n    const useQueryState: UseQueryState<any> = (\r\n      arg: any,\r\n      { skip = false, selectFromResult } = {}\r\n    ) => {\r\n      const { select } = api.endpoints[name] as ApiEndpointQuery<\r\n        QueryDefinition<any, any, any, any, any>,\r\n        Definitions\r\n      >\r\n      const stableArg = useStableQueryArgs(\r\n        skip ? skipToken : arg,\r\n        serializeQueryArgs,\r\n        context.endpointDefinitions[name],\r\n        name\r\n      )\r\n\r\n      type ApiRootState = Parameters<ReturnType<typeof select>>[0]\r\n\r\n      const lastValue = useRef<any>()\r\n\r\n      const selectDefaultResult: Selector<ApiRootState, any, [any]> = useMemo(\r\n        () =>\r\n          createSelector(\r\n            [\r\n              select(stableArg),\r\n              (_: ApiRootState, lastResult: any) => lastResult,\r\n              (_: ApiRootState) => stableArg,\r\n            ],\r\n            queryStatePreSelector\r\n          ),\r\n        [select, stableArg]\r\n      )\r\n\r\n      const querySelector: Selector<ApiRootState, any, [any]> = useMemo(\r\n        () =>\r\n          selectFromResult\r\n            ? createSelector([selectDefaultResult], selectFromResult)\r\n            : selectDefaultResult,\r\n        [selectDefaultResult, selectFromResult]\r\n      )\r\n\r\n      const currentState = useSelector(\r\n        (state: RootState<Definitions, any, any>) =>\r\n          querySelector(state, lastValue.current),\r\n        shallowEqual\r\n      )\r\n\r\n      const store = useStore<RootState<Definitions, any, any>>()\r\n      const newLastValue = selectDefaultResult(\r\n        store.getState(),\r\n        lastValue.current\r\n      )\r\n      useIsomorphicLayoutEffect(() => {\r\n        lastValue.current = newLastValue\r\n      }, [newLastValue])\r\n\r\n      return currentState\r\n    }\r\n\r\n    return {\r\n      useQueryState,\r\n      useQuerySubscription,\r\n      useLazyQuerySubscription,\r\n      useLazyQuery(options) {\r\n        const [trigger, arg] = useLazyQuerySubscription(options)\r\n        const queryStateResults = useQueryState(arg, {\r\n          ...options,\r\n          skip: arg === UNINITIALIZED_VALUE,\r\n        })\r\n\r\n        const info = useMemo(() => ({ lastArg: arg }), [arg])\r\n        return useMemo(\r\n          () => [trigger, queryStateResults, info],\r\n          [trigger, queryStateResults, info]\r\n        )\r\n      },\r\n      useQuery(arg, options) {\r\n        const querySubscriptionResults = useQuerySubscription(arg, options)\r\n        const queryStateResults = useQueryState(arg, {\r\n          selectFromResult:\r\n            arg === skipToken || options?.skip\r\n              ? undefined\r\n              : noPendingQueryStateSelector,\r\n          ...options,\r\n        })\r\n\r\n        const { data, status, isLoading, isSuccess, isError, error } =\r\n          queryStateResults\r\n        useDebugValue({ data, status, isLoading, isSuccess, isError, error })\r\n\r\n        return useMemo(\r\n          () => ({ ...queryStateResults, ...querySubscriptionResults }),\r\n          [queryStateResults, querySubscriptionResults]\r\n        )\r\n      },\r\n    }\r\n  }\r\n\r\n  function buildMutationHook(name: string): UseMutation<any> {\r\n    return ({\r\n      selectFromResult = defaultMutationStateSelector,\r\n      fixedCacheKey,\r\n    } = {}) => {\r\n      const { select, initiate } = api.endpoints[name] as ApiEndpointMutation<\r\n        MutationDefinition<any, any, any, any, any>,\r\n        Definitions\r\n      >\r\n      const dispatch = useDispatch<ThunkDispatch<any, any, AnyAction>>()\r\n      const [promise, setPromise] = useState<MutationActionCreatorResult<any>>()\r\n\r\n      useEffect(\r\n        () => () => {\r\n          if (!promise?.arg.fixedCacheKey) {\r\n            promise?.reset()\r\n          }\r\n        },\r\n        [promise]\r\n      )\r\n\r\n      const triggerMutation = useCallback(\r\n        function (arg: Parameters<typeof initiate>['0']) {\r\n          const promise = dispatch(initiate(arg, { fixedCacheKey }))\r\n          setPromise(promise)\r\n          return promise\r\n        },\r\n        [dispatch, initiate, fixedCacheKey]\r\n      )\r\n\r\n      const { requestId } = promise || {}\r\n      const mutationSelector = useMemo(\r\n        () =>\r\n          createSelector(\r\n            [select({ fixedCacheKey, requestId: promise?.requestId })],\r\n            selectFromResult\r\n          ),\r\n        [select, promise, selectFromResult, fixedCacheKey]\r\n      )\r\n\r\n      const currentState = useSelector(mutationSelector, shallowEqual)\r\n      const originalArgs =\r\n        fixedCacheKey == null ? promise?.arg.originalArgs : undefined\r\n      const reset = useCallback(() => {\r\n        batch(() => {\r\n          if (promise) {\r\n            setPromise(undefined)\r\n          }\r\n          if (fixedCacheKey) {\r\n            dispatch(\r\n              api.internalActions.removeMutationResult({\r\n                requestId,\r\n                fixedCacheKey,\r\n              })\r\n            )\r\n          }\r\n        })\r\n      }, [dispatch, fixedCacheKey, promise, requestId])\r\n\r\n      const {\r\n        endpointName,\r\n        data,\r\n        status,\r\n        isLoading,\r\n        isSuccess,\r\n        isError,\r\n        error,\r\n      } = currentState\r\n      useDebugValue({\r\n        endpointName,\r\n        data,\r\n        status,\r\n        isLoading,\r\n        isSuccess,\r\n        isError,\r\n        error,\r\n      })\r\n\r\n      const finalState = useMemo(\r\n        () => ({ ...currentState, originalArgs, reset }),\r\n        [currentState, originalArgs, reset]\r\n      )\r\n\r\n      return useMemo(\r\n        () => [triggerMutation, finalState] as const,\r\n        [triggerMutation, finalState]\r\n      )\r\n    }\r\n  }\r\n}\r\n", "import { useEffect, useRef, useMemo } from 'react'\r\nimport type { SerializeQueryArgs } from '@reduxjs/toolkit/query'\r\nimport type { EndpointDefinition } from '@reduxjs/toolkit/query'\r\n\r\nexport function useStableQueryArgs<T>(\r\n  queryArgs: T,\r\n  serialize: SerializeQueryArgs<any>,\r\n  endpointDefinition: EndpointDefinition<any, any, any, any>,\r\n  endpointName: string\r\n) {\r\n  const incoming = useMemo(\r\n    () => ({\r\n      queryArgs,\r\n      serialized:\r\n        typeof queryArgs == 'object'\r\n          ? serialize({ queryArgs, endpointDefinition, endpointName })\r\n          : queryArgs,\r\n    }),\r\n    [queryArgs, serialize, endpointDefinition, endpointName]\r\n  )\r\n  const cache = useRef(incoming)\r\n  useEffect(() => {\r\n    if (cache.current.serialized !== incoming.serialized) {\r\n      cache.current = incoming\r\n    }\r\n  }, [incoming])\r\n\r\n  return cache.current.serialized === incoming.serialized\r\n    ? cache.current.queryArgs\r\n    : queryArgs\r\n}\r\n", "export const UNINITIALIZED_VALUE = Symbol()\r\nexport type UninitializedValue = typeof UNINITIALIZED_VALUE\r\n", "import { useEffect, useRef } from 'react'\r\nimport { shallowEqual } from 'react-redux'\r\n\r\nexport function useShallowStableValue<T>(value: T) {\r\n  const cache = useRef(value)\r\n  useEffect(() => {\r\n    if (!shallowEqual(cache.current, value)) {\r\n      cache.current = value\r\n    }\r\n  }, [value])\r\n\r\n  return shallowEqual(cache.current, value) ? cache.current : value\r\n}\r\n", "import type { QueryCacheKey } from './core/apiState'\r\nimport type { EndpointDefinition } from './endpointDefinitions'\r\nimport { isPlainObject } from '@reduxjs/toolkit'\r\n\r\nconst cache: WeakMap<any, string> | undefined = WeakMap\r\n  ? new WeakMap()\r\n  : undefined\r\n\r\nexport const defaultSerializeQueryArgs: SerializeQueryArgs<any> = ({\r\n  endpointName,\r\n  queryArgs,\r\n}) => {\r\n  let serialized = ''\r\n\r\n  const cached = cache?.get(queryArgs)\r\n\r\n  if (typeof cached === 'string') {\r\n    serialized = cached\r\n  } else {\r\n    const stringified = JSON.stringify(queryArgs, (key, value) =>\r\n      isPlainObject(value)\r\n        ? Object.keys(value)\r\n            .sort()\r\n            .reduce<any>((acc, key) => {\r\n              acc[key] = (value as any)[key]\r\n              return acc\r\n            }, {})\r\n        : value\r\n    )\r\n    if (isPlainObject(queryArgs)) {\r\n      cache?.set(queryArgs, stringified)\r\n    }\r\n    serialized = stringified\r\n  }\r\n  // Sort the object keys before stringifying, to prevent useQuery({ a: 1, b: 2 }) having a different cache key than useQuery({ b: 2, a: 1 })\r\n  return `${endpointName}(${serialized})`\r\n}\r\n\r\nexport type SerializeQueryArgs<QueryArgs, ReturnType = string> = (_: {\r\n  queryArgs: QueryArgs\r\n  endpointDefinition: EndpointDefinition<any, any, any, any>\r\n  endpointName: string\r\n}) => ReturnType\r\n\r\nexport type InternalSerializeQueryArgs = (_: {\r\n  queryArgs: any\r\n  endpointDefinition: EndpointDefinition<any, any, any, any>\r\n  endpointName: string\r\n}) => QueryCacheKey\r\n", "import type { AnyAction, ThunkDispatch } from '@reduxjs/toolkit'\r\nimport type { SerializeQueryArgs } from './defaultSerializeQueryArgs'\r\nimport type { QuerySubState, RootState } from './core/apiState'\r\nimport type {\r\n  BaseQueryExtraOptions,\r\n  BaseQueryFn,\r\n  BaseQueryResult,\r\n  BaseQueryArg,\r\n  BaseQueryApi,\r\n  QueryReturnValue,\r\n  BaseQueryError,\r\n  BaseQueryMeta,\r\n} from './baseQueryTypes'\r\nimport type {\r\n  HasRequiredProps,\r\n  MaybePromise,\r\n  OmitFromUnion,\r\n  CastAny,\r\n  NonUndefined,\r\n  UnwrapPromise,\r\n} from './tsHelpers'\r\nimport type { NEVER } from './fakeBaseQuery'\r\nimport type { Api } from '@reduxjs/toolkit/query'\r\n\r\nconst resultType = /* @__PURE__ */ Symbol()\r\nconst baseQuery = /* @__PURE__ */ Symbol()\r\n\r\ninterface EndpointDefinitionWithQuery<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> {\r\n  /**\r\n   * `query` can be a function that returns either a `string` or an `object` which is passed to your `baseQuery`. If you are using [fetchBaseQuery](./fetchBaseQuery), this can return either a `string` or an `object` of properties in `FetchArgs`. If you use your own custom [`baseQuery`](../../rtk-query/usage/customizing-queries), you can customize this behavior to your liking.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"query example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Post'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       // highlight-start\r\n   *       query: () => 'posts',\r\n   *       // highlight-end\r\n   *     }),\r\n   *     addPost: build.mutation<Post, Partial<Post>>({\r\n   *      // highlight-start\r\n   *      query: (body) => ({\r\n   *        url: `posts`,\r\n   *        method: 'POST',\r\n   *        body,\r\n   *      }),\r\n   *      // highlight-end\r\n   *      invalidatesTags: [{ type: 'Post', id: 'LIST' }],\r\n   *    }),\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  query(arg: QueryArg): BaseQueryArg<BaseQuery>\r\n  queryFn?: never\r\n  /**\r\n   * A function to manipulate the data returned by a query or mutation.\r\n   */\r\n  transformResponse?(\r\n    baseQueryReturnValue: BaseQueryResult<BaseQuery>,\r\n    meta: BaseQueryMeta<BaseQuery>,\r\n    arg: QueryArg\r\n  ): ResultType | Promise<ResultType>\r\n  /**\r\n   * A function to manipulate the data returned by a failed query or mutation.\r\n   */\r\n  transformErrorResponse?(\r\n    baseQueryReturnValue: BaseQueryError<BaseQuery>,\r\n    meta: BaseQueryMeta<BaseQuery>,\r\n    arg: QueryArg\r\n  ): unknown\r\n  /**\r\n   * Defaults to `true`.\r\n   *\r\n   * Most apps should leave this setting on. The only time it can be a performance issue\r\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\r\n   * you're unable to paginate it.\r\n   *\r\n   * For details of how this works, please see the below. When it is set to `false`,\r\n   * every request will cause subscribed components to rerender, even when the data has not changed.\r\n   *\r\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\r\n   */\r\n  structuralSharing?: boolean\r\n}\r\n\r\ninterface EndpointDefinitionWithQueryFn<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> {\r\n  /**\r\n   * Can be used in place of `query` as an inline function that bypasses `baseQuery` completely for the endpoint.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Basic queryFn example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *     }),\r\n   *     flipCoin: build.query<'heads' | 'tails', void>({\r\n   *       // highlight-start\r\n   *       queryFn(arg, queryApi, extraOptions, baseQuery) {\r\n   *         const randomVal = Math.random()\r\n   *         if (randomVal < 0.45) {\r\n   *           return { data: 'heads' }\r\n   *         }\r\n   *         if (randomVal < 0.9) {\r\n   *           return { data: 'tails' }\r\n   *         }\r\n   *         return { error: { status: 500, statusText: 'Internal Server Error', data: \"Coin landed on it's edge!\" } }\r\n   *       }\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  queryFn(\r\n    arg: QueryArg,\r\n    api: BaseQueryApi,\r\n    extraOptions: BaseQueryExtraOptions<BaseQuery>,\r\n    baseQuery: (arg: Parameters<BaseQuery>[0]) => ReturnType<BaseQuery>\r\n  ): MaybePromise<QueryReturnValue<ResultType, BaseQueryError<BaseQuery>>>\r\n  query?: never\r\n  transformResponse?: never\r\n  transformErrorResponse?: never\r\n  /**\r\n   * Defaults to `true`.\r\n   *\r\n   * Most apps should leave this setting on. The only time it can be a performance issue\r\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\r\n   * you're unable to paginate it.\r\n   *\r\n   * For details of how this works, please see the below. When it is set to `false`,\r\n   * every request will cause subscribed components to rerender, even when the data has not changed.\r\n   *\r\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\r\n   */\r\n  structuralSharing?: boolean\r\n}\r\n\r\nexport interface BaseEndpointTypes<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> {\r\n  QueryArg: QueryArg\r\n  BaseQuery: BaseQuery\r\n  ResultType: ResultType\r\n}\r\n\r\nexport type BaseEndpointDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> = (\r\n  | ([CastAny<BaseQueryResult<BaseQuery>, {}>] extends [NEVER]\r\n      ? never\r\n      : EndpointDefinitionWithQuery<QueryArg, BaseQuery, ResultType>)\r\n  | EndpointDefinitionWithQueryFn<QueryArg, BaseQuery, ResultType>\r\n) & {\r\n  /* phantom type */\r\n  [resultType]?: ResultType\r\n  /* phantom type */\r\n  [baseQuery]?: BaseQuery\r\n} & HasRequiredProps<\r\n    BaseQueryExtraOptions<BaseQuery>,\r\n    { extraOptions: BaseQueryExtraOptions<BaseQuery> },\r\n    { extraOptions?: BaseQueryExtraOptions<BaseQuery> }\r\n  >\r\n\r\nexport enum DefinitionType {\r\n  query = 'query',\r\n  mutation = 'mutation',\r\n}\r\n\r\nexport type GetResultDescriptionFn<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  ErrorType,\r\n  MetaType\r\n> = (\r\n  result: ResultType | undefined,\r\n  error: ErrorType | undefined,\r\n  arg: QueryArg,\r\n  meta: MetaType\r\n) => ReadonlyArray<TagDescription<TagTypes>>\r\n\r\nexport type FullTagDescription<TagType> = {\r\n  type: TagType\r\n  id?: number | string\r\n}\r\nexport type TagDescription<TagType> = TagType | FullTagDescription<TagType>\r\nexport type ResultDescription<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  ErrorType,\r\n  MetaType\r\n> =\r\n  | ReadonlyArray<TagDescription<TagTypes>>\r\n  | GetResultDescriptionFn<TagTypes, ResultType, QueryArg, ErrorType, MetaType>\r\n\r\n/** @deprecated please use `onQueryStarted` instead */\r\nexport interface QueryApi<ReducerPath extends string, Context extends {}> {\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  dispatch: ThunkDispatch<any, any, AnyAction>\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  getState(): RootState<any, any, ReducerPath>\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  extra: unknown\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  requestId: string\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  context: Context\r\n}\r\n\r\nexport interface QueryTypes<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\r\n  /**\r\n   * The endpoint definition type. To be used with some internal generic types.\r\n   * @example\r\n   * ```ts\r\n   * const useMyWrappedHook: UseQuery<typeof api.endpoints.query.Types.QueryDefinition> = ...\r\n   * ```\r\n   */\r\n  QueryDefinition: QueryDefinition<\r\n    QueryArg,\r\n    BaseQuery,\r\n    TagTypes,\r\n    ResultType,\r\n    ReducerPath\r\n  >\r\n  TagTypes: TagTypes\r\n  ReducerPath: ReducerPath\r\n}\r\n\r\nexport interface QueryExtraOptions<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ReducerPath extends string = string\r\n> {\r\n  type: DefinitionType.query\r\n  /**\r\n   * Used by `query` endpoints. Determines which 'tag' is attached to the cached data returned by the query.\r\n   * Expects an array of tag type strings, an array of objects of tag types with ids, or a function that returns such an array.\r\n   * 1.  `['Post']` - equivalent to `2`\r\n   * 2.  `[{ type: 'Post' }]` - equivalent to `1`\r\n   * 3.  `[{ type: 'Post', id: 1 }]`\r\n   * 4.  `(result, error, arg) => ['Post']` - equivalent to `5`\r\n   * 5.  `(result, error, arg) => [{ type: 'Post' }]` - equivalent to `4`\r\n   * 6.  `(result, error, arg) => [{ type: 'Post', id: 1 }]`\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"providesTags example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Posts'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       // highlight-start\r\n   *       providesTags: (result) =>\r\n   *         result\r\n   *           ? [\r\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\r\n   *               { type: 'Posts', id: 'LIST' },\r\n   *             ]\r\n   *           : [{ type: 'Posts', id: 'LIST' }],\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  providesTags?: ResultDescription<\r\n    TagTypes,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQueryError<BaseQuery>,\r\n    BaseQueryMeta<BaseQuery>\r\n  >\r\n  /**\r\n   * Not to be used. A query should not invalidate tags in the cache.\r\n   */\r\n  invalidatesTags?: never\r\n\r\n  /**\r\n   * Can be provided to return a custom cache key value based on the query arguments.\r\n   *\r\n   * This is primarily intended for cases where a non-serializable value is passed as part of the query arg object and should be excluded from the cache key.  It may also be used for cases where an endpoint should only have a single cache entry, such as an infinite loading / pagination implementation.\r\n   *\r\n   * Unlike the `createApi` version which can _only_ return a string, this per-endpoint option can also return an an object, number, or boolean.  If it returns a string, that value will be used as the cache key directly.  If it returns an object / number / boolean, that value will be passed to the built-in `defaultSerializeQueryArgs`.  This simplifies the use case of stripping out args you don't want included in the cache key.\r\n   *\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"serializeQueryArgs : exclude value\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * interface MyApiClient {\r\n   *   fetchPost: (id: string) => Promise<Post>\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    // Example: an endpoint with an API client passed in as an argument,\r\n   *    // but only the item ID should be used as the cache key\r\n   *    getPost: build.query<Post, { id: string; client: MyApiClient }>({\r\n   *      queryFn: async ({ id, client }) => {\r\n   *        const post = await client.fetchPost(id)\r\n   *        return { data: post }\r\n   *      },\r\n   *      // highlight-start\r\n   *      serializeQueryArgs: ({ queryArgs, endpointDefinition, endpointName }) => {\r\n   *        const { id } = queryArgs\r\n   *        // This can return a string, an object, a number, or a boolean.\r\n   *        // If it returns an object, number or boolean, that value\r\n   *        // will be serialized automatically via `defaultSerializeQueryArgs`\r\n   *        return { id } // omit `client` from the cache key\r\n   *\r\n   *        // Alternately, you can use `defaultSerializeQueryArgs` yourself:\r\n   *        // return defaultSerializeQueryArgs({\r\n   *        //   endpointName,\r\n   *        //   queryArgs: { id },\r\n   *        //   endpointDefinition\r\n   *        // })\r\n   *        // Or  create and return a string yourself:\r\n   *        // return `getPost(${id})`\r\n   *      },\r\n   *      // highlight-end\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\r\n  serializeQueryArgs?: SerializeQueryArgs<\r\n    QueryArg,\r\n    string | number | boolean | Record<any, any>\r\n  >\r\n\r\n  /**\r\n   * Can be provided to merge an incoming response value into the current cache data.\r\n   * If supplied, no automatic structural sharing will be applied - it's up to\r\n   * you to update the cache appropriately.\r\n   *\r\n   * Since RTKQ normally replaces cache entries with the new response, you will usually\r\n   * need to use this with the `serializeQueryArgs` or `forceRefetch` options to keep\r\n   * an existing cache entry so that it can be updated.\r\n   *\r\n   * Since this is wrapped with Immer, you may either mutate the `currentCacheValue` directly,\r\n   * or return a new value, but _not_ both at once.\r\n   *\r\n   * Will only be called if the existing `currentCacheData` is _not_ `undefined` - on first response,\r\n   * the cache entry will just save the response data directly.\r\n   *\r\n   * Useful if you don't want a new request to completely override the current cache value,\r\n   * maybe because you have manually updated it from another source and don't want those\r\n   * updates to get lost.\r\n   *\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"merge: pagination\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    listItems: build.query<string[], number>({\r\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\r\n   *     // Only have one cache entry because the arg always maps to one string\r\n   *     serializeQueryArgs: ({ endpointName }) => {\r\n   *       return endpointName\r\n   *      },\r\n   *      // Always merge incoming data to the cache entry\r\n   *      merge: (currentCache, newItems) => {\r\n   *        currentCache.push(...newItems)\r\n   *      },\r\n   *      // Refetch when the page arg changes\r\n   *      forceRefetch({ currentArg, previousArg }) {\r\n   *        return currentArg !== previousArg\r\n   *      },\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\r\n  merge?(\r\n    currentCacheData: ResultType,\r\n    responseData: ResultType,\r\n    otherArgs: {\r\n      arg: QueryArg\r\n      baseQueryMeta: BaseQueryMeta<BaseQuery>\r\n      requestId: string\r\n      fulfilledTimeStamp: number\r\n    }\r\n  ): ResultType | void\r\n\r\n  /**\r\n   * Check to see if the endpoint should force a refetch in cases where it normally wouldn't.\r\n   * This is primarily useful for \"infinite scroll\" / pagination use cases where\r\n   * RTKQ is keeping a single cache entry that is added to over time, in combination\r\n   * with `serializeQueryArgs` returning a fixed cache key and a `merge` callback\r\n   * set to add incoming data to the cache entry each time.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"forceRefresh: pagination\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    listItems: build.query<string[], number>({\r\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\r\n   *     // Only have one cache entry because the arg always maps to one string\r\n   *     serializeQueryArgs: ({ endpointName }) => {\r\n   *       return endpointName\r\n   *      },\r\n   *      // Always merge incoming data to the cache entry\r\n   *      merge: (currentCache, newItems) => {\r\n   *        currentCache.push(...newItems)\r\n   *      },\r\n   *      // Refetch when the page arg changes\r\n   *      forceRefetch({ currentArg, previousArg }) {\r\n   *        return currentArg !== previousArg\r\n   *      },\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\r\n  forceRefetch?(params: {\r\n    currentArg: QueryArg | undefined\r\n    previousArg: QueryArg | undefined\r\n    state: RootState<any, any, string>\r\n    endpointState?: QuerySubState<any>\r\n  }): boolean\r\n\r\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\r\n  Types?: QueryTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n}\r\n\r\nexport type QueryDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> &\r\n  QueryExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>\r\n\r\nexport interface MutationTypes<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\r\n  /**\r\n   * The endpoint definition type. To be used with some internal generic types.\r\n   * @example\r\n   * ```ts\r\n   * const useMyWrappedHook: UseMutation<typeof api.endpoints.query.Types.MutationDefinition> = ...\r\n   * ```\r\n   */\r\n  MutationDefinition: MutationDefinition<\r\n    QueryArg,\r\n    BaseQuery,\r\n    TagTypes,\r\n    ResultType,\r\n    ReducerPath\r\n  >\r\n  TagTypes: TagTypes\r\n  ReducerPath: ReducerPath\r\n}\r\n\r\nexport interface MutationExtraOptions<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ReducerPath extends string = string\r\n> {\r\n  type: DefinitionType.mutation\r\n  /**\r\n   * Used by `mutation` endpoints. Determines which cached data should be either re-fetched or removed from the cache.\r\n   * Expects the same shapes as `providesTags`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"invalidatesTags example\"\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Posts'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       providesTags: (result) =>\r\n   *         result\r\n   *           ? [\r\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\r\n   *               { type: 'Posts', id: 'LIST' },\r\n   *             ]\r\n   *           : [{ type: 'Posts', id: 'LIST' }],\r\n   *     }),\r\n   *     addPost: build.mutation<Post, Partial<Post>>({\r\n   *       query(body) {\r\n   *         return {\r\n   *           url: `posts`,\r\n   *           method: 'POST',\r\n   *           body,\r\n   *         }\r\n   *       },\r\n   *       // highlight-start\r\n   *       invalidatesTags: [{ type: 'Posts', id: 'LIST' }],\r\n   *       // highlight-end\r\n   *     }),\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  invalidatesTags?: ResultDescription<\r\n    TagTypes,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQueryError<BaseQuery>,\r\n    BaseQueryMeta<BaseQuery>\r\n  >\r\n  /**\r\n   * Not to be used. A mutation should not provide tags to the cache.\r\n   */\r\n  providesTags?: never\r\n\r\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\r\n  Types?: MutationTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n}\r\n\r\nexport type MutationDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> &\r\n  MutationExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>\r\n\r\nexport type EndpointDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> =\r\n  | QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n  | MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n\r\nexport type EndpointDefinitions = Record<\r\n  string,\r\n  EndpointDefinition<any, any, any, any>\r\n>\r\n\r\nexport function isQueryDefinition(\r\n  e: EndpointDefinition<any, any, any, any>\r\n): e is QueryDefinition<any, any, any, any> {\r\n  return e.type === DefinitionType.query\r\n}\r\n\r\nexport function isMutationDefinition(\r\n  e: EndpointDefinition<any, any, any, any>\r\n): e is MutationDefinition<any, any, any, any> {\r\n  return e.type === DefinitionType.mutation\r\n}\r\n\r\nexport type EndpointBuilder<\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ReducerPath extends string\r\n> = {\r\n  /**\r\n   * An endpoint definition that retrieves data, and may provide tags to the cache.\r\n   *\r\n   * @example\r\n   * ```js\r\n   * // codeblock-meta title=\"Example of all query endpoint options\"\r\n   * const api = createApi({\r\n   *  baseQuery,\r\n   *  endpoints: (build) => ({\r\n   *    getPost: build.query({\r\n   *      query: (id) => ({ url: `post/${id}` }),\r\n   *      // Pick out data and prevent nested properties in a hook or selector\r\n   *      transformResponse: (response) => response.data,\r\n   *      // Pick out error and prevent nested properties in a hook or selector\r\n   *      transformErrorResponse: (response) => response.error,\r\n   *      // `result` is the server response\r\n   *      providesTags: (result, error, id) => [{ type: 'Post', id }],\r\n   *      // trigger side effects or optimistic updates\r\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry, updateCachedData }) {},\r\n   *      // handle subscriptions etc\r\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry, updateCachedData }) {},\r\n   *    }),\r\n   *  }),\r\n   *});\r\n   *```\r\n   */\r\n  query<ResultType, QueryArg>(\r\n    definition: OmitFromUnion<\r\n      QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>,\r\n      'type'\r\n    >\r\n  ): QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n  /**\r\n   * An endpoint definition that alters data on the server or will possibly invalidate the cache.\r\n   *\r\n   * @example\r\n   * ```js\r\n   * // codeblock-meta title=\"Example of all mutation endpoint options\"\r\n   * const api = createApi({\r\n   *   baseQuery,\r\n   *   endpoints: (build) => ({\r\n   *     updatePost: build.mutation({\r\n   *       query: ({ id, ...patch }) => ({ url: `post/${id}`, method: 'PATCH', body: patch }),\r\n   *       // Pick out data and prevent nested properties in a hook or selector\r\n   *       transformResponse: (response) => response.data,\r\n   *       // Pick out error and prevent nested properties in a hook or selector\r\n   *       transformErrorResponse: (response) => response.error,\r\n   *       // `result` is the server response\r\n   *       invalidatesTags: (result, error, id) => [{ type: 'Post', id }],\r\n   *      // trigger side effects or optimistic updates\r\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry }) {},\r\n   *      // handle subscriptions etc\r\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry }) {},\r\n   *     }),\r\n   *   }),\r\n   * });\r\n   * ```\r\n   */\r\n  mutation<ResultType, QueryArg>(\r\n    definition: OmitFromUnion<\r\n      MutationDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        TagTypes,\r\n        ResultType,\r\n        ReducerPath\r\n      >,\r\n      'type'\r\n    >\r\n  ): MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n}\r\n\r\nexport type AssertTagTypes = <T extends FullTagDescription<string>>(t: T) => T\r\n\r\nexport function calculateProvidedBy<ResultType, QueryArg, ErrorType, MetaType>(\r\n  description:\r\n    | ResultDescription<string, ResultType, QueryArg, ErrorType, MetaType>\r\n    | undefined,\r\n  result: ResultType | undefined,\r\n  error: ErrorType | undefined,\r\n  queryArg: QueryArg,\r\n  meta: MetaType | undefined,\r\n  assertTagTypes: AssertTagTypes\r\n): readonly FullTagDescription<string>[] {\r\n  if (isFunction(description)) {\r\n    return description(\r\n      result as ResultType,\r\n      error as undefined,\r\n      queryArg,\r\n      meta as MetaType\r\n    )\r\n      .map(expandTagDescription)\r\n      .map(assertTagTypes)\r\n  }\r\n  if (Array.isArray(description)) {\r\n    return description.map(expandTagDescription).map(assertTagTypes)\r\n  }\r\n  return []\r\n}\r\n\r\nfunction isFunction<T>(t: T): t is Extract<T, Function> {\r\n  return typeof t === 'function'\r\n}\r\n\r\nexport function expandTagDescription(\r\n  description: TagDescription<string>\r\n): FullTagDescription<string> {\r\n  return typeof description === 'string' ? { type: description } : description\r\n}\r\n\r\nexport type QueryArgFrom<D extends BaseEndpointDefinition<any, any, any>> =\r\n  D extends BaseEndpointDefinition<infer QA, any, any> ? QA : unknown\r\nexport type ResultTypeFrom<D extends BaseEndpointDefinition<any, any, any>> =\r\n  D extends BaseEndpointDefinition<any, any, infer RT> ? RT : unknown\r\n\r\nexport type ReducerPathFrom<\r\n  D extends EndpointDefinition<any, any, any, any, any>\r\n> = D extends EndpointDefinition<any, any, any, any, infer RP> ? RP : unknown\r\n\r\nexport type TagTypesFrom<D extends EndpointDefinition<any, any, any, any>> =\r\n  D extends EndpointDefinition<any, any, infer RP, any> ? RP : unknown\r\n\r\nexport type TagTypesFromApi<T> = T extends Api<any, any, any, infer TagTypes>\r\n  ? TagTypes\r\n  : never\r\n\r\nexport type DefinitionsFromApi<T> = T extends Api<\r\n  any,\r\n  infer Definitions,\r\n  any,\r\n  any\r\n>\r\n  ? Definitions\r\n  : never\r\n\r\nexport type TransformedResponse<\r\n  NewDefinitions extends EndpointDefinitions,\r\n  K,\r\n  ResultType\r\n> = K extends keyof NewDefinitions\r\n  ? NewDefinitions[K]['transformResponse'] extends undefined\r\n    ? ResultType\r\n    : UnwrapPromise<\r\n        ReturnType<NonUndefined<NewDefinitions[K]['transformResponse']>>\r\n      >\r\n  : ResultType\r\n\r\nexport type OverrideResultType<Definition, NewResultType> =\r\n  Definition extends QueryDefinition<\r\n    infer QueryArg,\r\n    infer BaseQuery,\r\n    infer TagTypes,\r\n    any,\r\n    infer ReducerPath\r\n  >\r\n    ? QueryDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath>\r\n    : Definition extends MutationDefinition<\r\n        infer QueryArg,\r\n        infer BaseQuery,\r\n        infer TagTypes,\r\n        any,\r\n        infer ReducerPath\r\n      >\r\n    ? MutationDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        TagTypes,\r\n        NewResultType,\r\n        ReducerPath\r\n      >\r\n    : never\r\n\r\nexport type UpdateDefinitions<\r\n  Definitions extends EndpointDefinitions,\r\n  NewTagTypes extends string,\r\n  NewDefinitions extends EndpointDefinitions\r\n> = {\r\n  [K in keyof Definitions]: Definitions[K] extends QueryDefinition<\r\n    infer QueryArg,\r\n    infer BaseQuery,\r\n    any,\r\n    infer ResultType,\r\n    infer ReducerPath\r\n  >\r\n    ? QueryDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        NewTagTypes,\r\n        TransformedResponse<NewDefinitions, K, ResultType>,\r\n        ReducerPath\r\n      >\r\n    : Definitions[K] extends MutationDefinition<\r\n        infer QueryArg,\r\n        infer BaseQuery,\r\n        any,\r\n        infer ResultType,\r\n        infer ReducerPath\r\n      >\r\n    ? MutationDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        NewTagTypes,\r\n        TransformedResponse<NewDefinitions, K, ResultType>,\r\n        ReducerPath\r\n      >\r\n    : never\r\n}\r\n", "export function capitalize(str: string) {\r\n  return str.replace(str[0], str[0].toUpperCase())\r\n}\r\n", "export type Id<T> = { [K in keyof T]: T[K] } & {}\r\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> &\r\n  Required<Pick<T, K>>\r\nexport type Override<T1, T2> = T2 extends any ? Omit<T1, keyof T2> & T2 : never\r\nexport function assertCast<T>(v: any): asserts v is T {}\r\n\r\nexport function safeAssign<T extends object>(\r\n  target: T,\r\n  ...args: Array<Partial<NoInfer<T>>>\r\n) {\r\n  Object.assign(target, ...args)\r\n}\r\n\r\n/**\r\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\r\n */\r\nexport type UnionToIntersection<U> = (\r\n  U extends any ? (k: U) => void : never\r\n) extends (k: infer I) => void\r\n  ? I\r\n  : never\r\n\r\nexport type NonOptionalKeys<T> = {\r\n  [K in keyof T]-?: undefined extends T[K] ? never : K\r\n}[keyof T]\r\n\r\nexport type HasRequiredProps<T, True, False> = NonOptionalKeys<T> extends never\r\n  ? False\r\n  : True\r\n\r\nexport type OptionalIfAllPropsOptional<T> = HasRequiredProps<T, T, T | never>\r\n\r\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\r\n\r\nexport type NonUndefined<T> = T extends undefined ? never : T\r\n\r\nexport type UnwrapPromise<T> = T extends PromiseLike<infer V> ? V : T\r\n\r\nexport type MaybePromise<T> = T | PromiseLike<T>\r\n\r\nexport type OmitFromUnion<T, K extends keyof T> = T extends any\r\n  ? Omit<T, K>\r\n  : never\r\n\r\nexport type IsAny<T, True, False = never> = true | false extends (\r\n  T extends never ? true : false\r\n)\r\n  ? True\r\n  : False\r\n\r\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>\r\n", "import type { Mu<PERSON>Hooks, QueryHooks } from './buildHooks'\r\nimport { buildHooks } from './buildHooks'\r\nimport { isQueryDefinition, isMutationDefinition } from '../endpointDefinitions'\r\nimport type {\r\n  EndpointDefinitions,\r\n  QueryDefinition,\r\n  MutationDefinition,\r\n  QueryArgFrom,\r\n} from '@reduxjs/toolkit/query'\r\nimport type { Api, Module } from '../apiTypes'\r\nimport { capitalize } from '../utils'\r\nimport { safeAssign } from '../tsHelpers'\r\nimport type { BaseQueryFn } from '@reduxjs/toolkit/query'\r\n\r\nimport type { HooksWithUniqueNames } from './namedHooks'\r\n\r\nimport {\r\n  useDispatch as rrUseDispatch,\r\n  useSelector as rrUseSelector,\r\n  useStore as rrUseStore,\r\n  batch as rrBatch,\r\n} from 'react-redux'\r\nimport type { QueryKeys } from '../core/apiState'\r\nimport type { PrefetchOptions } from '../core/module'\r\n\r\nexport const reactHooksModuleName = /* @__PURE__ */ Symbol()\r\nexport type ReactHooksModule = typeof reactHooksModuleName\r\n\r\ndeclare module '@reduxjs/toolkit/query' {\r\n  export interface ApiModules<\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    BaseQuery extends BaseQueryFn,\r\n    Definitions extends EndpointDefinitions,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    ReducerPath extends string,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    TagTypes extends string\r\n  > {\r\n    [reactHooksModuleName]: {\r\n      /**\r\n       *  Endpoints based on the input endpoints provided to `createApi`, containing `select`, `hooks` and `action matchers`.\r\n       */\r\n      endpoints: {\r\n        [K in keyof Definitions]: Definitions[K] extends QueryDefinition<\r\n          any,\r\n          any,\r\n          any,\r\n          any,\r\n          any\r\n        >\r\n          ? QueryHooks<Definitions[K]>\r\n          : Definitions[K] extends MutationDefinition<any, any, any, any, any>\r\n          ? MutationHooks<Definitions[K]>\r\n          : never\r\n      }\r\n      /**\r\n       * A hook that accepts a string endpoint name, and provides a callback that when called, pre-fetches the data for that endpoint.\r\n       */\r\n      usePrefetch<EndpointName extends QueryKeys<Definitions>>(\r\n        endpointName: EndpointName,\r\n        options?: PrefetchOptions\r\n      ): (\r\n        arg: QueryArgFrom<Definitions[EndpointName]>,\r\n        options?: PrefetchOptions\r\n      ) => void\r\n    } & HooksWithUniqueNames<Definitions>\r\n  }\r\n}\r\n\r\ntype RR = typeof import('react-redux')\r\n\r\nexport interface ReactHooksModuleOptions {\r\n  /**\r\n   * The version of the `batchedUpdates` function to be used\r\n   */\r\n  batch?: RR['batch']\r\n  /**\r\n   * The version of the `useDispatch` hook to be used\r\n   */\r\n  useDispatch?: RR['useDispatch']\r\n  /**\r\n   * The version of the `useSelector` hook to be used\r\n   */\r\n  useSelector?: RR['useSelector']\r\n  /**\r\n   * The version of the `useStore` hook to be used\r\n   */\r\n  useStore?: RR['useStore']\r\n  /**\r\n   * Enables performing asynchronous tasks immediately within a render.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * import {\r\n   *   buildCreateApi,\r\n   *   coreModule,\r\n   *   reactHooksModule\r\n   * } from '@reduxjs/toolkit/query/react'\r\n   *\r\n   * const createApi = buildCreateApi(\r\n   *   coreModule(),\r\n   *   reactHooksModule({ unstable__sideEffectsInRender: true })\r\n   * )\r\n   * ```\r\n   */\r\n  unstable__sideEffectsInRender?: boolean\r\n}\r\n\r\n/**\r\n * Creates a module that generates react hooks from endpoints, for use with `buildCreateApi`.\r\n *\r\n *  @example\r\n * ```ts\r\n * const MyContext = React.createContext<ReactReduxContextValue>(null as any);\r\n * const customCreateApi = buildCreateApi(\r\n *   coreModule(),\r\n *   reactHooksModule({ useDispatch: createDispatchHook(MyContext) })\r\n * );\r\n * ```\r\n *\r\n * @returns A module for use with `buildCreateApi`\r\n */\r\nexport const reactHooksModule = ({\r\n  batch = rrBatch,\r\n  useDispatch = rrUseDispatch,\r\n  useSelector = rrUseSelector,\r\n  useStore = rrUseStore,\r\n  unstable__sideEffectsInRender = false,\r\n}: ReactHooksModuleOptions = {}): Module<ReactHooksModule> => ({\r\n  name: reactHooksModuleName,\r\n  init(api, { serializeQueryArgs }, context) {\r\n    const anyApi = api as any as Api<\r\n      any,\r\n      Record<string, any>,\r\n      string,\r\n      string,\r\n      ReactHooksModule\r\n    >\r\n    const { buildQueryHooks, buildMutationHook, usePrefetch } = buildHooks({\r\n      api,\r\n      moduleOptions: {\r\n        batch,\r\n        useDispatch,\r\n        useSelector,\r\n        useStore,\r\n        unstable__sideEffectsInRender,\r\n      },\r\n      serializeQueryArgs,\r\n      context,\r\n    })\r\n    safeAssign(anyApi, { usePrefetch })\r\n    safeAssign(context, { batch })\r\n\r\n    return {\r\n      injectEndpoint(endpointName, definition) {\r\n        if (isQueryDefinition(definition)) {\r\n          const {\r\n            useQuery,\r\n            useLazyQuery,\r\n            useLazyQuerySubscription,\r\n            useQueryState,\r\n            useQuerySubscription,\r\n          } = buildQueryHooks(endpointName)\r\n          safeAssign(anyApi.endpoints[endpointName], {\r\n            useQuery,\r\n            useLazyQuery,\r\n            useLazyQuerySubscription,\r\n            useQueryState,\r\n            useQuerySubscription,\r\n          })\r\n          ;(api as any)[`use${capitalize(endpointName)}Query`] = useQuery\r\n          ;(api as any)[`useLazy${capitalize(endpointName)}Query`] =\r\n            useLazyQuery\r\n        } else if (isMutationDefinition(definition)) {\r\n          const useMutation = buildMutationHook(endpointName)\r\n          safeAssign(anyApi.endpoints[endpointName], {\r\n            useMutation,\r\n          })\r\n          ;(api as any)[`use${capitalize(endpointName)}Mutation`] = useMutation\r\n        }\r\n      },\r\n    }\r\n  },\r\n})\r\n", "import { configureStore } from '@reduxjs/toolkit'\r\nimport type { Context } from 'react'\r\nimport { useEffect } from 'react'\r\nimport React from 'react'\r\nimport type { ReactReduxContextValue } from 'react-redux'\r\nimport { Provider } from 'react-redux'\r\nimport { setupListeners } from '@reduxjs/toolkit/query'\r\nimport type { Api } from '@reduxjs/toolkit/query'\r\n\r\n/**\r\n * Can be used as a `Provider` if you **do not already have a Redux store**.\r\n *\r\n * @example\r\n * ```tsx\r\n * // codeblock-meta no-transpile title=\"Basic usage - wrap your App with ApiProvider\"\r\n * import * as React from 'react';\r\n * import { ApiProvider } from '@reduxjs/toolkit/query/react';\r\n * import { Pokemon } from './features/Pokemon';\r\n *\r\n * function App() {\r\n *   return (\r\n *     <ApiProvider api={api}>\r\n *       <Pokemon />\r\n *     </ApiProvider>\r\n *   );\r\n * }\r\n * ```\r\n *\r\n * @remarks\r\n * Using this together with an existing redux store, both will\r\n * conflict with each other - please use the traditional redux setup\r\n * in that case.\r\n */\r\nexport function ApiProvider<A extends Api<any, {}, any, any>>(props: {\r\n  children: any\r\n  api: A\r\n  setupListeners?: Parameters<typeof setupListeners>[1] | false\r\n  context?: Context<ReactReduxContextValue>\r\n}) {\r\n  const [store] = React.useState(() =>\r\n    configureStore({\r\n      reducer: {\r\n        [props.api.reducerPath]: props.api.reducer,\r\n      },\r\n      middleware: (gDM) => gDM().concat(props.api.middleware),\r\n    })\r\n  )\r\n  // Adds the event listeners for online/offline/focus/etc\r\n  useEffect(\r\n    (): undefined | (() => void) =>\r\n      props.setupListeners === false\r\n        ? undefined\r\n        : setupListeners(store.dispatch, props.setupListeners),\r\n    [props.setupListeners, store.dispatch]\r\n  )\r\n\r\n  return (\r\n    <Provider store={store} context={props.context}>\r\n      {props.children}\r\n    </Provider>\r\n  )\r\n}\r\n"], "mappings": "4mCAAAA,EAAAC,S,6DAAAC,CAAAD,QAAA,CAAAE,YAAA,kBAAAA,GAAAC,UAAA,kBAAAA,GAAAC,iBAAA,kBAAAA,GAAAC,qBAAA,kBAAAA,KAAA,IAAAC,EAA2CC,EAAAC,QAAA,2BCC3CC,EAA+BF,EAAAC,QAAA,qBAG/BE,EAQOH,EAAAC,QAAA,UACPG,EAAuCJ,EAAAC,QAAA,2BAwBvCI,EAA6BL,EAAAC,QAAA,gBCrC7BK,EAA2CN,EAAAC,QAAA,UAIpC,SAAAM,EACLC,EACAC,EACAC,EACAC,GAEA,IAAMC,GAAW,EAAAN,EAAAO,UACf,WAAO,OACLL,YACAM,WACsB,iBAAbN,EACHC,EAAU,CAAED,YAAWE,qBAAoBC,iBAC3CH,KAER,CAACA,EAAWC,EAAWC,EAAoBC,IAEvCI,GAAQ,EAAAT,EAAAU,QAAOJ,GAOrB,OANA,EAAAN,EAAAW,YAAU,WACJF,EAAMG,QAAQJ,aAAeF,EAASE,aACxCC,EAAMG,QAAUN,KAEjB,CAACA,IAEGG,EAAMG,QAAQJ,aAAeF,EAASE,WACzCC,EAAMG,QAAQV,UACdA,EC7BC,IAAMW,EAAsBC,SCAnCC,EAAkCrB,EAAAC,QAAA,UAClCqB,EAA6BtB,EAAAC,QAAA,gBAEtB,SAAAsB,EAAkCC,GACvC,IAAMT,GAAQ,EAAAM,EAAAL,QAAOQ,GAOrB,OANA,EAAAH,EAAAJ,YAAU,YACH,EAAAK,EAAAG,cAAaV,EAAMG,QAASM,KAC/BT,EAAMG,QAAUM,KAEjB,CAACA,KAEG,EAAAF,EAAAG,cAAaV,EAAMG,QAASM,GAAST,EAAMG,QAAUM,ECT9D,ICsMYE,EAAAC,EDtMZC,EAA8B5B,EAAAC,QAAA,qBAExB4B,EAA0CC,QAC5C,IAAIA,aACJ,EAESC,EAAqD,SAACC,G,IACjErB,EAAAqB,EAAArB,aACAH,EAAAwB,EAAAxB,UAEIM,EAAa,GAEXmB,EAAS,MAAAJ,OAAA,EAAAA,EAAOK,IAAI1B,GAE1B,GAAsB,iBAAXyB,EACTnB,EAAamB,MACR,CACL,IAAME,EAAcC,KAAKC,UAAU7B,GAAW,SAAC8B,EAAKd,GAClD,SAAAI,EAAAW,eAAcf,GACVgB,OAAOC,KAAKjB,GACTkB,OACAC,QAAY,SAACC,EAAKC,GAEjB,OADAD,EAAIC,GAAQrB,EAAcqB,GACnBD,IACN,IACLpB,MAEF,EAAAI,EAAAW,eAAc/B,KAChB,MAAAqB,KAAOiB,IAAItC,EAAW2B,IAExBrB,EAAaqB,EAGf,OAAUxB,EAAA,IAAgBG,EAAA,KJwBfiC,EACO,oBAAXC,QACLA,OAAOC,UACPD,OAAOC,SAASC,cACd/C,EAAAgD,gBACAhD,EAAAc,UA8dAmC,EAAgE,SAACC,GAAM,OAAAA,GAQvEC,EAA4D,SAChEC,GAEA,OAAIA,EAASC,gBACJC,EAAAC,EAAA,GACFH,GADE,CAELC,iBAAiB,EACjBG,YAAY,EACZC,eAA6B,IAAlBL,EAASM,KACpBC,OAAQ1D,EAAA2D,YAAYC,UAGjBT,GMljBF,SAAAU,EAAoBC,GACzB,OAAOA,EAAIC,QAAQD,EAAI,GAAIA,EAAI,GAAGE,eCK7B,SAAAC,EACLC,G,IAAA,IAAAC,EAAA,GAAAC,EAAA,EAAAA,EAAAC,UAAAC,OAAAF,IAAAD,EAAAC,EAAA,GAAAC,UAAAD,GAGAhC,OAAOmC,OAAAC,MAAPpC,OAAAqC,EAAA,CAAcP,GAAWC,KF8Lf5C,EAAAD,MAAA,KACV,MAAQ,QACRC,EAAA,SAAW,WG1Lb,IAAAmD,EAKO9E,EAAAC,QAAA,gBAIMH,EAAuCsB,SAkGvCvB,EAAmB,SAACmC,G,IAAA+C,OAAA,IAAA/C,EAMJ,GAAAA,EAL3BgD,EAAAD,EAAAE,aAAA,IAAAD,EAAQF,EAAAG,MAAAD,EACRE,EAAAH,EAAAI,mBAAA,IAAAD,EAAcJ,EAAAK,YAAAD,EACdE,EAAAL,EAAAM,mBAAA,IAAAD,EAAcN,EAAAO,YAAAD,EACdE,EAAAP,EAAAQ,gBAAA,IAAAD,EAAWR,EAAAS,SAAAD,EACXE,EAAAT,EAAAU,qCAAA,IAAAD,GAAgCA,EAC6B,OAC7DE,KAAM5F,EACN6F,KAAA,SAAKC,EAAK5D,EAAwB6D,G,IAC1BC,EAASF,EAOTb,ERwbH,SAA6D/C,G,IAClE4D,EAAA5D,EAAA4D,IACAb,EAAA/C,EAAA+D,cACEd,EAAAF,EAAAE,MACAE,EAAAJ,EAAAI,YACAE,EAAAN,EAAAM,YACAE,EAAAR,EAAAQ,SAGFS,EAAAhE,EAAAgE,mBACAH,EAAA7D,EAAA6D,QAOMI,EAVJlB,EAAAU,8BAa0C,SAACS,GAAO,OAAAA,KAAO/F,EAAAc,UAE3D,MAAO,CAAEkF,gBAsET,SAAyBT,GACvB,IAAMU,EAAkD,SACtDC,EACArE,G,IAAA+C,OAAA,IAAA/C,EAMI,GAAAA,EALFsE,EAAAvB,EAAAuB,mBACAC,EAAAxB,EAAAwB,eACAC,EAAAzB,EAAAyB,0BACAxB,EAAAD,EAAA0B,YAAA,IAAAzB,GAAOA,EACPE,EAAAH,EAAA2B,uBAAA,IAAAxB,EAAkB,EAAAA,EAGZyB,EAAaf,EAAIgB,UAAUlB,GAAAiB,SAI7BE,EAAW1B,IACX2B,EAAYvG,EAChBkG,EAAOrG,EAAA2G,UAAYV,EAMnBtE,EACA8D,EAAQmB,oBAAoBtB,GAC5BA,GAEIuB,EAA4B1F,EAAsB,CACtD+E,qBACAC,iBACAG,oBAGIQ,GAA4B,EAAA/G,EAAAa,SAAO,GAEnCmG,GAAa,EAAAhH,EAAAa,UAEfoE,EAA+B+B,EAAWjG,SAAW,GAAnDkG,EAAAhC,EAAAgC,cAAeC,EAAAjC,EAAAiC,UAIjBC,GAA+B,EACnC,GAAIF,GAAiBC,EAAW,CAE9B,IAAME,EAAgBV,EACpBjB,EAAI4B,gBAAgBC,2BAA2B,CAC7CL,gBACAC,eAaJC,IAAiCC,EAGnC,IAAMG,GACHJ,GAAgCJ,EAA0BhG,QA2D7D,OAzDA+E,GAA2B,WACzBiB,EAA0BhG,QAAUoG,KAGtCrB,GAA2B,WACrByB,IACFP,EAAWjG,aAAU,KAEtB,CAACwG,IAEJzB,GAA2B,WA3uBjC,IAAA0B,EA4uBcC,EAAcT,EAAWjG,QAS/B,GAAI4F,IAAc1G,EAAA2G,UAGhB,OAFA,MAAAa,KAAaC,mBACbV,EAAWjG,aAAU,GAIvB,IAAM4G,EAA0B,OAAAH,EAAAR,EAAWjG,cAAX,EAAAyG,EAAoBI,oBAEpD,GAAKH,GAAeA,EAAYvB,MAAQS,EAU7BG,IAA8Ba,GACvCF,EAAYI,0BAA0Bf,OAXW,CACjD,MAAAW,KAAaC,cACb,IAAMI,EAAUpB,EACdF,EAASG,EAAW,CAClBiB,oBAAqBd,EACrBiB,aAAc1B,KAIlBW,EAAWjG,QAAU+G,KAItB,CACDpB,EACAF,EACAH,EACAM,EACAG,EACAS,KAGF,EAAAvH,EAAAc,YAAU,WACR,OAAO,WApxBf,IAAA0G,EAqxBU,OAAAA,EAAAR,EAAWjG,UAAXyG,EAAoBE,cACpBV,EAAWjG,aAAU,KAEtB,KAEI,EAAAf,EAAAU,UACL,WAAO,OAILsH,QAAS,WA/xBnB,IAAAR,EAgyBY,IAAKR,EAAWjG,QACd,MAAM,IAAIkH,MACR,yDAEJ,OAAO,OAAAT,EAAAR,EAAWjG,cAAX,EAAAyG,EAAoBQ,cAG/B,KAIEE,EAA0D,SAACrG,G,IAAA+C,OAAA,IAAA/C,EAI7D,GAAAA,EAHFsE,EAAAvB,EAAAuB,mBACAC,EAAAxB,EAAAwB,eACAvB,EAAAD,EAAA2B,uBAAA,IAAA1B,EAAkB,EAAAA,EAEV2B,EAAaf,EAAIgB,UAAUlB,GAAAiB,SAI7BE,EAAW1B,IAEXD,GAAgB,EAAA/E,EAAAmI,UAAcnH,GAA7BkF,EAAAnB,EAAA,GAAKqD,EAAArD,EAAA,GACNiC,GAAa,EAAAhH,EAAAa,UAEbiG,EAA4B1F,EAAsB,CACtD+E,qBACAC,iBACAG,oBAGFT,GAA2B,WA/zBjC,IAAA0B,EAAAa,EAg0BcV,EAA0B,OAAAH,EAAAR,EAAWjG,cAAX,EAAAyG,EAAoBI,oBAEhDd,IAA8Ba,IAChC,OAAAU,EAAArB,EAAWjG,UAAXsH,EAAoBR,0BAClBf,MAGH,CAACA,IAEJ,IAAMwB,GAAyB,EAAAtI,EAAAa,QAAOiG,GACtChB,GAA2B,WACzBwC,EAAuBvH,QAAU+F,IAChC,CAACA,IAEJ,IAAMyB,GAAU,EAAAvI,EAAAwI,cACd,SAAUC,EAAUC,GAClB,IAAIZ,EAeJ,YAhBkB,IAAAY,OAAA,GAGlB5D,GAAM,WAl1BhB,IAAA0C,EAm1BY,OAAAA,EAAAR,EAAWjG,UAAXyG,EAAoBE,cAEpBV,EAAWjG,QAAU+G,EAAUpB,EAC7BF,EAASiC,EAAK,CACZb,oBAAqBU,EAAuBvH,QAC5CgH,cAAeW,KAInBN,EAAOK,MAGFX,IAET,CAACpB,EAAUF,IAiBb,OAbA,EAAAxG,EAAAc,YAAU,WACR,OAAO,WAt2Bf,IAAA0G,EAu2BU,OAAAA,EAAA,MAAAR,OAAA,EAAAA,EAAYjG,UAAZyG,EAAqBE,iBAEtB,KAGH,EAAA1H,EAAAc,YAAU,WACJoF,IAAQlF,GAAwBgG,EAAWjG,SAC7CwH,EAAQrC,GAAK,KAEd,CAACA,EAAKqC,KAEF,EAAAvI,EAAAU,UAAQ,WAAM,OAAC6H,EAASrC,KAAe,CAACqC,EAASrC,KAGpDyC,EAAoC,SACxCzC,EACArE,G,IAAA+C,OAAA,IAAA/C,EAAqC,GAAAA,EAAnCgD,EAAAD,EAAA0B,KAAcsC,EAAAhE,EAAAgE,iBAERC,EAAWpD,EAAIgB,UAAUlB,GAAAsD,OAI3BlC,EAAYvG,OANhB,IAAAyE,GAAOA,EAOA5E,EAAA2G,UAAYV,EACnBL,EACAH,EAAQmB,oBAAoBtB,GAC5BA,GAKIuD,GAAY,EAAA9I,EAAAa,UAEZkI,GAA0D,EAAA/I,EAAAU,UAC9D,WACE,SAAAX,EAAAiJ,gBACE,CACEH,EAAOlC,GACP,SAACsC,EAAiBC,GAAoB,OAAAA,GACtC,SAACD,GAAoB,OAAAtC,IAEvBwC,KAEJ,CAACN,EAAQlC,IAGLyC,GAAoD,EAAApJ,EAAAU,UACxD,WACE,OAAAkI,GACI,EAAA7I,EAAAiJ,gBAAe,CAACD,GAAsBH,GACtCG,IACN,CAACA,EAAqBH,IAGlBS,EAAenE,GACnB,SAACoE,GACC,OAAAF,EAAcE,EAAOR,EAAU/H,WACjCb,EAAAoB,cAGIiI,EAAQnE,IACRoE,EAAeT,EACnBQ,EAAME,WACNX,EAAU/H,SAMZ,OAJA6B,GAA0B,WACxBkG,EAAU/H,QAAUyI,IACnB,CAACA,IAEGH,GAGT,MAAO,CACLV,gBACA1C,uBACAiC,2BACAwB,aAAA,SAAaC,GACL,IAAA9H,EAAiBqG,EAAyByB,GAAzCpB,EAAA1G,EAAA,GAASqE,EAAArE,EAAA,GACV+H,EAAoBjB,EAAczC,EAAK5C,EAAAC,EAAA,GACxCoG,GADwC,CAE3CrD,KAAMJ,IAAQlF,KAGV6I,GAAO,EAAA7J,EAAAU,UAAQ,WAAO,OAAEoJ,QAAS5D,KAAQ,CAACA,IAChD,OAAO,EAAAlG,EAAAU,UACL,WAAM,OAAC6H,EAASqB,EAAmBC,KACnC,CAACtB,EAASqB,EAAmBC,KAGjCE,SAAA,SAAS7D,EAAKyD,GACZ,IAAMK,EAA2B/D,EAAqBC,EAAKyD,GACrDC,EAAoBjB,EAAczC,EAAK3C,EAAA,CAC3CqF,iBACE1C,IAAQjG,EAAA2G,YAAa,MAAA+C,OAAA,EAAAA,EAASrD,WAC1B,EACAnD,GACHwG,IAOL,OAFA,EAAA3J,EAAAiK,eAAc,CAAEvG,KADdkG,EAAAlG,KACoBC,OADpBiG,EAAAjG,OAC4BF,UAD5BmG,EAAAnG,UACuCyG,UADvCN,EAAAM,UACkDC,QADlDP,EAAAO,QAC2DC,MAD3DR,EAAAQ,SAGK,EAAApK,EAAAU,UACL,WAAO,OAAA6C,IAAA,GAAKqG,GAAsBI,KAClC,CAACJ,EAAmBI,OAvXFK,kBA6X1B,SAA2B9E,GACzB,OAAO,SAAC1D,G,IAAA+C,OAAA,IAAA/C,EAGJ,GAAAA,EAFFgD,EAAAD,EAAAgE,wBAAA,IAAA/D,EAAmB5B,EAAA4B,EACnByF,EAAA1F,EAAA0F,cAEMvF,EAAuBU,EAAIgB,UAAUlB,GAAnCsD,EAAA9D,EAAA8D,OAAQrC,EAAAzB,EAAAyB,SAIVE,EAAW1B,IACXC,GAAwB,EAAAjF,EAAAmI,YAAvBL,EAAA7C,EAAA,GAASsF,EAAAtF,EAAA,IAEhB,EAAAjF,EAAAc,YACE,WAAM,mBACC,MAAAgH,OAAA,EAAAA,EAAS5B,IAAIoE,gBAChB,MAAAxC,KAAS0C,WAGb,CAAC1C,IAGH,IAAM2C,GAAkB,EAAAzK,EAAAwI,cACtB,SAAUtC,GACR,IAAMwE,EAAUhE,EAASF,EAASN,EAAK,CAAEoE,mBAEzC,OADAC,EAAWG,GACJA,IAET,CAAChE,EAAUF,EAAU8D,IAGfpD,GAAcY,GAAW,IAAAZ,UAC3ByD,GAAmB,EAAA3K,EAAAU,UACvB,WACE,SAAAX,EAAAiJ,gBACE,CAACH,EAAO,CAAEyB,gBAAepD,UAAW,MAAAY,OAAA,EAAAA,EAASZ,aAC7C0B,KAEJ,CAACC,EAAQf,EAASc,EAAkB0B,IAGhCjB,EAAenE,EAAYyF,EAAkBzK,EAAAoB,cAC7CsJ,EACa,MAAjBN,EAAwB,MAAAxC,OAAA,EAAAA,EAAS5B,IAAI0E,kBAAe,EAChDJ,GAAQ,EAAAxK,EAAAwI,cAAY,WACxB1D,GAAM,WACAgD,GACFyC,OAAW,GAETD,GACF5D,EACEjB,EAAI4B,gBAAgBwD,qBAAqB,CACvC3D,YACAoD,wBAKP,CAAC5D,EAAU4D,EAAexC,EAASZ,KAWtC,EAAAlH,EAAAiK,eAAc,CACZzJ,aAFE6I,EAAA7I,aAGFkD,KAHE2F,EAAA3F,KAIFC,OAJE0F,EAAA1F,OAKFF,UALE4F,EAAA5F,UAMFyG,UANEb,EAAAa,UAOFC,QAPEd,EAAAc,QAQFC,MAREf,EAAAe,QAWJ,IAAMU,GAAa,EAAA9K,EAAAU,UACjB,WAAO,OAAA4C,EAAAC,EAAA,GAAK8F,GAAL,CAAmBuB,eAAcJ,YACxC,CAACnB,EAAcuB,EAAcJ,IAG/B,OAAO,EAAAxK,EAAAU,UACL,WAAM,OAAC+J,EAAiBK,KACxB,CAACL,EAAiBK,MAldqBC,YAmD7C,SACEvK,EACAwK,GAEA,IAAMtE,EAAW1B,IACXiG,EAAuB7J,EAAsB4J,GAEnD,OAAO,EAAAhL,EAAAwI,cACL,SAACtC,EAAUyD,GACT,OAAAjD,EACGjB,EAAIyF,KAAKC,SAAkC3K,EAAc0F,EAAK3C,IAAA,GAC1D0H,GACAtB,OAGT,CAACnJ,EAAckG,EAAUuE,MAhE7B,SAAA9B,EACEE,EACAH,EACA7I,GAKA,IAAI,MAAA6I,OAAA,EAAAA,EAAY1I,eAAgB6I,EAAahG,gBAAiB,CACpD,IAAA7C,EAAiB0I,EAAA1I,aACnBD,EAAqBmF,EAAQmB,oBAAoBrG,GAErDqF,EAAmB,CACjBxF,UAAW6I,EAAW0B,aACtBrK,qBACAC,mBAEFqF,EAAmB,CACjBxF,YACAE,qBACAC,mBAGF0I,OAAa,GAIjB,IAAIxF,EAAO2F,EAAaa,UAAYb,EAAa3F,KAAO,MAAAwF,OAAA,EAAAA,EAAYxF,UACvD,IAATA,IAAoBA,EAAO2F,EAAa3F,MAE5C,IAAM0H,OAAmB,IAAT1H,EAGVF,EAAa6F,EAAa5F,UAE1BA,GAAa2H,GAAW5H,EAExB0G,EAAYb,EAAaa,WAAc1G,GAAc4H,EAE3D,OAAO9H,EAAAC,EAAA,GACF8F,GADE,CAEL3F,OACA2H,YAAahC,EAAa3F,KAC1BF,aACAC,YACAyG,eQ7f0DoB,CAAW,CACrE7F,MACAG,cAAe,CACbd,QACAE,cACAE,cACAE,WACAE,iCAEFO,mBAjBQhE,EAAAgE,mBAkBRH,YAVMM,EAAApB,EAAAoB,gBAAiBqE,EAAAzF,EAAAyF,kBAezB,OAHAnG,EAAWyB,EAAQ,CAAEoF,YAZuBnG,EAAAmG,cAa5C7G,EAAWwB,EAAS,CAAEZ,UAEf,CACLyG,eAAA,SAAe/K,EAAcgL,GAC3B,GAAsBA,EHwenBC,OAASlK,EAAemK,MGxeQ,CAC3B,IAAA7J,EAMFmE,EAAgBxF,GALlBuJ,EAAAlI,EAAAkI,SACAL,EAAA7H,EAAA6H,aAKFxF,EAAWyB,EAAOc,UAAUjG,GAAe,CACzCuJ,WACAL,eACAxB,yBAPArG,EAAAqG,yBAQAS,cAPA9G,EAAA8G,cAQA1C,qBAPApE,EAAAoE,uBASAR,EAAY,MAAM3B,EAAWtD,GAAA,SAAwBuJ,EACrDtE,EAAY,UAAU3B,EAAWtD,GAAA,SACjCkJ,OAAA,GAC4B8B,EH4d7BC,OAASlK,EAAeoK,SG5dkB,CAC3C,IAAMC,EAAcvB,EAAkB7J,GACtC0D,EAAWyB,EAAOc,UAAUjG,GAAe,CACzCoL,gBAEAnG,EAAY,MAAM3B,EAAWtD,GAAA,YAA2BoL,QThLpEC,EAAAvM,QAAcO,EAAAC,QAAA,4BUHd,IAAAgM,EAA+BjM,EAAAC,QAAA,qBAE/BiM,EAA0BlM,EAAAC,QAAA,UAC1BkM,EAAkBnM,EAAAC,QAAA,UAElBmM,EAAyBpM,EAAAC,QAAA,gBACzBoM,EAA+BrM,EAAAC,QAAA,2BA2BxB,SAAAN,EAAuD2M,GAMrD,IAAA5C,EAASyC,EAAAI,QAAMjE,UAAS,W,MAC7B,SAAA2D,EAAAO,gBAAe,CACbC,SAAAzK,EAAA,GAASA,EACNsK,EAAM1G,IAAI8G,aAAcJ,EAAM1G,IAAI6G,Q,GAErCE,WAAY,SAACC,GAAQ,OAAAA,IAAMC,OAAOP,EAAM1G,IAAI+G,kBAAA,GAYhD,OARA,EAAAT,EAAAjL,YACE,WACE,OAAyB,IAAzBqL,EAAMQ,oBACF,GACA,EAAAT,EAAAS,gBAAepD,EAAM7C,SAAUyF,EAAMQ,kBAC3C,CAACR,EAAMQ,eAAgBpD,EAAM7C,WAI7BsF,EAAAI,QAAArJ,cAACkJ,EAAAW,SAAD,CAAUrD,QAAc7D,QAASyG,EAAMzG,SACpCyG,EAAMU,UVpDb,IAAMpN,GAA4B,EAAAG,EAAAkN,iBAChC,EAAAlN,EAAAmN,cACArN"}