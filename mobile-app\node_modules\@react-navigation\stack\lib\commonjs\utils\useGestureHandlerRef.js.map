{"version": 3, "names": ["useGestureHandlerRef", "ref", "React", "useContext", "StackGestureRefContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useGestureHandlerRef.tsx"], "mappings": ";;;;;;AAAA;AAEA;AAAgE;AAAA;AAAA;AAEjD,SAASA,oBAAoB,GAAG;EAC7C,MAAMC,GAAG,GAAGC,KAAK,CAACC,UAAU,CAACC,iCAAsB,CAAC;EAEpD,IAAIH,GAAG,KAAKI,SAAS,EAAE;IACrB,MAAM,IAAIC,KAAK,CACb,4EAA4E,CAC7E;EACH;EAEA,OAAOL,GAAG;AACZ"}