{"version": 3, "sources": ["../src/createTheming.js"], "names": ["createTheming", "defaultTheme", "ThemeContext", "React", "createContext", "ThemeProvider", "withTheme", "useTheme", "overrides", "theme", "useContext", "result", "useMemo"], "mappings": ";;;;;AAEA;;AACA;;AACA;;AACA;;;;;;AAWe,SAASA,aAAT,CACbC,YADa,EAEG;AAChB,MAAMC,YAA8B,GAAGC,KAAK,CAACC,aAAN,CAAoBH,YAApB,CAAvC;AAEA,MAAMI,aAAmC,GAAG,kCAC1CJ,YAD0C,EAE1CC,YAF0C,CAA5C;AAKA,MAAMI,SAA2B,GAAG,8BAClCD,aADkC,EAElCH,YAFkC,CAApC;;AAKA,MAAMK,QAAQ,GAAG,SAAXA,QAAW,CAACC,SAAD,EAAkC;AACjD,QAAMC,KAAK,GAAGN,KAAK,CAACO,UAAN,CAAiBR,YAAjB,CAAd;AACA,QAAMS,MAAM,GAAGR,KAAK,CAACS,OAAN,CACb;AAAA,aACEH,KAAK,IAAID,SAAT,GAAqB,wBAAUC,KAAV,EAAiBD,SAAjB,CAArB,GAAmDC,KAAK,IAAID,SAD9D;AAAA,KADa,EAGb,CAACC,KAAD,EAAQD,SAAR,CAHa,CAAf;AAMA,WAAOG,MAAP;AACD,GATD;;AAWA,SAAO;AACLT,IAAAA,YAAY,EAAZA,YADK;AAELG,IAAAA,aAAa,EAAbA,aAFK;AAGLC,IAAAA,SAAS,EAATA,SAHK;AAILC,IAAAA,QAAQ,EAARA;AAJK,GAAP;AAMD", "sourcesContent": ["/* @flow */\n\nimport * as React from 'react';\nimport deepmerge from 'deepmerge';\nimport createThemeProvider from './createThemeProvider';\nimport createWithTheme from './createWithTheme';\nimport type { WithThemeType } from './createWithTheme';\nimport type { ThemeProviderType } from './createThemeProvider';\nimport type { $DeepShape } from './types';\n\nexport type ThemingType<T> = {\n  ThemeProvider: ThemeProviderType<T>,\n  withTheme: WithThemeType<T>,\n  useTheme(overrides?: $DeepShape<T>): T,\n};\n\nexport default function createTheming<T: Object>(\n  defaultTheme: T\n): ThemingType<T> {\n  const ThemeContext: React.Context<T> = React.createContext(defaultTheme);\n\n  const ThemeProvider: ThemeProviderType<T> = createThemeProvider(\n    defaultTheme,\n    ThemeContext\n  );\n\n  const withTheme: WithThemeType<T> = createWithTheme(\n    ThemeProvider,\n    ThemeContext\n  );\n\n  const useTheme = (overrides?: $DeepShape<T>): T => {\n    const theme = React.useContext(ThemeContext);\n    const result = React.useMemo(\n      () =>\n        theme && overrides ? deepmerge(theme, overrides) : theme || overrides,\n      [theme, overrides]\n    );\n\n    return result;\n  };\n\n  return {\n    ThemeContext,\n    ThemeProvider,\n    withTheme,\n    useTheme,\n  };\n}\n"], "file": "createTheming.js"}