{"version": 3, "names": ["useTheme", "React", "Animated", "Platform", "StyleSheet", "HeaderBackground", "style", "rest", "colors", "styles", "container", "backgroundColor", "card", "borderBottomColor", "border", "shadowColor", "create", "flex", "select", "android", "elevation", "ios", "shadowOpacity", "shadowRadius", "shadowOffset", "width", "height", "hairlineWidth", "default", "borderBottomWidth"], "sourceRoot": "../../../src", "sources": ["Header/HeaderBackground.tsx"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EAERC,UAAU,QAGL,cAAc;AAOrB,eAAe,SAASC,gBAAgB,OAA4B;EAAA,IAA3B;IAAEC,KAAK;IAAE,GAAGC;EAAY,CAAC;EAChE,MAAM;IAAEC;EAAO,CAAC,GAAGR,QAAQ,EAAE;EAE7B,oBACE,oBAAC,QAAQ,CAAC,IAAI;IACZ,KAAK,EAAE,CACLS,MAAM,CAACC,SAAS,EAChB;MACEC,eAAe,EAAEH,MAAM,CAACI,IAAI;MAC5BC,iBAAiB,EAAEL,MAAM,CAACM,MAAM;MAChCC,WAAW,EAAEP,MAAM,CAACM;IACtB,CAAC,EACDR,KAAK;EACL,GACEC,IAAI,EACR;AAEN;AAEA,MAAME,MAAM,GAAGL,UAAU,CAACY,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,IAAI,EAAE,CAAC;IACP,GAAGd,QAAQ,CAACe,MAAM,CAAC;MACjBC,OAAO,EAAE;QACPC,SAAS,EAAE;MACb,CAAC;MACDC,GAAG,EAAE;QACHC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;UACZC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAEtB,UAAU,CAACuB;QACrB;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,iBAAiB,EAAEzB,UAAU,CAACuB;MAChC;IACF,CAAC;EACH;AACF,CAAC,CAAC"}