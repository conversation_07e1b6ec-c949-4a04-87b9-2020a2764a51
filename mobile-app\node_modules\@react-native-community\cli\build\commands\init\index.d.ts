declare const _default: {
    func: ([projectName]: string[], options: {
        template?: string | undefined;
        npm?: boolean | undefined;
        directory?: string | undefined;
        displayName?: string | undefined;
        title?: string | undefined;
        skipInstall?: boolean | undefined;
        version?: string | undefined;
        packageName?: string | undefined;
    }) => Promise<void>;
    detached: boolean;
    name: string;
    description: string;
    options: {
        name: string;
        description: string;
    }[];
};
export default _default;
//# sourceMappingURL=index.d.ts.map