{"version": 3, "names": ["useOnGetState", "getState", "getStateListeners", "addKeyedListener", "React", "useContext", "NavigationBuilderContext", "route", "NavigationRouteContext", "key", "getRehydratedState", "useCallback", "state", "routes", "map", "childState", "isArrayEqual", "useEffect"], "sourceRoot": "../../src", "sources": ["useOnGetState.tsx"], "mappings": ";;;;;;AACA;AAEA;AACA;AAGA;AAA8D;AAAA;AAAA;AAO/C,SAASA,aAAa,OAGzB;EAAA,IAH0B;IACpCC,QAAQ;IACRC;EACO,CAAC;EACR,MAAM;IAAEC;EAAiB,CAAC,GAAGC,KAAK,CAACC,UAAU,CAACC,iCAAwB,CAAC;EACvE,MAAMC,KAAK,GAAGH,KAAK,CAACC,UAAU,CAACG,+BAAsB,CAAC;EACtD,MAAMC,GAAG,GAAGF,KAAK,GAAGA,KAAK,CAACE,GAAG,GAAG,MAAM;EAEtC,MAAMC,kBAAkB,GAAGN,KAAK,CAACO,WAAW,CAAC,MAAM;IACjD,MAAMC,KAAK,GAAGX,QAAQ,EAAE;;IAExB;IACA,MAAMY,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACC,GAAG,CAAEP,KAAK,IAAK;MAAA;MACzC,MAAMQ,UAAU,4BAAGb,iBAAiB,CAACK,KAAK,CAACE,GAAG,CAAC,0DAA5B,2BAAAP,iBAAiB,CAAe;MAEnD,IAAIK,KAAK,CAACK,KAAK,KAAKG,UAAU,EAAE;QAC9B,OAAOR,KAAK;MACd;MAEA,OAAO;QAAE,GAAGA,KAAK;QAAEK,KAAK,EAAEG;MAAW,CAAC;IACxC,CAAC,CAAC;IAEF,IAAI,IAAAC,qBAAY,EAACJ,KAAK,CAACC,MAAM,EAAEA,MAAM,CAAC,EAAE;MACtC,OAAOD,KAAK;IACd;IAEA,OAAO;MAAE,GAAGA,KAAK;MAAEC;IAAO,CAAC;EAC7B,CAAC,EAAE,CAACZ,QAAQ,EAAEC,iBAAiB,CAAC,CAAC;EAEjCE,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,OAAOd,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAG,UAAU,EAAEM,GAAG,EAAEC,kBAAkB,CAAC;EAChE,CAAC,EAAE,CAACP,gBAAgB,EAAEO,kBAAkB,EAAED,GAAG,CAAC,CAAC;AACjD"}