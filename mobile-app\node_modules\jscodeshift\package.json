{"name": "jscodeshift", "version": "0.14.0", "description": "A toolkit for JavaScript codemods", "repository": {"type": "git", "url": "https://github.com/facebook/jscodeshift.git"}, "bugs": "https://github.com/facebook/jscodeshift/issues", "main": "index.js", "scripts": {"test": "jest --bail", "docs": "rm -rf docs && jsdoc -d docs -R README.md src/collections/* src/core.js src/Collection.js"}, "bin": {"jscodeshift": "./bin/jscodeshift.js"}, "keywords": ["codemod", "recast", "babel"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@babel/core": "^7.13.16", "@babel/parser": "^7.13.16", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.13.8", "@babel/plugin-proposal-optional-chaining": "^7.13.12", "@babel/plugin-transform-modules-commonjs": "^7.13.8", "@babel/preset-flow": "^7.13.13", "@babel/preset-typescript": "^7.13.0", "@babel/register": "^7.13.16", "babel-core": "^7.0.0-bridge.0", "chalk": "^4.1.2", "flow-parser": "0.*", "graceful-fs": "^4.2.4", "micromatch": "^4.0.4", "neo-async": "^2.5.0", "node-dir": "^0.1.17", "recast": "^0.21.0", "temp": "^0.8.4", "write-file-atomic": "^2.3.0"}, "peerDependencies": {"@babel/preset-env": "^7.1.6"}, "devDependencies": {"babel-eslint": "^10.0.1", "eslint": "^5.9.0", "jest": "^26", "jsdoc": "3.6.7", "mkdirp": "^0.5.1"}, "jest": {"roots": ["src", "bin", "parser", "sample"]}}