{"version": 3, "names": ["assign", "target", "sources", "for<PERSON>ach", "source", "descriptors", "Object", "keys", "reduce", "acc", "key", "propertyDescriptor", "getOwnPropertyDescriptor", "undefined", "getOwnPropertySymbols", "sym", "descriptor", "enumerable", "toString", "defineProperties"], "sources": ["../src/assign.ts"], "sourcesContent": ["/**\n * Source: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n *\n * Similar to Object.assign(), but it doesn't execute getters. This allows us to have\n * lazy properties on an object and still be able to merge them together\n *\n */\nexport default function assign(target: Object, ...sources: Object[]) {\n  sources.forEach((source) => {\n    let descriptors = Object.keys(source).reduce((acc, key) => {\n      const propertyDescriptor = Object.getOwnPropertyDescriptor(source, key);\n      if (propertyDescriptor !== undefined) {\n        acc[key] = propertyDescriptor;\n      }\n      return acc;\n    }, {} as PropertyDescriptorMap);\n    // by default, Object.assign copies enumerable Symbols too\n    Object.getOwnPropertySymbols(source).forEach((sym) => {\n      let descriptor = Object.getOwnPropertyDescriptor(source, sym);\n      if (descriptor && descriptor.enumerable) {\n        descriptors[sym.toString()] = descriptor;\n      }\n    });\n    Object.defineProperties(target, descriptors);\n  });\n  return target;\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,MAAM,CAACC,MAAc,EAAE,GAAGC,OAAiB,EAAE;EACnEA,OAAO,CAACC,OAAO,CAAEC,MAAM,IAAK;IAC1B,IAAIC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACzD,MAAMC,kBAAkB,GAAGL,MAAM,CAACM,wBAAwB,CAACR,MAAM,EAAEM,GAAG,CAAC;MACvE,IAAIC,kBAAkB,KAAKE,SAAS,EAAE;QACpCJ,GAAG,CAACC,GAAG,CAAC,GAAGC,kBAAkB;MAC/B;MACA,OAAOF,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAA0B;IAC/B;IACAH,MAAM,CAACQ,qBAAqB,CAACV,MAAM,CAAC,CAACD,OAAO,CAAEY,GAAG,IAAK;MACpD,IAAIC,UAAU,GAAGV,MAAM,CAACM,wBAAwB,CAACR,MAAM,EAAEW,GAAG,CAAC;MAC7D,IAAIC,UAAU,IAAIA,UAAU,CAACC,UAAU,EAAE;QACvCZ,WAAW,CAACU,GAAG,CAACG,QAAQ,EAAE,CAAC,GAAGF,UAAU;MAC1C;IACF,CAAC,CAAC;IACFV,MAAM,CAACa,gBAAgB,CAAClB,MAAM,EAAEI,WAAW,CAAC;EAC9C,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf"}