"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DefinitionType = void 0;
var DefinitionType;
(function (DefinitionType) {
    DefinitionType["CatchClause"] = "CatchClause";
    DefinitionType["ClassName"] = "ClassName";
    DefinitionType["FunctionName"] = "FunctionName";
    DefinitionType["ImplicitGlobalVariable"] = "ImplicitGlobalVariable";
    DefinitionType["ImportBinding"] = "ImportBinding";
    DefinitionType["Parameter"] = "Parameter";
    DefinitionType["TSEnumName"] = "TSEnumName";
    DefinitionType["TSEnumMember"] = "TSEnumMemberName";
    DefinitionType["TSModuleName"] = "TSModuleName";
    DefinitionType["Type"] = "Type";
    DefinitionType["Variable"] = "Variable";
})(DefinitionType || (exports.DefinitionType = DefinitionType = {}));
//# sourceMappingURL=DefinitionType.js.map