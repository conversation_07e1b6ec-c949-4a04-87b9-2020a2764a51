{"name": "es-iterator-helpers", "version": "1.2.1", "description": "An ESnext spec-compliant iterator helpers shim/polyfill/replacement that works as far down as ES3.", "main": "index.json", "exports": {".": "./index.json", "./auto": "./auto.js", "./shim": "./shim.js", "./Iterator": "./Iterator/index.js", "./Iterator/auto": "./Iterator/auto.js", "./Iterator/polyfill": "./Iterator/polyfill.js", "./Iterator/implementation": "./Iterator/implementation.js", "./Iterator/shim": "./Iterator/shim.js", "./Iterator.prototype": "./Iterator.prototype/index.js", "./Iterator.prototype/auto": "./Iterator.prototype/auto.js", "./Iterator.prototype/polyfill": "./Iterator.prototype/polyfill.js", "./Iterator.prototype/implementation": "./Iterator.prototype/implementation.js", "./Iterator.prototype/shim": "./Iterator.prototype/shim.js", "./Iterator.concat": "./Iterator.concat/index.js", "./Iterator.concat/auto": "./Iterator.concat/auto.js", "./Iterator.concat/polyfill": "./Iterator.concat/polyfill.js", "./Iterator.concat/implementation": "./Iterator.concat/implementation.js", "./Iterator.concat/shim": "./Iterator.concat/shim.js", "./Iterator.from": "./Iterator.from/index.js", "./Iterator.from/auto": "./Iterator.from/auto.js", "./Iterator.from/polyfill": "./Iterator.from/polyfill.js", "./Iterator.from/implementation": "./Iterator.from/implementation.js", "./Iterator.from/shim": "./Iterator.from/shim.js", "./Iterator.zip": "./Iterator.zip/index.js", "./Iterator.zip/auto": "./Iterator.zip/auto.js", "./Iterator.zip/polyfill": "./Iterator.zip/polyfill.js", "./Iterator.zip/implementation": "./Iterator.zip/implementation.js", "./Iterator.zip/shim": "./Iterator.zip/shim.js", "./Iterator.zipKeyed": "./Iterator.zipKeyed/index.js", "./Iterator.zipKeyed/auto": "./Iterator.zipKeyed/auto.js", "./Iterator.zipKeyed/polyfill": "./Iterator.zipKeyed/polyfill.js", "./Iterator.zipKeyed/implementation": "./Iterator.zipKeyed/implementation.js", "./Iterator.zipKeyed/shim": "./Iterator.zipKeyed/shim.js", "./Iterator.prototype.constructor": "./Iterator.prototype.constructor/index.js", "./Iterator.prototype.constructor/auto": "./Iterator.prototype.constructor/auto.js", "./Iterator.prototype.constructor/polyfill": "./Iterator.prototype.constructor/polyfill.js", "./Iterator.prototype.constructor/implementation": "./Iterator.prototype.constructor/implementation.js", "./Iterator.prototype.constructor/shim": "./Iterator.prototype.constructor/shim.js", "./Iterator.prototype.map": "./Iterator.prototype.map/index.js", "./Iterator.prototype.map/auto": "./Iterator.prototype.map/auto.js", "./Iterator.prototype.map/polyfill": "./Iterator.prototype.map/polyfill.js", "./Iterator.prototype.map/implementation": "./Iterator.prototype.map/implementation.js", "./Iterator.prototype.map/shim": "./Iterator.prototype.map/shim.js", "./Iterator.prototype.filter": "./Iterator.prototype.filter/index.js", "./Iterator.prototype.filter/auto": "./Iterator.prototype.filter/auto.js", "./Iterator.prototype.filter/polyfill": "./Iterator.prototype.filter/polyfill.js", "./Iterator.prototype.filter/implementation": "./Iterator.prototype.filter/implementation.js", "./Iterator.prototype.filter/shim": "./Iterator.prototype.filter/shim.js", "./Iterator.prototype.take": "./Iterator.prototype.take/index.js", "./Iterator.prototype.take/auto": "./Iterator.prototype.take/auto.js", "./Iterator.prototype.take/polyfill": "./Iterator.prototype.take/polyfill.js", "./Iterator.prototype.take/implementation": "./Iterator.prototype.take/implementation.js", "./Iterator.prototype.take/shim": "./Iterator.prototype.take/shim.js", "./Iterator.prototype.drop": "./Iterator.prototype.drop/index.js", "./Iterator.prototype.drop/auto": "./Iterator.prototype.drop/auto.js", "./Iterator.prototype.drop/polyfill": "./Iterator.prototype.drop/polyfill.js", "./Iterator.prototype.drop/implementation": "./Iterator.prototype.drop/implementation.js", "./Iterator.prototype.drop/shim": "./Iterator.prototype.drop/shim.js", "./Iterator.prototype.flatMap": "./Iterator.prototype.flatMap/index.js", "./Iterator.prototype.flatMap/auto": "./Iterator.prototype.flatMap/auto.js", "./Iterator.prototype.flatMap/polyfill": "./Iterator.prototype.flatMap/polyfill.js", "./Iterator.prototype.flatMap/implementation": "./Iterator.prototype.flatMap/implementation.js", "./Iterator.prototype.flatMap/shim": "./Iterator.prototype.flatMap/shim.js", "./Iterator.prototype.reduce": "./Iterator.prototype.reduce/index.js", "./Iterator.prototype.reduce/auto": "./Iterator.prototype.reduce/auto.js", "./Iterator.prototype.reduce/polyfill": "./Iterator.prototype.reduce/polyfill.js", "./Iterator.prototype.reduce/implementation": "./Iterator.prototype.reduce/implementation.js", "./Iterator.prototype.reduce/shim": "./Iterator.prototype.reduce/shim.js", "./Iterator.prototype.toArray": "./Iterator.prototype.toArray/index.js", "./Iterator.prototype.toArray/auto": "./Iterator.prototype.toArray/auto.js", "./Iterator.prototype.toArray/polyfill": "./Iterator.prototype.toArray/polyfill.js", "./Iterator.prototype.toArray/implementation": "./Iterator.prototype.toArray/implementation.js", "./Iterator.prototype.toArray/shim": "./Iterator.prototype.toArray/shim.js", "./Iterator.prototype.forEach": "./Iterator.prototype.forEach/index.js", "./Iterator.prototype.forEach/auto": "./Iterator.prototype.forEach/auto.js", "./Iterator.prototype.forEach/polyfill": "./Iterator.prototype.forEach/polyfill.js", "./Iterator.prototype.forEach/implementation": "./Iterator.prototype.forEach/implementation.js", "./Iterator.prototype.forEach/shim": "./Iterator.prototype.forEach/shim.js", "./Iterator.prototype.some": "./Iterator.prototype.some/index.js", "./Iterator.prototype.some/auto": "./Iterator.prototype.some/auto.js", "./Iterator.prototype.some/polyfill": "./Iterator.prototype.some/polyfill.js", "./Iterator.prototype.some/implementation": "./Iterator.prototype.some/implementation.js", "./Iterator.prototype.some/shim": "./Iterator.prototype.some/shim.js", "./Iterator.prototype.every": "./Iterator.prototype.every/index.js", "./Iterator.prototype.every/auto": "./Iterator.prototype.every/auto.js", "./Iterator.prototype.every/polyfill": "./Iterator.prototype.every/polyfill.js", "./Iterator.prototype.every/implementation": "./Iterator.prototype.every/implementation.js", "./Iterator.prototype.every/shim": "./Iterator.prototype.every/shim.js", "./Iterator.prototype.find": "./Iterator.prototype.find/index.js", "./Iterator.prototype.find/auto": "./Iterator.prototype.find/auto.js", "./Iterator.prototype.find/polyfill": "./Iterator.prototype.find/polyfill.js", "./Iterator.prototype.find/implementation": "./Iterator.prototype.find/implementation.js", "./Iterator.prototype.find/shim": "./Iterator.prototype.find/shim.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "es-shim-api --type=multi --ignore-dirs=WrapForValidIteratorPrototype,IteratorHelperPrototype", "lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/iterator-helpers.git"}, "keywords": ["javascript", "ecmascript", "polyfill", "shim", "es-shims", "es-shim API", "Iterator", "helpers", "from", "map", "filter", "take", "drop", "flatMap", "reduce", "toArray", "for<PERSON>ach", "some", "every", "find"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/es-shims/iterator-helpers/issues"}, "homepage": "https://github.com/es-shims/iterator-helpers#readme", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-set-tostringtag": "^2.0.3", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.6", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "iterator.prototype": "^1.1.4", "safe-array-concat": "^1.1.3"}, "devDependencies": {"@es-shims/api": "^3.0.2", "@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "eslint-plugin-import": "^2.31.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "iterate-iterator": "^1.0.2", "make-generator-function": "^2.0.0", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}}