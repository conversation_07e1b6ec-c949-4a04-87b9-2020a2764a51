{"version": 3, "file": "useDescriptors.d.ts", "sourceRoot": "", "sources": ["../../../src/useDescriptors.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,MAAM,EACP,MAAM,2BAA2B,CAAC;AAGnC,OAAiC,EAC/B,gBAAgB,EAChB,WAAW,EACZ,MAAM,4BAA4B,CAAC;AAIpC,OAAO,KAAK,EACV,UAAU,EACV,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,WAAW,EACX,SAAS,EACV,MAAM,SAAS,CAAC;AACjB,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAIhE,MAAM,MAAM,sBAAsB,CAChC,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,IAC3B;IACF,IAAI,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC;IAC7B,OAAO,EAAE,CAAC,uBAAuB,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC;IAC5E,KAAK,EAAE,WAAW,CAAC,aAAa,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;CAC3E,CAAC;AAEF,KAAK,uBAAuB,CAAC,aAAa,SAAS,EAAE,IACjD,aAAa,GACb,CAAC,CAAC,KAAK,EAAE;IACP,KAAK,EAAE,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IACxC,UAAU,EAAE,GAAG,CAAC;CACjB,KAAK,aAAa,CAAC,CAAC;AAEzB,KAAK,OAAO,CACV,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,IAC3B;IACF,KAAK,EAAE,KAAK,CAAC;IACb,OAAO,EAAE,MAAM,CACb,MAAM,EACN,sBAAsB,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,CAAC,CACvD,CAAC;IACF,UAAU,EAAE,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAC7C,aAAa,CAAC,EAAE,uBAAuB,CAAC,aAAa,CAAC,CAAC;IACvD,oBAAoB,CAAC,EACjB,aAAa,GACb,CAAC,CAAC,KAAK,EAAE;QACP,KAAK,EAAE,SAAS,CAAC,aAAa,CAAC,CAAC;QAChC,UAAU,EAAE,GAAG,CAAC;QAChB,OAAO,EAAE,aAAa,CAAC;KACxB,KAAK,aAAa,CAAC,CAAC;IACzB,QAAQ,EAAE,CAAC,MAAM,EAAE,gBAAgB,KAAK,OAAO,CAAC;IAChD,QAAQ,EAAE,MAAM,KAAK,CAAC;IACtB,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IACjC,WAAW,EAAE,WAAW,CAAC;IACzB,gBAAgB,EAAE,gBAAgB,CAAC;IACnC,YAAY,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;IACpC,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IACxC,OAAO,EAAE,sBAAsB,CAAC,QAAQ,CAAC,CAAC;CAC3C,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,CAAC,OAAO,UAAU,cAAc,CACpC,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,MAAM,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,EAChD,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,EAC7B,EACA,KAAK,EACL,OAAO,EACP,UAAU,EACV,aAAa,EACb,oBAAoB,EACpB,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,MAAM,EACN,OAAO,GACR,EAAE,OAAO,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mNAyIzC"}