{"version": 3, "names": ["_semver", "require", "_pretty", "_utils", "getInclusionReasons", "item", "targetVersions", "list", "minVersions", "Object", "keys", "reduce", "result", "env", "minVersion", "getLowestImplementedVersion", "targetVersion", "prettifyVersion", "minIsUnreleased", "isUnreleasedVersion", "targetIsUnreleased", "semver", "lt", "toString", "semverify"], "sources": ["../src/debug.ts"], "sourcesContent": ["import semver from \"semver\";\nimport { prettifyVersion } from \"./pretty.ts\";\nimport {\n  semverify,\n  isUnreleasedVersion,\n  getLowestImplementedVersion,\n} from \"./utils.ts\";\nimport type { Target, Targets } from \"./types.ts\";\n\nexport function getInclusionReasons(\n  item: string,\n  targetVersions: Targets,\n  list: { [key: string]: Targets },\n) {\n  const minVersions = list[item] || {};\n\n  return (Object.keys(targetVersions) as Target[]).reduce(\n    (result, env) => {\n      const minVersion = getLowestImplementedVersion(minVersions, env);\n      const targetVersion = targetVersions[env];\n\n      if (!minVersion) {\n        result[env] = prettifyVersion(targetVersion);\n      } else {\n        const minIsUnreleased = isUnreleasedVersion(minVersion, env);\n        const targetIsUnreleased = isUnreleasedVersion(targetVersion, env);\n\n        if (\n          !targetIsUnreleased &&\n          (minIsUnreleased ||\n            semver.lt(targetVersion.toString(), semverify(minVersion)))\n        ) {\n          result[env] = prettifyVersion(targetVersion);\n        }\n      }\n\n      return result;\n    },\n    {} as Partial<Record<Target, string>>,\n  );\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAOO,SAASG,mBAAmBA,CACjCC,IAAY,EACZC,cAAuB,EACvBC,IAAgC,EAChC;EACA,MAAMC,WAAW,GAAGD,IAAI,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;EAEpC,OAAQI,MAAM,CAACC,IAAI,CAACJ,cAAc,CAAC,CAAcK,MAAM,CACrD,CAACC,MAAM,EAAEC,GAAG,KAAK;IACf,MAAMC,UAAU,GAAG,IAAAC,kCAA2B,EAACP,WAAW,EAAEK,GAAG,CAAC;IAChE,MAAMG,aAAa,GAAGV,cAAc,CAACO,GAAG,CAAC;IAEzC,IAAI,CAACC,UAAU,EAAE;MACfF,MAAM,CAACC,GAAG,CAAC,GAAG,IAAAI,uBAAe,EAACD,aAAa,CAAC;IAC9C,CAAC,MAAM;MACL,MAAME,eAAe,GAAG,IAAAC,0BAAmB,EAACL,UAAU,EAAED,GAAG,CAAC;MAC5D,MAAMO,kBAAkB,GAAG,IAAAD,0BAAmB,EAACH,aAAa,EAAEH,GAAG,CAAC;MAElE,IACE,CAACO,kBAAkB,KAClBF,eAAe,IACdG,OAAM,CAACC,EAAE,CAACN,aAAa,CAACO,QAAQ,CAAC,CAAC,EAAE,IAAAC,gBAAS,EAACV,UAAU,CAAC,CAAC,CAAC,EAC7D;QACAF,MAAM,CAACC,GAAG,CAAC,GAAG,IAAAI,uBAAe,EAACD,aAAa,CAAC;MAC9C;IACF;IAEA,OAAOJ,MAAM;EACf,CAAC,EACD,CAAC,CACH,CAAC;AACH", "ignoreList": []}