"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e,r,t=require("fs"),n=require("util"),o=(e=require("path"))&&"object"==typeof e&&"default"in e?e.default:e,a=require("source-map");function i(){return(i=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function s(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function u(e,r){var t;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return s(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?s(e,void 0):void 0}}(e))||r&&e&&"number"==typeof e.length){t&&(e=t);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}return(t=e[Symbol.iterator]()).next.bind(t)}!function(e){e.DURATION_EVENTS_BEGIN="B",e.DURATION_EVENTS_END="E",e.COMPLETE_EVENTS="X",e.INSTANT_EVENTS="I",e.COUNTER_EVENTS="C",e.ASYNC_EVENTS_NESTABLE_START="b",e.ASYNC_EVENTS_NESTABLE_INSTANT="n",e.ASYNC_EVENTS_NESTABLE_END="e",e.FLOW_EVENTS_START="s",e.FLOW_EVENTS_STEP="t",e.FLOW_EVENTS_END="f",e.SAMPLE_EVENTS="P",e.OBJECT_EVENTS_CREATED="N",e.OBJECT_EVENTS_SNAPSHOT="O",e.OBJECT_EVENTS_DESTROYED="D",e.METADATA_EVENTS="M",e.MEMORY_DUMP_EVENTS_GLOBAL="V",e.MEMORY_DUMP_EVENTS_PROCESS="v",e.MARK_EVENTS="R",e.CLOCK_SYNC_EVENTS="c",e.CONTEXT_EVENTS_ENTER="(",e.CONTEXT_EVENTS_LEAVE=")",e.ASYNC_EVENTS_START="S",e.ASYNC_EVENTS_STEP_INTO="T",e.ASYNC_EVENTS_STEP_PAST="p",e.ASYNC_EVENTS_END="F",e.LINKED_ID_EVENTS="="}(r||(r={}));var c=function(){function e(e){this._profile=e,this._nodesById=this._createNodeMap(),this._activeNodeArraysById=this._createActiveNodeArrays()}var t=e.prototype;return t._createNodeMap=function(){for(var e,r=new Map,t=u(this._profile.nodes);!(e=t()).done;){var n=e.value;r.set(n.id,n)}return r},t._createActiveNodeArrays=function(){for(var e,r=this,t=new Map,n=function e(n){if(t.has(n))return t.get(n)||[];var o=r._nodesById.get(n);if(!o)throw new Error("No such node "+n);if(o.parent){var a=e(o.parent).concat([n]);return t.set(n,a),a}return[n]},o=u(this._profile.nodes);!(e=o()).done;){var a=e.value;t.set(a.id,n(a.id))}return t},t._getActiveNodeIds=function(e){var r=this._activeNodeArraysById.get(e);if(!r)throw new Error("No such node ID "+e);return r},t._createStartEndEventsForTransition=function(e,t,n){var o=this,a=n.filter((function(e){return!t.includes(e)})).map((function(e){return o._nodesById.get(e)})),s=t.filter((function(e){return!n.includes(e)})).map((function(e){return o._nodesById.get(e)})),u=function(t){return{ts:e,pid:o._profile.pid,tid:Number(o._profile.tid),ph:r.DURATION_EVENTS_BEGIN,name:(n=t.callFrame.name,n.includes("http://")&&(n=n.substring(0,n.lastIndexOf("("))),n||"anonymous"),cat:t.callFrame.category,args:i({},t.callFrame)};var n},c=a.map(u).map((function(e){return i({},e,{ph:r.DURATION_EVENTS_BEGIN})})),l=s.map(u).map((function(e){return i({},e,{ph:r.DURATION_EVENTS_END})}));return[].concat(l.reverse(),c)},t.createStartEndEvents=function(){var e=this._profile,r=e.samples.length;if(e.timeDeltas.length!==r||e.samples.length!==r)throw new Error("Invalid CPU profile length");for(var t=[],n=e.startTime,o=[],a=0;a<e.samples.length;a++){var i=e.samples[a],s=Math.max(e.timeDeltas[a],0);if(!this._nodesById.get(i))throw new Error("Missing node "+i);n+=s;var u=this._getActiveNodeIds(i);t.push.apply(t,this._createStartEndEventsForTransition(n,o,u)),o=u}return t.push.apply(t,this._createStartEndEventsForTransition(n,o,[])),t},e.createStartEndEvents=function(r){return new e(r).createStartEndEvents()},e.collectProfileEvents=function(e){if(e.samples.length>=0){var r=e.samples,t=e.stackFrames,n=r[0].pid,o=r[0].tid,a=Number(r[0].ts),i=this.constructNodes(r,t);return{id:"0x1",pid:n,tid:o,startTime:a,nodes:i.nodes,samples:i.sampleNumbers,timeDeltas:i.timeDeltas}}throw new Error("The hermes profile has zero samples")},e.constructNodes=function(e,r){e=e.map((function(e){return e.stackFrameData=r[e.sf],e}));var t=Object.keys(r).map((function(e){var t=r[e];return{id:Number(e),callFrame:i({},t,{url:t.name}),parent:r[e].parent}})),n=[],o=[],a=Number(e[0].ts);return e.forEach((function(e,r){if(n.push(e.sf),0===r)o.push(0);else{var t=Number(e.ts)-a;a=Number(e.ts),o.push(t)}})),{nodes:t,sampleNumbers:n,timeDeltas:o}},e}();"undefined"!=typeof Symbol&&(Symbol.iterator||(Symbol.iterator=Symbol("Symbol.iterator"))),"undefined"!=typeof Symbol&&(Symbol.asyncIterator||(Symbol.asyncIterator=Symbol("Symbol.asyncIterator")));var l=function(e){try{return Promise.resolve(function(r,o){try{var a=function(){var r=n.promisify(t.readFile);return Promise.resolve(r(e,"utf-8")).then((function(r){if(0===r.length)throw new Error(e+" is an empty file");return JSON.parse(r)}))}()}catch(e){return o(e)}return a&&a.then?a.then(void 0,o):a}(0,(function(e){throw e})))}catch(e){return Promise.reject(e)}},E=function(e,r,t){try{var n={version:Number(e.version),file:t||"index.bundle",sources:e.sources,mappings:e.mappings,names:e.names};return Promise.resolve(new a.SourceMapConsumer(n)).then((function(e){var t=r.map((function(r){if(!r.args)throw new Error("Source maps could not be derived for an event at "+r.ts+" and with stackFrame ID "+r.sf);var t,n,a=e.originalPositionFor({line:Number(r.args.line),column:Number(r.args.column)});return r.cat=(t=r.cat,(n=a.source)?function(e){var r=e.substring(e.lastIndexOf(o.sep+"node_modules"+o.sep)).split(o.sep);return r.length>2&&"node_modules"===r[1]?r[2]:t}(n):t),r.args=i({},r.args,{url:a.source,line:a.line,column:a.column,params:a.name,allocatedCategory:r.cat,allocatedName:r.name}),r}));return e.destroy(),t}))}catch(e){return Promise.reject(e)}};exports.default=function(e,r,t){try{return Promise.resolve(l(e)).then((function(e){var n=!1,o=c.collectProfileEvents(e),a=new c(o).createStartEndEvents(),i=function(){if(r)return Promise.resolve(l(r)).then((function(e){var r=E(e,a,t);return n=!0,r}))}();return i&&i.then?i.then((function(e){return n?e:a})):n?i:a}))}catch(e){return Promise.reject(e)}};
//# sourceMappingURL=hermes-profile-transformer.cjs.production.min.js.map
