{"version": 3, "names": ["label", "description", "getDiagnostics", "adbPath", "getAdbPath", "devices", "adb", "getDevices", "length", "adbArgs", "reverseList", "execFileSync", "encoding", "needsToBeFixed", "runAutomaticFix", "loader", "logManualInstallation", "fail", "hash", "link", "getOS", "device", "listAndroidDevices", "connected", "tryRunAdbReverse", "process", "env", "RCT_METRO_PORT", "deviceId", "succeed", "e", "healthcheck", "url", "docs", "guide", "platform"], "sources": ["../../../src/tools/healthchecks/adb.ts"], "sourcesContent": ["import {HealthCheckInterface} from '../../types';\nimport {\n  adb,\n  getAdbPath,\n  listAndroidDevices,\n  tryRunAdbReverse,\n} from '@react-native-community/cli-platform-android';\nimport {execFileSync} from 'child_process';\nimport {link} from '@react-native-community/cli-tools';\n\nexport default {\n  label: 'Adb',\n  description: 'Required to verify if the android device is attached correctly',\n  getDiagnostics: async () => {\n    const adbPath = getAdbPath();\n    const devices = adb.getDevices(adbPath);\n\n    if (devices.length > 0) {\n      const adbArgs = ['reverse', '--list'];\n      const reverseList = execFileSync(adbPath, adbArgs, {encoding: 'utf8'});\n      if (reverseList.length > 0) {\n        return {\n          needsToBeFixed: false,\n        };\n      } else {\n        return {\n          description:\n            'The reverse proxy for the Android device has not been set.',\n          needsToBeFixed: true,\n        };\n      }\n    } else {\n      return {\n        description:\n          'No devices and/or emulators connected. Please create emulator with Android Studio or connect Android device.',\n        needsToBeFixed: true,\n      };\n    }\n  },\n  runAutomaticFix: async ({loader, logManualInstallation}) => {\n    loader.fail();\n    let hash: string;\n    switch (link.getOS()) {\n      case 'macos':\n        hash = 'method-1-using-adb-reverse-recommended';\n        break;\n      case 'windows':\n        hash = 'method-1-using-adb-reverse-recommended-1';\n        break;\n      case 'linux':\n        hash = 'method-1-using-adb-reverse-recommended-2';\n        break;\n      default:\n        hash = '';\n        break;\n    }\n    try {\n      const device = await listAndroidDevices();\n      if (device && device.connected) {\n        tryRunAdbReverse(process.env.RCT_METRO_PORT || 8081, device.deviceId);\n      }\n      return loader.succeed();\n    } catch (e) {\n      return logManualInstallation({\n        healthcheck: 'Adb',\n        url: link.docs('running-on-device', {\n          hash: hash,\n          guide: 'native',\n          platform: 'android',\n        }),\n      });\n    }\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAuD,eAExC;EACbA,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE,gEAAgE;EAC7EC,cAAc,EAAE,YAAY;IAC1B,MAAMC,OAAO,GAAG,IAAAC,gCAAU,GAAE;IAC5B,MAAMC,OAAO,GAAGC,yBAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;IAEvC,IAAIE,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,OAAO,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC;MACrC,MAAMC,WAAW,GAAG,IAAAC,6BAAY,EAACR,OAAO,EAAEM,OAAO,EAAE;QAACG,QAAQ,EAAE;MAAM,CAAC,CAAC;MACtE,IAAIF,WAAW,CAACF,MAAM,GAAG,CAAC,EAAE;QAC1B,OAAO;UACLK,cAAc,EAAE;QAClB,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLZ,WAAW,EACT,4DAA4D;UAC9DY,cAAc,EAAE;QAClB,CAAC;MACH;IACF,CAAC,MAAM;MACL,OAAO;QACLZ,WAAW,EACT,8GAA8G;QAChHY,cAAc,EAAE;MAClB,CAAC;IACH;EACF,CAAC;EACDC,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEC;EAAqB,CAAC,KAAK;IAC1DD,MAAM,CAACE,IAAI,EAAE;IACb,IAAIC,IAAY;IAChB,QAAQC,gBAAI,CAACC,KAAK,EAAE;MAClB,KAAK,OAAO;QACVF,IAAI,GAAG,wCAAwC;QAC/C;MACF,KAAK,SAAS;QACZA,IAAI,GAAG,0CAA0C;QACjD;MACF,KAAK,OAAO;QACVA,IAAI,GAAG,0CAA0C;QACjD;MACF;QACEA,IAAI,GAAG,EAAE;QACT;IAAM;IAEV,IAAI;MACF,MAAMG,MAAM,GAAG,MAAM,IAAAC,wCAAkB,GAAE;MACzC,IAAID,MAAM,IAAIA,MAAM,CAACE,SAAS,EAAE;QAC9B,IAAAC,sCAAgB,EAACC,OAAO,CAACC,GAAG,CAACC,cAAc,IAAI,IAAI,EAAEN,MAAM,CAACO,QAAQ,CAAC;MACvE;MACA,OAAOb,MAAM,CAACc,OAAO,EAAE;IACzB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAOd,qBAAqB,CAAC;QAC3Be,WAAW,EAAE,KAAK;QAClBC,GAAG,EAAEb,gBAAI,CAACc,IAAI,CAAC,mBAAmB,EAAE;UAClCf,IAAI,EAAEA,IAAI;UACVgB,KAAK,EAAE,QAAQ;UACfC,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC,CAAC;IACJ;EACF;AACF,CAAC;AAAA"}