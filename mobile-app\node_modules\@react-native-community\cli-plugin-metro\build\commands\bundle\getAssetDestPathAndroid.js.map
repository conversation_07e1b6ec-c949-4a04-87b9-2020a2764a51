{"version": 3, "names": ["getAssetDestPathAndroid", "asset", "scale", "androidFolder", "assetPathUtils", "getAndroidResourceFolderName", "fileName", "getResourceIdentifier", "path", "join", "type"], "sources": ["../../../src/commands/bundle/getAssetDestPathAndroid.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport assetPathUtils, {PackagerAsset} from './assetPathUtils';\n\nfunction getAssetDestPathAndroid(asset: PackagerAsset, scale: number): string {\n  const androidFolder = assetPathUtils.getAndroidResourceFolderName(\n    asset,\n    scale,\n  );\n  const fileName = assetPathUtils.getResourceIdentifier(asset);\n  return path.join(androidFolder, `${fileName}.${asset.type}`);\n}\n\nexport default getAssetDestPathAndroid;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA+D;AAT/D;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,SAASA,uBAAuB,CAACC,KAAoB,EAAEC,KAAa,EAAU;EAC5E,MAAMC,aAAa,GAAGC,uBAAc,CAACC,4BAA4B,CAC/DJ,KAAK,EACLC,KAAK,CACN;EACD,MAAMI,QAAQ,GAAGF,uBAAc,CAACG,qBAAqB,CAACN,KAAK,CAAC;EAC5D,OAAOO,eAAI,CAACC,IAAI,CAACN,aAAa,EAAG,GAAEG,QAAS,IAAGL,KAAK,CAACS,IAAK,EAAC,CAAC;AAC9D;AAAC,eAEcV,uBAAuB;AAAA"}