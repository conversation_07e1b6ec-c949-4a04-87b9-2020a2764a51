"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true,
});
exports.Watcher = void 0;
var _watchman = _interopRequireDefault(require("./crawlers/watchman"));
var _node = _interopRequireDefault(require("./crawlers/node"));
var _WatchmanWatcher = _interopRequireDefault(
  require("./watchers/WatchmanWatcher")
);
var _FSEventsWatcher = _interopRequireDefault(
  require("./watchers/FSEventsWatcher")
);
var _NodeWatcher = _interopRequireDefault(require("./watchers/NodeWatcher"));
var path = _interopRequireWildcard(require("path"));
var fs = _interopRequireWildcard(require("fs"));
var _common = require("./watchers/common");
var _events = _interopRequireDefault(require("events"));
var _perf_hooks = require("perf_hooks");
var _nullthrows = _interopRequireDefault(require("nullthrows"));
function _getRequireWildcardCache(nodeInterop) {
  if (typeof WeakMap !== "function") return null;
  var cacheBabelInterop = new WeakMap();
  var cacheNodeInterop = new WeakMap();
  return (_getRequireWildcardCache = function (nodeInterop) {
    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
  })(nodeInterop);
}
function _interopRequireWildcard(obj, nodeInterop) {
  if (!nodeInterop && obj && obj.__esModule) {
    return obj;
  }
  if (obj === null || (typeof obj !== "object" && typeof obj !== "function")) {
    return { default: obj };
  }
  var cache = _getRequireWildcardCache(nodeInterop);
  if (cache && cache.has(obj)) {
    return cache.get(obj);
  }
  var newObj = {};
  var hasPropertyDescriptor =
    Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var key in obj) {
    if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
      var desc = hasPropertyDescriptor
        ? Object.getOwnPropertyDescriptor(obj, key)
        : null;
      if (desc && (desc.get || desc.set)) {
        Object.defineProperty(newObj, key, desc);
      } else {
        newObj[key] = obj[key];
      }
    }
  }
  newObj.default = obj;
  if (cache) {
    cache.set(obj, newObj);
  }
  return newObj;
}
function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : { default: obj };
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 *
 */

const debug = require("debug")("Metro:Watcher");
const MAX_WAIT_TIME = 240000;
let nextInstanceId = 0;
class Watcher extends _events.default {
  _backends = [];
  _nextHealthCheckId = 0;
  _pendingHealthChecks = new Map();
  constructor(options) {
    super();
    this._options = options;
    this._instanceId = nextInstanceId++;
  }
  async crawl() {
    this._options.perfLogger?.point("crawl_start");
    const options = this._options;
    const ignore = (filePath) =>
      options.ignore(filePath) ||
      path.basename(filePath).startsWith(this._options.healthCheckFilePrefix);
    const crawl = options.useWatchman ? _watchman.default : _node.default;
    let crawler = crawl === _watchman.default ? "watchman" : "node";
    options.abortSignal.throwIfAborted();
    const crawlerOptions = {
      abortSignal: options.abortSignal,
      computeSha1: options.computeSha1,
      includeSymlinks: options.enableSymlinks,
      extensions: options.extensions,
      forceNodeFilesystemAPI: options.forceNodeFilesystemAPI,
      ignore,
      onStatus: (status) => {
        this.emit("status", status);
      },
      perfLogger: options.perfLogger,
      previousState: options.previousState,
      rootDir: options.rootDir,
      roots: options.roots,
    };
    const retry = (error) => {
      if (crawl === _watchman.default) {
        crawler = "node";
        options.console.warn(
          "metro-file-map: Watchman crawl failed. Retrying once with node " +
            "crawler.\n" +
            "  Usually this happens when watchman isn't running. Create an " +
            "empty `.watchmanconfig` file in your project's root folder or " +
            "initialize a git or hg repository in your project.\n" +
            "  " +
            error.toString()
        );
        // $FlowFixMe[prop-missing] Found when updating Promise type definition
        return (0, _node.default)(crawlerOptions).catch((e) => {
          throw new Error(
            "Crawler retry failed:\n" +
              `  Original error: ${error.message}\n` +
              `  Retry error: ${e.message}\n`
          );
        });
      }
      throw error;
    };
    const logEnd = (delta) => {
      debug(
        'Crawler "%s" returned %d added/modified, %d removed, %d clock(s).',
        crawler,
        delta.changedFiles.size,
        delta.removedFiles.size,
        delta.clocks?.size ?? 0
      );
      this._options.perfLogger?.point("crawl_end");
      return delta;
    };
    debug('Beginning crawl with "%s".', crawler);
    try {
      // $FlowFixMe[incompatible-call] Found when updating Promise type definition
      return crawl(crawlerOptions).catch(retry).then(logEnd);
    } catch (error) {
      return retry(error).then(logEnd);
    }
  }
  async watch(onChange) {
    const { extensions, ignorePattern, useWatchman } = this._options;

    // WatchmanWatcher > FSEventsWatcher > sane.NodeWatcher
    const WatcherImpl = useWatchman
      ? _WatchmanWatcher.default
      : _FSEventsWatcher.default.isSupported()
      ? _FSEventsWatcher.default
      : _NodeWatcher.default;
    let watcher = "node";
    if (WatcherImpl === _WatchmanWatcher.default) {
      watcher = "watchman";
    } else if (WatcherImpl === _FSEventsWatcher.default) {
      watcher = "fsevents";
    }
    debug(`Using watcher: ${watcher}`);
    this._options.perfLogger?.annotate({
      string: {
        watcher,
      },
    });
    this._activeWatcher = watcher;
    const createWatcherBackend = (root) => {
      const watcherOptions = {
        dot: true,
        glob: [
          // Ensure we always include package.json files, which are crucial for
          /// module resolution.
          "**/package.json",
          // Ensure we always watch any health check files
          "**/" + this._options.healthCheckFilePrefix + "*",
          ...extensions.map((extension) => "**/*." + extension),
        ],
        ignored: ignorePattern,
        watchmanDeferStates: this._options.watchmanDeferStates,
      };
      const watcher = new WatcherImpl(root, watcherOptions);
      return new Promise((resolve, reject) => {
        const rejectTimeout = setTimeout(
          () => reject(new Error("Failed to start watch mode.")),
          MAX_WAIT_TIME
        );
        watcher.once("ready", () => {
          clearTimeout(rejectTimeout);
          watcher.on("all", (type, filePath, root, metadata) => {
            const basename = path.basename(filePath);
            if (basename.startsWith(this._options.healthCheckFilePrefix)) {
              if (type === _common.ADD_EVENT || type === _common.CHANGE_EVENT) {
                debug(
                  "Observed possible health check cookie: %s in %s",
                  filePath,
                  root
                );
                this._handleHealthCheckObservation(basename);
              }
              return;
            }
            onChange(type, filePath, root, metadata);
          });
          resolve(watcher);
        });
      });
    };
    this._backends = await Promise.all(
      this._options.roots.map(createWatcherBackend)
    );
  }
  _handleHealthCheckObservation(basename) {
    const resolveHealthCheck = this._pendingHealthChecks.get(basename);
    if (!resolveHealthCheck) {
      return;
    }
    resolveHealthCheck();
  }
  async close() {
    await Promise.all(this._backends.map((watcher) => watcher.close()));
    this._activeWatcher = null;
  }
  async checkHealth(timeout) {
    const healthCheckId = this._nextHealthCheckId++;
    if (healthCheckId === Number.MAX_SAFE_INTEGER) {
      this._nextHealthCheckId = 0;
    }
    const watcher = this._activeWatcher;
    const basename =
      this._options.healthCheckFilePrefix +
      "-" +
      process.pid +
      "-" +
      this._instanceId +
      "-" +
      healthCheckId;
    const healthCheckPath = path.join(this._options.rootDir, basename);
    let result;
    const timeoutPromise = new Promise((resolve) =>
      setTimeout(resolve, timeout)
    ).then(() => {
      if (!result) {
        result = {
          type: "timeout",
          pauseReason: this._backends[0]?.getPauseReason(),
          timeout,
          watcher,
        };
      }
    });
    const startTime = _perf_hooks.performance.now();
    debug("Creating health check cookie: %s", healthCheckPath);
    const creationPromise = fs.promises
      .writeFile(healthCheckPath, String(startTime))
      .catch((error) => {
        if (!result) {
          result = {
            type: "error",
            error,
            timeout,
            watcher,
          };
        }
      });
    const observationPromise = new Promise((resolve) => {
      this._pendingHealthChecks.set(basename, resolve);
    }).then(() => {
      if (!result) {
        result = {
          type: "success",
          timeElapsed: _perf_hooks.performance.now() - startTime,
          timeout,
          watcher,
        };
      }
    });
    await Promise.race([
      timeoutPromise,
      creationPromise.then(() => observationPromise),
    ]);
    this._pendingHealthChecks.delete(basename);
    // Chain a deletion to the creation promise (which may not have even settled yet!),
    // don't await it, and swallow errors. This is just best-effort cleanup.
    // $FlowFixMe[unused-promise]
    creationPromise.then(() =>
      fs.promises.unlink(healthCheckPath).catch(() => {})
    );
    debug("Health check result: %o", result);
    return (0, _nullthrows.default)(result);
  }
}
exports.Watcher = Watcher;
