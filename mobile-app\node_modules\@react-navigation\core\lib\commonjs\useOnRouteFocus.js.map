{"version": 3, "names": ["useOnRouteFocus", "router", "getState", "key", "sourceRouteKey", "setState", "onRouteFocus", "onRouteFocusParent", "React", "useContext", "NavigationBuilderContext", "useCallback", "state", "result", "getStateForRouteFocus", "undefined"], "sourceRoot": "../../src", "sources": ["useOnRouteFocus.tsx"], "mappings": ";;;;;;AAKA;AAEA;AAAkE;AAAA;AAAA;AASlE;AACA;AACA;AACA;AACA;AACe,SAASA,eAAe,OAKnB;EAAA,IALqD;IACvEC,MAAM;IACNC,QAAQ;IACRC,GAAG,EAAEC,cAAc;IACnBC;EACe,CAAC;EAChB,MAAM;IAAEC,YAAY,EAAEC;EAAmB,CAAC,GAAGC,KAAK,CAACC,UAAU,CAC3DC,iCAAwB,CACzB;EAED,OAAOF,KAAK,CAACG,WAAW,CACrBR,GAAW,IAAK;IACf,MAAMS,KAAK,GAAGV,QAAQ,EAAE;IACxB,MAAMW,MAAM,GAAGZ,MAAM,CAACa,qBAAqB,CAACF,KAAK,EAAET,GAAG,CAAC;IAEvD,IAAIU,MAAM,KAAKD,KAAK,EAAE;MACpBP,QAAQ,CAACQ,MAAM,CAAC;IAClB;IAEA,IAAIN,kBAAkB,KAAKQ,SAAS,IAAIX,cAAc,KAAKW,SAAS,EAAE;MACpER,kBAAkB,CAACH,cAAc,CAAC;IACpC;EACF,CAAC,EACD,CAACF,QAAQ,EAAEK,kBAAkB,EAAEN,MAAM,EAAEI,QAAQ,EAAED,cAAc,CAAC,CACjE;AACH"}