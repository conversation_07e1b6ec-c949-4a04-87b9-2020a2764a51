"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.decorators_legacy = void 0;
const base_config_1 = require("./base-config");
exports.decorators_legacy = {
    ClassDecorator: base_config_1.TYPE,
    PropertyDecorator: base_config_1.TYPE,
    MethodDecorator: base_config_1.TYPE,
    ParameterDecorator: base_config_1.TYPE,
};
//# sourceMappingURL=decorators.legacy.js.map