/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

"use strict";

var _path = _interopRequireDefault(require("path"));
function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : { default: obj };
}
class Module {
  constructor(file, moduleCache) {
    if (!_path.default.isAbsolute(file)) {
      throw new Error("Expected file to be absolute path but got " + file);
    }
    this.path = file;
    this._moduleCache = moduleCache;
  }
  getPackage() {
    return this._moduleCache.getPackageForModule(this);
  }
  invalidate() {}
}
module.exports = Module;
