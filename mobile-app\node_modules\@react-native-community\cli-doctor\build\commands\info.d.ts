/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Config } from '@react-native-community/cli-types';
declare const _default: {
    name: string;
    description: string;
    func: (_argv: string[], ctx: Config) => Promise<void>;
};
export default _default;
//# sourceMappingURL=info.d.ts.map