{"version": 3, "names": ["getActionFromState", "getActionFromStateDefault", "getStateFromPath", "getStateFromPathDefault", "React", "Linking", "Platform", "extractPathFromURL", "linkingHandlers", "useLinking", "ref", "independent", "enabled", "prefixes", "filter", "config", "getInitialURL", "Promise", "race", "resolve", "setTimeout", "subscribe", "listener", "callback", "url", "subscription", "addEventListener", "removeEventListener", "bind", "remove", "useEffect", "process", "env", "NODE_ENV", "undefined", "length", "console", "error", "OS", "join", "trim", "handler", "Symbol", "push", "index", "indexOf", "splice", "enabledRef", "useRef", "prefixesRef", "filterRef", "configRef", "getInitialURLRef", "getStateFromPathRef", "getActionFromStateRef", "current", "getStateFromURL", "useCallback", "path", "getInitialState", "state", "then", "thenable", "onfulfilled", "catch", "navigation", "rootState", "getRootState", "routes", "some", "r", "routeNames", "includes", "name", "warn", "action", "dispatch", "e", "message", "resetRoot"], "sourceRoot": "../../src", "sources": ["useLinking.native.tsx"], "mappings": "AAAA,SACEA,kBAAkB,IAAIC,yBAAyB,EAC/CC,gBAAgB,IAAIC,uBAAuB,QAGtC,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,QAAQ,QAAQ,cAAc;AAEhD,OAAOC,kBAAkB,MAAM,sBAAsB;AASrD,IAAIC,eAAyB,GAAG,EAAE;AAElC,eAAe,SAASC,UAAU,CAChCC,GAA2D,QAuC3D;EAAA,IAtCA;IACEC,WAAW;IACXC,OAAO,GAAG,IAAI;IACdC,QAAQ;IACRC,MAAM;IACNC,MAAM;IACNC,aAAa,GAAG,MACdC,OAAO,CAACC,IAAI,CAAC,CACXb,OAAO,CAACW,aAAa,EAAE,EACvB,IAAIC,OAAO,CAAaE,OAAO;IAC7B;IACA;IACAC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CACzB,CACF,CAAC;IACJE,SAAS,GAAIC,QAAQ,IAAK;MAAA;MACxB,MAAMC,QAAQ,GAAG;QAAA,IAAC;UAAEC;QAAqB,CAAC;QAAA,OAAKF,QAAQ,CAACE,GAAG,CAAC;MAAA;MAE5D,MAAMC,YAAY,GAAGpB,OAAO,CAACqB,gBAAgB,CAAC,KAAK,EAAEH,QAAQ,CAEhD;;MAEb;MACA;MACA,MAAMI,mBAAmB,4BAAGtB,OAAO,CAACsB,mBAAmB,0DAA3B,sBAA6BC,IAAI,CAACvB,OAAO,CAAC;MAEtE,OAAO,MAAM;QACX;QACA,IAAIoB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEI,MAAM,EAAE;UACxBJ,YAAY,CAACI,MAAM,EAAE;QACvB,CAAC,MAAM;UACLF,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAG,KAAK,EAAEJ,QAAQ,CAAC;QACxC;MACF,CAAC;IACH,CAAC;IACDrB,gBAAgB,GAAGC,uBAAuB;IAC1CH,kBAAkB,GAAGC;EACd,CAAC;EAEVG,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,OAAOC,SAAS;IAClB;IAEA,IAAIvB,WAAW,EAAE;MACf,OAAOuB,SAAS;IAClB;IAEA,IAAItB,OAAO,KAAK,KAAK,IAAIJ,eAAe,CAAC2B,MAAM,EAAE;MAC/CC,OAAO,CAACC,KAAK,CACX,CACE,6KAA6K,EAC7K,uFAAuF,EACvF,4DAA4D,EAC5D/B,QAAQ,CAACgC,EAAE,KAAK,SAAS,GACrB,sJAAsJ,GACtJ,EAAE,CACP,CACEC,IAAI,CAAC,IAAI,CAAC,CACVC,IAAI,EAAE,CACV;IACH;IAEA,MAAMC,OAAO,GAAGC,MAAM,EAAE;IAExB,IAAI9B,OAAO,KAAK,KAAK,EAAE;MACrBJ,eAAe,CAACmC,IAAI,CAACF,OAAO,CAAC;IAC/B;IAEA,OAAO,MAAM;MACX,MAAMG,KAAK,GAAGpC,eAAe,CAACqC,OAAO,CAACJ,OAAO,CAAC;MAE9C,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;QACdpC,eAAe,CAACsC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,CAAChC,OAAO,EAAED,WAAW,CAAC,CAAC;;EAE1B;EACA;EACA;EACA,MAAMoC,UAAU,GAAG3C,KAAK,CAAC4C,MAAM,CAACpC,OAAO,CAAC;EACxC,MAAMqC,WAAW,GAAG7C,KAAK,CAAC4C,MAAM,CAACnC,QAAQ,CAAC;EAC1C,MAAMqC,SAAS,GAAG9C,KAAK,CAAC4C,MAAM,CAAClC,MAAM,CAAC;EACtC,MAAMqC,SAAS,GAAG/C,KAAK,CAAC4C,MAAM,CAACjC,MAAM,CAAC;EACtC,MAAMqC,gBAAgB,GAAGhD,KAAK,CAAC4C,MAAM,CAAChC,aAAa,CAAC;EACpD,MAAMqC,mBAAmB,GAAGjD,KAAK,CAAC4C,MAAM,CAAC9C,gBAAgB,CAAC;EAC1D,MAAMoD,qBAAqB,GAAGlD,KAAK,CAAC4C,MAAM,CAAChD,kBAAkB,CAAC;EAE9DI,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpBiB,UAAU,CAACQ,OAAO,GAAG3C,OAAO;IAC5BqC,WAAW,CAACM,OAAO,GAAG1C,QAAQ;IAC9BqC,SAAS,CAACK,OAAO,GAAGzC,MAAM;IAC1BqC,SAAS,CAACI,OAAO,GAAGxC,MAAM;IAC1BqC,gBAAgB,CAACG,OAAO,GAAGvC,aAAa;IACxCqC,mBAAmB,CAACE,OAAO,GAAGrD,gBAAgB;IAC9CoD,qBAAqB,CAACC,OAAO,GAAGvD,kBAAkB;EACpD,CAAC,CAAC;EAEF,MAAMwD,eAAe,GAAGpD,KAAK,CAACqD,WAAW,CACtCjC,GAA8B,IAAK;IAClC,IAAI,CAACA,GAAG,IAAK0B,SAAS,CAACK,OAAO,IAAI,CAACL,SAAS,CAACK,OAAO,CAAC/B,GAAG,CAAE,EAAE;MAC1D,OAAOU,SAAS;IAClB;IAEA,MAAMwB,IAAI,GAAGnD,kBAAkB,CAAC0C,WAAW,CAACM,OAAO,EAAE/B,GAAG,CAAC;IAEzD,OAAOkC,IAAI,KAAKxB,SAAS,GACrBmB,mBAAmB,CAACE,OAAO,CAACG,IAAI,EAAEP,SAAS,CAACI,OAAO,CAAC,GACpDrB,SAAS;EACf,CAAC,EACD,EAAE,CACH;EAED,MAAMyB,eAAe,GAAGvD,KAAK,CAACqD,WAAW,CAAC,MAAM;IAC9C,IAAIG,KAA8B;IAElC,IAAIb,UAAU,CAACQ,OAAO,EAAE;MACtB,MAAM/B,GAAG,GAAG4B,gBAAgB,CAACG,OAAO,EAAE;MAEtC,IAAI/B,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAC1C,OAAOA,GAAG,CAACqC,IAAI,CAAErC,GAAG,IAAK;UACvB,MAAMoC,KAAK,GAAGJ,eAAe,CAAChC,GAAG,CAAC;UAElC,OAAOoC,KAAK;QACd,CAAC,CAAC;MACJ;MAEAA,KAAK,GAAGJ,eAAe,CAAChC,GAAG,CAAC;IAC9B;IAEA,MAAMsC,QAAQ,GAAG;MACfD,IAAI,CAACE,WAAsD,EAAE;QAC3D,OAAO9C,OAAO,CAACE,OAAO,CAAC4C,WAAW,GAAGA,WAAW,CAACH,KAAK,CAAC,GAAGA,KAAK,CAAC;MAClE,CAAC;MACDI,KAAK,GAAG;QACN,OAAOF,QAAQ;MACjB;IACF,CAAC;IAED,OAAOA,QAAQ;EACjB,CAAC,EAAE,CAACN,eAAe,CAAC,CAAC;EAErBpD,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpB,MAAMR,QAAQ,GAAIE,GAAW,IAAK;MAChC,IAAI,CAACZ,OAAO,EAAE;QACZ;MACF;MAEA,MAAMqD,UAAU,GAAGvD,GAAG,CAAC6C,OAAO;MAC9B,MAAMK,KAAK,GAAGK,UAAU,GAAGT,eAAe,CAAChC,GAAG,CAAC,GAAGU,SAAS;MAE3D,IAAI+B,UAAU,IAAIL,KAAK,EAAE;QACvB;QACA;QACA,MAAMM,SAAS,GAAGD,UAAU,CAACE,YAAY,EAAE;QAE3C,IAAIP,KAAK,CAACQ,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAK,EAACJ,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEK,UAAU,CAACC,QAAQ,CAACF,CAAC,CAACG,IAAI,CAAC,EAAC,EAAE;UACrErC,OAAO,CAACsC,IAAI,CACV,0SAA0S,CAC3S;UACD;QACF;QAEA,MAAMC,MAAM,GAAGrB,qBAAqB,CAACC,OAAO,CAACK,KAAK,EAAET,SAAS,CAACI,OAAO,CAAC;QAEtE,IAAIoB,MAAM,KAAKzC,SAAS,EAAE;UACxB,IAAI;YACF+B,UAAU,CAACW,QAAQ,CAACD,MAAM,CAAC;UAC7B,CAAC,CAAC,OAAOE,CAAC,EAAE;YACV;YACA;YACAzC,OAAO,CAACsC,IAAI,CACT,qDAAoDlD,GAAI,MACvD,OAAOqD,CAAC,KAAK,QAAQ,IAAIA,CAAC,IAAI,IAAI,IAAI,SAAS,IAAIA,CAAC,GAChDA,CAAC,CAACC,OAAO,GACTD,CACL,EAAC,CACH;UACH;QACF,CAAC,MAAM;UACLZ,UAAU,CAACc,SAAS,CAACnB,KAAK,CAAC;QAC7B;MACF;IACF,CAAC;IAED,OAAOvC,SAAS,CAACC,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACV,OAAO,EAAE4C,eAAe,EAAE9C,GAAG,EAAEW,SAAS,CAAC,CAAC;EAE9C,OAAO;IACLsC;EACF,CAAC;AACH"}