{"name": "metro-config", "version": "0.76.7", "description": "🚇 Config parser for Metro.", "main": "src/index.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "license": "MIT", "dependencies": {"connect": "^3.6.5", "cosmiconfig": "^5.0.5", "jest-validate": "^29.2.1", "metro": "0.76.7", "metro-cache": "0.76.7", "metro-core": "0.76.7", "metro-runtime": "0.76.7"}, "devDependencies": {"@types/connect": "^3.4.35", "pretty-format": "^26.5.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=16"}}