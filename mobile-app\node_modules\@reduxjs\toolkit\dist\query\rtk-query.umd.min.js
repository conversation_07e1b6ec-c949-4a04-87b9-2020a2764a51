(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
	typeof define === 'function' && define.amd ? define(['exports'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.RTKQ = {}));
})(this, (function (exports) { 'use strict';

	var e;exports.QueryStatus = void 0;var n,r=undefined&&undefined.__extends||(e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t;}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);},e(t,n)},function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t;}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r);}),i=undefined&&undefined.__generator||function(e,t){var n,r,i,a,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function o(a){return function(o){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return u.label++,{value:a[1],done:!1};case 5:u.label++,r=a[1],a=[0];continue;case 7:a=u.ops.pop(),u.trys.pop();continue;default:if(!((i=(i=u.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){u=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){u.label=a[1];break}if(6===a[0]&&u.label<i[1]){u.label=i[1],i=a;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(a);break}i[2]&&u.ops.pop(),u.trys.pop();continue}a=t.call(e,u);}catch(e){a=[6,e],r=0;}finally{n=i=0;}if(5&a[0])throw a[1];return {value:a[0]?a[1]:void 0,done:!0}}([a,o])}}},a=undefined&&undefined.__spreadArray||function(e,t){for(var n=0,r=t.length,i=e.length;n<r;n++,i++)e[i]=t[n];return e},u=Object.defineProperty,o=Object.defineProperties,c=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,f=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,d=function(e,t,n){return t in e?u(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},p=function(e,t){for(var n in t||(t={}))f.call(t,n)&&d(e,n,t[n]);if(s)for(var r=0,i=s(t);r<i.length;r++)l.call(t,n=i[r])&&d(e,n,t[n]);return e},v=function(e,t){return o(e,c(t))},h=function(e,t){var n={};for(var r in e)f.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&s)for(var i=0,a=s(e);i<a.length;i++)t.indexOf(r=a[i])<0&&l.call(e,r)&&(n[r]=e[r]);return n},y=function(e,t,n){return new Promise((function(r,i){var a=function(e){try{o(n.next(e));}catch(e){i(e);}},u=function(e){try{o(n.throw(e));}catch(e){i(e);}},o=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(a,u)};o((n=n.apply(e,t)).next());}))};(n=exports.QueryStatus||(exports.QueryStatus={})).uninitialized="uninitialized",n.pending="pending",n.fulfilled="fulfilled",n.rejected="rejected";var g,m,b=function(e){return [].concat.apply([],e)};function O(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map((function(e){return "'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function w(e){return !!e&&!!e[ue]}function S(e){var t;return !!e&&(function(e){if(!e||"object"!=typeof e)return !1;var t=Object.getPrototypeOf(e);if(null===t)return !0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===oe}(e)||Array.isArray(e)||!!e[ae]||!!(null===(t=e.constructor)||void 0===t?void 0:t[ae])||k(e)||x(e))}function j(e,t,n){void 0===n&&(n=!1),0===q(e)?(n?Object.keys:ce)(e).forEach((function(r){n&&"symbol"==typeof r||t(r,e[r],e);})):e.forEach((function(n,r){return t(r,n,e)}));}function q(e){var t=e[ue];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:k(e)?2:x(e)?3:0}function A(e,t){return 2===q(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function R(e,t){return 2===q(e)?e.get(t):e[t]}function P(e,t,n){var r=q(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n;}function T(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function k(e){return te&&e instanceof Map}function x(e){return ne&&e instanceof Set}function I(e){return e.o||e.t}function C(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=se(e);delete t[ue];for(var n=ce(t),r=0;r<n.length;r++){var i=n[r],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]});}return Object.create(Object.getPrototypeOf(e),t)}function E(e,t){return void 0===t&&(t=!1),Q(e)||w(e)||!S(e)||(q(e)>1&&(e.set=e.add=e.clear=e.delete=N),Object.freeze(e),t&&j(e,(function(e,t){return E(t,!0)}),!0)),e}function N(){O(2);}function Q(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function M(e){var t=fe[e];return t||O(18,e),t}function D(e,t){fe[e]||(fe[e]=t);}function _(){return m}function K(e,t){t&&(M("Patches"),e.u=[],e.s=[],e.v=t);}function F(e){z(e),e.p.forEach(W),e.p=null;}function z(e){e===m&&(m=e.l);}function U(e){return m={p:[],l:m,h:e,m:!0,_:0}}function W(e){var t=e[ue];0===t.i||1===t.i?t.j():t.g=!0;}function B(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.O||M("ES5").S(t,e,r),r?(n[ue].P&&(F(t),O(4)),S(e)&&(e=L(t,e),t.l||H(t,e)),t.u&&M("Patches").M(n[ue].t,e,t.u,t.s)):e=L(t,n,[]),F(t),t.u&&t.v(t.u,t.s),e!==ie?e:void 0}function L(e,t,n){if(Q(t))return t;var r=t[ue];if(!r)return j(t,(function(i,a){return J(e,r,t,i,a,n)}),!0),t;if(r.A!==e)return t;if(!r.P)return H(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var i=4===r.i||5===r.i?r.o=C(r.k):r.o,a=i,u=!1;3===r.i&&(a=new Set(i),i.clear(),u=!0),j(a,(function(t,a){return J(e,r,i,t,a,n,u)})),H(e,i,!1),n&&e.u&&M("Patches").N(r,n,e.u,e.s);}return r.o}function J(e,t,n,r,i,a,u){if(w(i)){var o=L(e,i,a&&t&&3!==t.i&&!A(t.R,r)?a.concat(r):void 0);if(P(n,r,o),!w(o))return;e.m=!1;}else u&&n.add(i);if(S(i)&&!Q(i)){if(!e.h.D&&e._<1)return;L(e,i),t&&t.A.l||H(e,i);}}function H(e,t,n){void 0===n&&(n=!1),!e.l&&e.h.D&&e.m&&E(t,n);}function V(e,t){var n=e[ue];return (n?I(n):e)[t]}function G(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n);}}function $(e){e.P||(e.P=!0,e.l&&$(e.l));}function X(e){e.o||(e.o=C(e.t));}function Y(e,t,n){var r=k(t)?M("MapSet").F(t,n):x(t)?M("MapSet").T(t,n):e.O?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:_(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},i=r,a=le;n&&(i=[r],a=de);var u=Proxy.revocable(i,a),o=u.revoke,c=u.proxy;return r.k=c,r.j=o,c}(t,n):M("ES5").J(t,n);return (n?n.A:_()).p.push(r),r}function Z(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return C(e)}var ee="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),te="undefined"!=typeof Map,ne="undefined"!=typeof Set,re="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,ie=ee?Symbol.for("immer-nothing"):((g={})["immer-nothing"]=!0,g),ae=ee?Symbol.for("immer-draftable"):"__$immer_draftable",ue=ee?Symbol.for("immer-state"):"__$immer_state",oe=(""+Object.prototype.constructor),ce="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,se=Object.getOwnPropertyDescriptors||function(e){var t={};return ce(e).forEach((function(n){t[n]=Object.getOwnPropertyDescriptor(e,n);})),t},fe={},le={get:function(e,t){if(t===ue)return e;var n,r,i,a=I(e);if(!A(a,t))return n=e,(i=G(a,t))?"value"in i?i.value:null===(r=i.get)||void 0===r?void 0:r.call(n.k):void 0;var u=a[t];return e.I||!S(u)?u:u===V(e.t,t)?(X(e),e.o[t]=Y(e.A.h,u,e)):u},has:function(e,t){return t in I(e)},ownKeys:function(e){return Reflect.ownKeys(I(e))},set:function(e,t,n){var r=G(I(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var i=V(I(e),t),a=null==i?void 0:i[ue];if(a&&a.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(T(n,i)&&(void 0!==n||A(e.t,t)))return !0;X(e),$(e);}return e.o[t]===n&&(void 0!==n||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==V(e.t,t)||t in e.t?(e.R[t]=!1,X(e),$(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=I(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){O(11);},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){O(12);}},de={};j(le,(function(e,t){de[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)};})),de.deleteProperty=function(e,t){return de.set.call(this,e,t,void 0)},de.set=function(e,t,n){return le.set.call(this,e[0],t,n,e[0])};var pe=new(function(){function e(e){var t=this;this.O=re,this.D=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var i=n;n=e;var a=t;return function(e){var t=this;void 0===e&&(e=i);for(var r=arguments.length,u=Array(r>1?r-1:0),o=1;o<r;o++)u[o-1]=arguments[o];return a.produce(e,(function(e){var r;return (r=n).call.apply(r,[t,e].concat(u))}))}}var u;if("function"!=typeof n&&O(6),void 0!==r&&"function"!=typeof r&&O(7),S(e)){var o=U(t),c=Y(t,e,void 0),s=!0;try{u=n(c),s=!1;}finally{s?F(o):z(o);}return "undefined"!=typeof Promise&&u instanceof Promise?u.then((function(e){return K(o,r),B(e,o)}),(function(e){throw F(o),e})):(K(o,r),B(u,o))}if(!e||"object"!=typeof e){if(void 0===(u=n(e))&&(u=e),u===ie&&(u=void 0),t.D&&E(u,!0),r){var f=[],l=[];M("Patches").M(e,u,f,l),r(f,l);}return u}O(21,e);},this.produceWithPatches=function(e,n){if("function"==typeof e)return function(n){for(var r=arguments.length,i=Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];return t.produceWithPatches(n,(function(t){return e.apply(void 0,[t].concat(i))}))};var r,i,a=t.produce(e,n,(function(e,t){r=e,i=t;}));return "undefined"!=typeof Promise&&a instanceof Promise?a.then((function(e){return [e,r,i]})):[a,r,i]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze);}var t=e.prototype;return t.createDraft=function(e){S(e)||O(8),w(e)&&(e=function(e){return w(e)||O(22,e),function e(t){if(!S(t))return t;var n,r=t[ue],i=q(t);if(r){if(!r.P&&(r.i<4||!M("ES5").K(r)))return r.t;r.I=!0,n=Z(t,i),r.I=!1;}else n=Z(t,i);return j(n,(function(t,i){r&&R(r.t,t)===i||P(n,t,e(i));})),3===i?new Set(n):n}(e)}(e));var t=U(this),n=Y(this,e,void 0);return n[ue].C=!0,z(t),n},t.finishDraft=function(e,t){var n=(e&&e[ue]).A;return K(n,t),B(void 0,n)},t.setAutoFreeze=function(e){this.D=e;},t.setUseProxies=function(e){e&&!re&&O(20),this.O=e;},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));var i=M("Patches").$;return w(e)?i(e,t):this.produce(e,(function(e){return i(e,t)}))},e}()),ve=pe.produce,he=pe.produceWithPatches.bind(pe),ye=(pe.setAutoFreeze.bind(pe),pe.setUseProxies.bind(pe),pe.applyPatches.bind(pe)),ge=(pe.createDraft.bind(pe),pe.finishDraft.bind(pe),ve);function me(e){return "Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var be=function(){return Math.random().toString(36).substring(7).split("").join(".")},Oe={INIT:"@@redux/INIT"+be(),REPLACE:"@@redux/REPLACE"+be(),PROBE_UNKNOWN_ACTION:function(){return "@@redux/PROBE_UNKNOWN_ACTION"+be()}},we=function(e,t){return e===t};function Se(e,t){var n,r,i,a="object"==typeof t?t:{equalityCheck:t},u=a.equalityCheck,o=a.maxSize,c=void 0===o?1:o,s=a.resultEqualityCheck,f=(i=void 0===u?we:u,function(e,t){if(null===e||null===t||e.length!==t.length)return !1;for(var n=e.length,r=0;r<n;r++)if(!i(e[r],t[r]))return !1;return !0}),l=1===c?(n=f,{get:function(e){return r&&n(r.key,e)?r.value:"NOT_FOUND"},put:function(e,t){r={key:e,value:t};},getEntries:function(){return r?[r]:[]},clear:function(){r=void 0;}}):function(e,t){var n=[];function r(e){var r=n.findIndex((function(n){return t(e,n.key)}));if(r>-1){var i=n[r];return r>0&&(n.splice(r,1),n.unshift(i)),i.value}return "NOT_FOUND"}return {get:r,put:function(t,i){"NOT_FOUND"===r(t)&&(n.unshift({key:t,value:i}),n.length>e&&n.pop());},getEntries:function(){return n},clear:function(){n=[];}}}(c,f);function d(){var t=l.get(arguments);if("NOT_FOUND"===t){if(t=e.apply(null,arguments),s){var n=l.getEntries(),r=n.find((function(e){return s(e.value,t)}));r&&(t=r.value);}l.put(arguments,t);}return t}return d.clearCache=function(){return l.clear()},d}function je(e){var t=Array.isArray(e[0])?e[0]:e;if(!t.every((function(e){return "function"==typeof e}))){var n=t.map((function(e){return "function"==typeof e?"function "+(e.name||"unnamed")+"()":typeof e})).join(", ");throw new Error("createSelector expects all input-selectors to be functions, but received the following types: ["+n+"]")}return t}function qe(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i=function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];var a,u=0,o={memoizeOptions:void 0},c=r.pop();if("object"==typeof c&&(o=c,c=r.pop()),"function"!=typeof c)throw new Error("createSelector expects an output function after the inputs, but received: ["+typeof c+"]");var s=o,f=s.memoizeOptions,l=void 0===f?n:f,d=Array.isArray(l)?l:[l],p=je(r),v=e.apply(void 0,[function(){return u++,c.apply(null,arguments)}].concat(d)),h=e((function(){for(var e=[],t=p.length,n=0;n<t;n++)e.push(p[n].apply(null,arguments));return a=v.apply(null,e)}));return Object.assign(h,{resultFunc:c,memoizedResultFunc:v,dependencies:p,lastResult:function(){return a},recomputations:function(){return u},resetRecomputations:function(){return u=0}}),h};return i}var Ae=qe(Se);function Re(e){if("object"!=typeof e||null===e)return !1;var t=Object.getPrototypeOf(e);if(null===t)return !0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n}function Pe(e,t){function n(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(t){var i=t.apply(void 0,n);if(!i)throw new Error("prepareAction did not return an object");return p(p({type:e,payload:i.payload},"meta"in i&&{meta:i.meta}),"error"in i&&{error:i.error})}return {type:e,payload:n[0]}}return n.toString=function(){return ""+e},n.type=e,n.match=function(t){return t.type===e},n}function Te(e){return S(e)?ge(e,(function(){})):e}function ke(e){var t,n={},r=[],i={addCase:function(e,t){var r="string"==typeof e?e:e.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in n)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return n[r]=t,i},addMatcher:function(e,t){return r.push({matcher:e,reducer:t}),i},addDefaultCase:function(e){return t=e,i}};return e(i),[n,r,t]}function xe(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");var n,r="function"==typeof e.initialState?e.initialState:Te(e.initialState),i=e.reducers||{},u=Object.keys(i),o={},c={},s={};function f(){var t="function"==typeof e.extraReducers?ke(e.extraReducers):[e.extraReducers],n=t[0],i=t[1],u=void 0===i?[]:i,o=t[2],s=void 0===o?void 0:o,f=p(p({},void 0===n?{}:n),c);return function(e,t,n,r){var i,u=ke(t),o=u[0],c=u[1],s=u[2];if("function"==typeof e)i=function(){return Te(e())};else {var f=Te(e);i=function(){return f};}function l(e,t){void 0===e&&(e=i());var n=a([o[t.type]],c.filter((function(e){return (0, e.matcher)(t)})).map((function(e){return e.reducer})));return 0===n.filter((function(e){return !!e})).length&&(n=[s]),n.reduce((function(e,n){if(n){var r;if(w(e))return void 0===(r=n(e,t))?e:r;if(S(e))return ge(e,(function(e){return n(e,t)}));if(void 0===(r=n(e,t))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}return e}),e)}return l.getInitialState=i,l}(r,(function(e){for(var t in f)e.addCase(t,f[t]);for(var n=0,r=u;n<r.length;n++){var i=r[n];e.addMatcher(i.matcher,i.reducer);}s&&e.addDefaultCase(s);}))}return u.forEach((function(e){var n,r,a=i[e],u=t+"/"+e;"reducer"in a?(n=a.reducer,r=a.prepare):n=a,o[e]=n,c[u]=n,s[e]=r?Pe(u,r):Pe(u);})),{name:t,reducer:function(e,t){return n||(n=f()),n(e,t)},actions:s,caseReducers:o,getInitialState:function(){return n||(n=f()),n.getInitialState()}}}((function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=e.apply(this,n)||this;return Object.setPrototypeOf(i,t.prototype),i}r(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,a([void 0],e[0].concat(this)))):new(t.bind.apply(t,a([void 0],e.concat(this))))};}))(Array),function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=e.apply(this,n)||this;return Object.setPrototypeOf(i,t.prototype),i}r(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,a([void 0],e[0].concat(this)))):new(t.bind.apply(t,a([void 0],e.concat(this))))};}(Array);var Ie=function(e){void 0===e&&(e=21);for(var t="",n=e;n--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},Ce=["name","message","stack","code"],Ee=function(e,t){this.payload=e,this.meta=t;},Ne=function(e,t){this.payload=e,this.meta=t;},Qe=function(e){if("object"==typeof e&&null!==e){for(var t={},n=0,r=Ce;n<r.length;n++){var i=r[n];"string"==typeof e[i]&&(t[i]=e[i]);}return t}return {message:String(e)}},Me=function(){function e(e,t,n){var r=Pe(e+"/fulfilled",(function(e,t,n,r){return {payload:e,meta:v(p({},r||{}),{arg:n,requestId:t,requestStatus:"fulfilled"})}})),a=Pe(e+"/pending",(function(e,t,n){return {payload:void 0,meta:v(p({},n||{}),{arg:t,requestId:e,requestStatus:"pending"})}})),u=Pe(e+"/rejected",(function(e,t,r,i,a){return {payload:i,error:(n&&n.serializeError||Qe)(e||"Rejected"),meta:v(p({},a||{}),{arg:r,requestId:t,rejectedWithValue:!!i,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),o="undefined"!=typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return !1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}};}return e.prototype.abort=function(){},e}();return Object.assign((function(e){return function(c,s,f){var l,d=(null==n?void 0:n.idGenerator)?n.idGenerator(e):Ie(),p=new o;function v(e){l=e,p.abort();}var h=function(){return y(this,null,(function(){var o,h,y,g,m,b;return i(this,(function(i){switch(i.label){case 0:return i.trys.push([0,4,,5]),null===(O=g=null==(o=null==n?void 0:n.condition)?void 0:o.call(n,e,{getState:s,extra:f}))||"object"!=typeof O||"function"!=typeof O.then?[3,2]:[4,g];case 1:g=i.sent(),i.label=2;case 2:if(!1===g||p.signal.aborted)throw {name:"ConditionError",message:"Aborted due to condition callback returning false."};return m=new Promise((function(e,t){return p.signal.addEventListener("abort",(function(){return t({name:"AbortError",message:l||"Aborted"})}))})),c(a(d,e,null==(h=null==n?void 0:n.getPendingMeta)?void 0:h.call(n,{requestId:d,arg:e},{getState:s,extra:f}))),[4,Promise.race([m,Promise.resolve(t(e,{dispatch:c,getState:s,extra:f,requestId:d,signal:p.signal,abort:v,rejectWithValue:function(e,t){return new Ee(e,t)},fulfillWithValue:function(e,t){return new Ne(e,t)}})).then((function(t){if(t instanceof Ee)throw t;return t instanceof Ne?r(t.payload,d,e,t.meta):r(t,d,e)}))])];case 3:return y=i.sent(),[3,5];case 4:return b=i.sent(),y=b instanceof Ee?u(null,d,e,b.payload,b.meta):u(b,d,e),[3,5];case 5:return n&&!n.dispatchConditionRejection&&u.match(y)&&y.meta.condition||c(y),[2,y]}var O;}))}))}();return Object.assign(h,{abort:v,requestId:d,arg:e,unwrap:function(){return h.then(De)}})}}),{pending:a,rejected:u,fulfilled:r,typePrefix:e})}return e.withTypes=function(){return e},e}();function De(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var _e=function(e,t){return (n=e)&&"function"==typeof n.match?e.match(t):e(t);var n;};function Ke(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){return e.some((function(e){return _e(e,t)}))}}function Fe(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){return e.every((function(e){return _e(e,t)}))}}function ze(e,t){if(!e||!e.meta)return !1;var n="string"==typeof e.meta.requestId,r=t.indexOf(e.meta.requestStatus)>-1;return n&&r}function Ue(e){return "function"==typeof e[0]&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function We(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 0===e.length?function(e){return ze(e,["pending"])}:Ue(e)?function(t){var n=e.map((function(e){return e.pending}));return Ke.apply(void 0,n)(t)}:We()(e[0])}function Be(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 0===e.length?function(e){return ze(e,["rejected"])}:Ue(e)?function(t){var n=e.map((function(e){return e.rejected}));return Ke.apply(void 0,n)(t)}:Be()(e[0])}function Le(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=function(e){return e&&e.meta&&e.meta.rejectedWithValue};return 0===e.length||Ue(e)?function(t){return Fe(Be.apply(void 0,e),n)(t)}:Le()(e[0])}function Je(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 0===e.length?function(e){return ze(e,["fulfilled"])}:Ue(e)?function(t){var n=e.map((function(e){return e.fulfilled}));return Ke.apply(void 0,n)(t)}:Je()(e[0])}function He(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 0===e.length?function(e){return ze(e,["pending","fulfilled","rejected"])}:Ue(e)?function(t){for(var n=[],r=0,i=e;r<i.length;r++){var a=i[r];n.push(a.pending,a.rejected,a.fulfilled);}return Ke.apply(void 0,n)(t)}:He()(e[0])}"function"==typeof queueMicrotask&&queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis),function(){function e(e,t){var n=i[e];return n?n.enumerable=t:i[e]=n={configurable:!0,enumerable:t,get:function(){return le.get(this[ue],e)},set:function(t){le.set(this[ue],e,t);}},n}function t(e){for(var t=e.length-1;t>=0;t--){var i=e[t][ue];if(!i.P)switch(i.i){case 5:r(i)&&$(i);break;case 4:n(i)&&$(i);}}}function n(e){for(var t=e.t,n=e.k,r=ce(n),i=r.length-1;i>=0;i--){var a=r[i];if(a!==ue){var u=t[a];if(void 0===u&&!A(t,a))return !0;var o=n[a],c=o&&o[ue];if(c?c.t!==u:!T(o,u))return !0}}var s=!!t[ue];return r.length!==ce(t).length+(s?0:1)}function r(e){var t=e.k;if(t.length!==e.t.length)return !0;var n=Object.getOwnPropertyDescriptor(t,t.length-1);if(n&&!n.get)return !0;for(var r=0;r<t.length;r++)if(!t.hasOwnProperty(r))return !0;return !1}var i={};D("ES5",{J:function(t,n){var r=Array.isArray(t),i=function(t,n){if(t){for(var r=Array(n.length),i=0;i<n.length;i++)Object.defineProperty(r,""+i,e(i,!0));return r}var a=se(n);delete a[ue];for(var u=ce(a),o=0;o<u.length;o++){var c=u[o];a[c]=e(c,t||!!a[c].enumerable);}return Object.create(Object.getPrototypeOf(n),a)}(r,t),a={i:r?5:4,A:n?n.A:_(),P:!1,I:!1,R:{},l:n,t:t,k:i,o:null,g:!1,C:!1};return Object.defineProperty(i,ue,{value:a,writable:!0}),i},S:function(e,n,i){i?w(n)&&n[ue].A===e&&t(e.p):(e.u&&function e(t){if(t&&"object"==typeof t){var n=t[ue];if(n){var i=n.t,a=n.k,u=n.R,o=n.i;if(4===o)j(a,(function(t){t!==ue&&(void 0!==i[t]||A(i,t)?u[t]||e(a[t]):(u[t]=!0,$(n)));})),j(i,(function(e){void 0!==a[e]||A(a,e)||(u[e]=!1,$(n));}));else if(5===o){if(r(n)&&($(n),u.length=!0),a.length<i.length)for(var c=a.length;c<i.length;c++)u[c]=!1;else for(var s=i.length;s<a.length;s++)u[s]=!0;for(var f=Math.min(a.length,i.length),l=0;l<f;l++)a.hasOwnProperty(l)||(u[l]=!0),void 0===u[l]&&e(a[l]);}}}}(e.p[0]),t(e.p));},K:function(e){return 4===e.i?n(e):r(e)}});}();var Ve=Re;function Ge(e,t){if(e===t||!(Ve(e)&&Ve(t)||Array.isArray(e)&&Array.isArray(t)))return t;for(var n=Object.keys(t),r=Object.keys(e),i=n.length===r.length,a=Array.isArray(t)?[]:{},u=0,o=n;u<o.length;u++){var c=o[u];a[c]=Ge(e[c],t[c]),i&&(i=e[c]===a[c]);}return i?e:a}var $e=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return fetch.apply(void 0,e)},Xe=function(e){return e.status>=200&&e.status<=299},Ye=function(e){return /ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"")};function Ze(e){if(!Re(e))return e;for(var t=p({},e),n=0,r=Object.entries(t);n<r.length;n++){var i=r[n];void 0===i[1]&&delete t[i[0]];}return t}function et(e){var t=this;void 0===e&&(e={});var n=e.baseUrl,r=e.prepareHeaders,a=void 0===r?function(e){return e}:r,u=e.fetchFn,o=void 0===u?$e:u,c=e.paramsSerializer,s=e.isJsonContentType,f=void 0===s?Ye:s,l=e.jsonContentType,d=void 0===l?"application/json":l,g=e.jsonReplacer,m=e.timeout,b=e.responseHandler,O=e.validateStatus,w=h(e,["baseUrl","prepareHeaders","fetchFn","paramsSerializer","isJsonContentType","jsonContentType","jsonReplacer","timeout","responseHandler","validateStatus"]);return "undefined"==typeof fetch&&o===$e&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),function(e,r){return y(t,null,(function(){var t,u,s,l,y,j,q,A,R,P,T,k,x,I,C,E,N,Q,M,D,_,K,F,z,U,W,B,L,J,H,V,G,$,X,Y,Z;return i(this,(function(i){switch(i.label){case 0:return t=r.signal,u=r.getState,s=r.extra,l=r.endpoint,y=r.forced,j=r.type,R=(A="string"==typeof e?{url:e}:e).url,T=void 0===(P=A.headers)?new Headers(w.headers):P,x=void 0===(k=A.params)?void 0:k,C=void 0===(I=A.responseHandler)?null!=b?b:"json":I,N=void 0===(E=A.validateStatus)?null!=O?O:Xe:E,M=void 0===(Q=A.timeout)?m:Q,D=h(A,["url","headers","params","responseHandler","validateStatus","timeout"]),_=p(v(p({},w),{signal:t}),D),T=new Headers(Ze(T)),K=_,[4,a(T,{getState:u,extra:s,endpoint:l,forced:y,type:j})];case 1:K.headers=i.sent()||T,F=function(e){return "object"==typeof e&&(Re(e)||Array.isArray(e)||"function"==typeof e.toJSON)},!_.headers.has("content-type")&&F(_.body)&&_.headers.set("content-type",d),F(_.body)&&f(_.headers)&&(_.body=JSON.stringify(_.body,g)),x&&(z=~R.indexOf("?")?"&":"?",U=c?c(x):new URLSearchParams(Ze(x)),R+=z+U),R=function(e,t){if(!e)return t;if(!t)return e;if(function(e){return new RegExp("(^|:)//").test(e)}(t))return t;var n=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=function(e){return e.replace(/\/$/,"")}(e),""+e+n+function(e){return e.replace(/^\//,"")}(t)}(n,R),W=new Request(R,_),B=new Request(R,_),q={request:B},J=!1,H=M&&setTimeout((function(){J=!0,r.abort();}),M),i.label=2;case 2:return i.trys.push([2,4,5,6]),[4,o(W)];case 3:return L=i.sent(),[3,6];case 4:return V=i.sent(),[2,{error:{status:J?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(V)},meta:q}];case 5:return H&&clearTimeout(H),[7];case 6:G=L.clone(),q.response=G,X="",i.label=7;case 7:return i.trys.push([7,9,,10]),[4,Promise.all([S(L,C).then((function(e){return $=e}),(function(e){return Y=e})),G.text().then((function(e){return X=e}),(function(){}))])];case 8:if(i.sent(),Y)throw Y;return [3,10];case 9:return Z=i.sent(),[2,{error:{status:"PARSING_ERROR",originalStatus:L.status,data:X,error:String(Z)},meta:q}];case 10:return [2,N(L,$)?{data:$,meta:q}:{error:{status:L.status,data:$},meta:q}]}}))}))};function S(e,t){return y(this,null,(function(){var n;return i(this,(function(r){switch(r.label){case 0:return "function"==typeof t?[2,t(e)]:("content-type"===t&&(t=f(e.headers)?"json":"text"),"json"!==t?[3,2]:[4,e.text()]);case 1:return [2,(n=r.sent()).length?JSON.parse(n):null];case 2:return [2,e.text()]}}))}))}}var tt=function(e,t){void 0===t&&(t=void 0),this.value=e,this.meta=t;};function nt(e,t){return void 0===e&&(e=0),void 0===t&&(t=5),y(this,null,(function(){var n,r;return i(this,(function(i){switch(i.label){case 0:return n=Math.min(e,t),r=~~((Math.random()+.4)*(300<<n)),[4,new Promise((function(e){return setTimeout((function(t){return e(t)}),r)}))];case 1:return i.sent(),[2]}}))}))}var rt,it,at={},ut=Object.assign((function(e,t){return function(n,r,a){return y(void 0,null,(function(){var u,o,c,s,f,l,d;return i(this,(function(i){switch(i.label){case 0:u=[5,(t||at).maxRetries,(a||at).maxRetries].filter((function(e){return void 0!==e})),o=u.slice(-1)[0],c=function(e,t,n){return n.attempt<=o},s=p(p({maxRetries:o,backoff:nt,retryCondition:c},t),a),f=0,i.label=1;case 1:i.label=2;case 2:return i.trys.push([2,4,,6]),[4,e(n,r,a)];case 3:if((l=i.sent()).error)throw new tt(l);return [2,l];case 4:if(d=i.sent(),f++,d.throwImmediately){if(d instanceof tt)return [2,d.value];throw d}return d instanceof tt&&!s.retryCondition(d.value.error,n,{attempt:f,baseQueryApi:r,extraOptions:a})?[2,d.value]:[4,s.backoff(f,s.maxRetries)];case 5:return i.sent(),[3,6];case 6:return [3,1];case 7:return [2]}}))}))}}),{fail:function(e){throw Object.assign(new tt({error:e}),{throwImmediately:!0})}}),ot=Pe("__rtkq/focused"),ct=Pe("__rtkq/unfocused"),st=Pe("__rtkq/online"),ft=Pe("__rtkq/offline"),lt=!1;function dt(e,t){return t?t(e,{onFocus:ot,onFocusLost:ct,onOffline:ft,onOnline:st}):(n=function(){return e(ot())},r=function(){return e(st())},i=function(){return e(ft())},a=function(){"visible"===window.document.visibilityState?n():e(ct());},lt||"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("visibilitychange",a,!1),window.addEventListener("focus",n,!1),window.addEventListener("online",r,!1),window.addEventListener("offline",i,!1),lt=!0),function(){window.removeEventListener("focus",n),window.removeEventListener("visibilitychange",a),window.removeEventListener("online",r),window.removeEventListener("offline",i),lt=!1;});var n,r,i,a;}function pt(e){return e.type===rt.query}function vt(e,t,n,r,i,a){return "function"==typeof e?e(t,n,r,i).map(ht).map(a):Array.isArray(e)?e.map(ht).map(a):[]}function ht(e){return "string"==typeof e?{type:e}:e}function yt(e){return null!=e}(it=rt||(rt={})).query="query",it.mutation="mutation";var gt=Symbol("forceQueryFn"),mt=function(e){return "function"==typeof e[gt]};function bt(e){return e}function Ot(e,t,n,r){return vt(n[e.meta.arg.endpointName][t],Je(e)?e.payload:void 0,Le(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,r)}function wt(e,t,n){var r=e[t];r&&n(r);}function St(e){var t;return null!=(t="arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)?t:e.requestId}function jt(e,t,n){var r=e[St(t)];r&&n(r);}var qt={},At=Symbol.for("RTKQ/skipToken"),Rt=At,Pt={status:exports.QueryStatus.uninitialized},Tt=ge(Pt,(function(){})),kt=ge(Pt,(function(){})),xt=WeakMap?new WeakMap:void 0,It=function(e){var t=e.endpointName,n=e.queryArgs,r="",i=null==xt?void 0:xt.get(n);if("string"==typeof i)r=i;else {var a=JSON.stringify(n,(function(e,t){return Re(t)?Object.keys(t).sort().reduce((function(e,n){return e[n]=t[n],e}),{}):t}));Re(n)&&(null==xt||xt.set(n,a)),r=a;}return t+"("+r+")"};function Ct(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){var n=Se((function(e){var n,r;return null==(r=t.extractRehydrationInfo)?void 0:r.call(t,e,{reducerPath:null!=(n=t.reducerPath)?n:"api"})})),r=v(p({reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1},t),{extractRehydrationInfo:n,serializeQueryArgs:function(e){var n=It;if("serializeQueryArgs"in e.endpointDefinition){var r=e.endpointDefinition.serializeQueryArgs;n=function(e){var t=r(e);return "string"==typeof t?t:It(v(p({},e),{queryArgs:t}))};}else t.serializeQueryArgs&&(n=t.serializeQueryArgs);return n(e)},tagTypes:a([],t.tagTypes||[])}),i={endpointDefinitions:{},batch:function(e){e();},apiUid:Ie(),extractRehydrationInfo:n,hasRehydrationInfo:Se((function(e){return null!=n(e)}))},u={injectEndpoints:function(e){for(var t=e.endpoints({query:function(e){return v(p({},e),{type:rt.query})},mutation:function(e){return v(p({},e),{type:rt.mutation})}}),n=0,r=Object.entries(t);n<r.length;n++){var a=r[n],c=a[0],s=a[1];if(e.overrideExisting||!(c in i.endpointDefinitions)){i.endpointDefinitions[c]=s;for(var f=0,l=o;f<l.length;f++)l[f].injectEndpoint(c,s);}}return u},enhanceEndpoints:function(e){var t=e.addTagTypes,n=e.endpoints;if(t)for(var a=0,o=t;a<o.length;a++){var c=o[a];r.tagTypes.includes(c)||r.tagTypes.push(c);}if(n)for(var s=0,f=Object.entries(n);s<f.length;s++){var l=f[s],d=l[0],p=l[1];"function"==typeof p?p(i.endpointDefinitions[d]):Object.assign(i.endpointDefinitions[d]||{},p);}return u}},o=e.map((function(e){return e.init(u,r,i)}));return u.injectEndpoints({endpoints:t.endpoints})}}function Et(){return function(){throw new Error("When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.")}}var Nt,Qt=function(e){var t=e.reducerPath,n=e.api,r=e.context,i=e.internalState,a=n.internalActions,u=a.removeQueryResult,o=a.unsubscribeQueryResult;function c(e){var t=i.currentSubscriptions[e];return !!t&&!function(e){for(var t in e)return !1;return !0}(t)}var s={};function f(e,t,n,i){var a,o=r.endpointDefinitions[t],f=null!=(a=null==o?void 0:o.keepUnusedDataFor)?a:i.keepUnusedDataFor;if(Infinity!==f){var l=Math.max(0,Math.min(f,2147482.647));if(!c(e)){var d=s[e];d&&clearTimeout(d),s[e]=setTimeout((function(){c(e)||n.dispatch(u({queryCacheKey:e})),delete s[e];}),1e3*l);}}}return function(e,i,a){var u;if(o.match(e)){var c=i.getState()[t];f(b=e.payload.queryCacheKey,null==(u=c.queries[b])?void 0:u.endpointName,i,c.config);}if(n.util.resetApiState.match(e))for(var l=0,d=Object.entries(s);l<d.length;l++){var p=d[l],v=p[0],h=p[1];h&&clearTimeout(h),delete s[v];}if(r.hasRehydrationInfo(e)){c=i.getState()[t];for(var y=r.extractRehydrationInfo(e).queries,g=0,m=Object.entries(y);g<m.length;g++){var b,O=m[g],w=O[1];f(b=O[0],null==w?void 0:w.endpointName,i,c.config);}}}},Mt=function(e){var n=e.reducerPath,r=e.context,i=e.context.endpointDefinitions,a=e.mutationThunk,u=e.api,o=e.assertTagType,c=e.refetchQuery,s=u.internalActions.removeQueryResult,f=Ke(Je(a),Le(a));function l(e,i){var a=i.getState(),o=a[n],f=u.util.selectInvalidatedBy(a,e);r.batch((function(){for(var e,n=0,r=Array.from(f.values());n<r.length;n++){var a=r[n].queryCacheKey,u=o.queries[a],l=null!=(e=o.subscriptions[a])?e:{};u&&(0===Object.keys(l).length?i.dispatch(s({queryCacheKey:a})):u.status!==exports.QueryStatus.uninitialized&&i.dispatch(c(u,a)));}}));}return function(e,t){f(e)&&l(Ot(e,"invalidatesTags",i,o),t),u.util.invalidateTags.match(e)&&l(vt(e.payload,void 0,void 0,void 0,void 0,o),t);}},Dt=function(e){var n=e.reducerPath,r=e.queryThunk,i=e.api,a=e.refetchQuery,u=e.internalState,o={};function c(e,r){var i=e.queryCacheKey,c=r.getState()[n].queries[i];if(c&&c.status!==exports.QueryStatus.uninitialized){var s=l(u.currentSubscriptions[i]);if(Number.isFinite(s)){var f=o[i];(null==f?void 0:f.timeout)&&(clearTimeout(f.timeout),f.timeout=void 0);var d=Date.now()+s,p=o[i]={nextPollTimestamp:d,pollingInterval:s,timeout:setTimeout((function(){p.timeout=void 0,r.dispatch(a(c,i));}),s)};}}}function s(e,r){var i=e.queryCacheKey,a=r.getState()[n].queries[i];if(a&&a.status!==exports.QueryStatus.uninitialized){var s=l(u.currentSubscriptions[i]);if(Number.isFinite(s)){var d=o[i],p=Date.now()+s;(!d||p<d.nextPollTimestamp)&&c({queryCacheKey:i},r);}else f(i);}}function f(e){var t=o[e];(null==t?void 0:t.timeout)&&clearTimeout(t.timeout),delete o[e];}function l(e){void 0===e&&(e={});var t=Number.POSITIVE_INFINITY;for(var n in e)e[n].pollingInterval&&(t=Math.min(e[n].pollingInterval,t));return t}return function(e,t){(i.internalActions.updateSubscriptionOptions.match(e)||i.internalActions.unsubscribeQueryResult.match(e))&&s(e.payload,t),(r.pending.match(e)||r.rejected.match(e)&&e.meta.condition)&&s(e.meta.arg,t),(r.fulfilled.match(e)||r.rejected.match(e)&&!e.meta.condition)&&c(e.meta.arg,t),i.util.resetApiState.match(e)&&function(){for(var e=0,t=Object.keys(o);e<t.length;e++)f(t[e]);}();}},_t=new Error("Promise never resolved before cacheEntryRemoved."),Kt=function(e){var t=e.api,n=e.reducerPath,r=e.context,i=e.queryThunk,a=e.mutationThunk,u=He(i),o=He(a),c=Je(i,a),s={};function f(e,n,i,a,u){var o=r.endpointDefinitions[e],c=null==o?void 0:o.onCacheEntryAdded;if(c){var f={},l=new Promise((function(e){f.cacheEntryRemoved=e;})),d=Promise.race([new Promise((function(e){f.valueResolved=e;})),l.then((function(){throw _t}))]);d.catch((function(){})),s[i]=f;var h=t.endpoints[e].select(o.type===rt.query?n:i),y=a.dispatch((function(e,t,n){return n})),g=v(p({},a),{getCacheEntry:function(){return h(a.getState())},requestId:u,extra:y,updateCachedData:o.type===rt.query?function(r){return a.dispatch(t.util.updateQueryData(e,n,r))}:void 0,cacheDataLoaded:d,cacheEntryRemoved:l}),m=c(n,g);Promise.resolve(m).catch((function(e){if(e!==_t)throw e}));}}return function(e,r,l){var d=function(e){return u(e)?e.meta.arg.queryCacheKey:o(e)?e.meta.requestId:t.internalActions.removeQueryResult.match(e)?e.payload.queryCacheKey:t.internalActions.removeMutationResult.match(e)?St(e.payload):""}(e);if(i.pending.match(e)){var p=l[n].queries[d],v=r.getState()[n].queries[d];!p&&v&&f(e.meta.arg.endpointName,e.meta.arg.originalArgs,d,r,e.meta.requestId);}else if(a.pending.match(e))(v=r.getState()[n].mutations[d])&&f(e.meta.arg.endpointName,e.meta.arg.originalArgs,d,r,e.meta.requestId);else if(c(e))(null==(m=s[d])?void 0:m.valueResolved)&&(m.valueResolved({data:e.payload,meta:e.meta.baseQueryMeta}),delete m.valueResolved);else if(t.internalActions.removeQueryResult.match(e)||t.internalActions.removeMutationResult.match(e))(m=s[d])&&(delete s[d],m.cacheEntryRemoved());else if(t.util.resetApiState.match(e))for(var h=0,y=Object.entries(s);h<y.length;h++){var g=y[h],m=g[1];delete s[g[0]],m.cacheEntryRemoved();}}},Ft=function(e){var t=e.api,n=e.context,r=e.queryThunk,i=e.mutationThunk,a=We(r,i),u=Be(r,i),o=Je(r,i),c={};return function(e,r){var i,s,f;if(a(e)){var l=e.meta,d=l.requestId,h=l.arg,y=h.endpointName,g=h.originalArgs,m=n.endpointDefinitions[y],b=null==m?void 0:m.onQueryStarted;if(b){var O={},w=new Promise((function(e,t){O.resolve=e,O.reject=t;}));w.catch((function(){})),c[d]=O;var S=t.endpoints[y].select(m.type===rt.query?g:d),j=r.dispatch((function(e,t,n){return n})),q=v(p({},r),{getCacheEntry:function(){return S(r.getState())},requestId:d,extra:j,updateCachedData:m.type===rt.query?function(e){return r.dispatch(t.util.updateQueryData(y,g,e))}:void 0,queryFulfilled:w});b(g,q);}}else if(o(e)){var A=e.meta,R=A.baseQueryMeta;null==(i=c[d=A.requestId])||i.resolve({data:e.payload,meta:R}),delete c[d];}else if(u(e)){var P=e.meta;R=P.baseQueryMeta,null==(f=c[d=P.requestId])||f.reject({error:null!=(s=e.payload)?s:e.error,isUnhandledError:!P.rejectedWithValue,meta:R}),delete c[d];}}},zt=function(e){var t=e.api,n=e.context.apiUid;return function(e,r){t.util.resetApiState.match(e)&&r.dispatch(t.internalActions.middlewareRegistered(n));}},Ut="function"==typeof queueMicrotask?queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis):function(e){return (Nt||(Nt=Promise.resolve())).then(e).catch((function(e){return setTimeout((function(){throw e}),0)}))};function Wt(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];Object.assign.apply(Object,a([e],t));}var Bt=Symbol(),Lt=function(){return {name:Bt,init:function(e,n,r){var u=n.baseQuery,o=n.reducerPath,c=n.serializeQueryArgs,s=n.keepUnusedDataFor,f=n.refetchOnMountOrArgChange,l=n.refetchOnFocus,d=n.refetchOnReconnect;!function(){function e(t){if(!S(t))return t;if(Array.isArray(t))return t.map(e);if(k(t))return new Map(Array.from(t.entries()).map((function(t){return [t[0],e(t[1])]})));if(x(t))return new Set(Array.from(t).map(e));var n=Object.create(Object.getPrototypeOf(t));for(var r in t)n[r]=e(t[r]);return A(t,ae)&&(n[ae]=t[ae]),n}function t(t){return w(t)?e(t):t}var n="add";D("Patches",{$:function(t,r){return r.forEach((function(r){for(var i=r.path,a=r.op,u=t,o=0;o<i.length-1;o++){var c=q(u),s=i[o];"string"!=typeof s&&"number"!=typeof s&&(s=""+s),0!==c&&1!==c||"__proto__"!==s&&"constructor"!==s||O(24),"function"==typeof u&&"prototype"===s&&O(24),"object"!=typeof(u=R(u,s))&&O(15,i.join("/"));}var f=q(u),l=e(r.value),d=i[i.length-1];switch(a){case"replace":switch(f){case 2:return u.set(d,l);case 3:O(16);default:return u[d]=l}case n:switch(f){case 1:return "-"===d?u.push(l):u.splice(d,0,l);case 2:return u.set(d,l);case 3:return u.add(l);default:return u[d]=l}case"remove":switch(f){case 1:return u.splice(d,1);case 2:return u.delete(d);case 3:return u.delete(r.value);default:return delete u[d]}default:O(17,a);}})),t},N:function(e,r,i,a){switch(e.i){case 0:case 4:case 2:return o=r,c=i,s=a,f=(u=e).t,l=u.o,void j(u.R,(function(e,r){var i=R(f,e),a=R(l,e),u=r?A(f,e)?"replace":n:"remove";if(i!==a||"replace"!==u){var d=o.concat(e);c.push("remove"===u?{op:u,path:d}:{op:u,path:d,value:a}),s.push(u===n?{op:"remove",path:d}:"remove"===u?{op:n,path:d,value:t(i)}:{op:"replace",path:d,value:t(i)});}}));case 5:case 1:return function(e,r,i,a){var u=e.t,o=e.R,c=e.o;if(c.length<u.length){var s=[c,u];u=s[0],c=s[1];var f=[a,i];i=f[0],a=f[1];}for(var l=0;l<u.length;l++)if(o[l]&&c[l]!==u[l]){var d=r.concat([l]);i.push({op:"replace",path:d,value:t(c[l])}),a.push({op:"replace",path:d,value:t(u[l])});}for(var p=u.length;p<c.length;p++){var v=r.concat([p]);i.push({op:n,path:v,value:t(c[p])});}u.length<c.length&&a.push({op:"replace",path:r.concat(["length"]),value:u.length});}(e,r,i,a);case 3:return function(e,t,r,i){var a=e.t,u=e.o,o=0;a.forEach((function(e){if(!u.has(e)){var a=t.concat([o]);r.push({op:"remove",path:a,value:e}),i.unshift({op:n,path:a,value:e});}o++;})),o=0,u.forEach((function(e){if(!a.has(e)){var u=t.concat([o]);r.push({op:n,path:u,value:e}),i.unshift({op:"remove",path:u,value:e});}o++;}));}(e,r,i,a)}var u,o,c,s,f,l;},M:function(e,t,n,r){n.push({op:"replace",path:[],value:t===ie?void 0:t}),r.push({op:"replace",path:[],value:e});}});}();var h=function(e){return e};Object.assign(e,{reducerPath:o,endpoints:{},internalActions:{onOnline:st,onOffline:ft,onFocus:ot,onFocusLost:ct},util:{}});var g=function(e){var n=this,r=e.reducerPath,a=e.baseQuery,u=e.context.endpointDefinitions,o=e.serializeQueryArgs,c=e.api,s=e.assertTagType,f=function(e,t){return y(n,[e,t],(function(e,t){var n,r,o,c,s,f,d,p,v,h,y,g,m,b=t.signal,O=t.abort,w=t.rejectWithValue,S=t.fulfillWithValue,j=t.dispatch,q=t.getState,A=t.extra;return i(this,(function(t){switch(t.label){case 0:n=u[e.endpointName],t.label=1;case 1:return t.trys.push([1,8,,13]),r=bt,o=void 0,c={signal:b,abort:O,dispatch:j,getState:q,extra:A,endpoint:e.endpointName,type:e.type,forced:"query"===e.type?l(e,q()):void 0},(s="query"===e.type?e[gt]:void 0)?(o=s(),[3,6]):[3,2];case 2:return n.query?[4,a(n.query(e.originalArgs),c,n.extraOptions)]:[3,4];case 3:return o=t.sent(),n.transformResponse&&(r=n.transformResponse),[3,6];case 4:return [4,n.queryFn(e.originalArgs,c,n.extraOptions,(function(e){return a(e,c,n.extraOptions)}))];case 5:o=t.sent(),t.label=6;case 6:if(o.error)throw new tt(o.error,o.meta);return f=S,[4,r(o.data,o.meta,e.originalArgs)];case 7:return [2,f.apply(void 0,[t.sent(),(g={fulfilledTimeStamp:Date.now(),baseQueryMeta:o.meta},g.RTK_autoBatch=!0,g)])];case 8:if(d=t.sent(),!((p=d)instanceof tt))return [3,12];v=bt,n.query&&n.transformErrorResponse&&(v=n.transformErrorResponse),t.label=9;case 9:return t.trys.push([9,11,,12]),h=w,[4,v(p.value,p.meta,e.originalArgs)];case 10:return [2,h.apply(void 0,[t.sent(),(m={baseQueryMeta:p.meta},m.RTK_autoBatch=!0,m)])];case 11:return y=t.sent(),p=y,[3,12];case 12:throw console.error(p),p;case 13:return [2]}}))}))};function l(e,t){var n,i,a,u,o=null==(i=null==(n=t[r])?void 0:n.queries)?void 0:i[e.queryCacheKey],c=null==(a=t[r])?void 0:a.config.refetchOnMountOrArgChange,s=null==o?void 0:o.fulfilledTimeStamp,f=null!=(u=e.forceRefetch)?u:e.subscribe&&c;return !!f&&(!0===f||(Number(new Date)-Number(s))/1e3>=f)}var d=Me(r+"/executeQuery",f,{getPendingMeta:function(){var e;return (e={startedTimeStamp:Date.now()}).RTK_autoBatch=!0,e},condition:function(e,t){var n,i,a,o=(0, t.getState)(),c=null==(i=null==(n=o[r])?void 0:n.queries)?void 0:i[e.queryCacheKey],s=null==c?void 0:c.fulfilledTimeStamp,f=e.originalArgs,d=null==c?void 0:c.originalArgs,p=u[e.endpointName];return !(!mt(e)&&("pending"===(null==c?void 0:c.status)||!l(e,o)&&(!pt(p)||!(null==(a=null==p?void 0:p.forceRefetch)?void 0:a.call(p,{currentArg:f,previousArg:d,endpointState:c,state:o})))&&s))},dispatchConditionRejection:!0}),p=Me(r+"/executeMutation",f,{getPendingMeta:function(){var e;return (e={startedTimeStamp:Date.now()}).RTK_autoBatch=!0,e}});function v(e){return function(t){var n,r;return (null==(r=null==(n=null==t?void 0:t.meta)?void 0:n.arg)?void 0:r.endpointName)===e}}return {queryThunk:d,mutationThunk:p,prefetch:function(e,t,n){return function(r,i){var a=function(e){return "force"in e}(n)&&n.force,u=function(e){return "ifOlderThan"in e}(n)&&n.ifOlderThan,o=function(n){return void 0===n&&(n=!0),c.endpoints[e].initiate(t,{forceRefetch:n})},s=c.endpoints[e].select(t)(i());if(a)r(o());else if(u){var f=null==s?void 0:s.fulfilledTimeStamp;if(!f)return void r(o());(Number(new Date)-Number(new Date(f)))/1e3>=u&&r(o());}else r(o(!1));}},updateQueryData:function(e,n,r,i){return void 0===i&&(i=!0),function(a,u){var o,s,f,l=c.endpoints[e].select(n)(u()),d={patches:[],inversePatches:[],undo:function(){return a(c.util.patchQueryData(e,n,d.inversePatches,i))}};if(l.status===exports.QueryStatus.uninitialized)return d;if("data"in l)if(S(l.data)){var p=he(l.data,r),v=p[0],h=p[2];(o=d.patches).push.apply(o,p[1]),(s=d.inversePatches).push.apply(s,h),f=v;}else f=r(l.data),d.patches.push({op:"replace",path:[],value:f}),d.inversePatches.push({op:"replace",path:[],value:l.data});return a(c.util.patchQueryData(e,n,d.patches,i)),d}},upsertQueryData:function(e,t,n){return function(r){var i;return r(c.endpoints[e].initiate(t,((i={subscribe:!1,forceRefetch:!0})[gt]=function(){return {data:n}},i)))}},patchQueryData:function(e,t,n,r){return function(i,a){var f=u[e],l=o({queryArgs:t,endpointDefinition:f,endpointName:e});if(i(c.internalActions.queryResultPatched({queryCacheKey:l,patches:n})),r){var d=c.endpoints[e].select(t)(a()),p=vt(f.providesTags,d.data,void 0,t,{},s);i(c.internalActions.updateProvidedBy({queryCacheKey:l,providedTags:p}));}}},buildMatchThunkActions:function(e,t){return {matchPending:Fe(We(e),v(t)),matchFulfilled:Fe(Je(e),v(t)),matchRejected:Fe(Be(e),v(t))}}}}({baseQuery:u,reducerPath:o,context:r,api:e,serializeQueryArgs:c,assertTagType:h}),m=g.queryThunk,P=g.mutationThunk,T=g.patchQueryData,I=g.updateQueryData,C=g.upsertQueryData,E=g.prefetch,N=g.buildMatchThunkActions,Q=function(e){var n=e.reducerPath,r=e.queryThunk,i=e.mutationThunk,a=e.context,u=a.endpointDefinitions,o=a.apiUid,c=a.extractRehydrationInfo,s=a.hasRehydrationInfo,f=e.assertTagType,l=e.config,d=Pe(n+"/resetApiState"),h=xe({name:n+"/queries",initialState:qt,reducers:{removeQueryResult:{reducer:function(e,t){delete e[t.payload.queryCacheKey];},prepare:function(e){var t;return {payload:e,meta:(t={},t.RTK_autoBatch=!0,t)}}},queryResultPatched:{reducer:function(e,t){var n=t.payload,r=n.patches;wt(e,n.queryCacheKey,(function(e){e.data=ye(e.data,r.concat());}));},prepare:function(e){var t;return {payload:e,meta:(t={},t.RTK_autoBatch=!0,t)}}}},extraReducers:function(e){e.addCase(r.pending,(function(e,n){var r,i=n.meta,a=n.meta.arg,u=mt(a);(a.subscribe||u)&&(null!=e[r=a.queryCacheKey]||(e[r]={status:exports.QueryStatus.uninitialized,endpointName:a.endpointName})),wt(e,a.queryCacheKey,(function(e){e.status=exports.QueryStatus.pending,e.requestId=u&&e.requestId?e.requestId:i.requestId,void 0!==a.originalArgs&&(e.originalArgs=a.originalArgs),e.startedTimeStamp=i.startedTimeStamp;}));})).addCase(r.fulfilled,(function(e,n){var r=n.meta,i=n.payload;wt(e,r.arg.queryCacheKey,(function(e){var n;if(e.requestId===r.requestId||mt(r.arg)){var a,o=u[r.arg.endpointName].merge;if(e.status=exports.QueryStatus.fulfilled,o)if(void 0!==e.data){var c=r.fulfilledTimeStamp,s=r.arg,f=r.baseQueryMeta,l=r.requestId,d=ge(e.data,(function(e){return o(e,i,{arg:s.originalArgs,baseQueryMeta:f,fulfilledTimeStamp:c,requestId:l})}));e.data=d;}else e.data=i;else e.data=null==(n=u[r.arg.endpointName].structuralSharing)||n?Ge(w(e.data)?(w(a=e.data)||O(23,a),a[ue].t):e.data,i):i;delete e.error,e.fulfilledTimeStamp=r.fulfilledTimeStamp;}}));})).addCase(r.rejected,(function(e,n){var r=n.meta,i=r.condition,a=r.requestId,u=n.error,o=n.payload;wt(e,r.arg.queryCacheKey,(function(e){if(i);else {if(e.requestId!==a)return;e.status=exports.QueryStatus.rejected,e.error=null!=o?o:u;}}));})).addMatcher(s,(function(e,n){for(var r=c(n).queries,i=0,a=Object.entries(r);i<a.length;i++){var u=a[i],o=u[1];(null==o?void 0:o.status)!==exports.QueryStatus.fulfilled&&(null==o?void 0:o.status)!==exports.QueryStatus.rejected||(e[u[0]]=o);}}));}}),y=xe({name:n+"/mutations",initialState:qt,reducers:{removeMutationResult:{reducer:function(e,t){var n=St(t.payload);n in e&&delete e[n];},prepare:function(e){var t;return {payload:e,meta:(t={},t.RTK_autoBatch=!0,t)}}}},extraReducers:function(e){e.addCase(i.pending,(function(e,n){var r=n.meta,i=r.requestId,a=r.arg,u=r.startedTimeStamp;a.track&&(e[St(n.meta)]={requestId:i,status:exports.QueryStatus.pending,endpointName:a.endpointName,startedTimeStamp:u});})).addCase(i.fulfilled,(function(e,n){var r=n.payload,i=n.meta;i.arg.track&&jt(e,i,(function(e){e.requestId===i.requestId&&(e.status=exports.QueryStatus.fulfilled,e.data=r,e.fulfilledTimeStamp=i.fulfilledTimeStamp);}));})).addCase(i.rejected,(function(e,n){var r=n.payload,i=n.error,a=n.meta;a.arg.track&&jt(e,a,(function(e){e.requestId===a.requestId&&(e.status=exports.QueryStatus.rejected,e.error=null!=r?r:i);}));})).addMatcher(s,(function(e,n){for(var r=c(n).mutations,i=0,a=Object.entries(r);i<a.length;i++){var u=a[i],o=u[0],s=u[1];(null==s?void 0:s.status)!==exports.QueryStatus.fulfilled&&(null==s?void 0:s.status)!==exports.QueryStatus.rejected||o===(null==s?void 0:s.requestId)||(e[o]=s);}}));}}),g=xe({name:n+"/invalidation",initialState:qt,reducers:{updateProvidedBy:{reducer:function(e,t){for(var n,r,i,a,u=t.payload,o=u.queryCacheKey,c=u.providedTags,s=0,f=Object.values(e);s<f.length;s++)for(var l=0,d=Object.values(f[s]);l<d.length;l++){var p=d[l],v=p.indexOf(o);-1!==v&&p.splice(v,1);}for(var h=0,y=c;h<y.length;h++){var g=y[h],m=g.type,b=g.id,O=null!=(a=(r=null!=(n=e[m])?n:e[m]={})[i=b||"__internal_without_id"])?a:r[i]=[];O.includes(o)||O.push(o);}},prepare:function(e){var t;return {payload:e,meta:(t={},t.RTK_autoBatch=!0,t)}}}},extraReducers:function(e){e.addCase(h.actions.removeQueryResult,(function(e,t){for(var n=t.payload.queryCacheKey,r=0,i=Object.values(e);r<i.length;r++)for(var a=0,u=Object.values(i[r]);a<u.length;a++){var o=u[a],c=o.indexOf(n);-1!==c&&o.splice(c,1);}})).addMatcher(s,(function(e,t){for(var n,r,i,a,u=c(t).provided,o=0,s=Object.entries(u);o<s.length;o++)for(var f=s[o],l=f[0],d=0,p=Object.entries(f[1]);d<p.length;d++)for(var v=p[d],h=v[0],y=v[1],g=null!=(a=(r=null!=(n=e[l])?n:e[l]={})[i=h||"__internal_without_id"])?a:r[i]=[],m=0,b=y;m<b.length;m++){var O=b[m];g.includes(O)||g.push(O);}})).addMatcher(Ke(Je(r),Le(r)),(function(e,t){var n=Ot(t,"providesTags",u,f);g.caseReducers.updateProvidedBy(e,g.actions.updateProvidedBy({queryCacheKey:t.meta.arg.queryCacheKey,providedTags:n}));}));}}),m=xe({name:n+"/subscriptions",initialState:qt,reducers:{updateSubscriptionOptions:function(e,t){},unsubscribeQueryResult:function(e,t){},internal_probeSubscription:function(e,t){}}}),b=xe({name:n+"/internalSubscriptions",initialState:qt,reducers:{subscriptionsUpdated:{reducer:function(e,t){return ye(e,t.payload)},prepare:function(e){var t;return {payload:e,meta:(t={},t.RTK_autoBatch=!0,t)}}}}}),S=xe({name:n+"/config",initialState:p({online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine,focused:"undefined"==typeof document||"hidden"!==document.visibilityState,middlewareRegistered:!1},l),reducers:{middlewareRegistered:function(e,t){e.middlewareRegistered="conflict"!==e.middlewareRegistered&&o===t.payload||"conflict";}},extraReducers:function(e){e.addCase(st,(function(e){e.online=!0;})).addCase(ft,(function(e){e.online=!1;})).addCase(ot,(function(e){e.focused=!0;})).addCase(ct,(function(e){e.focused=!1;})).addMatcher(s,(function(e){return p({},e)}));}}),j=function(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var i=t[r];"function"==typeof e[i]&&(n[i]=e[i]);}var a,u=Object.keys(n);try{!function(e){Object.keys(e).forEach((function(t){var n=e[t];if(void 0===n(void 0,{type:Oe.INIT}))throw new Error(me(12));if(void 0===n(void 0,{type:Oe.PROBE_UNKNOWN_ACTION()}))throw new Error(me(13))}));}(n);}catch(e){a=e;}return function(e,t){if(void 0===e&&(e={}),a)throw a;for(var r=!1,i={},o=0;o<u.length;o++){var c=u[o],s=e[c],f=(0, n[c])(s,t);if(void 0===f)throw new Error(me(14));i[c]=f,r=r||f!==s;}return (r=r||u.length!==Object.keys(e).length)?i:e}}({queries:h.reducer,mutations:y.reducer,provided:g.reducer,subscriptions:b.reducer,config:S.reducer});return {reducer:function(e,t){return j(d.match(t)?void 0:e,t)},actions:v(p(p(p(p(p(p({},S.actions),h.actions),m.actions),b.actions),y.actions),g.actions),{unsubscribeMutationResult:y.actions.removeMutationResult,resetApiState:d})}}({context:r,queryThunk:m,mutationThunk:P,reducerPath:o,assertTagType:h,config:{refetchOnFocus:l,refetchOnReconnect:d,refetchOnMountOrArgChange:f,keepUnusedDataFor:s,reducerPath:o}}),M=Q.reducer,_=Q.actions;Wt(e.util,{patchQueryData:T,updateQueryData:I,upsertQueryData:C,prefetch:E,resetApiState:_.resetApiState}),Wt(e.internalActions,_);var K=function(e){var n=e.reducerPath,r=e.queryThunk,i=e.api,a=e.context,u=a.apiUid,o={invalidateTags:Pe(n+"/invalidateTags")},c=[zt,Qt,Mt,Dt,Kt,Ft];return {middleware:function(r){var o=!1,f=v(p({},e),{internalState:{currentSubscriptions:{}},refetchQuery:s}),l=c.map((function(e){return e(f)})),d=function(e){var t=e.api,n=e.queryThunk,r=e.internalState,i=t.reducerPath+"/subscriptions",a=null,u=!1,o=t.internalActions,c=o.updateSubscriptionOptions,s=o.unsubscribeQueryResult;return function(e,o){var f,l;if(a||(a=JSON.parse(JSON.stringify(r.currentSubscriptions))),t.util.resetApiState.match(e))return a=r.currentSubscriptions={},[!0,!1];if(t.internalActions.internal_probeSubscription.match(e)){var d=e.payload;return [!1,!!(null==(f=r.currentSubscriptions[d.queryCacheKey])?void 0:f[d.requestId])]}var p=function(e,r){var i,a,u,o,f,l,d,p,v;if(c.match(r)){var h=r.payload,y=h.queryCacheKey,g=h.requestId;return (null==(i=null==e?void 0:e[y])?void 0:i[g])&&(e[y][g]=h.options),!0}if(s.match(r)){var m=r.payload;return g=m.requestId,e[y=m.queryCacheKey]&&delete e[y][g],!0}if(t.internalActions.removeQueryResult.match(r))return delete e[r.payload.queryCacheKey],!0;if(n.pending.match(r)){var b=r.meta;if(g=b.requestId,(S=b.arg).subscribe)return (O=null!=(u=e[a=S.queryCacheKey])?u:e[a]={})[g]=null!=(f=null!=(o=S.subscriptionOptions)?o:O[g])?f:{},!0}if(n.rejected.match(r)){var O,w=r.meta,S=w.arg;if(g=w.requestId,w.condition&&S.subscribe)return (O=null!=(d=e[l=S.queryCacheKey])?d:e[l]={})[g]=null!=(v=null!=(p=S.subscriptionOptions)?p:O[g])?v:{},!0}return !1}(r.currentSubscriptions,e);if(p){u||(Ut((function(){var e=JSON.parse(JSON.stringify(r.currentSubscriptions)),n=he(a,(function(){return e}));o.next(t.internalActions.subscriptionsUpdated(n[1])),a=e,u=!1;})),u=!0);var v=!!(null==(l=e.type)?void 0:l.startsWith(i)),h=n.rejected.match(e)&&e.meta.condition&&!!e.meta.arg.subscribe;return [!v&&!h,!1]}return [!0,!1]}}(f),h=function(e){var n=e.reducerPath,r=e.context,i=e.refetchQuery,a=e.internalState,u=e.api.internalActions.removeQueryResult;function o(e,o){var c=e.getState()[n],s=c.queries,f=a.currentSubscriptions;r.batch((function(){for(var n=0,r=Object.keys(f);n<r.length;n++){var a=r[n],l=s[a],d=f[a];d&&l&&(Object.values(d).some((function(e){return !0===e[o]}))||Object.values(d).every((function(e){return void 0===e[o]}))&&c.config[o])&&(0===Object.keys(d).length?e.dispatch(u({queryCacheKey:a})):l.status!==exports.QueryStatus.uninitialized&&e.dispatch(i(l,a)));}}));}return function(e,t){ot.match(e)&&o(t,"refetchOnFocus"),st.match(e)&&o(t,"refetchOnReconnect");}}(f);return function(e){return function(t){o||(o=!0,r.dispatch(i.internalActions.middlewareRegistered(u)));var c,s=v(p({},r),{next:e}),f=r.getState(),y=d(t,s,f),g=y[1];if(c=y[0]?e(t):g,r.getState()[n]&&(h(t,s,f),function(e){return !!e&&"string"==typeof e.type&&e.type.startsWith(n+"/")}(t)||a.hasRehydrationInfo(t)))for(var m=0,b=l;m<b.length;m++)(0, b[m])(t,s,f);return c}}},actions:o};function s(e,t,n){return void 0===n&&(n={}),r(p({type:"query",endpointName:e.endpointName,originalArgs:e.originalArgs,subscribe:!1,forceRefetch:!0,queryCacheKey:t},n))}}({reducerPath:o,context:r,queryThunk:m,mutationThunk:P,api:e,assertTagType:h}),F=K.middleware;Wt(e.util,K.actions),Wt(e,{reducer:M,middleware:F});var z=function(e){var n=e.serializeQueryArgs,r=e.reducerPath,i=function(e){return Tt},a=function(e){return kt};return {buildQuerySelector:function(e,t){return function(r){var a=n({queryArgs:r,endpointDefinition:t,endpointName:e});return Ae(r===At?i:function(e){var t,n,r;return null!=(r=null==(n=null==(t=o(e))?void 0:t.queries)?void 0:n[a])?r:Tt},u)}},buildMutationSelector:function(){return function(e){var t,n;return n="object"==typeof e?null!=(t=St(e))?t:At:e,Ae(n===At?a:function(e){var t,r,i;return null!=(i=null==(r=null==(t=o(e))?void 0:t.mutations)?void 0:r[n])?i:kt},u)}},selectInvalidatedBy:function(e,t){for(var n,i=e[r],a=new Set,u=0,o=t.map(ht);u<o.length;u++){var c=o[u],s=i.provided[c.type];if(s)for(var f=0,l=null!=(n=void 0!==c.id?s[c.id]:b(Object.values(s)))?n:[];f<l.length;f++)a.add(l[f]);}return b(Array.from(a.values()).map((function(e){var t=i.queries[e];return t?[{queryCacheKey:e,endpointName:t.endpointName,originalArgs:t.originalArgs}]:[]})))}};function u(e){return p(p({},e),{status:n=e.status,isUninitialized:n===exports.QueryStatus.uninitialized,isLoading:n===exports.QueryStatus.pending,isSuccess:n===exports.QueryStatus.fulfilled,isError:n===exports.QueryStatus.rejected});var n;}function o(e){return e[r]}}({serializeQueryArgs:c,reducerPath:o}),U=z.buildQuerySelector,W=z.buildMutationSelector;Wt(e.util,{selectInvalidatedBy:z.selectInvalidatedBy});var B=function(e){var t=e.serializeQueryArgs,n=e.queryThunk,r=e.mutationThunk,u=e.api,o=e.context,c=new Map,s=new Map,f=u.internalActions,l=f.unsubscribeQueryResult,d=f.removeMutationResult,p=f.updateSubscriptionOptions;return {buildInitiateQuery:function(e,r){var a=function(o,s){var f=void 0===s?{}:s,d=f.subscribe,v=void 0===d||d,h=f.forceRefetch,g=f.subscriptionOptions,m=f[gt];return function(s,f){var d,b,O=t({queryArgs:o,endpointDefinition:r,endpointName:e}),w=n(((d={type:"query",subscribe:v,forceRefetch:h,subscriptionOptions:g,endpointName:e,originalArgs:o,queryCacheKey:O})[gt]=m,d)),S=u.endpoints[e].select(o),j=s(w),q=S(f()),A=j.requestId,R=j.abort,P=q.requestId!==A,T=null==(b=c.get(s))?void 0:b[O],k=function(){return S(f())},x=Object.assign(m?j.then(k):P&&!T?Promise.resolve(q):Promise.all([T,j]).then(k),{arg:o,requestId:A,subscriptionOptions:g,queryCacheKey:O,abort:R,unwrap:function(){return y(this,null,(function(){var e;return i(this,(function(t){switch(t.label){case 0:return [4,x];case 1:if((e=t.sent()).isError)throw e.error;return [2,e.data]}}))}))},refetch:function(){return s(a(o,{subscribe:!1,forceRefetch:!0}))},unsubscribe:function(){v&&s(l({queryCacheKey:O,requestId:A}));},updateSubscriptionOptions:function(t){x.subscriptionOptions=t,s(p({endpointName:e,requestId:A,queryCacheKey:O,options:t}));}});if(!T&&!P&&!m){var I=c.get(s)||{};I[O]=x,c.set(s,I),x.then((function(){delete I[O],Object.keys(I).length||c.delete(s);}));}return x}};return a},buildInitiateMutation:function(e){return function(t,n){var i=void 0===n?{}:n,a=i.track,u=void 0===a||a,o=i.fixedCacheKey;return function(n,i){var a=r({type:"mutation",endpointName:e,originalArgs:t,track:u,fixedCacheKey:o}),c=n(a),f=c.requestId,l=c.abort,p=c.unwrap,v=c.unwrap().then((function(e){return {data:e}})).catch((function(e){return {error:e}})),h=function(){n(d({requestId:f,fixedCacheKey:o}));},y=Object.assign(v,{arg:c.arg,requestId:f,abort:l,unwrap:p,unsubscribe:h,reset:h}),g=s.get(n)||{};return s.set(n,g),g[f]=y,y.then((function(){delete g[f],Object.keys(g).length||s.delete(n);})),o&&(g[o]=y,y.then((function(){g[o]===y&&(delete g[o],Object.keys(g).length||s.delete(n));}))),y}}},getRunningQueryThunk:function(e,n){return function(r){var i,a=t({queryArgs:n,endpointDefinition:o.endpointDefinitions[e],endpointName:e});return null==(i=c.get(r))?void 0:i[a]}},getRunningMutationThunk:function(e,t){return function(e){var n;return null==(n=s.get(e))?void 0:n[t]}},getRunningQueriesThunk:function(){return function(e){return Object.values(c.get(e)||{}).filter(yt)}},getRunningMutationsThunk:function(){return function(e){return Object.values(s.get(e)||{}).filter(yt)}},getRunningOperationPromises:function(){var e=function(e){return Array.from(e.values()).flatMap((function(e){return e?Object.values(e):[]}))};return a(a([],e(c)),e(s)).filter(yt)},removalWarning:function(){throw new Error("This method had to be removed due to a conceptual bug in RTK.\n       Please see https://github.com/reduxjs/redux-toolkit/pull/2481 for details.\n       See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for new guidance on SSR.")}}}({queryThunk:m,mutationThunk:P,api:e,serializeQueryArgs:c,context:r}),L=B.buildInitiateQuery,J=B.buildInitiateMutation;return Wt(e.util,{getRunningOperationPromises:B.getRunningOperationPromises,getRunningOperationPromise:B.removalWarning,getRunningMutationThunk:B.getRunningMutationThunk,getRunningMutationsThunk:B.getRunningMutationsThunk,getRunningQueryThunk:B.getRunningQueryThunk,getRunningQueriesThunk:B.getRunningQueriesThunk}),{name:Bt,injectEndpoint:function(t,n){var r,i=e;null!=(r=i.endpoints)[t]||(r[t]={}),pt(n)?Wt(i.endpoints[t],{name:t,select:U(t,n),initiate:L(t,n)},N(m,t)):n.type===rt.mutation&&Wt(i.endpoints[t],{name:t,select:W(),initiate:J(t)},N(P,t));}}}}},Jt=Ct(Lt());

	exports.buildCreateApi = Ct;
	exports.copyWithStructuralSharing = Ge;
	exports.coreModule = Lt;
	exports.coreModuleName = Bt;
	exports.createApi = Jt;
	exports.defaultSerializeQueryArgs = It;
	exports.fakeBaseQuery = Et;
	exports.fetchBaseQuery = et;
	exports.retry = ut;
	exports.setupListeners = dt;
	exports.skipSelector = Rt;
	exports.skipToken = At;

	Object.defineProperty(exports, '__esModule', { value: true });

}));
//# sourceMappingURL=rtk-query.umd.min.js.map
