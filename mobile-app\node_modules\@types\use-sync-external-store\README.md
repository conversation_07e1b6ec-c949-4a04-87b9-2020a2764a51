# Installation
> `npm install --save @types/use-sync-external-store`

# Summary
This package contains type definitions for use-sync-external-store (https://github.com/facebook/react#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store.

### Additional Details
 * Last updated: Wed, 03 Nov 2021 17:31:24 GMT
 * Dependencies: none
 * Global values: none

# Credits
These definitions were written by [eps1lon](https://github.com/eps1lon), and [<PERSON>](https://github.com/markerikson).
