{"version": 3, "names": ["MULTIPLE_NAVIGATOR_ERROR", "SingleNavigatorContext", "React", "createContext", "undefined", "EnsureSingleNavigator", "children", "navigator<PERSON><PERSON><PERSON><PERSON>", "useRef", "value", "useMemo", "register", "key", "current<PERSON><PERSON>", "current", "Error", "unregister"], "sourceRoot": "../../src", "sources": ["EnsureSingleNavigator.tsx"], "mappings": ";;;;;;;AAAA;AAA+B;AAAA;AAM/B,MAAMA,wBAAwB,GAAI,oSAAmS;AAE9T,MAAMC,sBAAsB,gBAAGC,KAAK,CAACC,aAAa,CAMvDC,SAAS,CAAC;;AAEZ;AACA;AACA;AAFA;AAGe,SAASC,qBAAqB,OAAsB;EAAA,IAArB;IAAEC;EAAgB,CAAC;EAC/D,MAAMC,eAAe,GAAGL,KAAK,CAACM,MAAM,EAAsB;EAE1D,MAAMC,KAAK,GAAGP,KAAK,CAACQ,OAAO,CACzB,OAAO;IACLC,QAAQ,CAACC,GAAW,EAAE;MACpB,MAAMC,UAAU,GAAGN,eAAe,CAACO,OAAO;MAE1C,IAAID,UAAU,KAAKT,SAAS,IAAIQ,GAAG,KAAKC,UAAU,EAAE;QAClD,MAAM,IAAIE,KAAK,CAACf,wBAAwB,CAAC;MAC3C;MAEAO,eAAe,CAACO,OAAO,GAAGF,GAAG;IAC/B,CAAC;IACDI,UAAU,CAACJ,GAAW,EAAE;MACtB,MAAMC,UAAU,GAAGN,eAAe,CAACO,OAAO;MAE1C,IAAIF,GAAG,KAAKC,UAAU,EAAE;QACtB;MACF;MAEAN,eAAe,CAACO,OAAO,GAAGV,SAAS;IACrC;EACF,CAAC,CAAC,EACF,EAAE,CACH;EAED,oBACE,oBAAC,sBAAsB,CAAC,QAAQ;IAAC,KAAK,EAAEK;EAAM,GAC3CH,QAAQ,CACuB;AAEtC"}