/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type {SyncTransformer} from '@jest/transform';
import type {TransformerCreator} from '@jest/transform';
import {TransformOptions} from '@babel/core';

export declare const createTransformer: TransformerCreator<
  SyncTransformer<TransformOptions>,
  TransformOptions
>;

declare const transformerFactory: {
  createTransformer: TransformerCreator<
    SyncTransformer<TransformOptions>,
    TransformOptions
  >;
};
export default transformerFactory;

export {};
