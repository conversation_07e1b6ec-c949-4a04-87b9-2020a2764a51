{"version": 3, "names": ["setEnvironment", "variable", "value", "command", "executeCommand", "process", "env", "updateEnvironment", "envVariable", "includes"], "sources": ["../../../src/tools/windows/environmentVariables.ts"], "sourcesContent": ["import {executeCommand} from './executeWinCommand';\n\n/**\n * Creates a new variable in the user's environment\n */\nconst setEnvironment = async (variable: string, value: string) => {\n  // https://superuser.com/a/601034\n  const command = `setx ${variable} \"${value}\"`;\n\n  await executeCommand(command);\n\n  process.env[variable] = value;\n};\n\n/**\n * Prepends the given `value` to the user's environment `variable`.\n * @param {string} variable The environment variable to modify\n * @param {string} value The value to add to the variable\n * @returns {Promise<void>}\n */\nconst updateEnvironment = async (variable: string, value: string) => {\n  // Avoid adding the value multiple times to PATH\n  // Need to do the following to avoid TSLint complaining about possible\n  // undefined values even if I check before via `typeof` or another way\n  const envVariable = process.env[variable] || '';\n  if (variable === 'PATH' && envVariable.includes(`${value};`)) {\n    return;\n  }\n  // https://superuser.com/a/601034\n  const command = `for /f \"skip=2 tokens=3*\" %a in ('reg query HKCU\\\\Environment /v ${variable}') do @if [%b]==[] ( @setx ${variable} \"${value};%~a\" ) else ( @setx ${variable} \"${value};%~a %~b\" )\n  `;\n\n  await executeCommand(command);\n\n  process.env[variable] = `${process.env[variable]}${value};`;\n};\n\nexport {setEnvironment, updateEnvironment};\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;AACA;AACA,MAAMA,cAAc,GAAG,OAAOC,QAAgB,EAAEC,KAAa,KAAK;EAChE;EACA,MAAMC,OAAO,GAAI,QAAOF,QAAS,KAAIC,KAAM,GAAE;EAE7C,MAAM,IAAAE,iCAAc,EAACD,OAAO,CAAC;EAE7BE,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC,GAAGC,KAAK;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA;AAMA,MAAMK,iBAAiB,GAAG,OAAON,QAAgB,EAAEC,KAAa,KAAK;EACnE;EACA;EACA;EACA,MAAMM,WAAW,GAAGH,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC,IAAI,EAAE;EAC/C,IAAIA,QAAQ,KAAK,MAAM,IAAIO,WAAW,CAACC,QAAQ,CAAE,GAAEP,KAAM,GAAE,CAAC,EAAE;IAC5D;EACF;EACA;EACA,MAAMC,OAAO,GAAI,oEAAmEF,QAAS,8BAA6BA,QAAS,KAAIC,KAAM,wBAAuBD,QAAS,KAAIC,KAAM;AACzL,GAAG;EAED,MAAM,IAAAE,iCAAc,EAACD,OAAO,CAAC;EAE7BE,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC,GAAI,GAAEI,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAE,GAAEC,KAAM,GAAE;AAC7D,CAAC;AAAC"}