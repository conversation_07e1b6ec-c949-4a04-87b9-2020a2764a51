{"version": 3, "names": ["findDependencies", "root", "pjson", "JSON", "parse", "fs", "readFileSync", "path", "join", "e", "deps", "Object", "keys", "dependencies", "devDependencies"], "sources": ["../src/findDependencies.ts"], "sourcesContent": ["import path from 'path';\nimport fs from 'fs';\n\n/**\n * Returns an array of dependencies from project's package.json\n */\nexport default function findDependencies(root: string): Array<string> {\n  let pjson;\n\n  try {\n    pjson = JSON.parse(\n      fs.readFileSync(path.join(root, 'package.json'), 'utf8'),\n    );\n  } catch (e) {\n    return [];\n  }\n\n  const deps = [\n    ...Object.keys(pjson.dependencies || {}),\n    ...Object.keys(pjson.devDependencies || {}),\n  ];\n\n  return deps;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAoB;AAEpB;AACA;AACA;AACe,SAASA,gBAAgB,CAACC,IAAY,EAAiB;EACpE,IAAIC,KAAK;EAET,IAAI;IACFA,KAAK,GAAGC,IAAI,CAACC,KAAK,CAChBC,aAAE,CAACC,YAAY,CAACC,eAAI,CAACC,IAAI,CAACP,IAAI,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CACzD;EACH,CAAC,CAAC,OAAOQ,CAAC,EAAE;IACV,OAAO,EAAE;EACX;EAEA,MAAMC,IAAI,GAAG,CACX,GAAGC,MAAM,CAACC,IAAI,CAACV,KAAK,CAACW,YAAY,IAAI,CAAC,CAAC,CAAC,EACxC,GAAGF,MAAM,CAACC,IAAI,CAACV,KAAK,CAACY,eAAe,IAAI,CAAC,CAAC,CAAC,CAC5C;EAED,OAAOJ,IAAI;AACb"}