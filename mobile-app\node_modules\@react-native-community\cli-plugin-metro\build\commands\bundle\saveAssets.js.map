{"version": 3, "names": ["saveAssets", "assets", "platform", "assetsDest", "assetCatalogDest", "logger", "warn", "filesToCopy", "Object", "create", "getAssetDestPath", "getAssetDestPathAndroid", "getAssetDestPathIOS", "addAssetToCopy", "asset", "validScales", "Set", "filterPlatformAssetScales", "scales", "for<PERSON>ach", "scale", "idx", "has", "src", "files", "dest", "path", "join", "catalogDir", "fs", "existsSync", "error", "info", "cleanAssetCatalog", "isCatalogAsset", "imageSet", "getImageSet", "writeImageSet", "copyAll", "queue", "keys", "length", "Promise", "resolve", "reject", "copyNext", "shift", "copy", "callback", "destDir", "dirname", "mkdir", "recursive", "err", "createReadStream", "pipe", "createWriteStream", "on"], "sources": ["../../../src/commands/bundle/saveAssets.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {logger} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport type {AssetData} from 'metro';\nimport path from 'path';\nimport {\n  cleanAssetCatalog,\n  getImageSet,\n  isCatalogAsset,\n  writeImageSet,\n} from './assetCatalogIOS';\nimport filterPlatformAssetScales from './filterPlatformAssetScales';\nimport getAssetDestPathAndroid from './getAssetDestPathAndroid';\nimport getAssetDestPathIOS from './getAssetDestPathIOS';\n\ninterface CopiedFiles {\n  [src: string]: string;\n}\n\nfunction saveAssets(\n  assets: ReadonlyArray<AssetData>,\n  platform: string,\n  assetsDest: string | undefined,\n  assetCatalogDest: string | undefined,\n) {\n  if (!assetsDest) {\n    logger.warn('Assets destination folder is not set, skipping...');\n    return;\n  }\n\n  const filesToCopy: CopiedFiles = Object.create(null); // Map src -> dest\n\n  const getAssetDestPath =\n    platform === 'android' ? getAssetDestPathAndroid : getAssetDestPathIOS;\n\n  const addAssetToCopy = (asset: AssetData) => {\n    const validScales = new Set(\n      filterPlatformAssetScales(platform, asset.scales),\n    );\n\n    asset.scales.forEach((scale, idx) => {\n      if (!validScales.has(scale)) {\n        return;\n      }\n      const src = asset.files[idx];\n      const dest = path.join(assetsDest, getAssetDestPath(asset, scale));\n      filesToCopy[src] = dest;\n    });\n  };\n\n  if (platform === 'ios' && assetCatalogDest != null) {\n    // Use iOS Asset Catalog for images. This will allow Apple app thinning to\n    // remove unused scales from the optimized bundle.\n    const catalogDir = path.join(assetCatalogDest, 'RNAssets.xcassets');\n    if (!fs.existsSync(catalogDir)) {\n      logger.error(\n        `Could not find asset catalog 'RNAssets.xcassets' in ${assetCatalogDest}. Make sure to create it if it does not exist.`,\n      );\n      return;\n    }\n\n    logger.info('Adding images to asset catalog', catalogDir);\n    cleanAssetCatalog(catalogDir);\n    for (const asset of assets) {\n      if (isCatalogAsset(asset)) {\n        const imageSet = getImageSet(\n          catalogDir,\n          asset,\n          filterPlatformAssetScales(platform, asset.scales),\n        );\n        writeImageSet(imageSet);\n      } else {\n        addAssetToCopy(asset);\n      }\n    }\n    logger.info('Done adding images to asset catalog');\n  } else {\n    assets.forEach(addAssetToCopy);\n  }\n\n  return copyAll(filesToCopy);\n}\n\nfunction copyAll(filesToCopy: CopiedFiles) {\n  const queue = Object.keys(filesToCopy);\n  if (queue.length === 0) {\n    return Promise.resolve();\n  }\n\n  logger.info(`Copying ${queue.length} asset files`);\n  return new Promise<void>((resolve, reject) => {\n    const copyNext = (error?: NodeJS.ErrnoException) => {\n      if (error) {\n        reject(error);\n        return;\n      }\n      if (queue.length === 0) {\n        logger.info('Done copying assets');\n        resolve();\n      } else {\n        // queue.length === 0 is checked in previous branch, so this is string\n        const src = queue.shift() as string;\n        const dest = filesToCopy[src];\n        copy(src, dest, copyNext);\n      }\n    };\n    copyNext();\n  });\n}\n\nfunction copy(\n  src: string,\n  dest: string,\n  callback: (error: NodeJS.ErrnoException) => void,\n): void {\n  const destDir = path.dirname(dest);\n  fs.mkdir(destDir, {recursive: true}, (err?) => {\n    if (err) {\n      callback(err);\n      return;\n    }\n    fs.createReadStream(src)\n      .pipe(fs.createWriteStream(dest))\n      .on('finish', callback);\n  });\n}\n\nexport default saveAssets;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAMA;AACA;AACA;AAAwD;AApBxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAoBA,SAASA,UAAU,CACjBC,MAAgC,EAChCC,QAAgB,EAChBC,UAA8B,EAC9BC,gBAAoC,EACpC;EACA,IAAI,CAACD,UAAU,EAAE;IACfE,kBAAM,CAACC,IAAI,CAAC,mDAAmD,CAAC;IAChE;EACF;EAEA,MAAMC,WAAwB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtD,MAAMC,gBAAgB,GACpBR,QAAQ,KAAK,SAAS,GAAGS,gCAAuB,GAAGC,4BAAmB;EAExE,MAAMC,cAAc,GAAIC,KAAgB,IAAK;IAC3C,MAAMC,WAAW,GAAG,IAAIC,GAAG,CACzB,IAAAC,kCAAyB,EAACf,QAAQ,EAAEY,KAAK,CAACI,MAAM,CAAC,CAClD;IAEDJ,KAAK,CAACI,MAAM,CAACC,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;MACnC,IAAI,CAACN,WAAW,CAACO,GAAG,CAACF,KAAK,CAAC,EAAE;QAC3B;MACF;MACA,MAAMG,GAAG,GAAGT,KAAK,CAACU,KAAK,CAACH,GAAG,CAAC;MAC5B,MAAMI,IAAI,GAAGC,eAAI,CAACC,IAAI,CAACxB,UAAU,EAAEO,gBAAgB,CAACI,KAAK,EAAEM,KAAK,CAAC,CAAC;MAClEb,WAAW,CAACgB,GAAG,CAAC,GAAGE,IAAI;IACzB,CAAC,CAAC;EACJ,CAAC;EAED,IAAIvB,QAAQ,KAAK,KAAK,IAAIE,gBAAgB,IAAI,IAAI,EAAE;IAClD;IACA;IACA,MAAMwB,UAAU,GAAGF,eAAI,CAACC,IAAI,CAACvB,gBAAgB,EAAE,mBAAmB,CAAC;IACnE,IAAI,CAACyB,aAAE,CAACC,UAAU,CAACF,UAAU,CAAC,EAAE;MAC9BvB,kBAAM,CAAC0B,KAAK,CACT,uDAAsD3B,gBAAiB,gDAA+C,CACxH;MACD;IACF;IAEAC,kBAAM,CAAC2B,IAAI,CAAC,gCAAgC,EAAEJ,UAAU,CAAC;IACzD,IAAAK,kCAAiB,EAACL,UAAU,CAAC;IAC7B,KAAK,MAAMd,KAAK,IAAIb,MAAM,EAAE;MAC1B,IAAI,IAAAiC,+BAAc,EAACpB,KAAK,CAAC,EAAE;QACzB,MAAMqB,QAAQ,GAAG,IAAAC,4BAAW,EAC1BR,UAAU,EACVd,KAAK,EACL,IAAAG,kCAAyB,EAACf,QAAQ,EAAEY,KAAK,CAACI,MAAM,CAAC,CAClD;QACD,IAAAmB,8BAAa,EAACF,QAAQ,CAAC;MACzB,CAAC,MAAM;QACLtB,cAAc,CAACC,KAAK,CAAC;MACvB;IACF;IACAT,kBAAM,CAAC2B,IAAI,CAAC,qCAAqC,CAAC;EACpD,CAAC,MAAM;IACL/B,MAAM,CAACkB,OAAO,CAACN,cAAc,CAAC;EAChC;EAEA,OAAOyB,OAAO,CAAC/B,WAAW,CAAC;AAC7B;AAEA,SAAS+B,OAAO,CAAC/B,WAAwB,EAAE;EACzC,MAAMgC,KAAK,GAAG/B,MAAM,CAACgC,IAAI,CAACjC,WAAW,CAAC;EACtC,IAAIgC,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IACtB,OAAOC,OAAO,CAACC,OAAO,EAAE;EAC1B;EAEAtC,kBAAM,CAAC2B,IAAI,CAAE,WAAUO,KAAK,CAACE,MAAO,cAAa,CAAC;EAClD,OAAO,IAAIC,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC5C,MAAMC,QAAQ,GAAId,KAA6B,IAAK;MAClD,IAAIA,KAAK,EAAE;QACTa,MAAM,CAACb,KAAK,CAAC;QACb;MACF;MACA,IAAIQ,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;QACtBpC,kBAAM,CAAC2B,IAAI,CAAC,qBAAqB,CAAC;QAClCW,OAAO,EAAE;MACX,CAAC,MAAM;QACL;QACA,MAAMpB,GAAG,GAAGgB,KAAK,CAACO,KAAK,EAAY;QACnC,MAAMrB,IAAI,GAAGlB,WAAW,CAACgB,GAAG,CAAC;QAC7BwB,IAAI,CAACxB,GAAG,EAAEE,IAAI,EAAEoB,QAAQ,CAAC;MAC3B;IACF,CAAC;IACDA,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ;AAEA,SAASE,IAAI,CACXxB,GAAW,EACXE,IAAY,EACZuB,QAAgD,EAC1C;EACN,MAAMC,OAAO,GAAGvB,eAAI,CAACwB,OAAO,CAACzB,IAAI,CAAC;EAClCI,aAAE,CAACsB,KAAK,CAACF,OAAO,EAAE;IAACG,SAAS,EAAE;EAAI,CAAC,EAAGC,GAAI,IAAK;IAC7C,IAAIA,GAAG,EAAE;MACPL,QAAQ,CAACK,GAAG,CAAC;MACb;IACF;IACAxB,aAAE,CAACyB,gBAAgB,CAAC/B,GAAG,CAAC,CACrBgC,IAAI,CAAC1B,aAAE,CAAC2B,iBAAiB,CAAC/B,IAAI,CAAC,CAAC,CAChCgC,EAAE,CAAC,QAAQ,EAAET,QAAQ,CAAC;EAC3B,CAAC,CAAC;AACJ;AAAC,eAEchD,UAAU;AAAA"}