{"version": 3, "names": ["escapeStringRegexp", "extractPathFromURL", "prefixes", "url", "prefix", "protocol", "match", "host", "replace", "RegExp", "prefixRegex", "split", "map", "it", "join", "originAndPath", "searchParams", "normalizedURL", "concat", "test", "undefined"], "sourceRoot": "../../src", "sources": ["extractPathFromURL.tsx"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AAErD,eAAe,SAASC,kBAAkB,CAACC,QAAkB,EAAEC,GAAW,EAAE;EAC1E,KAAK,MAAMC,MAAM,IAAIF,QAAQ,EAAE;IAAA;IAC7B,MAAMG,QAAQ,GAAG,kBAAAD,MAAM,CAACE,KAAK,CAAC,SAAS,CAAC,kDAAvB,cAA0B,CAAC,CAAC,KAAI,EAAE;IACnD,MAAMC,IAAI,GAAGH,MAAM,CAChBI,OAAO,CAAC,IAAIC,MAAM,CAAE,IAAGT,kBAAkB,CAACK,QAAQ,CAAE,EAAC,CAAC,EAAE,EAAE,CAAC,CAC3DG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAAA,CACrBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;;IAEvB,MAAME,WAAW,GAAG,IAAID,MAAM,CAC3B,IAAGT,kBAAkB,CAACK,QAAQ,CAAE,OAAME,IAAI,CACxCI,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,EAAE,IAAMA,EAAE,KAAK,GAAG,GAAG,OAAO,GAAGb,kBAAkB,CAACa,EAAE,CAAE,CAAC,CAC5DC,IAAI,CAAC,KAAK,CAAE,EAAC,CACjB;IAED,MAAM,CAACC,aAAa,EAAEC,YAAY,CAAC,GAAGb,GAAG,CAACQ,KAAK,CAAC,GAAG,CAAC;IACpD,MAAMM,aAAa,GAAGF,aAAa,CAChCP,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBU,MAAM,CAACF,YAAY,GAAI,IAAGA,YAAa,EAAC,GAAG,EAAE,CAAC;IAEjD,IAAIN,WAAW,CAACS,IAAI,CAACF,aAAa,CAAC,EAAE;MACnC,OAAOA,aAAa,CAACT,OAAO,CAACE,WAAW,EAAE,EAAE,CAAC;IAC/C;EACF;EAEA,OAAOU,SAAS;AAClB"}