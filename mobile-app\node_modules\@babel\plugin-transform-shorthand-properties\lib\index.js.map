{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "ObjectMethod", "path", "node", "kind", "func", "t", "functionExpression", "params", "body", "generator", "async", "returnType", "computedKey", "toCom<PERSON><PERSON>ey", "isStringLiteral", "value", "replaceWith", "objectProperty", "key", "computed", "ObjectProperty", "shorthand"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-shorthand-properties\",\n\n    visitor: {\n      ObjectMethod(path) {\n        const { node } = path;\n        if (node.kind === \"method\") {\n          const func = t.functionExpression(\n            null,\n            node.params,\n            node.body,\n            node.generator,\n            node.async,\n          );\n          func.returnType = node.returnType;\n\n          const computedKey = t.toComputedKey(node);\n          if (t.isStringLiteral(computedKey, { value: \"__proto__\" })) {\n            path.replaceWith(t.objectProperty(computedKey, func, true));\n          } else {\n            path.replaceWith(t.objectProperty(node.key, func, node.computed));\n          }\n        }\n      },\n\n      ObjectProperty(path) {\n        const { node } = path;\n        if (node.shorthand) {\n          const computedKey = t.toComputedKey(node);\n          if (t.isStringLiteral(computedKey, { value: \"__proto__\" })) {\n            path.replaceWith(t.objectProperty(computedKey, node.value, true));\n          } else {\n            node.shorthand = false;\n          }\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,gCAAgC;IAEtCC,OAAO,EAAE;MACPC,YAAYA,CAACC,IAAI,EAAE;QACjB,MAAM;UAAEC;QAAK,CAAC,GAAGD,IAAI;QACrB,IAAIC,IAAI,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC1B,MAAMC,IAAI,GAAGC,WAAC,CAACC,kBAAkB,CAC/B,IAAI,EACJJ,IAAI,CAACK,MAAM,EACXL,IAAI,CAACM,IAAI,EACTN,IAAI,CAACO,SAAS,EACdP,IAAI,CAACQ,KACP,CAAC;UACDN,IAAI,CAACO,UAAU,GAAGT,IAAI,CAACS,UAAU;UAEjC,MAAMC,WAAW,GAAGP,WAAC,CAACQ,aAAa,CAACX,IAAI,CAAC;UACzC,IAAIG,WAAC,CAACS,eAAe,CAACF,WAAW,EAAE;YAAEG,KAAK,EAAE;UAAY,CAAC,CAAC,EAAE;YAC1Dd,IAAI,CAACe,WAAW,CAACX,WAAC,CAACY,cAAc,CAACL,WAAW,EAAER,IAAI,EAAE,IAAI,CAAC,CAAC;UAC7D,CAAC,MAAM;YACLH,IAAI,CAACe,WAAW,CAACX,WAAC,CAACY,cAAc,CAACf,IAAI,CAACgB,GAAG,EAAEd,IAAI,EAAEF,IAAI,CAACiB,QAAQ,CAAC,CAAC;UACnE;QACF;MACF,CAAC;MAEDC,cAAcA,CAACnB,IAAI,EAAE;QACnB,MAAM;UAAEC;QAAK,CAAC,GAAGD,IAAI;QACrB,IAAIC,IAAI,CAACmB,SAAS,EAAE;UAClB,MAAMT,WAAW,GAAGP,WAAC,CAACQ,aAAa,CAACX,IAAI,CAAC;UACzC,IAAIG,WAAC,CAACS,eAAe,CAACF,WAAW,EAAE;YAAEG,KAAK,EAAE;UAAY,CAAC,CAAC,EAAE;YAC1Dd,IAAI,CAACe,WAAW,CAACX,WAAC,CAACY,cAAc,CAACL,WAAW,EAAEV,IAAI,CAACa,KAAK,EAAE,IAAI,CAAC,CAAC;UACnE,CAAC,MAAM;YACLb,IAAI,CAACmB,SAAS,GAAG,KAAK;UACxB;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}