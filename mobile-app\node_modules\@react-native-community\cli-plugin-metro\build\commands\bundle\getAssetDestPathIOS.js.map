{"version": 3, "names": ["getAssetDestPathIOS", "asset", "scale", "suffix", "fileName", "name", "type", "path", "join", "httpServerLocation", "substr", "replace"], "sources": ["../../../src/commands/bundle/getAssetDestPathIOS.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport {PackagerAsset} from './assetPathUtils';\n\nfunction getAssetDestPathIOS(asset: PackagerAsset, scale: number): string {\n  const suffix = scale === 1 ? '' : `@${scale}x`;\n  const fileName = `${asset.name + suffix}.${asset.type}`;\n  return path.join(\n    // Assets can have relative paths outside of the project root.\n    // Replace `../` with `_` to make sure they don't end up outside of\n    // the expected assets directory.\n    asset.httpServerLocation.substr(1).replace(/\\.\\.\\//g, '_'),\n    fileName,\n  );\n}\n\nexport default getAssetDestPathIOS;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AARxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,SAASA,mBAAmB,CAACC,KAAoB,EAAEC,KAAa,EAAU;EACxE,MAAMC,MAAM,GAAGD,KAAK,KAAK,CAAC,GAAG,EAAE,GAAI,IAAGA,KAAM,GAAE;EAC9C,MAAME,QAAQ,GAAI,GAAEH,KAAK,CAACI,IAAI,GAAGF,MAAO,IAAGF,KAAK,CAACK,IAAK,EAAC;EACvD,OAAOC,eAAI,CAACC,IAAI;EACd;EACA;EACA;EACAP,KAAK,CAACQ,kBAAkB,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,EAC1DP,QAAQ,CACT;AACH;AAAC,eAEcJ,mBAAmB;AAAA"}