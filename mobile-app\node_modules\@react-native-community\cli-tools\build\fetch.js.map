{"version": 3, "names": ["unwrapFetchResult", "response", "data", "text", "JSON", "parse", "e", "fetchToTemp", "url", "Promise", "resolve", "reject", "fileName", "path", "basename", "tmpDir", "join", "os", "tmpdir", "nodeFetch", "then", "result", "status", "dest", "fs", "createWriteStream", "body", "pipe", "on", "logger", "error", "fetch", "options", "CLIError", "headers"], "sources": ["../src/fetch.ts"], "sourcesContent": ["import * as os from 'os';\nimport * as path from 'path';\nimport * as fs from 'fs';\n\nimport nodeFetch, {\n  RequestInit as FetchOptions,\n  Response,\n  Request,\n  Headers,\n} from 'node-fetch';\nimport {CLIError} from './errors';\nimport logger from './logger';\n\nasync function unwrapFetchResult(response: Response) {\n  const data = await response.text();\n\n  try {\n    return JSON.parse(data);\n  } catch (e) {\n    return data;\n  }\n}\n\n/**\n * Downloads the given `url` to the OS's temp folder and\n * returns the path to it.\n */\nconst fetchToTemp = (url: string): Promise<string> => {\n  try {\n    return new Promise((resolve, reject) => {\n      const fileName = path.basename(url);\n      const tmpDir = path.join(os.tmpdir(), fileName);\n\n      nodeFetch(url).then((result) => {\n        if (result.status >= 400) {\n          return reject(`Fetch request failed with status ${result.status}`);\n        }\n\n        const dest = fs.createWriteStream(tmpDir);\n        result.body.pipe(dest);\n\n        result.body.on('end', () => {\n          resolve(tmpDir);\n        });\n\n        result.body.on('error', reject);\n      });\n    });\n  } catch (e) {\n    logger.error(e as any);\n    throw e;\n  }\n};\n\nconst fetch = async (\n  url: string | Request,\n  options?: FetchOptions,\n): Promise<{status: number; data: any; headers: Headers}> => {\n  const result = await nodeFetch(url, options);\n  const data = await unwrapFetchResult(result);\n\n  if (result.status >= 400) {\n    throw new CLIError(\n      `Fetch request failed with status ${result.status}: ${data}.`,\n    );\n  }\n\n  return {\n    status: result.status,\n    headers: result.headers,\n    data,\n  };\n};\n\nexport {fetch, fetchToTemp};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;AACA;AAA8B;AAAA;AAAA;AAE9B,eAAeA,iBAAiB,CAACC,QAAkB,EAAE;EACnD,MAAMC,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,EAAE;EAElC,IAAI;IACF,OAAOC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;EACzB,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,OAAOJ,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA,MAAMK,WAAW,GAAIC,GAAW,IAAsB;EACpD,IAAI;IACF,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,QAAQ,GAAGC,IAAI,GAACC,QAAQ,CAACN,GAAG,CAAC;MACnC,MAAMO,MAAM,GAAGF,IAAI,GAACG,IAAI,CAACC,EAAE,GAACC,MAAM,EAAE,EAAEN,QAAQ,CAAC;MAE/C,IAAAO,oBAAS,EAACX,GAAG,CAAC,CAACY,IAAI,CAAEC,MAAM,IAAK;QAC9B,IAAIA,MAAM,CAACC,MAAM,IAAI,GAAG,EAAE;UACxB,OAAOX,MAAM,CAAE,oCAAmCU,MAAM,CAACC,MAAO,EAAC,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAGC,EAAE,GAACC,iBAAiB,CAACV,MAAM,CAAC;QACzCM,MAAM,CAACK,IAAI,CAACC,IAAI,CAACJ,IAAI,CAAC;QAEtBF,MAAM,CAACK,IAAI,CAACE,EAAE,CAAC,KAAK,EAAE,MAAM;UAC1BlB,OAAO,CAACK,MAAM,CAAC;QACjB,CAAC,CAAC;QAEFM,MAAM,CAACK,IAAI,CAACE,EAAE,CAAC,OAAO,EAAEjB,MAAM,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOL,CAAC,EAAE;IACVuB,eAAM,CAACC,KAAK,CAACxB,CAAC,CAAQ;IACtB,MAAMA,CAAC;EACT;AACF,CAAC;AAAC;AAEF,MAAMyB,KAAK,GAAG,OACZvB,GAAqB,EACrBwB,OAAsB,KACqC;EAC3D,MAAMX,MAAM,GAAG,MAAM,IAAAF,oBAAS,EAACX,GAAG,EAAEwB,OAAO,CAAC;EAC5C,MAAM9B,IAAI,GAAG,MAAMF,iBAAiB,CAACqB,MAAM,CAAC;EAE5C,IAAIA,MAAM,CAACC,MAAM,IAAI,GAAG,EAAE;IACxB,MAAM,IAAIW,gBAAQ,CACf,oCAAmCZ,MAAM,CAACC,MAAO,KAAIpB,IAAK,GAAE,CAC9D;EACH;EAEA,OAAO;IACLoB,MAAM,EAAED,MAAM,CAACC,MAAM;IACrBY,OAAO,EAAEb,MAAM,CAACa,OAAO;IACvBhC;EACF,CAAC;AACH,CAAC;AAAC"}