{"version": 3, "names": ["Animated", "I18nManager", "add", "forUIKit", "current", "next", "layouts", "defaultOffset", "leftSpacing", "leftLabelOffset", "leftLabel", "screen", "width", "titleLeftOffset", "title", "rightOffset", "progress", "interpolate", "inputRange", "outputRange", "extrapolate", "leftButtonStyle", "opacity", "leftLabelStyle", "transform", "translateX", "getConstants", "isRTL", "rightButtonStyle", "titleStyle", "backgroundStyle", "forFade", "forSlideLeft", "forSlideRight", "forSlideUp", "header", "translateY", "height", "forNoAnimation"], "sourceRoot": "../../../src", "sources": ["TransitionConfigs/HeaderStyleInterpolators.tsx"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AAOpD,MAAM;EAAEC;AAAI,CAAC,GAAGF,QAAQ;;AAExB;AACA;AACA;AACA,OAAO,SAASG,QAAQ,OAIwC;EAAA,IAJvC;IACvBC,OAAO;IACPC,IAAI;IACJC;EAC6B,CAAC;EAC9B,MAAMC,aAAa,GAAG,GAAG;EACzB,MAAMC,WAAW,GAAG,EAAE;;EAEtB;EACA;EACA;EACA;EACA;EACA,MAAMC,eAAe,GAAGH,OAAO,CAACI,SAAS,GACrC,CAACJ,OAAO,CAACK,MAAM,CAACC,KAAK,GAAGN,OAAO,CAACI,SAAS,CAACE,KAAK,IAAI,CAAC,GAAGJ,WAAW,GAClED,aAAa;EACjB,MAAMM,eAAe,GAAGP,OAAO,CAACQ,KAAK,GACjC,CAACR,OAAO,CAACK,MAAM,CAACC,KAAK,GAAGN,OAAO,CAACQ,KAAK,CAACF,KAAK,IAAI,CAAC,GAAGJ,WAAW,GAC9DD,aAAa;;EAEjB;EACA;EACA,MAAMQ,WAAW,GAAGT,OAAO,CAACK,MAAM,CAACC,KAAK,GAAG,CAAC;EAE5C,MAAMI,QAAQ,GAAGd,GAAG,CAClBE,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,OAAO;IACLC,eAAe,EAAE;MACfC,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QACzBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvB,CAAC;IACH,CAAC;IACDI,cAAc,EAAE;MACdC,SAAS,EAAE,CACT;QACEC,UAAU,EAAET,QAAQ,CAACC,WAAW,CAAC;UAC/BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrBC,WAAW,EAAElB,WAAW,CAACyB,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACZ,WAAW,EAAE,CAAC,EAAEN,eAAe,CAAC,GAClC,CAACA,eAAe,EAAE,CAAC,EAAE,CAACM,WAAW;QACvC,CAAC;MACH,CAAC;IAEL,CAAC;IACDa,gBAAgB,EAAE;MAChBN,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QACzBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvB,CAAC;IACH,CAAC;IACDU,UAAU,EAAE;MACVP,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QAC5BC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;MAC5B,CAAC,CAAC;MACFK,SAAS,EAAE,CACT;QACEC,UAAU,EAAET,QAAQ,CAACC,WAAW,CAAC;UAC/BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAElB,WAAW,CAACyB,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACd,eAAe,EAAE,CAAC,EAAEE,WAAW,CAAC,GAClC,CAACA,WAAW,EAAE,CAAC,EAAE,CAACF,eAAe;QACvC,CAAC;MACH,CAAC;IAEL,CAAC;IACDiB,eAAe,EAAE;MACfN,SAAS,EAAE,CACT;QACEC,UAAU,EAAET,QAAQ,CAACC,WAAW,CAAC;UAC/BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrBC,WAAW,EAAElB,WAAW,CAACyB,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACrB,OAAO,CAACK,MAAM,CAACC,KAAK,EAAE,CAAC,EAAEN,OAAO,CAACK,MAAM,CAACC,KAAK,CAAC,GAChD,CAACN,OAAO,CAACK,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACN,OAAO,CAACK,MAAM,CAACC,KAAK;QACrD,CAAC;MACH,CAAC;IAEL;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASmB,OAAO,QAGyC;EAAA,IAHxC;IACtB3B,OAAO;IACPC;EAC6B,CAAC;EAC9B,MAAMW,QAAQ,GAAGd,GAAG,CAClBE,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,MAAME,OAAO,GAAGN,QAAQ,CAACC,WAAW,CAAC;IACnCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;EAEF,OAAO;IACLE,eAAe,EAAE;MAAEC;IAAQ,CAAC;IAC5BM,gBAAgB,EAAE;MAAEN;IAAQ,CAAC;IAC7BO,UAAU,EAAE;MAAEP;IAAQ,CAAC;IACvBQ,eAAe,EAAE;MACfR,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1BC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;MAC1B,CAAC;IACH;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASa,YAAY,QAIoC;EAAA,IAJnC;IAC3B5B,OAAO;IACPC,IAAI;IACJC,OAAO,EAAE;MAAEK;IAAO;EACW,CAAC;EAC9B,MAAMK,QAAQ,GAAGd,GAAG,CAClBE,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,MAAMK,UAAU,GAAGT,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAElB,WAAW,CAACyB,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAAChB,MAAM,CAACC,KAAK,EAAE,CAAC,EAAED,MAAM,CAACC,KAAK,CAAC,GAChC,CAACD,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACD,MAAM,CAACC,KAAK;EACrC,CAAC,CAAC;EAEF,MAAMY,SAAS,GAAG,CAAC;IAAEC;EAAW,CAAC,CAAC;EAElC,OAAO;IACLJ,eAAe,EAAE;MAAEG;IAAU,CAAC;IAC9BI,gBAAgB,EAAE;MAAEJ;IAAU,CAAC;IAC/BK,UAAU,EAAE;MAAEL;IAAU,CAAC;IACzBM,eAAe,EAAE;MAAEN;IAAU;EAC/B,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASS,aAAa,QAImC;EAAA,IAJlC;IAC5B7B,OAAO;IACPC,IAAI;IACJC,OAAO,EAAE;MAAEK;IAAO;EACW,CAAC;EAC9B,MAAMK,QAAQ,GAAGd,GAAG,CAClBE,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,MAAMK,UAAU,GAAGT,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAElB,WAAW,CAACyB,YAAY,EAAE,CAACC,KAAK,GACzC,CAAChB,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACD,MAAM,CAACC,KAAK,CAAC,GAChC,CAAC,CAACD,MAAM,CAACC,KAAK,EAAE,CAAC,EAAED,MAAM,CAACC,KAAK;EACrC,CAAC,CAAC;EAEF,MAAMY,SAAS,GAAG,CAAC;IAAEC;EAAW,CAAC,CAAC;EAElC,OAAO;IACLJ,eAAe,EAAE;MAAEG;IAAU,CAAC;IAC9BI,gBAAgB,EAAE;MAAEJ;IAAU,CAAC;IAC/BK,UAAU,EAAE;MAAEL;IAAU,CAAC;IACzBM,eAAe,EAAE;MAAEN;IAAU;EAC/B,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASU,UAAU,QAIsC;EAAA,IAJrC;IACzB9B,OAAO;IACPC,IAAI;IACJC,OAAO,EAAE;MAAE6B;IAAO;EACW,CAAC;EAC9B,MAAMnB,QAAQ,GAAGd,GAAG,CAClBE,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,MAAMgB,UAAU,GAAGpB,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,CAAC,CAACgB,MAAM,CAACE,MAAM,EAAE,CAAC,EAAE,CAACF,MAAM,CAACE,MAAM;EACjD,CAAC,CAAC;EAEF,MAAMb,SAAS,GAAG,CAAC;IAAEY;EAAW,CAAC,CAAC;EAElC,OAAO;IACLf,eAAe,EAAE;MAAEG;IAAU,CAAC;IAC9BI,gBAAgB,EAAE;MAAEJ;IAAU,CAAC;IAC/BK,UAAU,EAAE;MAAEL;IAAU,CAAC;IACzBM,eAAe,EAAE;MAAEN;IAAU;EAC/B,CAAC;AACH;AAEA,OAAO,SAASc,cAAc,GAAiC;EAC7D,OAAO,CAAC,CAAC;AACX"}