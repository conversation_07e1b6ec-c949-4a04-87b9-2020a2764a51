{"version": 3, "names": ["Link", "to", "action", "rest", "props", "useLinkProps", "onPress", "e", "React", "createElement", "Text", "Platform", "select", "web", "onClick", "default"], "sourceRoot": "../../src", "sources": ["Link.tsx"], "mappings": ";;;;;;AACA;AACA;AAEA;AAA0C;AAAA;AAAA;AAe1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,IAAI,OAIP;EAAA,IAJyD;IAC5EC,EAAE;IACFC,MAAM;IACN,GAAGC;EACa,CAAC;EACjB,MAAMC,KAAK,GAAG,IAAAC,qBAAY,EAAY;IAAEJ,EAAE;IAAEC;EAAO,CAAC,CAAC;EAErD,MAAMI,OAAO,GACXC,CAA0E,IACvE;IACH,IAAI,SAAS,IAAIJ,IAAI,EAAE;MAAA;MACrB,iBAAAA,IAAI,CAACG,OAAO,kDAAZ,mBAAAH,IAAI,EAAWI,CAAC,CAAC;IACnB;IAEAH,KAAK,CAACE,OAAO,CAACC,CAAC,CAAC;EAClB,CAAC;EAED,oBAAOC,KAAK,CAACC,aAAa,CAACC,iBAAI,EAAE;IAC/B,GAAGN,KAAK;IACR,GAAGD,IAAI;IACP,GAAGQ,qBAAQ,CAACC,MAAM,CAAC;MACjBC,GAAG,EAAE;QAAEC,OAAO,EAAER;MAAQ,CAAQ;MAChCS,OAAO,EAAE;QAAET;MAAQ;IACrB,CAAC;EACH,CAAC,CAAC;AACJ"}