{"version": 3, "sources": ["../src/index.ts", "../src/createDraftSafeSelector.ts", "../src/configureStore.ts", "../src/devtoolsExtension.ts", "../src/isPlainObject.ts", "../src/getDefaultMiddleware.ts", "../src/tsHelpers.ts", "../src/createAction.ts", "../src/actionCreatorInvariantMiddleware.ts", "../src/utils.ts", "../src/immutableStateInvariantMiddleware.ts", "../src/serializableStateInvariantMiddleware.ts", "../src/createReducer.ts", "../src/mapBuilders.ts", "../src/createSlice.ts", "../src/entities/entity_state.ts", "../src/entities/state_selectors.ts", "../src/entities/state_adapter.ts", "../src/entities/utils.ts", "../src/entities/unsorted_state_adapter.ts", "../src/entities/sorted_state_adapter.ts", "../src/entities/create_adapter.ts", "../src/nanoid.ts", "../src/createAsyncThunk.ts", "../src/matchers.ts", "../src/listenerMiddleware/utils.ts", "../src/listenerMiddleware/exceptions.ts", "../src/listenerMiddleware/task.ts", "../src/listenerMiddleware/index.ts", "../src/autoBatchEnhancer.ts", "redux-toolkit.modern.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAA,EAAA,SAAA,EAAA,MAAA,OAAA,CAAA;AACA,cAAA,OAAA,CAAA;AACA,OAAA,EAAA,OAAA,IAAA,QAAA,EAAA,OAAA,IAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,IAAA,QAAA,EAAA,MAAA,OAAA,CAAA;AAQA,OAAA,EAAA,cAAA,IAAA,eAAA,EAAA,MAAA,UAAA,CAAA;;ACVA,OAAA,EAAA,OAAA,EAAA,OAAA,EAAA,MAAA,OAAA,CAAA;AACA,OAAA,EAAA,cAAA,EAAA,MAAA,UAAA,CAAA;AASO,IAAM,uBAAA,GAAiD,CAAA,GACzD,IAAA,EAAA,EAAA;IAEH,MAAM,QAAA,GAAY,cAAA,CAAuB,GAAG,IAAA,CAAA,CAAA;IAC5C,MAAM,eAAA,GAAkB,CAAC,KAAA,EAAA,GAAmB,IAAA,EAAA,EAAA,CAC1C,QAAA,CAAS,OAAA,CAAQ,KAAA,CAAA,CAAA,CAAA,CAAS,OAAA,CAAQ,KAAA,CAAA,CAAA,CAAA,CAAS,KAAA,EAAO,GAAG,IAAA,CAAA,CAAA;IACvD,OAAO,eAAA,CAAA;AAAA,CAAA,CAAA;;ACJT,OAAA,EAAA,WAAA,EAAA,OAAA,IAAA,QAAA,EAAA,eAAA,EAAA,eAAA,EAAA,MAAA,OAAA,CAAA;;ACXA,OAAA,EAAA,OAAA,EAAA,MAAA,OAAA,CAAA;AAmOO,IAAM,mBAAA,GACX,OAAO,MAAA,KAAW,WAAA,IACjB,MAAA,CAAe,oCAAA,CAAA,CAAA,CACX,MAAA,CAAe,oCAAA,CAAA,CAAA,CAChB;IACE,IAAI,SAAA,CAAU,MAAA,KAAW,CAAA;QAAG,OAAO,KAAA,CAAA,CAAA;IACnC,IAAI,OAAO,SAAA,CAAU,CAAA,CAAA,KAAO,QAAA;QAAU,OAAO,OAAA,CAAA;IAC7C,OAAO,OAAA,CAAQ,KAAA,CAAM,IAAA,EAAM,SAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAM5B,IAAM,gBAAA,GAGX,OAAO,MAAA,KAAW,WAAA,IAAgB,MAAA,CAAe,4BAAA,CAAA,CAAA,CAC5C,MAAA,CAAe,4BAAA,CAAA,CAAA,CAChB;IACE,OAAO,UAAU,KAAA;QACf,OAAO,KAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA,CAAA;;AC9OF,SAAA,aAAA,CAAuB,KAAA;IACpC,IAAI,OAAO,KAAA,KAAU,QAAA,IAAY,KAAA,KAAU,IAAA;QAAM,OAAO,KAAA,CAAA;IAExD,IAAI,KAAA,GAAQ,MAAA,CAAO,cAAA,CAAe,KAAA,CAAA,CAAA;IAClC,IAAI,KAAA,KAAU,IAAA;QAAM,OAAO,IAAA,CAAA;IAE3B,IAAI,SAAA,GAAY,KAAA,CAAA;IAChB,OAAO,MAAA,CAAO,cAAA,CAAe,SAAA,CAAA,KAAe,IAAA,EAAM;QAChD,SAAA,GAAY,MAAA,CAAO,cAAA,CAAe,SAAA,CAAA,CAAA;KAAA;IAGpC,OAAO,KAAA,KAAU,SAAA,CAAA;AAAA,CAAA;;ACnBnB,OAAA,eAAA,MAAA,aAAA,CAAA;;AC2KO,IAAM,gBAAA,GAAmB,CAC9B,CAAA,EAAA,EAAA;IAEA,OAAO,CAAA,IAAK,OAAQ,CAAA,CAA0B,KAAA,KAAU,UAAA,CAAA;AAAA,CAAA,CAAA;;ACqFnD,SAAA,YAAA,CAAsB,IAAA,EAAc,aAAA;IACzC,SAAA,aAAA,CAAA,GAA0B,IAAA;QACxB,IAAI,aAAA,EAAe;YACjB,IAAI,QAAA,GAAW,aAAA,CAAc,GAAG,IAAA,CAAA,CAAA;YAChC,IAAI,CAAC,QAAA,EAAU;gBACb,MAAM,IAAI,KAAA,CAAM,wCAAA,CAAA,CAAA;aAAA;YAGlB,OAAO,cAAA,CAAA,cAAA,CAAA;gBACL,IAAA;gBACA,OAAA,EAAS,QAAA,CAAS,OAAA;aAAA,EACd,MAAA,IAAU,QAAA,IAAY,EAAE,IAAA,EAAM,QAAA,CAAS,IAAA,EAAA,CAAA,EACvC,OAAA,IAAW,QAAA,IAAY,EAAE,KAAA,EAAO,QAAA,CAAS,KAAA,EAAA,CAAA,CAAA;SAAA;QAGjD,OAAO,EAAE,IAAA,EAAM,OAAA,EAAS,IAAA,CAAK,CAAA,CAAA,EAAA,CAAA;IAAA,CAAA;IAG/B,aAAA,CAAc,QAAA,GAAW,GAAA,EAAA,CAAM,GAAG,IAAA,EAAA,CAAA;IAElC,aAAA,CAAc,IAAA,GAAO,IAAA,CAAA;IAErB,aAAA,CAAc,KAAA,GAAQ,CAAC,MAAA,EAAA,EAAA,CACrB,MAAA,CAAO,IAAA,KAAS,IAAA,CAAA;IAElB,OAAO,aAAA,CAAA;AAAA,CAAA;AAMF,SAAA,QAAA,CAAkB,MAAA;IACvB,OAAO,aAAA,CAAc,MAAA,CAAA,IAAW,MAAA,IAAU,MAAA,CAAA;AAAA,CAAA;AAMrC,SAAA,eAAA,CACL,MAAA;IAEA,OACE,OAAO,MAAA,KAAW,UAAA,IAClB,MAAA,IAAU,MAAA,IAEV,gBAAA,CAAiB,MAAA,CAAA,CAAA;AAAA,CAAA;AAOd,SAAA,KAAA,CAAe,MAAA;IAMpB,OACE,QAAA,CAAS,MAAA,CAAA,IACT,OAAO,MAAA,CAAO,IAAA,KAAS,QAAA,IACvB,MAAA,CAAO,IAAA,CAAK,MAAA,CAAA,CAAQ,KAAA,CAAM,UAAA,CAAA,CAAA;AAAA,CAAA;AAI9B,SAAA,UAAA,CAAoB,GAAA;IAClB,OAAO,CAAC,MAAA,EAAQ,SAAA,EAAW,OAAA,EAAS,MAAA,CAAA,CAAQ,OAAA,CAAQ,GAAA,CAAA,GAAO,CAAA,CAAA,CAAA;AAAA,CAAA;AAatD,SAAA,OAAA,CACL,aAAA;IAEA,OAAO,GAAG,aAAA,EAAA,CAAA;AAAA,CAAA;;AC5UL,SAAA,UAAA,CAAoB,IAAA;IACzB,MAAM,SAAA,GAAY,IAAA,CAAA,CAAA,CAAO,GAAG,IAAA,EAAA,CAAO,KAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA;IAChD,MAAM,UAAA,GAAa,SAAA,CAAU,SAAA,CAAU,MAAA,GAAS,CAAA,CAAA,IAAM,eAAA,CAAA;IACtD,OAAO,yCACL,IAAA,IAAQ,SAAA;kFAEsE,UAAA,+BAAyC,UAAA,2DAAA,CAAA;AAAA,CAAA;AAGpH,SAAA,sCAAA,CACL,OAAA,GAAmD,EAAA;IAEnD,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;QACzC,OAAO,GAAA,EAAA,CAAM,CAAC,IAAA,EAAA,EAAA,CAAS,CAAC,MAAA,EAAA,EAAA,CAAW,IAAA,CAAK,MAAA,CAAA,CAAA;KAAA;IAE1C,MAAM,EAAE,eAAA,EAAA,gBAAA,GAAkB,eAAA,EAAA,GAAgB,OAAA,CAAA;IAC1C,OAAO,GAAA,EAAA,CAAM,CAAC,IAAA,EAAA,EAAA,CAAS,CAAC,MAAA,EAAA,EAAA;QACtB,IAAI,gBAAA,CAAgB,MAAA,CAAA,EAAS;YAC3B,OAAA,CAAQ,IAAA,CAAK,UAAA,CAAW,MAAA,CAAO,IAAA,CAAA,CAAA,CAAA;SAAA;QAEjC,OAAO,IAAA,CAAK,MAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;;AC/BhB,OAAA,eAAA,EAAA,EAAA,WAAA,EAAA,MAAA,OAAA,CAAA;AAGO,SAAA,mBAAA,CAA6B,QAAA,EAAkB,MAAA;IACpD,IAAI,OAAA,GAAU,CAAA,CAAA;IACd,OAAO;QACL,WAAA,CAAe,EAAA;YACb,MAAM,OAAA,GAAU,IAAA,CAAK,GAAA,EAAA,CAAA;YACrB,IAAI;gBACF,OAAO,EAAA,EAAA,CAAA;aAAA;oBACP;gBACA,MAAM,QAAA,GAAW,IAAA,CAAK,GAAA,EAAA,CAAA;gBACtB,OAAA,IAAW,QAAA,GAAW,OAAA,CAAA;aAAA;QAAA,CAAA;QAG1B,cAAA;YACE,IAAI,OAAA,GAAU,QAAA,EAAU;gBACtB,OAAA,CAAQ,IAAA,CAAK,GAAG,MAAA,SAAe,OAAA,mDAA0D,QAAA;;4EAAA,CAAA,CAAA;aAAA;QAAA,CAAA;KAAA,CAAA;AAAA,CAAA;AAe1F,IAAA,eAAA,GAAA,KAAA,SAEG,KAAA;IAER,YAAA,GAAe,IAAA;QACb,KAAA,CAAM,GAAG,IAAA,CAAA,CAAA;QACT,MAAA,CAAO,cAAA,CAAe,IAAA,EAAM,eAAA,CAAgB,SAAA,CAAA,CAAA;IAAA,CAAA;IAAA,MAAA,KAAA,CAGlC,MAAA,CAAO,OAAA,CAAA;QACjB,OAAO,eAAA,CAAA;IAAA,CAAA;IAUT,MAAA,CAAA,GAAU,GAAA;QACR,OAAO,KAAA,CAAM,MAAA,CAAO,KAAA,CAAM,IAAA,EAAM,GAAA,CAAA,CAAA;IAAA,CAAA;IAWlC,OAAA,CAAA,GAAW,GAAA;QACT,IAAI,GAAA,CAAI,MAAA,KAAW,CAAA,IAAK,KAAA,CAAM,OAAA,CAAQ,GAAA,CAAI,CAAA,CAAA,CAAA,EAAK;YAC7C,OAAO,IAAI,eAAA,CAAgB,GAAG,GAAA,CAAI,CAAA,CAAA,CAAG,MAAA,CAAO,IAAA,CAAA,CAAA,CAAA;SAAA;QAE9C,OAAO,IAAI,eAAA,CAAgB,GAAG,GAAA,CAAI,MAAA,CAAO,IAAA,CAAA,CAAA,CAAA;IAAA,CAAA;CAAA,CAAA;AAOtC,IAAA,aAAA,GAAA,KAAA,SAEG,KAAA;IAER,YAAA,GAAe,IAAA;QACb,KAAA,CAAM,GAAG,IAAA,CAAA,CAAA;QACT,MAAA,CAAO,cAAA,CAAe,IAAA,EAAM,aAAA,CAAc,SAAA,CAAA,CAAA;IAAA,CAAA;IAAA,MAAA,KAAA,CAGhC,MAAA,CAAO,OAAA,CAAA;QACjB,OAAO,aAAA,CAAA;IAAA,CAAA;IAUT,MAAA,CAAA,GAAU,GAAA;QACR,OAAO,KAAA,CAAM,MAAA,CAAO,KAAA,CAAM,IAAA,EAAM,GAAA,CAAA,CAAA;IAAA,CAAA;IAWlC,OAAA,CAAA,GAAW,GAAA;QACT,IAAI,GAAA,CAAI,MAAA,KAAW,CAAA,IAAK,KAAA,CAAM,OAAA,CAAQ,GAAA,CAAI,CAAA,CAAA,CAAA,EAAK;YAC7C,OAAO,IAAI,aAAA,CAAc,GAAG,GAAA,CAAI,CAAA,CAAA,CAAG,MAAA,CAAO,IAAA,CAAA,CAAA,CAAA;SAAA;QAE5C,OAAO,IAAI,aAAA,CAAc,GAAG,GAAA,CAAI,MAAA,CAAO,IAAA,CAAA,CAAA,CAAA;IAAA,CAAA;CAAA,CAAA;AAIpC,SAAA,eAAA,CAA4B,GAAA;IACjC,OAAO,WAAA,CAAY,GAAA,CAAA,CAAA,CAAA,CAAO,eAAA,CAAgB,GAAA,EAAK,GAAA,EAAA;IAAM,CAAA,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA;AAAA,CAAA;;AC/G7D,IAAM,YAAA,GAAwB,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,CAAA;AACvD,IAAM,MAAA,GAAiB,kBAAA,CAAA;AAKvB,SAAA,SAAA,CAAmB,SAAA,EAAgB,OAAA;IACjC,IAAI,SAAA,EAAW;QACb,OAAA;KAAA;IAKF,IAAI,YAAA,EAAc;QAChB,MAAM,IAAI,KAAA,CAAM,MAAA,CAAA,CAAA;KAAA;IAKlB,MAAM,IAAI,KAAA,CAAM,GAAG,MAAA,KAAW,OAAA,IAAW,EAAA,EAAA,CAAA,CAAA;AAAA,CAAA;AAG3C,SAAA,SAAA,CACE,GAAA,EACA,UAAA,EACA,MAAA,EACA,QAAA;IAEA,OAAO,IAAA,CAAK,SAAA,CAAU,GAAA,EAAK,YAAA,CAAa,UAAA,EAAY,QAAA,CAAA,EAAW,MAAA,CAAA,CAAA;AAAA,CAAA;AAGjE,SAAA,YAAA,CACE,UAAA,EACA,QAAA;IAEA,IAAI,KAAA,GAAe,EAAA,EACjB,IAAA,GAAc,EAAA,CAAA;IAEhB,IAAI,CAAC,QAAA;QACH,QAAA,GAAW,UAAU,CAAA,EAAW,KAAA;YAC9B,IAAI,KAAA,CAAM,CAAA,CAAA,KAAO,KAAA;gBAAO,OAAO,cAAA,CAAA;YAC/B,OACE,cAAA,GAAiB,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,KAAA,CAAM,OAAA,CAAQ,KAAA,CAAA,CAAA,CAAQ,IAAA,CAAK,GAAA,CAAA,GAAO,GAAA,CAAA;QAAA,CAAA,CAAA;IAIvE,OAAO,UAAqB,GAAA,EAAa,KAAA;QACvC,IAAI,KAAA,CAAM,MAAA,GAAS,CAAA,EAAG;YACpB,IAAI,OAAA,GAAU,KAAA,CAAM,OAAA,CAAQ,IAAA,CAAA,CAAA;YAC5B,CAAC,OAAA,CAAA,CAAA,CAAU,KAAA,CAAM,MAAA,CAAO,OAAA,GAAU,CAAA,CAAA,CAAA,CAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAA,CAAA;YAClD,CAAC,OAAA,CAAA,CAAA,CAAU,IAAA,CAAK,MAAA,CAAO,OAAA,EAAS,QAAA,EAAU,GAAA,CAAA,CAAA,CAAA,CAAO,IAAA,CAAK,IAAA,CAAK,GAAA,CAAA,CAAA;YAC3D,IAAI,CAAC,KAAA,CAAM,OAAA,CAAQ,KAAA,CAAA;gBAAQ,KAAA,GAAQ,QAAA,CAAU,IAAA,CAAK,IAAA,EAAM,GAAA,EAAK,KAAA,CAAA,CAAA;SAAA;;YACxD,KAAA,CAAM,IAAA,CAAK,KAAA,CAAA,CAAA;QAElB,OAAO,UAAA,IAAc,IAAA,CAAA,CAAA,CAAO,KAAA,CAAA,CAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,IAAA,EAAM,GAAA,EAAK,KAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;AAS5D,SAAA,kBAAA,CAA4B,KAAA;IACjC,OAAO,OAAO,KAAA,KAAU,QAAA,IAAY,KAAA,IAAS,IAAA,IAAQ,MAAA,CAAO,QAAA,CAAS,KAAA,CAAA,CAAA;AAAA,CAAA;AAGhE,SAAA,iBAAA,CACL,WAAA,EACA,WAAA,EACA,GAAA;IAEA,MAAM,iBAAA,GAAoB,eAAA,CAAgB,WAAA,EAAa,WAAA,EAAa,GAAA,CAAA,CAAA;IACpE,OAAO;QACL,eAAA;YACE,OAAO,eAAA,CAAgB,WAAA,EAAa,WAAA,EAAa,iBAAA,EAAmB,GAAA,CAAA,CAAA;QAAA,CAAA;KAAA,CAAA;AAAA,CAAA;AAU1E,SAAA,eAAA,CACE,WAAA,EACA,WAAA,GAA2B,EAAA,EAC3B,GAAA,EACA,IAAA,GAAe,EAAA,EACf,cAAA,GAA2C,IAAI,GAAA,EAAA;IAE/C,MAAM,OAAA,GAAoC,EAAE,KAAA,EAAO,GAAA,EAAA,CAAA;IAEnD,IAAI,CAAC,WAAA,CAAY,GAAA,CAAA,IAAQ,CAAC,cAAA,CAAe,GAAA,CAAI,GAAA,CAAA,EAAM;QACjD,cAAA,CAAe,GAAA,CAAI,GAAA,CAAA,CAAA;QACnB,OAAA,CAAQ,QAAA,GAAW,EAAA,CAAA;QAEnB,KAAA,MAAW,GAAA,IAAO,GAAA,EAAK;YACrB,MAAM,SAAA,GAAY,IAAA,CAAA,CAAA,CAAO,IAAA,GAAO,GAAA,GAAM,GAAA,CAAA,CAAA,CAAM,GAAA,CAAA;YAC5C,IAAI,WAAA,CAAY,MAAA,IAAU,WAAA,CAAY,OAAA,CAAQ,SAAA,CAAA,KAAe,CAAA,CAAA,EAAI;gBAC/D,SAAA;aAAA;YAGF,OAAA,CAAQ,QAAA,CAAS,GAAA,CAAA,GAAO,eAAA,CACtB,WAAA,EACA,WAAA,EACA,GAAA,CAAI,GAAA,CAAA,EACJ,SAAA,CAAA,CAAA;SAAA;KAAA;IAIN,OAAO,OAAA,CAAA;AAAA,CAAA;AAKT,SAAA,eAAA,CACE,WAAA,EACA,YAAA,GAA4B,EAAA,EAC5B,eAAA,EACA,GAAA,EACA,aAAA,GAAyB,KAAA,EACzB,IAAA,GAAe,EAAA;IAEf,MAAM,OAAA,GAAU,eAAA,CAAA,CAAA,CAAkB,eAAA,CAAgB,KAAA,CAAA,CAAA,CAAQ,KAAA,CAAA,CAAA;IAE1D,MAAM,OAAA,GAAU,OAAA,KAAY,GAAA,CAAA;IAE5B,IAAI,aAAA,IAAiB,CAAC,OAAA,IAAW,CAAC,MAAA,CAAO,KAAA,CAAM,GAAA,CAAA,EAAM;QACnD,OAAO,EAAE,UAAA,EAAY,IAAA,EAAM,IAAA,EAAA,CAAA;KAAA;IAG7B,IAAI,WAAA,CAAY,OAAA,CAAA,IAAY,WAAA,CAAY,GAAA,CAAA,EAAM;QAC5C,OAAO,EAAE,UAAA,EAAY,KAAA,EAAA,CAAA;KAAA;IAIvB,MAAM,YAAA,GAAwC,EAAA,CAAA;IAC9C,KAAA,IAAS,GAAA,IAAO,eAAA,CAAgB,QAAA,EAAU;QACxC,YAAA,CAAa,GAAA,CAAA,GAAO,IAAA,CAAA;KAAA;IAEtB,KAAA,IAAS,GAAA,IAAO,GAAA,EAAK;QACnB,YAAA,CAAa,GAAA,CAAA,GAAO,IAAA,CAAA;KAAA;IAGtB,MAAM,eAAA,GAAkB,YAAA,CAAa,MAAA,GAAS,CAAA,CAAA;IAE9C,KAAA,IAAS,GAAA,IAAO,YAAA,EAAc;QAC5B,MAAM,UAAA,GAAa,IAAA,CAAA,CAAA,CAAO,IAAA,GAAO,GAAA,GAAM,GAAA,CAAA,CAAA,CAAM,GAAA,CAAA;QAE7C,IAAI,eAAA,EAAiB;YACnB,MAAM,UAAA,GAAa,YAAA,CAAa,IAAA,CAAK,CAAC,OAAA,EAAA,EAAA;gBACpC,IAAI,OAAA,YAAmB,MAAA,EAAQ;oBAC7B,OAAO,OAAA,CAAQ,IAAA,CAAK,UAAA,CAAA,CAAA;iBAAA;gBAEtB,OAAO,UAAA,KAAe,OAAA,CAAA;YAAA,CAAA,CAAA,CAAA;YAExB,IAAI,UAAA,EAAY;gBACd,SAAA;aAAA;SAAA;QAIJ,MAAM,MAAA,GAAS,eAAA,CACb,WAAA,EACA,YAAA,EACA,eAAA,CAAgB,QAAA,CAAS,GAAA,CAAA,EACzB,GAAA,CAAI,GAAA,CAAA,EACJ,OAAA,EACA,UAAA,CAAA,CAAA;QAGF,IAAI,MAAA,CAAO,UAAA,EAAY;YACrB,OAAO,MAAA,CAAA;SAAA;KAAA;IAGX,OAAO,EAAE,UAAA,EAAY,KAAA,EAAA,CAAA;AAAA,CAAA;AAuChB,SAAA,uCAAA,CACL,OAAA,GAAoD,EAAA;IAEpD,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;QACzC,OAAO,GAAA,EAAA,CAAM,CAAC,IAAA,EAAA,EAAA,CAAS,CAAC,MAAA,EAAA,EAAA,CAAW,IAAA,CAAK,MAAA,CAAA,CAAA;KAAA;IAG1C,IAAI,EACF,WAAA,GAAc,kBAAA,EACd,YAAA,EACA,SAAA,GAAY,EAAA,EACZ,MAAA,EAAA,GACE,OAAA,CAAA;IAGJ,YAAA,GAAe,YAAA,IAAgB,MAAA,CAAA;IAE/B,MAAM,KAAA,GAAQ,iBAAA,CAAkB,IAAA,CAAK,IAAA,EAAM,WAAA,EAAa,YAAA,CAAA,CAAA;IAExD,OAAO,CAAC,EAAE,QAAA,EAAA,EAAA,EAAA;QACR,IAAI,KAAA,GAAQ,QAAA,EAAA,CAAA;QACZ,IAAI,OAAA,GAAU,KAAA,CAAM,KAAA,CAAA,CAAA;QAEpB,IAAI,MAAA,CAAA;QACJ,OAAO,CAAC,IAAA,EAAA,EAAA,CAAS,CAAC,MAAA,EAAA,EAAA;YAChB,MAAM,YAAA,GAAe,mBAAA,CACnB,SAAA,EACA,mCAAA,CAAA,CAAA;YAGF,YAAA,CAAa,WAAA,CAAY,GAAA,EAAA;gBACvB,KAAA,GAAQ,QAAA,EAAA,CAAA;gBAER,MAAA,GAAS,OAAA,CAAQ,eAAA,EAAA,CAAA;gBAEjB,OAAA,GAAU,KAAA,CAAM,KAAA,CAAA,CAAA;gBAEhB,SAAA,CACE,CAAC,MAAA,CAAO,UAAA,EACR,kEACE,MAAA,CAAO,IAAA,IAAQ,EAAA,2GAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA;YAKrB,MAAM,gBAAA,GAAmB,IAAA,CAAK,MAAA,CAAA,CAAA;YAE9B,YAAA,CAAa,WAAA,CAAY,GAAA,EAAA;gBACvB,KAAA,GAAQ,QAAA,EAAA,CAAA;gBAER,MAAA,GAAS,OAAA,CAAQ,eAAA,EAAA,CAAA;gBAEjB,OAAA,GAAU,KAAA,CAAM,KAAA,CAAA,CAAA;gBAEhB,MAAA,CAAO,UAAA,IACL,SAAA,CACE,CAAC,MAAA,CAAO,UAAA,EACR,iEACE,MAAA,CAAO,IAAA,IAAQ,EAAA,uDACsC,SAAA,CACrD,MAAA,CAAA,sEAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA;YAKR,YAAA,CAAa,cAAA,EAAA,CAAA;YAEb,OAAO,gBAAA,CAAA;QAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;;AClRN,SAAA,OAAA,CAAiB,GAAA;IACtB,MAAM,IAAA,GAAO,OAAO,GAAA,CAAA;IACpB,OACE,GAAA,IAAO,IAAA,IACP,IAAA,KAAS,QAAA,IACT,IAAA,KAAS,SAAA,IACT,IAAA,KAAS,QAAA,IACT,KAAA,CAAM,OAAA,CAAQ,GAAA,CAAA,IACd,aAAA,CAAc,GAAA,CAAA,CAAA;AAAA,CAAA;AAcX,SAAA,wBAAA,CACL,KAAA,EACA,IAAA,GAAe,EAAA,EACf,cAAA,GAA8C,OAAA,EAC9C,UAAA,EACA,YAAA,GAA4B,EAAA,EAC5B,KAAA;IAEA,IAAI,uBAAA,CAAA;IAEJ,IAAI,CAAC,cAAA,CAAe,KAAA,CAAA,EAAQ;QAC1B,OAAO;YACL,OAAA,EAAS,IAAA,IAAQ,QAAA;YACjB,KAAA;SAAA,CAAA;KAAA;IAIJ,IAAI,OAAO,KAAA,KAAU,QAAA,IAAY,KAAA,KAAU,IAAA,EAAM;QAC/C,OAAO,KAAA,CAAA;KAAA;IAGT,IAAI,KAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAO,GAAA,CAAI,KAAA,CAAA;QAAQ,OAAO,KAAA,CAAA;IAE9B,MAAM,OAAA,GAAU,UAAA,IAAc,IAAA,CAAA,CAAA,CAAO,UAAA,CAAW,KAAA,CAAA,CAAA,CAAA,CAAS,MAAA,CAAO,OAAA,CAAQ,KAAA,CAAA,CAAA;IAExE,MAAM,eAAA,GAAkB,YAAA,CAAa,MAAA,GAAS,CAAA,CAAA;IAE9C,KAAA,MAAW,CAAC,GAAA,EAAK,WAAA,CAAA,IAAgB,OAAA,EAAS;QACxC,MAAM,UAAA,GAAa,IAAA,CAAA,CAAA,CAAO,IAAA,GAAO,GAAA,GAAM,GAAA,CAAA,CAAA,CAAM,GAAA,CAAA;QAE7C,IAAI,eAAA,EAAiB;YACnB,MAAM,UAAA,GAAa,YAAA,CAAa,IAAA,CAAK,CAAC,OAAA,EAAA,EAAA;gBACpC,IAAI,OAAA,YAAmB,MAAA,EAAQ;oBAC7B,OAAO,OAAA,CAAQ,IAAA,CAAK,UAAA,CAAA,CAAA;iBAAA;gBAEtB,OAAO,UAAA,KAAe,OAAA,CAAA;YAAA,CAAA,CAAA,CAAA;YAExB,IAAI,UAAA,EAAY;gBACd,SAAA;aAAA;SAAA;QAIJ,IAAI,CAAC,cAAA,CAAe,WAAA,CAAA,EAAc;YAChC,OAAO;gBACL,OAAA,EAAS,UAAA;gBACT,KAAA,EAAO,WAAA;aAAA,CAAA;SAAA;QAIX,IAAI,OAAO,WAAA,KAAgB,QAAA,EAAU;YACnC,uBAAA,GAA0B,wBAAA,CACxB,WAAA,EACA,UAAA,EACA,cAAA,EACA,UAAA,EACA,YAAA,EACA,KAAA,CAAA,CAAA;YAGF,IAAI,uBAAA,EAAyB;gBAC3B,OAAO,uBAAA,CAAA;aAAA;SAAA;KAAA;IAKb,IAAI,KAAA,IAAS,cAAA,CAAe,KAAA,CAAA;QAAQ,KAAA,CAAM,GAAA,CAAI,KAAA,CAAA,CAAA;IAE9C,OAAO,KAAA,CAAA;AAAA,CAAA;AAGF,SAAA,cAAA,CAAwB,KAAA;IAC7B,IAAI,CAAC,MAAA,CAAO,QAAA,CAAS,KAAA,CAAA;QAAQ,OAAO,KAAA,CAAA;IAEpC,KAAA,MAAW,WAAA,IAAe,MAAA,CAAO,MAAA,CAAO,KAAA,CAAA,EAAQ;QAC9C,IAAI,OAAO,WAAA,KAAgB,QAAA,IAAY,WAAA,KAAgB,IAAA;YAAM,SAAA;QAE7D,IAAI,CAAC,cAAA,CAAe,WAAA,CAAA;YAAc,OAAO,KAAA,CAAA;KAAA;IAG3C,OAAO,IAAA,CAAA;AAAA,CAAA;AAyEF,SAAA,0CAAA,CACL,OAAA,GAAuD,EAAA;IAEvD,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;QACzC,OAAO,GAAA,EAAA,CAAM,CAAC,IAAA,EAAA,EAAA,CAAS,CAAC,MAAA,EAAA,EAAA,CAAW,IAAA,CAAK,MAAA,CAAA,CAAA;KAAA;IAE1C,MAAM,EACJ,cAAA,GAAiB,OAAA,EACjB,UAAA,EACA,cAAA,GAAiB,EAAA,EACjB,kBAAA,GAAqB,CAAC,UAAA,EAAY,oBAAA,CAAA,EAClC,YAAA,GAAe,EAAA,EACf,SAAA,GAAY,EAAA,EACZ,WAAA,GAAc,KAAA,EACd,aAAA,GAAgB,KAAA,EAChB,YAAA,GAAe,KAAA,EAAA,GACb,OAAA,CAAA;IAEJ,MAAM,KAAA,GACJ,CAAC,YAAA,IAAgB,OAAA,CAAA,CAAA,CAAU,IAAI,OAAA,EAAA,CAAA,CAAA,CAAY,KAAA,CAAA,CAAA;IAE7C,OAAO,CAAC,QAAA,EAAA,EAAA,CAAa,CAAC,IAAA,EAAA,EAAA,CAAS,CAAC,MAAA,EAAA,EAAA;QAC9B,MAAM,MAAA,GAAS,IAAA,CAAK,MAAA,CAAA,CAAA;QAEpB,MAAM,YAAA,GAAe,mBAAA,CACnB,SAAA,EACA,sCAAA,CAAA,CAAA;QAGF,IACE,CAAC,aAAA,IACD,CAAE,CAAA,cAAA,CAAe,MAAA,IAAU,cAAA,CAAe,OAAA,CAAQ,MAAA,CAAO,IAAA,CAAA,KAAU,CAAA,CAAA,CAAA,EACnE;YACA,YAAA,CAAa,WAAA,CAAY,GAAA,EAAA;gBACvB,MAAM,+BAAA,GAAkC,wBAAA,CACtC,MAAA,EACA,EAAA,EACA,cAAA,EACA,UAAA,EACA,kBAAA,EACA,KAAA,CAAA,CAAA;gBAGF,IAAI,+BAAA,EAAiC;oBACnC,MAAM,EAAE,OAAA,EAAS,KAAA,EAAA,GAAU,+BAAA,CAAA;oBAE3B,OAAA,CAAQ,KAAA,CACN,sEAAsE,OAAA,YAAA,EACtE,KAAA,EACA,0DAAA,EACA,MAAA,EACA,uIAAA,EACA,6HAAA,CAAA,CAAA;iBAAA;YAAA,CAAA,CAAA,CAAA;SAAA;QAMR,IAAI,CAAC,WAAA,EAAa;YAChB,YAAA,CAAa,WAAA,CAAY,GAAA,EAAA;gBACvB,MAAM,KAAA,GAAQ,QAAA,CAAS,QAAA,EAAA,CAAA;gBAEvB,MAAM,8BAAA,GAAiC,wBAAA,CACrC,KAAA,EACA,EAAA,EACA,cAAA,EACA,UAAA,EACA,YAAA,EACA,KAAA,CAAA,CAAA;gBAGF,IAAI,8BAAA,EAAgC;oBAClC,MAAM,EAAE,OAAA,EAAS,KAAA,EAAA,GAAU,8BAAA,CAAA;oBAE3B,OAAA,CAAQ,KAAA,CACN,sEAAsE,OAAA,YAAA,EACtE,KAAA,EACA;2DAC+C,MAAA,CAAO,IAAA;+HAAA,CAAA,CAAA;iBAAA;YAAA,CAAA,CAAA,CAAA;YAM5D,YAAA,CAAa,cAAA,EAAA,CAAA;SAAA;QAGf,OAAO,MAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;;ANnQX,SAAA,SAAA,CAAmB,CAAA;IACjB,OAAO,OAAO,CAAA,KAAM,SAAA,CAAA;AAAA,CAAA;AAoCf,SAAA,yBAAA;IAGL,OAAO,SAAA,2BAAA,CAAqC,OAAA;QAC1C,OAAO,oBAAA,CAAqB,OAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;AAgBzB,SAAA,oBAAA,CASL,OAAA,GAAa,EAAA;IAEb,MAAM,EACJ,KAAA,GAAQ,IAAA,EACR,cAAA,GAAiB,IAAA,EACjB,iBAAA,GAAoB,IAAA,EACpB,kBAAA,GAAqB,IAAA,EAAA,GACnB,OAAA,CAAA;IAEJ,IAAI,eAAA,GAAkB,IAAI,eAAA,EAAA,CAAA;IAE1B,IAAI,KAAA,EAAO;QACT,IAAI,SAAA,CAAU,KAAA,CAAA,EAAQ;YACpB,eAAA,CAAgB,IAAA,CAAK,eAAA,CAAA,CAAA;SAAA;aAChB;YACL,eAAA,CAAgB,IAAA,CACd,eAAA,CAAgB,iBAAA,CAAkB,KAAA,CAAM,aAAA,CAAA,CAAA,CAAA;SAAA;KAAA;IAK9C,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;QACzC,IAAI,cAAA,EAAgB;YAElB,IAAI,gBAAA,GAA6D,EAAA,CAAA;YAEjE,IAAI,CAAC,SAAA,CAAU,cAAA,CAAA,EAAiB;gBAC9B,gBAAA,GAAmB,cAAA,CAAA;aAAA;YAGrB,eAAA,CAAgB,OAAA,CACd,uCAAA,CAAwC,gBAAA,CAAA,CAAA,CAAA;SAAA;QAK5C,IAAI,iBAAA,EAAmB;YACrB,IAAI,mBAAA,GAAmE,EAAA,CAAA;YAEvE,IAAI,CAAC,SAAA,CAAU,iBAAA,CAAA,EAAoB;gBACjC,mBAAA,GAAsB,iBAAA,CAAA;aAAA;YAGxB,eAAA,CAAgB,IAAA,CACd,0CAAA,CAA2C,mBAAA,CAAA,CAAA,CAAA;SAAA;QAG/C,IAAI,kBAAA,EAAoB;YACtB,IAAI,oBAAA,GAAgE,EAAA,CAAA;YAEpE,IAAI,CAAC,SAAA,CAAU,kBAAA,CAAA,EAAqB;gBAClC,oBAAA,GAAuB,kBAAA,CAAA;aAAA;YAGzB,eAAA,CAAgB,OAAA,CACd,sCAAA,CAAuC,oBAAA,CAAA,CAAA,CAAA;SAAA;KAAA;IAK7C,OAAO,eAAA,CAAA;AAAA,CAAA;;AH/GT,IAAM,aAAA,GAAgB,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,CAAA;AAiHxC,SAAA,cAAA,CAKL,OAAA;IACA,MAAM,2BAAA,GAA8B,yBAAA,EAAA,CAAA;IAEpC,MAAM,EACJ,OAAA,GAAU,KAAA,CAAA,EACV,UAAA,GAAa,2BAAA,EAAA,EACb,QAAA,GAAW,IAAA,EACX,cAAA,GAAiB,KAAA,CAAA,EACjB,SAAA,GAAY,KAAA,CAAA,EAAA,GACV,OAAA,IAAW,EAAA,CAAA;IAEf,IAAI,WAAA,CAAA;IAEJ,IAAI,OAAO,OAAA,KAAY,UAAA,EAAY;QACjC,WAAA,GAAc,OAAA,CAAA;KAAA;SAAA,IACL,aAAA,CAAc,OAAA,CAAA,EAAU;QACjC,WAAA,GAAc,eAAA,CAAgB,OAAA,CAAA,CAAA;KAAA;SACzB;QACL,MAAM,IAAI,KAAA,CACR,0HAAA,CAAA,CAAA;KAAA;IAIJ,IAAI,eAAA,GAAkB,UAAA,CAAA;IACtB,IAAI,OAAO,eAAA,KAAoB,UAAA,EAAY;QACzC,eAAA,GAAkB,eAAA,CAAgB,2BAAA,CAAA,CAAA;QAElC,IAAI,CAAC,aAAA,IAAiB,CAAC,KAAA,CAAM,OAAA,CAAQ,eAAA,CAAA,EAAkB;YACrD,MAAM,IAAI,KAAA,CACR,mFAAA,CAAA,CAAA;SAAA;KAAA;IAIN,IACE,CAAC,aAAA,IACD,eAAA,CAAgB,IAAA,CAAK,CAAC,IAAA,EAAA,EAAA,CAAc,OAAO,IAAA,KAAS,UAAA,CAAA,EACpD;QACA,MAAM,IAAI,KAAA,CACR,+DAAA,CAAA,CAAA;KAAA;IAIJ,MAAM,kBAAA,GAAoC,eAAA,CAAgB,GAAG,eAAA,CAAA,CAAA;IAE7D,IAAI,YAAA,GAAe,QAAA,CAAA;IAEnB,IAAI,QAAA,EAAU;QACZ,YAAA,GAAe,mBAAA,CAAoB,cAAA,CAAA;YAEjC,KAAA,EAAO,CAAC,aAAA;SAAA,EACJ,OAAO,QAAA,KAAa,QAAA,IAAY,QAAA,CAAA,CAAA,CAAA;KAAA;IAIxC,MAAM,gBAAA,GAAmB,IAAI,aAAA,CAAc,kBAAA,CAAA,CAAA;IAC3C,IAAI,cAAA,GAA4B,gBAAA,CAAA;IAEhC,IAAI,KAAA,CAAM,OAAA,CAAQ,SAAA,CAAA,EAAY;QAC5B,cAAA,GAAiB,CAAC,kBAAA,EAAoB,GAAG,SAAA,CAAA,CAAA;KAAA;SAAA,IAChC,OAAO,SAAA,KAAc,UAAA,EAAY;QAC1C,cAAA,GAAiB,SAAA,CAAU,gBAAA,CAAA,CAAA;KAAA;IAG7B,MAAM,gBAAA,GAAmB,YAAA,CAAa,GAAG,cAAA,CAAA,CAAA;IAEzC,OAAO,WAAA,CAAY,WAAA,EAAa,cAAA,EAAgB,gBAAA,CAAA,CAAA;AAAA,CAAA;;AUpNlD,OAAA,gBAAA,EAAA,EAAA,OAAA,IAAA,QAAA,EAAA,WAAA,IAAA,YAAA,EAAA,MAAA,OAAA,CAAA;;AC4HO,SAAA,6BAAA,CACL,eAAA;IAMA,MAAM,UAAA,GAAmC,EAAA,CAAA;IACzC,MAAM,cAAA,GAAwD,EAAA,CAAA;IAC9D,IAAI,kBAAA,CAAA;IACJ,MAAM,OAAA,GAAU;QACd,OAAA,CACE,mBAAA,EACA,OAAA;YAEA,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;gBAMzC,IAAI,cAAA,CAAe,MAAA,GAAS,CAAA,EAAG;oBAC7B,MAAM,IAAI,KAAA,CACR,6EAAA,CAAA,CAAA;iBAAA;gBAGJ,IAAI,kBAAA,EAAoB;oBACtB,MAAM,IAAI,KAAA,CACR,iFAAA,CAAA,CAAA;iBAAA;aAAA;YAIN,MAAM,IAAA,GACJ,OAAO,mBAAA,KAAwB,QAAA,CAAA,CAAA,CAC3B,mBAAA,CAAA,CAAA,CACA,mBAAA,CAAoB,IAAA,CAAA;YAC1B,IAAI,CAAC,IAAA,EAAM;gBACT,MAAM,IAAI,KAAA,CACR,8DAAA,CAAA,CAAA;aAAA;YAGJ,IAAI,IAAA,IAAQ,UAAA,EAAY;gBACtB,MAAM,IAAI,KAAA,CACR,+EAAA,CAAA,CAAA;aAAA;YAGJ,UAAA,CAAW,IAAA,CAAA,GAAQ,OAAA,CAAA;YACnB,OAAO,OAAA,CAAA;QAAA,CAAA;QAET,UAAA,CACE,OAAA,EACA,OAAA;YAEA,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;gBACzC,IAAI,kBAAA,EAAoB;oBACtB,MAAM,IAAI,KAAA,CACR,oFAAA,CAAA,CAAA;iBAAA;aAAA;YAIN,cAAA,CAAe,IAAA,CAAK,EAAE,OAAA,EAAS,OAAA,EAAA,CAAA,CAAA;YAC/B,OAAO,OAAA,CAAA;QAAA,CAAA;QAET,cAAA,CAAe,OAAA;YACb,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;gBACzC,IAAI,kBAAA,EAAoB;oBACtB,MAAM,IAAI,KAAA,CAAM,kDAAA,CAAA,CAAA;iBAAA;aAAA;YAGpB,kBAAA,GAAqB,OAAA,CAAA;YACrB,OAAO,OAAA,CAAA;QAAA,CAAA;KAAA,CAAA;IAGX,eAAA,CAAgB,OAAA,CAAA,CAAA;IAChB,OAAO,CAAC,UAAA,EAAY,cAAA,EAAgB,kBAAA,CAAA,CAAA;AAAA,CAAA;;AD7HtC,SAAA,eAAA,CAA4B,CAAA;IAC1B,OAAO,OAAO,CAAA,KAAM,UAAA,CAAA;AAAA,CAAA;AAOtB,IAAI,4BAAA,GAA+B,KAAA,CAAA;AAqI5B,SAAA,aAAA,CACL,YAAA,EACA,oBAAA,EAGA,cAAA,GAAgE,EAAA,EAChE,kBAAA;IAEA,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;QACzC,IAAI,OAAO,oBAAA,KAAyB,QAAA,EAAU;YAC5C,IAAI,CAAC,4BAAA,EAA8B;gBACjC,4BAAA,GAA+B,IAAA,CAAA;gBAC/B,OAAA,CAAQ,IAAA,CACN,2LAAA,CAAA,CAAA;aAAA;SAAA;KAAA;IAMR,IAAI,CAAC,UAAA,EAAY,mBAAA,EAAqB,uBAAA,CAAA,GACpC,OAAO,oBAAA,KAAyB,UAAA,CAAA,CAAA,CAC5B,6BAAA,CAA8B,oBAAA,CAAA,CAAA,CAAA,CAC9B,CAAC,oBAAA,EAAsB,cAAA,EAAgB,kBAAA,CAAA,CAAA;IAG7C,IAAI,eAAA,CAAA;IACJ,IAAI,eAAA,CAAgB,YAAA,CAAA,EAAe;QACjC,eAAA,GAAkB,GAAA,EAAA,CAAM,eAAA,CAAgB,YAAA,EAAA,CAAA,CAAA;KAAA;SACnC;QACL,MAAM,kBAAA,GAAqB,eAAA,CAAgB,YAAA,CAAA,CAAA;QAC3C,eAAA,GAAkB,GAAA,EAAA,CAAM,kBAAA,CAAA;KAAA;IAG1B,SAAA,OAAA,CAAiB,KAAA,GAAQ,eAAA,EAAA,EAAmB,MAAA;QAC1C,IAAI,YAAA,GAAe;YACjB,UAAA,CAAW,MAAA,CAAO,IAAA,CAAA;YAClB,GAAG,mBAAA,CACA,MAAA,CAAO,CAAC,EAAE,OAAA,EAAA,EAAA,EAAA,CAAc,OAAA,CAAQ,MAAA,CAAA,CAAA,CAChC,GAAA,CAAI,CAAC,EAAE,OAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAc,QAAA,CAAA;SAAA,CAAA;QAE1B,IAAI,YAAA,CAAa,MAAA,CAAO,CAAC,EAAA,EAAA,EAAA,CAAO,CAAC,CAAC,EAAA,CAAA,CAAI,MAAA,KAAW,CAAA,EAAG;YAClD,YAAA,GAAe,CAAC,uBAAA,CAAA,CAAA;SAAA;QAGlB,OAAO,YAAA,CAAa,MAAA,CAAO,CAAC,aAAA,EAAe,WAAA,EAAA,EAAA;YACzC,IAAI,WAAA,EAAa;gBACf,IAAI,QAAA,CAAQ,aAAA,CAAA,EAAgB;oBAI1B,MAAM,KAAA,GAAQ,aAAA,CAAA;oBACd,MAAM,MAAA,GAAS,WAAA,CAAY,KAAA,EAAO,MAAA,CAAA,CAAA;oBAElC,IAAI,MAAA,KAAW,KAAA,CAAA,EAAW;wBACxB,OAAO,aAAA,CAAA;qBAAA;oBAGT,OAAO,MAAA,CAAA;iBAAA;qBAAA,IACE,CAAC,YAAA,CAAY,aAAA,CAAA,EAAgB;oBAGtC,MAAM,MAAA,GAAS,WAAA,CAAY,aAAA,EAAsB,MAAA,CAAA,CAAA;oBAEjD,IAAI,MAAA,KAAW,KAAA,CAAA,EAAW;wBACxB,IAAI,aAAA,KAAkB,IAAA,EAAM;4BAC1B,OAAO,aAAA,CAAA;yBAAA;wBAET,MAAM,KAAA,CACJ,mEAAA,CAAA,CAAA;qBAAA;oBAIJ,OAAO,MAAA,CAAA;iBAAA;qBACF;oBAIL,OAAO,gBAAA,CAAgB,aAAA,EAAe,CAAC,KAAA,EAAA,EAAA;wBACrC,OAAO,WAAA,CAAY,KAAA,EAAO,MAAA,CAAA,CAAA;oBAAA,CAAA,CAAA,CAAA;iBAAA;aAAA;YAKhC,OAAO,aAAA,CAAA;QAAA,CAAA,EACN,KAAA,CAAA,CAAA;IAAA,CAAA;IAGL,OAAA,CAAQ,eAAA,GAAkB,eAAA,CAAA;IAE1B,OAAO,OAAA,CAAA;AAAA,CAAA;;AE3RT,IAAI,6BAAA,GAA+B,KAAA,CAAA;AA6OnC,SAAA,QAAA,CAAiB,KAAA,EAAe,SAAA;IAC9B,OAAO,GAAG,KAAA,IAAS,SAAA,EAAA,CAAA;AAAA,CAAA;AAad,SAAA,WAAA,CAKL,OAAA;IAEA,MAAM,EAAE,IAAA,EAAA,GAAS,OAAA,CAAA;IACjB,IAAI,CAAC,IAAA,EAAM;QACT,MAAM,IAAI,KAAA,CAAM,6CAAA,CAAA,CAAA;KAAA;IAGlB,IACE,OAAO,OAAA,KAAY,WAAA,IACnB,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,aAAA,EACzB;QACA,IAAI,OAAA,CAAQ,YAAA,KAAiB,KAAA,CAAA,EAAW;YACtC,OAAA,CAAQ,KAAA,CACN,0GAAA,CAAA,CAAA;SAAA;KAAA;IAKN,MAAM,YAAA,GACJ,OAAO,OAAA,CAAQ,YAAA,IAAgB,UAAA,CAAA,CAAA,CAC3B,OAAA,CAAQ,YAAA,CAAA,CAAA,CACR,eAAA,CAAgB,OAAA,CAAQ,YAAA,CAAA,CAAA;IAE9B,MAAM,QAAA,GAAW,OAAA,CAAQ,QAAA,IAAY,EAAA,CAAA;IAErC,MAAM,YAAA,GAAe,MAAA,CAAO,IAAA,CAAK,QAAA,CAAA,CAAA;IAEjC,MAAM,uBAAA,GAAuD,EAAA,CAAA;IAC7D,MAAM,uBAAA,GAAuD,EAAA,CAAA;IAC7D,MAAM,cAAA,GAA2C,EAAA,CAAA;IAEjD,YAAA,CAAa,OAAA,CAAQ,CAAC,WAAA,EAAA,EAAA;QACpB,MAAM,uBAAA,GAA0B,QAAA,CAAS,WAAA,CAAA,CAAA;QACzC,MAAM,IAAA,GAAO,QAAA,CAAQ,IAAA,EAAM,WAAA,CAAA,CAAA;QAE3B,IAAI,WAAA,CAAA;QACJ,IAAI,eAAA,CAAA;QAEJ,IAAI,SAAA,IAAa,uBAAA,EAAyB;YACxC,WAAA,GAAc,uBAAA,CAAwB,OAAA,CAAA;YACtC,eAAA,GAAkB,uBAAA,CAAwB,OAAA,CAAA;SAAA;aACrC;YACL,WAAA,GAAc,uBAAA,CAAA;SAAA;QAGhB,uBAAA,CAAwB,WAAA,CAAA,GAAe,WAAA,CAAA;QACvC,uBAAA,CAAwB,IAAA,CAAA,GAAQ,WAAA,CAAA;QAChC,cAAA,CAAe,WAAA,CAAA,GAAe,eAAA,CAAA,CAAA,CAC1B,YAAA,CAAa,IAAA,EAAM,eAAA,CAAA,CAAA,CAAA,CACnB,YAAA,CAAa,IAAA,CAAA,CAAA;IAAA,CAAA,CAAA,CAAA;IAGnB,SAAA,YAAA;QACE,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;YACzC,IAAI,OAAO,OAAA,CAAQ,aAAA,KAAkB,QAAA,EAAU;gBAC7C,IAAI,CAAC,6BAAA,EAA8B;oBACjC,6BAAA,GAA+B,IAAA,CAAA;oBAC/B,OAAA,CAAQ,IAAA,CACN,qMAAA,CAAA,CAAA;iBAAA;aAAA;SAAA;QAKR,MAAM,CACJ,aAAA,GAAgB,EAAA,EAChB,cAAA,GAAiB,EAAA,EACjB,kBAAA,GAAqB,KAAA,CAAA,CAAA,GAErB,OAAO,OAAA,CAAQ,aAAA,KAAkB,UAAA,CAAA,CAAA,CAC7B,6BAAA,CAA8B,OAAA,CAAQ,aAAA,CAAA,CAAA,CAAA,CACtC,CAAC,OAAA,CAAQ,aAAA,CAAA,CAAA;QAEf,MAAM,iBAAA,GAAoB,cAAA,CAAA,cAAA,CAAA,EAAA,EAAK,aAAA,CAAA,EAAkB,uBAAA,CAAA,CAAA;QAEjD,OAAO,aAAA,CAAc,YAAA,EAAc,CAAC,OAAA,EAAA,EAAA;YAClC,KAAA,IAAS,GAAA,IAAO,iBAAA,EAAmB;gBACjC,OAAA,CAAQ,OAAA,CAAQ,GAAA,EAAK,iBAAA,CAAkB,GAAA,CAAA,CAAA,CAAA;aAAA;YAEzC,KAAA,IAAS,CAAA,IAAK,cAAA,EAAgB;gBAC5B,OAAA,CAAQ,UAAA,CAAW,CAAA,CAAE,OAAA,EAAS,CAAA,CAAE,OAAA,CAAA,CAAA;aAAA;YAElC,IAAI,kBAAA,EAAoB;gBACtB,OAAA,CAAQ,cAAA,CAAe,kBAAA,CAAA,CAAA;aAAA;QAAA,CAAA,CAAA,CAAA;IAAA,CAAA;IAK7B,IAAI,QAAA,CAAA;IAEJ,OAAO;QACL,IAAA;QACA,OAAA,CAAQ,KAAA,EAAO,MAAA;YACb,IAAI,CAAC,QAAA;gBAAU,QAAA,GAAW,YAAA,EAAA,CAAA;YAE1B,OAAO,QAAA,CAAS,KAAA,EAAO,MAAA,CAAA,CAAA;QAAA,CAAA;QAEzB,OAAA,EAAS,cAAA;QACT,YAAA,EAAc,uBAAA;QACd,eAAA;YACE,IAAI,CAAC,QAAA;gBAAU,QAAA,GAAW,YAAA,EAAA,CAAA;YAE1B,OAAO,QAAA,CAAS,eAAA,EAAA,CAAA;QAAA,CAAA;KAAA,CAAA;AAAA,CAAA;;ACxXf,SAAA,qBAAA;IACL,OAAO;QACL,GAAA,EAAK,EAAA;QACL,QAAA,EAAU,EAAA;KAAA,CAAA;AAAA,CAAA;AAIP,SAAA,yBAAA;IAKL,SAAA,eAAA,CAAyB,eAAA,GAAuB,EAAA;QAC9C,OAAO,MAAA,CAAO,MAAA,CAAO,qBAAA,EAAA,EAAyB,eAAA,CAAA,CAAA;IAAA,CAAA;IAGhD,OAAO,EAAE,eAAA,EAAA,CAAA;AAAA,CAAA;;ACTJ,SAAA,sBAAA;IAKL,SAAA,YAAA,CACE,WAAA;QAEA,MAAM,SAAA,GAAY,CAAC,KAAA,EAAA,EAAA,CAA0B,KAAA,CAAM,GAAA,CAAA;QAEnD,MAAM,cAAA,GAAiB,CAAC,KAAA,EAAA,EAAA,CAA0B,KAAA,CAAM,QAAA,CAAA;QAExD,MAAM,SAAA,GAAY,uBAAA,CAChB,SAAA,EACA,cAAA,EACA,CAAC,GAAA,EAAK,QAAA,EAAA,EAAA,CAAkB,GAAA,CAAI,GAAA,CAAI,CAAC,EAAA,EAAA,EAAA,CAAO,QAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA;QAGnD,MAAM,QAAA,GAAW,CAAC,CAAA,EAAY,EAAA,EAAA,EAAA,CAAiB,EAAA,CAAA;QAE/C,MAAM,UAAA,GAAa,CAAC,QAAA,EAAyB,EAAA,EAAA,EAAA,CAAiB,QAAA,CAAS,EAAA,CAAA,CAAA;QAEvE,MAAM,WAAA,GAAc,uBAAA,CAAwB,SAAA,EAAW,CAAC,GAAA,EAAA,EAAA,CAAQ,GAAA,CAAI,MAAA,CAAA,CAAA;QAEpE,IAAI,CAAC,WAAA,EAAa;YAChB,OAAO;gBACL,SAAA;gBACA,cAAA;gBACA,SAAA;gBACA,WAAA;gBACA,UAAA,EAAY,uBAAA,CACV,cAAA,EACA,QAAA,EACA,UAAA,CAAA;aAAA,CAAA;SAAA;QAKN,MAAM,wBAAA,GAA2B,uBAAA,CAC/B,WAAA,EACA,cAAA,CAAA,CAAA;QAGF,OAAO;YACL,SAAA,EAAW,uBAAA,CAAwB,WAAA,EAAa,SAAA,CAAA;YAChD,cAAA,EAAgB,wBAAA;YAChB,SAAA,EAAW,uBAAA,CAAwB,WAAA,EAAa,SAAA,CAAA;YAChD,WAAA,EAAa,uBAAA,CAAwB,WAAA,EAAa,WAAA,CAAA;YAClD,UAAA,EAAY,uBAAA,CACV,wBAAA,EACA,QAAA,EACA,UAAA,CAAA;SAAA,CAAA;IAAA,CAAA;IAKN,OAAO,EAAE,YAAA,EAAA,CAAA;AAAA,CAAA;;ACjEX,OAAA,gBAAA,EAAA,EAAA,OAAA,IAAA,QAAA,EAAA,MAAA,OAAA,CAAA;AAMO,SAAA,iCAAA,CACL,OAAA;IAEA,MAAM,QAAA,GAAW,mBAAA,CAAoB,CAAC,CAAA,EAAc,KAAA,EAAA,EAAA,CAClD,OAAA,CAAQ,KAAA,CAAA,CAAA,CAAA;IAGV,OAAO,SAAA,SAAA,CACL,KAAA;QAEA,OAAO,QAAA,CAAS,KAAA,EAAY,KAAA,CAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;AAIzB,SAAA,mBAAA,CACL,OAAA;IAEA,OAAO,SAAA,SAAA,CACL,KAAA,EACA,GAAA;QAEA,SAAA,uBAAA,CACE,IAAA;YAEA,OAAO,KAAA,CAAM,IAAA,CAAA,CAAA;QAAA,CAAA;QAGf,MAAM,UAAA,GAAa,CAAC,KAAA,EAAA,EAAA;YAClB,IAAI,uBAAA,CAAwB,GAAA,CAAA,EAAM;gBAChC,OAAA,CAAQ,GAAA,CAAI,OAAA,EAAS,KAAA,CAAA,CAAA;aAAA;iBAChB;gBACL,OAAA,CAAQ,GAAA,EAAK,KAAA,CAAA,CAAA;aAAA;QAAA,CAAA,CAAA;QAIjB,IAAI,QAAA,CAAQ,KAAA,CAAA,EAAQ;YAIlB,UAAA,CAAW,KAAA,CAAA,CAAA;YAGX,OAAO,KAAA,CAAA;SAAA;aACF;YAIL,OAAO,gBAAA,CAAgB,KAAA,EAAO,UAAA,CAAA,CAAA;SAAA;IAAA,CAAA,CAAA;AAAA,CAAA;;ACnD7B,SAAA,aAAA,CAA0B,MAAA,EAAW,QAAA;IAC1C,MAAM,GAAA,GAAM,QAAA,CAAS,MAAA,CAAA,CAAA;IAErB,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,IAAgB,GAAA,KAAQ,KAAA,CAAA,EAAW;QAC9D,OAAA,CAAQ,IAAA,CACN,wEAAA,EACA,iEAAA,EACA,6BAAA,EACA,MAAA,EACA,gCAAA,EACA,QAAA,CAAS,QAAA,EAAA,CAAA,CAAA;KAAA;IAIb,OAAO,GAAA,CAAA;AAAA,CAAA;AAGF,SAAA,mBAAA,CACL,QAAA;IAEA,IAAI,CAAC,KAAA,CAAM,OAAA,CAAQ,QAAA,CAAA,EAAW;QAC5B,QAAA,GAAW,MAAA,CAAO,MAAA,CAAO,QAAA,CAAA,CAAA;KAAA;IAG3B,OAAO,QAAA,CAAA;AAAA,CAAA;AAGF,SAAA,yBAAA,CACL,WAAA,EACA,QAAA,EACA,KAAA;IAEA,WAAA,GAAc,mBAAA,CAAoB,WAAA,CAAA,CAAA;IAElC,MAAM,KAAA,GAAa,EAAA,CAAA;IACnB,MAAM,OAAA,GAAuB,EAAA,CAAA;IAE7B,KAAA,MAAW,MAAA,IAAU,WAAA,EAAa;QAChC,MAAM,EAAA,GAAK,aAAA,CAAc,MAAA,EAAQ,QAAA,CAAA,CAAA;QACjC,IAAI,EAAA,IAAM,KAAA,CAAM,QAAA,EAAU;YACxB,OAAA,CAAQ,IAAA,CAAK,EAAE,EAAA,EAAI,OAAA,EAAS,MAAA,EAAA,CAAA,CAAA;SAAA;aACvB;YACL,KAAA,CAAM,IAAA,CAAK,MAAA,CAAA,CAAA;SAAA;KAAA;IAGf,OAAO,CAAC,KAAA,EAAO,OAAA,CAAA,CAAA;AAAA,CAAA;;AC9BV,SAAA,0BAAA,CACL,QAAA;IAIA,SAAA,aAAA,CAAuB,MAAA,EAAW,KAAA;QAChC,MAAM,GAAA,GAAM,aAAA,CAAc,MAAA,EAAQ,QAAA,CAAA,CAAA;QAElC,IAAI,GAAA,IAAO,KAAA,CAAM,QAAA,EAAU;YACzB,OAAA;SAAA;QAGF,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,GAAA,CAAA,CAAA;QACf,KAAA,CAAM,QAAA,CAAS,GAAA,CAAA,GAAO,MAAA,CAAA;IAAA,CAAA;IAGxB,SAAA,cAAA,CACE,WAAA,EACA,KAAA;QAEA,WAAA,GAAc,mBAAA,CAAoB,WAAA,CAAA,CAAA;QAElC,KAAA,MAAW,MAAA,IAAU,WAAA,EAAa;YAChC,aAAA,CAAc,MAAA,EAAQ,KAAA,CAAA,CAAA;SAAA;IAAA,CAAA;IAI1B,SAAA,aAAA,CAAuB,MAAA,EAAW,KAAA;QAChC,MAAM,GAAA,GAAM,aAAA,CAAc,MAAA,EAAQ,QAAA,CAAA,CAAA;QAClC,IAAI,CAAE,CAAA,GAAA,IAAO,KAAA,CAAM,QAAA,CAAA,EAAW;YAC5B,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,GAAA,CAAA,CAAA;SAAA;QAEjB,KAAA,CAAM,QAAA,CAAS,GAAA,CAAA,GAAO,MAAA,CAAA;IAAA,CAAA;IAGxB,SAAA,cAAA,CACE,WAAA,EACA,KAAA;QAEA,WAAA,GAAc,mBAAA,CAAoB,WAAA,CAAA,CAAA;QAClC,KAAA,MAAW,MAAA,IAAU,WAAA,EAAa;YAChC,aAAA,CAAc,MAAA,EAAQ,KAAA,CAAA,CAAA;SAAA;IAAA,CAAA;IAI1B,SAAA,aAAA,CACE,WAAA,EACA,KAAA;QAEA,WAAA,GAAc,mBAAA,CAAoB,WAAA,CAAA,CAAA;QAElC,KAAA,CAAM,GAAA,GAAM,EAAA,CAAA;QACZ,KAAA,CAAM,QAAA,GAAW,EAAA,CAAA;QAEjB,cAAA,CAAe,WAAA,EAAa,KAAA,CAAA,CAAA;IAAA,CAAA;IAG9B,SAAA,gBAAA,CAA0B,GAAA,EAAe,KAAA;QACvC,OAAO,iBAAA,CAAkB,CAAC,GAAA,CAAA,EAAM,KAAA,CAAA,CAAA;IAAA,CAAA;IAGlC,SAAA,iBAAA,CAA2B,IAAA,EAA2B,KAAA;QACpD,IAAI,SAAA,GAAY,KAAA,CAAA;QAEhB,IAAA,CAAK,OAAA,CAAQ,CAAC,GAAA,EAAA,EAAA;YACZ,IAAI,GAAA,IAAO,KAAA,CAAM,QAAA,EAAU;gBACzB,OAAO,KAAA,CAAM,QAAA,CAAS,GAAA,CAAA,CAAA;gBACtB,SAAA,GAAY,IAAA,CAAA;aAAA;QAAA,CAAA,CAAA,CAAA;QAIhB,IAAI,SAAA,EAAW;YACb,KAAA,CAAM,GAAA,GAAM,KAAA,CAAM,GAAA,CAAI,MAAA,CAAO,CAAC,EAAA,EAAA,EAAA,CAAO,EAAA,IAAM,KAAA,CAAM,QAAA,CAAA,CAAA;SAAA;IAAA,CAAA;IAIrD,SAAA,gBAAA,CAA0B,KAAA;QACxB,MAAA,CAAO,MAAA,CAAO,KAAA,EAAO;YACnB,GAAA,EAAK,EAAA;YACL,QAAA,EAAU,EAAA;SAAA,CAAA,CAAA;IAAA,CAAA;IAId,SAAA,UAAA,CACE,IAAA,EACA,MAAA,EACA,KAAA;QAEA,MAAM,SAAA,GAAW,KAAA,CAAM,QAAA,CAAS,MAAA,CAAO,EAAA,CAAA,CAAA;QACvC,MAAM,OAAA,GAAa,MAAA,CAAO,MAAA,CAAO,EAAA,EAAI,SAAA,EAAU,MAAA,CAAO,OAAA,CAAA,CAAA;QACtD,MAAM,MAAA,GAAS,aAAA,CAAc,OAAA,EAAS,QAAA,CAAA,CAAA;QACtC,MAAM,SAAA,GAAY,MAAA,KAAW,MAAA,CAAO,EAAA,CAAA;QAEpC,IAAI,SAAA,EAAW;YACb,IAAA,CAAK,MAAA,CAAO,EAAA,CAAA,GAAM,MAAA,CAAA;YAClB,OAAO,KAAA,CAAM,QAAA,CAAS,MAAA,CAAO,EAAA,CAAA,CAAA;SAAA;QAG/B,KAAA,CAAM,QAAA,CAAS,MAAA,CAAA,GAAU,OAAA,CAAA;QAEzB,OAAO,SAAA,CAAA;IAAA,CAAA;IAGT,SAAA,gBAAA,CAA0B,MAAA,EAAmB,KAAA;QAC3C,OAAO,iBAAA,CAAkB,CAAC,MAAA,CAAA,EAAS,KAAA,CAAA,CAAA;IAAA,CAAA;IAGrC,SAAA,iBAAA,CACE,OAAA,EACA,KAAA;QAEA,MAAM,OAAA,GAAsC,EAAA,CAAA;QAE5C,MAAM,gBAAA,GAAgD,EAAA,CAAA;QAEtD,OAAA,CAAQ,OAAA,CAAQ,CAAC,MAAA,EAAA,EAAA;YAEf,IAAI,MAAA,CAAO,EAAA,IAAM,KAAA,CAAM,QAAA,EAAU;gBAE/B,gBAAA,CAAiB,MAAA,CAAO,EAAA,CAAA,GAAM;oBAC5B,EAAA,EAAI,MAAA,CAAO,EAAA;oBAGX,OAAA,EAAS,cAAA,CAAA,cAAA,CAAA,EAAA,EACH,gBAAA,CAAiB,MAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CACxB,gBAAA,CAAiB,MAAA,CAAO,EAAA,CAAA,CAAI,OAAA,CAAA,CAAA,CAC5B,IAAA,CAAA,EACD,MAAA,CAAO,OAAA,CAAA;iBAAA,CAAA;aAAA;QAAA,CAAA,CAAA,CAAA;QAMlB,OAAA,GAAU,MAAA,CAAO,MAAA,CAAO,gBAAA,CAAA,CAAA;QAExB,MAAM,iBAAA,GAAoB,OAAA,CAAQ,MAAA,GAAS,CAAA,CAAA;QAE3C,IAAI,iBAAA,EAAmB;YACrB,MAAM,YAAA,GACJ,OAAA,CAAQ,MAAA,CAAO,CAAC,MAAA,EAAA,EAAA,CAAW,UAAA,CAAW,OAAA,EAAS,MAAA,EAAQ,KAAA,CAAA,CAAA,CAAQ,MAAA,GAC/D,CAAA,CAAA;YAEF,IAAI,YAAA,EAAc;gBAChB,KAAA,CAAM,GAAA,GAAM,MAAA,CAAO,IAAA,CAAK,KAAA,CAAM,QAAA,CAAA,CAAA;aAAA;SAAA;IAAA,CAAA;IAKpC,SAAA,gBAAA,CAA0B,MAAA,EAAW,KAAA;QACnC,OAAO,iBAAA,CAAkB,CAAC,MAAA,CAAA,EAAS,KAAA,CAAA,CAAA;IAAA,CAAA;IAGrC,SAAA,iBAAA,CACE,WAAA,EACA,KAAA;QAEA,MAAM,CAAC,KAAA,EAAO,OAAA,CAAA,GAAW,yBAAA,CACvB,WAAA,EACA,QAAA,EACA,KAAA,CAAA,CAAA;QAGF,iBAAA,CAAkB,OAAA,EAAS,KAAA,CAAA,CAAA;QAC3B,cAAA,CAAe,KAAA,EAAO,KAAA,CAAA,CAAA;IAAA,CAAA;IAGxB,OAAO;QACL,SAAA,EAAW,iCAAA,CAAkC,gBAAA,CAAA;QAC7C,MAAA,EAAQ,mBAAA,CAAoB,aAAA,CAAA;QAC5B,OAAA,EAAS,mBAAA,CAAoB,cAAA,CAAA;QAC7B,MAAA,EAAQ,mBAAA,CAAoB,aAAA,CAAA;QAC5B,OAAA,EAAS,mBAAA,CAAoB,cAAA,CAAA;QAC7B,MAAA,EAAQ,mBAAA,CAAoB,aAAA,CAAA;QAC5B,SAAA,EAAW,mBAAA,CAAoB,gBAAA,CAAA;QAC/B,UAAA,EAAY,mBAAA,CAAoB,iBAAA,CAAA;QAChC,SAAA,EAAW,mBAAA,CAAoB,gBAAA,CAAA;QAC/B,UAAA,EAAY,mBAAA,CAAoB,iBAAA,CAAA;QAChC,SAAA,EAAW,mBAAA,CAAoB,gBAAA,CAAA;QAC/B,UAAA,EAAY,mBAAA,CAAoB,iBAAA,CAAA;KAAA,CAAA;AAAA,CAAA;;ACnL7B,SAAA,wBAAA,CACL,QAAA,EACA,IAAA;IAIA,MAAM,EAAE,SAAA,EAAW,UAAA,EAAY,SAAA,EAAA,GAC7B,0BAAA,CAA2B,QAAA,CAAA,CAAA;IAE7B,SAAA,aAAA,CAAuB,MAAA,EAAW,KAAA;QAChC,OAAO,cAAA,CAAe,CAAC,MAAA,CAAA,EAAS,KAAA,CAAA,CAAA;IAAA,CAAA;IAGlC,SAAA,cAAA,CACE,WAAA,EACA,KAAA;QAEA,WAAA,GAAc,mBAAA,CAAoB,WAAA,CAAA,CAAA;QAElC,MAAM,MAAA,GAAS,WAAA,CAAY,MAAA,CACzB,CAAC,KAAA,EAAA,EAAA,CAAU,CAAE,CAAA,aAAA,CAAc,KAAA,EAAO,QAAA,CAAA,IAAa,KAAA,CAAM,QAAA,CAAA,CAAA,CAAA;QAGvD,IAAI,MAAA,CAAO,MAAA,KAAW,CAAA,EAAG;YACvB,KAAA,CAAM,MAAA,EAAQ,KAAA,CAAA,CAAA;SAAA;IAAA,CAAA;IAIlB,SAAA,aAAA,CAAuB,MAAA,EAAW,KAAA;QAChC,OAAO,cAAA,CAAe,CAAC,MAAA,CAAA,EAAS,KAAA,CAAA,CAAA;IAAA,CAAA;IAGlC,SAAA,cAAA,CACE,WAAA,EACA,KAAA;QAEA,WAAA,GAAc,mBAAA,CAAoB,WAAA,CAAA,CAAA;QAClC,IAAI,WAAA,CAAY,MAAA,KAAW,CAAA,EAAG;YAC5B,KAAA,CAAM,WAAA,EAAa,KAAA,CAAA,CAAA;SAAA;IAAA,CAAA;IAIvB,SAAA,aAAA,CACE,WAAA,EACA,KAAA;QAEA,WAAA,GAAc,mBAAA,CAAoB,WAAA,CAAA,CAAA;QAClC,KAAA,CAAM,QAAA,GAAW,EAAA,CAAA;QACjB,KAAA,CAAM,GAAA,GAAM,EAAA,CAAA;QAEZ,cAAA,CAAe,WAAA,EAAa,KAAA,CAAA,CAAA;IAAA,CAAA;IAG9B,SAAA,gBAAA,CAA0B,MAAA,EAAmB,KAAA;QAC3C,OAAO,iBAAA,CAAkB,CAAC,MAAA,CAAA,EAAS,KAAA,CAAA,CAAA;IAAA,CAAA;IAGrC,SAAA,iBAAA,CACE,OAAA,EACA,KAAA;QAEA,IAAI,cAAA,GAAiB,KAAA,CAAA;QAErB,KAAA,IAAS,MAAA,IAAU,OAAA,EAAS;YAC1B,MAAM,MAAA,GAAS,KAAA,CAAM,QAAA,CAAS,MAAA,CAAO,EAAA,CAAA,CAAA;YACrC,IAAI,CAAC,MAAA,EAAQ;gBACX,SAAA;aAAA;YAGF,cAAA,GAAiB,IAAA,CAAA;YAEjB,MAAA,CAAO,MAAA,CAAO,MAAA,EAAQ,MAAA,CAAO,OAAA,CAAA,CAAA;YAC7B,MAAM,KAAA,GAAQ,QAAA,CAAS,MAAA,CAAA,CAAA;YACvB,IAAI,MAAA,CAAO,EAAA,KAAO,KAAA,EAAO;gBACvB,OAAO,KAAA,CAAM,QAAA,CAAS,MAAA,CAAO,EAAA,CAAA,CAAA;gBAC7B,KAAA,CAAM,QAAA,CAAS,KAAA,CAAA,GAAS,MAAA,CAAA;aAAA;SAAA;QAI5B,IAAI,cAAA,EAAgB;YAClB,cAAA,CAAe,KAAA,CAAA,CAAA;SAAA;IAAA,CAAA;IAInB,SAAA,gBAAA,CAA0B,MAAA,EAAW,KAAA;QACnC,OAAO,iBAAA,CAAkB,CAAC,MAAA,CAAA,EAAS,KAAA,CAAA,CAAA;IAAA,CAAA;IAGrC,SAAA,iBAAA,CACE,WAAA,EACA,KAAA;QAEA,MAAM,CAAC,KAAA,EAAO,OAAA,CAAA,GAAW,yBAAA,CACvB,WAAA,EACA,QAAA,EACA,KAAA,CAAA,CAAA;QAGF,iBAAA,CAAkB,OAAA,EAAS,KAAA,CAAA,CAAA;QAC3B,cAAA,CAAe,KAAA,EAAO,KAAA,CAAA,CAAA;IAAA,CAAA;IAGxB,SAAA,cAAA,CAAwB,CAAA,EAAuB,CAAA;QAC7C,IAAI,CAAA,CAAE,MAAA,KAAW,CAAA,CAAE,MAAA,EAAQ;YACzB,OAAO,KAAA,CAAA;SAAA;QAGT,KAAA,IAAS,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,CAAA,CAAE,MAAA,IAAU,CAAA,GAAI,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;YACjD,IAAI,CAAA,CAAE,CAAA,CAAA,KAAO,CAAA,CAAE,CAAA,CAAA,EAAI;gBACjB,SAAA;aAAA;YAEF,OAAO,KAAA,CAAA;SAAA;QAET,OAAO,IAAA,CAAA;IAAA,CAAA;IAGT,SAAA,KAAA,CAAe,MAAA,EAAsB,KAAA;QAEnC,MAAA,CAAO,OAAA,CAAQ,CAAC,KAAA,EAAA,EAAA;YACd,KAAA,CAAM,QAAA,CAAS,QAAA,CAAS,KAAA,CAAA,CAAA,GAAU,KAAA,CAAA;QAAA,CAAA,CAAA,CAAA;QAGpC,cAAA,CAAe,KAAA,CAAA,CAAA;IAAA,CAAA;IAGjB,SAAA,cAAA,CAAwB,KAAA;QACtB,MAAM,WAAA,GAAc,MAAA,CAAO,MAAA,CAAO,KAAA,CAAM,QAAA,CAAA,CAAA;QACxC,WAAA,CAAY,IAAA,CAAK,IAAA,CAAA,CAAA;QAEjB,MAAM,YAAA,GAAe,WAAA,CAAY,GAAA,CAAI,QAAA,CAAA,CAAA;QACrC,MAAM,EAAE,GAAA,EAAA,GAAQ,KAAA,CAAA;QAEhB,IAAI,CAAC,cAAA,CAAe,GAAA,EAAK,YAAA,CAAA,EAAe;YACtC,KAAA,CAAM,GAAA,GAAM,YAAA,CAAA;SAAA;IAAA,CAAA;IAIhB,OAAO;QACL,SAAA;QACA,UAAA;QACA,SAAA;QACA,MAAA,EAAQ,mBAAA,CAAoB,aAAA,CAAA;QAC5B,SAAA,EAAW,mBAAA,CAAoB,gBAAA,CAAA;QAC/B,SAAA,EAAW,mBAAA,CAAoB,gBAAA,CAAA;QAC/B,MAAA,EAAQ,mBAAA,CAAoB,aAAA,CAAA;QAC5B,OAAA,EAAS,mBAAA,CAAoB,cAAA,CAAA;QAC7B,MAAA,EAAQ,mBAAA,CAAoB,aAAA,CAAA;QAC5B,OAAA,EAAS,mBAAA,CAAoB,cAAA,CAAA;QAC7B,UAAA,EAAY,mBAAA,CAAoB,iBAAA,CAAA;QAChC,UAAA,EAAY,mBAAA,CAAoB,iBAAA,CAAA;KAAA,CAAA;AAAA,CAAA;;ACpJ7B,SAAA,mBAAA,CACL,OAAA,GAGI,EAAA;IAEJ,MAAM,EAAE,QAAA,EAAU,YAAA,EAAA,GAAsC,cAAA,CAAA;QACtD,YAAA,EAAc,KAAA;QACd,QAAA,EAAU,CAAC,QAAA,EAAA,EAAA,CAAkB,QAAA,CAAS,EAAA;KAAA,EACnC,OAAA,CAAA,CAAA;IAGL,MAAM,YAAA,GAAe,yBAAA,EAAA,CAAA;IACrB,MAAM,gBAAA,GAAmB,sBAAA,EAAA,CAAA;IACzB,MAAM,YAAA,GAAe,YAAA,CAAA,CAAA,CACjB,wBAAA,CAAyB,QAAA,EAAU,YAAA,CAAA,CAAA,CAAA,CACnC,0BAAA,CAA2B,QAAA,CAAA,CAAA;IAE/B,OAAO,cAAA,CAAA,cAAA,CAAA,cAAA,CAAA;QACL,QAAA;QACA,YAAA;KAAA,EACG,YAAA,CAAA,EACA,gBAAA,CAAA,EACA,YAAA,CAAA,CAAA;AAAA,CAAA;;ACrCP,IAAI,WAAA,GACF,kEAAA,CAAA;AAMK,IAAI,MAAA,GAAS,CAAC,IAAA,GAAO,EAAA,EAAA,EAAA;IAC1B,IAAI,EAAA,GAAK,EAAA,CAAA;IAET,IAAI,CAAA,GAAI,IAAA,CAAA;IACR,OAAO,CAAA,EAAA,EAAK;QAEV,EAAA,IAAM,WAAA,CAAa,IAAA,CAAK,MAAA,EAAA,GAAW,EAAA,GAAM,CAAA,CAAA,CAAA;KAAA;IAE3C,OAAO,EAAA,CAAA;AAAA,CAAA,CAAA;;ACqCT,IAAM,gBAAA,GAAiD;IACrD,MAAA;IACA,SAAA;IACA,OAAA;IACA,MAAA;CAAA,CAAA;AAGF,IAAA,eAAA,GAAA;IAME,YACkB,OAAA,EACA,IAAA;QADA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA;QACA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA;IAAA,CAAA;CAAA,CAAA;AAIpB,IAAA,eAAA,GAAA;IAME,YACkB,OAAA,EACA,IAAA;QADA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA;QACA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA;IAAA,CAAA;CAAA,CAAA;AAUb,IAAM,kBAAA,GAAqB,CAAC,KAAA,EAAA,EAAA;IACjC,IAAI,OAAO,KAAA,KAAU,QAAA,IAAY,KAAA,KAAU,IAAA,EAAM;QAC/C,MAAM,WAAA,GAA+B,EAAA,CAAA;QACrC,KAAA,MAAW,QAAA,IAAY,gBAAA,EAAkB;YACvC,IAAI,OAAO,KAAA,CAAM,QAAA,CAAA,KAAc,QAAA,EAAU;gBACvC,WAAA,CAAY,QAAA,CAAA,GAAY,KAAA,CAAM,QAAA,CAAA,CAAA;aAAA;SAAA;QAIlC,OAAO,WAAA,CAAA;KAAA;IAGT,OAAO,EAAE,OAAA,EAAS,MAAA,CAAO,KAAA,CAAA,EAAA,CAAA;AAAA,CAAA,CAAA;AA8WpB,IAAM,gBAAA,GAAoB,CAAA,GAAA,EAAA;IAC/B,SAAA,iBAAA,CAKE,UAAA,EACA,cAAA,EAKA,OAAA;QAOA,MAAM,SAAA,GAIF,YAAA,CACF,UAAA,GAAa,YAAA,EACb,CACE,OAAA,EACA,SAAA,EACA,GAAA,EACA,IAAA,EAAA,EAAA,CACI,CAAA;YACJ,OAAA;YACA,IAAA,EAAM,aAAA,CAAA,cAAA,CAAA,EAAA,EACC,IAAA,IAAgB,EAAA,CAAA,EADjB;gBAEJ,GAAA;gBACA,SAAA;gBACA,aAAA,EAAe,WAAA;aAAA,CAAA;SAAA,CAAA,CAAA,CAAA;QAKrB,MAAM,OAAA,GACJ,YAAA,CACE,UAAA,GAAa,UAAA,EACb,CAAC,SAAA,EAAmB,GAAA,EAAe,IAAA,EAAA,EAAA,CAAwB,CAAA;YACzD,OAAA,EAAS,KAAA,CAAA;YACT,IAAA,EAAM,aAAA,CAAA,cAAA,CAAA,EAAA,EACC,IAAA,IAAgB,EAAA,CAAA,EADjB;gBAEJ,GAAA;gBACA,SAAA;gBACA,aAAA,EAAe,SAAA;aAAA,CAAA;SAAA,CAAA,CAAA,CAAA;QAKvB,MAAM,QAAA,GACJ,YAAA,CACE,UAAA,GAAa,WAAA,EACb,CACE,KAAA,EACA,SAAA,EACA,GAAA,EACA,OAAA,EACA,IAAA,EAAA,EAAA,CACI,CAAA;YACJ,OAAA;YACA,KAAA,EAAS,CAAA,OAAA,IAAW,OAAA,CAAQ,cAAA,IAAmB,kBAAA,CAAA,CAC7C,KAAA,IAAS,UAAA,CAAA;YAEX,IAAA,EAAM,aAAA,CAAA,cAAA,CAAA,EAAA,EACC,IAAA,IAAgB,EAAA,CAAA,EADjB;gBAEJ,GAAA;gBACA,SAAA;gBACA,iBAAA,EAAmB,CAAC,CAAC,OAAA;gBACrB,aAAA,EAAe,UAAA;gBACf,OAAA,EAAS,CAAA,KAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAO,IAAA,CAAA,KAAS,YAAA;gBACzB,SAAA,EAAW,CAAA,KAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAO,IAAA,CAAA,KAAS,gBAAA;aAAA,CAAA;SAAA,CAAA,CAAA,CAAA;QAKnC,IAAI,gBAAA,GAAmB,KAAA,CAAA;QAEvB,MAAM,EAAA,GACJ,OAAO,eAAA,KAAoB,WAAA,CAAA,CAAA,CACvB,eAAA,CAAA,CAAA,CACA;YAAA;gBACE,IAAA,CAAA,MAAA,GAAS;oBACP,OAAA,EAAS,KAAA;oBACT,gBAAA;oBAAmB,CAAA;oBACnB,aAAA;wBACE,OAAO,KAAA,CAAA;oBAAA,CAAA;oBAET,OAAA;oBAAU,CAAA;oBACV,mBAAA;oBAAsB,CAAA;oBACtB,MAAA,EAAQ,KAAA,CAAA;oBACR,cAAA;oBAAiB,CAAA;iBAAA,CAAA;YAAA,CAAA;YAEnB,KAAA;gBACE,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAA,KAAa,YAAA,EAAc;oBACzC,IAAI,CAAC,gBAAA,EAAkB;wBACrB,gBAAA,GAAmB,IAAA,CAAA;wBACnB,OAAA,CAAQ,IAAA,CACN;8KAAA,CAAA,CAAA;qBAAA;iBAAA;YAAA,CAAA;SAAA,CAAA;QAQhB,SAAA,aAAA,CACE,GAAA;YAEA,OAAO,CAAC,QAAA,EAAU,QAAA,EAAU,KAAA,EAAA,EAAA;gBAC1B,MAAM,SAAA,GAAY,CAAA,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,WAAA,CAAA,CAAA,CAAA,CACvB,OAAA,CAAQ,WAAA,CAAY,GAAA,CAAA,CAAA,CAAA,CACpB,MAAA,EAAA,CAAA;gBAEJ,MAAM,eAAA,GAAkB,IAAI,EAAA,EAAA,CAAA;gBAC5B,IAAI,WAAA,CAAA;gBAEJ,IAAI,OAAA,GAAU,KAAA,CAAA;gBACd,SAAA,KAAA,CAAe,MAAA;oBACb,WAAA,GAAc,MAAA,CAAA;oBACd,eAAA,CAAgB,KAAA,EAAA,CAAA;gBAAA,CAAA;gBAGlB,MAAM,QAAA,GAAW,KAAA;oBAtlBzB,IAAA,EAAA,EAAA,EAAA,CAAA;oBAulBU,IAAI,WAAA,CAAA;oBACJ,IAAI;wBACF,IAAI,eAAA,GAAkB,CAAA,EAAA,GAAA,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,SAAA,CAAA,IAAT,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,OAAA,EAAqB,GAAA,EAAK,EAAE,QAAA,EAAU,KAAA,EAAA,CAAA,CAAA;wBAC5D,IAAI,UAAA,CAAW,eAAA,CAAA,EAAkB;4BAC/B,eAAA,GAAkB,MAAM,eAAA,CAAA;yBAAA;wBAG1B,IAAI,eAAA,KAAoB,KAAA,IAAS,eAAA,CAAgB,MAAA,CAAO,OAAA,EAAS;4BAE/D,MAAM;gCACJ,IAAA,EAAM,gBAAA;gCACN,OAAA,EAAS,oDAAA;6BAAA,CAAA;yBAAA;wBAGb,OAAA,GAAU,IAAA,CAAA;wBAEV,MAAM,cAAA,GAAiB,IAAI,OAAA,CAAe,CAAC,CAAA,EAAG,MAAA,EAAA,EAAA,CAC5C,eAAA,CAAgB,MAAA,CAAO,gBAAA,CAAiB,OAAA,EAAS,GAAA,EAAA,CAC/C,MAAA,CAAO;4BACL,IAAA,EAAM,YAAA;4BACN,OAAA,EAAS,WAAA,IAAe,SAAA;yBAAA,CAAA,CAAA,CAAA,CAAA;wBAI9B,QAAA,CACE,OAAA,CACE,SAAA,EACA,GAAA,EACA,CAAA,EAAA,GAAA,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,cAAA,CAAA,IAAT,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,OAAA,EACE,EAAE,SAAA,EAAW,GAAA,EAAA,EACb,EAAE,QAAA,EAAU,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA;wBAIlB,WAAA,GAAc,MAAM,OAAA,CAAQ,IAAA,CAAK;4BAC/B,cAAA;4BACA,OAAA,CAAQ,OAAA,CACN,cAAA,CAAe,GAAA,EAAK;gCAClB,QAAA;gCACA,QAAA;gCACA,KAAA;gCACA,SAAA;gCACA,MAAA,EAAQ,eAAA,CAAgB,MAAA;gCACxB,KAAA;gCACA,eAAA,EAAkB,CAChB,KAAA,EACA,IAAA,EAAA,EAAA;oCAEA,OAAO,IAAI,eAAA,CAAgB,KAAA,EAAO,IAAA,CAAA,CAAA;gCAAA,CAAA;gCAEpC,gBAAA,EAAmB,CAAC,KAAA,EAAgB,IAAA,EAAA,EAAA;oCAClC,OAAO,IAAI,eAAA,CAAgB,KAAA,EAAO,IAAA,CAAA,CAAA;gCAAA,CAAA;6BAAA,CAAA,CAAA,CAGtC,IAAA,CAAK,CAAC,MAAA,EAAA,EAAA;gCACN,IAAI,MAAA,YAAkB,eAAA,EAAiB;oCACrC,MAAM,MAAA,CAAA;iCAAA;gCAER,IAAI,MAAA,YAAkB,eAAA,EAAiB;oCACrC,OAAO,SAAA,CAAU,MAAA,CAAO,OAAA,EAAS,SAAA,EAAW,GAAA,EAAK,MAAA,CAAO,IAAA,CAAA,CAAA;iCAAA;gCAE1D,OAAO,SAAA,CAAU,MAAA,EAAe,SAAA,EAAW,GAAA,CAAA,CAAA;4BAAA,CAAA,CAAA;yBAAA,CAAA,CAAA;qBAAA;oBAAA,OAGxC,GAAA,EAAP;wBACA,WAAA,GACE,GAAA,YAAe,eAAA,CAAA,CAAA,CACX,QAAA,CAAS,IAAA,EAAM,SAAA,EAAW,GAAA,EAAK,GAAA,CAAI,OAAA,EAAS,GAAA,CAAI,IAAA,CAAA,CAAA,CAAA,CAChD,QAAA,CAAS,GAAA,EAAY,SAAA,EAAW,GAAA,CAAA,CAAA;qBAAA;oBAOxC,MAAM,YAAA,GACJ,OAAA,IACA,CAAC,OAAA,CAAQ,0BAAA,IACT,QAAA,CAAS,KAAA,CAAM,WAAA,CAAA,IACd,WAAA,CAAoB,IAAA,CAAK,SAAA,CAAA;oBAE5B,IAAI,CAAC,YAAA,EAAc;wBACjB,QAAA,CAAS,WAAA,CAAA,CAAA;qBAAA;oBAEX,OAAO,WAAA,CAAA;gBAAA,CAAA,EAAA,CAAA;gBAET,OAAO,MAAA,CAAO,MAAA,CAAO,QAAA,EAAyB;oBAC5C,KAAA;oBACA,SAAA;oBACA,GAAA;oBACA,MAAA;wBACE,OAAO,QAAA,CAAQ,IAAA,CAAU,YAAA,CAAA,CAAA;oBAAA,CAAA;iBAAA,CAAA,CAAA;YAAA,CAAA,CAAA;QAAA,CAAA;QAMjC,OAAO,MAAA,CAAO,MAAA,CACZ,aAAA,EAKA;YACE,OAAA;YACA,QAAA;YACA,SAAA;YACA,UAAA;SAAA,CAAA,CAAA;IAAA,CAAA;IAIN,iBAAA,CAAiB,SAAA,GAAY,GAAA,EAAA,CAAM,iBAAA,CAAA;IAEnC,OAAO,iBAAA,CAAA;AAAA,CAAA,CAAA,EAAA,CAAA;AAiBF,SAAA,YAAA,CACL,MAAA;IAEA,IAAI,MAAA,CAAO,IAAA,IAAQ,MAAA,CAAO,IAAA,CAAK,iBAAA,EAAmB;QAChD,MAAM,MAAA,CAAO,OAAA,CAAA;KAAA;IAEf,IAAI,MAAA,CAAO,KAAA,EAAO;QAChB,MAAM,MAAA,CAAO,KAAA,CAAA;KAAA;IAEf,OAAO,MAAA,CAAO,OAAA,CAAA;AAAA,CAAA;AAOhB,SAAA,UAAA,CAAoB,KAAA;IAClB,OACE,KAAA,KAAU,IAAA,IACV,OAAO,KAAA,KAAU,QAAA,IACjB,OAAO,KAAA,CAAM,IAAA,KAAS,UAAA,CAAA;AAAA,CAAA;;ACxtB1B,IAAM,OAAA,GAAU,CAAC,OAAA,EAAuB,MAAA,EAAA,EAAA;IACtC,IAAI,gBAAA,CAAiB,OAAA,CAAA,EAAU;QAC7B,OAAO,OAAA,CAAQ,KAAA,CAAM,MAAA,CAAA,CAAA;KAAA;SAChB;QACL,OAAO,OAAA,CAAQ,MAAA,CAAA,CAAA;KAAA;AAAA,CAAA,CAAA;AAaZ,SAAA,OAAA,CAAA,GACF,QAAA;IAEH,OAAO,CAAC,MAAA,EAAA,EAAA;QACN,OAAO,QAAA,CAAS,IAAA,CAAK,CAAC,OAAA,EAAA,EAAA,CAAY,OAAA,CAAQ,OAAA,EAAS,MAAA,CAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;AAahD,SAAA,OAAA,CAAA,GACF,QAAA;IAEH,OAAO,CAAC,MAAA,EAAA,EAAA;QACN,OAAO,QAAA,CAAS,KAAA,CAAM,CAAC,OAAA,EAAA,EAAA,CAAY,OAAA,CAAQ,OAAA,EAAS,MAAA,CAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;AAUjD,SAAA,0BAAA,CACL,MAAA,EACA,WAAA;IAEA,IAAI,CAAC,MAAA,IAAU,CAAC,MAAA,CAAO,IAAA;QAAM,OAAO,KAAA,CAAA;IAEpC,MAAM,iBAAA,GAAoB,OAAO,MAAA,CAAO,IAAA,CAAK,SAAA,KAAc,QAAA,CAAA;IAC3D,MAAM,qBAAA,GACJ,WAAA,CAAY,OAAA,CAAQ,MAAA,CAAO,IAAA,CAAK,aAAA,CAAA,GAAiB,CAAA,CAAA,CAAA;IAEnD,OAAO,iBAAA,IAAqB,qBAAA,CAAA;AAAA,CAAA;AAG9B,SAAA,iBAAA,CAA2B,CAAA;IACzB,OACE,OAAO,CAAA,CAAE,CAAA,CAAA,KAAO,UAAA,IAChB,SAAA,IAAa,CAAA,CAAE,CAAA,CAAA,IACf,WAAA,IAAe,CAAA,CAAE,CAAA,CAAA,IACjB,UAAA,IAAc,CAAA,CAAE,CAAA,CAAA,CAAA;AAAA,CAAA;AAwCb,SAAA,SAAA,CAAA,GAEF,WAAA;IACH,IAAI,WAAA,CAAY,MAAA,KAAW,CAAA,EAAG;QAC5B,OAAO,CAAC,MAAA,EAAA,EAAA,CAAgB,0BAAA,CAA2B,MAAA,EAAQ,CAAC,SAAA,CAAA,CAAA,CAAA;KAAA;IAG9D,IAAI,CAAC,iBAAA,CAAkB,WAAA,CAAA,EAAc;QACnC,OAAO,SAAA,EAAA,CAAY,WAAA,CAAY,CAAA,CAAA,CAAA,CAAA;KAAA;IAGjC,OAAO,CACL,MAAA,EAAA,EAAA;QAGA,MAAM,QAAA,GAA8C,WAAA,CAAY,GAAA,CAC9D,CAAC,UAAA,EAAA,EAAA,CAAe,UAAA,CAAW,OAAA,CAAA,CAAA;QAG7B,MAAM,eAAA,GAAkB,OAAA,CAAQ,GAAG,QAAA,CAAA,CAAA;QAEnC,OAAO,eAAA,CAAgB,MAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;AA0CpB,SAAA,UAAA,CAAA,GAEF,WAAA;IACH,IAAI,WAAA,CAAY,MAAA,KAAW,CAAA,EAAG;QAC5B,OAAO,CAAC,MAAA,EAAA,EAAA,CAAgB,0BAAA,CAA2B,MAAA,EAAQ,CAAC,UAAA,CAAA,CAAA,CAAA;KAAA;IAG9D,IAAI,CAAC,iBAAA,CAAkB,WAAA,CAAA,EAAc;QACnC,OAAO,UAAA,EAAA,CAAa,WAAA,CAAY,CAAA,CAAA,CAAA,CAAA;KAAA;IAGlC,OAAO,CACL,MAAA,EAAA,EAAA;QAGA,MAAM,QAAA,GAA8C,WAAA,CAAY,GAAA,CAC9D,CAAC,UAAA,EAAA,EAAA,CAAe,UAAA,CAAW,QAAA,CAAA,CAAA;QAG7B,MAAM,eAAA,GAAkB,OAAA,CAAQ,GAAG,QAAA,CAAA,CAAA;QAEnC,OAAO,eAAA,CAAgB,MAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;AA+CpB,SAAA,mBAAA,CAAA,GAEF,WAAA;IACH,MAAM,OAAA,GAAU,CAAC,MAAA,EAAA,EAAA;QACf,OAAO,MAAA,IAAU,MAAA,CAAO,IAAA,IAAQ,MAAA,CAAO,IAAA,CAAK,iBAAA,CAAA;IAAA,CAAA,CAAA;IAG9C,IAAI,WAAA,CAAY,MAAA,KAAW,CAAA,EAAG;QAC5B,OAAO,CAAC,MAAA,EAAA,EAAA;YACN,MAAM,eAAA,GAAkB,OAAA,CAAQ,UAAA,CAAW,GAAG,WAAA,CAAA,EAAc,OAAA,CAAA,CAAA;YAE5D,OAAO,eAAA,CAAgB,MAAA,CAAA,CAAA;QAAA,CAAA,CAAA;KAAA;IAI3B,IAAI,CAAC,iBAAA,CAAkB,WAAA,CAAA,EAAc;QACnC,OAAO,mBAAA,EAAA,CAAsB,WAAA,CAAY,CAAA,CAAA,CAAA,CAAA;KAAA;IAG3C,OAAO,CACL,MAAA,EAAA,EAAA;QAEA,MAAM,eAAA,GAAkB,OAAA,CAAQ,UAAA,CAAW,GAAG,WAAA,CAAA,EAAc,OAAA,CAAA,CAAA;QAE5D,OAAO,eAAA,CAAgB,MAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;AA0CpB,SAAA,WAAA,CAAA,GAEF,WAAA;IACH,IAAI,WAAA,CAAY,MAAA,KAAW,CAAA,EAAG;QAC5B,OAAO,CAAC,MAAA,EAAA,EAAA,CAAgB,0BAAA,CAA2B,MAAA,EAAQ,CAAC,WAAA,CAAA,CAAA,CAAA;KAAA;IAG9D,IAAI,CAAC,iBAAA,CAAkB,WAAA,CAAA,EAAc;QACnC,OAAO,WAAA,EAAA,CAAc,WAAA,CAAY,CAAA,CAAA,CAAA,CAAA;KAAA;IAGnC,OAAO,CACL,MAAA,EAAA,EAAA;QAGA,MAAM,QAAA,GAA8C,WAAA,CAAY,GAAA,CAC9D,CAAC,UAAA,EAAA,EAAA,CAAe,UAAA,CAAW,SAAA,CAAA,CAAA;QAG7B,MAAM,eAAA,GAAkB,OAAA,CAAQ,GAAG,QAAA,CAAA,CAAA;QAEnC,OAAO,eAAA,CAAgB,MAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;AAiDpB,SAAA,kBAAA,CAAA,GAEF,WAAA;IACH,IAAI,WAAA,CAAY,MAAA,KAAW,CAAA,EAAG;QAC5B,OAAO,CAAC,MAAA,EAAA,EAAA,CACN,0BAAA,CAA2B,MAAA,EAAQ,CAAC,SAAA,EAAW,WAAA,EAAa,UAAA,CAAA,CAAA,CAAA;KAAA;IAGhE,IAAI,CAAC,iBAAA,CAAkB,WAAA,CAAA,EAAc;QACnC,OAAO,kBAAA,EAAA,CAAqB,WAAA,CAAY,CAAA,CAAA,CAAA,CAAA;KAAA;IAG1C,OAAO,CACL,MAAA,EAAA,EAAA;QAGA,MAAM,QAAA,GAA8C,EAAA,CAAA;QAEpD,KAAA,MAAW,UAAA,IAAc,WAAA,EAAa;YACpC,QAAA,CAAS,IAAA,CACP,UAAA,CAAW,OAAA,EACX,UAAA,CAAW,QAAA,EACX,UAAA,CAAW,SAAA,CAAA,CAAA;SAAA;QAIf,MAAM,eAAA,GAAkB,OAAA,CAAQ,GAAG,QAAA,CAAA,CAAA;QAEnC,OAAO,eAAA,CAAgB,MAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA;;ACpapB,IAAM,cAAA,GAG0C,CACrD,IAAA,EACA,QAAA,EAAA,EAAA;IAEA,IAAI,OAAO,IAAA,KAAS,UAAA,EAAY;QAC9B,MAAM,IAAI,SAAA,CAAU,GAAG,QAAA,oBAAA,CAAA,CAAA;KAAA;AAAA,CAAA,CAAA;AAIpB,IAAM,IAAA,GAAO,GAAA,EAAA;AAAM,CAAA,CAAA;AAEnB,IAAM,cAAA,GAAiB,CAC5B,QAAA,EACA,OAAA,GAAU,IAAA,EAAA,EAAA;IAEV,QAAA,CAAQ,KAAA,CAAM,OAAA,CAAA,CAAA;IAEd,OAAO,QAAA,CAAA;AAAA,CAAA,CAAA;AAGF,IAAM,sBAAA,GAAyB,CACpC,WAAA,EACA,QAAA,EAAA,EAAA;IAEA,WAAA,CAAY,gBAAA,CAAiB,OAAA,EAAS,QAAA,EAAU,EAAE,IAAA,EAAM,IAAA,EAAA,CAAA,CAAA;IACxD,OAAO,GAAA,EAAA,CAAM,WAAA,CAAY,mBAAA,CAAoB,OAAA,EAAS,QAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAajD,IAAM,yBAAA,GAA4B,CACvC,eAAA,EACA,MAAA,EAAA,EAAA;IAIA,MAAM,MAAA,GAAS,eAAA,CAAgB,MAAA,CAAA;IAE/B,IAAI,MAAA,CAAO,OAAA,EAAS;QAClB,OAAA;KAAA;IAOF,IAAI,CAAE,CAAA,QAAA,IAAY,MAAA,CAAA,EAAS;QACzB,MAAA,CAAO,cAAA,CAAe,MAAA,EAAQ,QAAA,EAAU;YACtC,UAAA,EAAY,IAAA;YACZ,KAAA,EAAO,MAAA;YACP,YAAA,EAAc,IAAA;YACd,QAAA,EAAU,IAAA;SAAA,CAAA,CAAA;KAAA;IAId,CAAA;IAAE,eAAA,CAAgB,KAAA,CAAkC,MAAA,CAAA,CAAA;AAAA,CAAA,CAAA;;AClEtD,IAAM,IAAA,GAAO,MAAA,CAAA;AACb,IAAM,QAAA,GAAW,UAAA,CAAA;AACjB,IAAM,SAAA,GAAY,WAAA,CAAA;AAClB,IAAM,SAAA,GAAY,WAAA,CAAA;AAGX,IAAM,aAAA,GAAgB,QAAQ,SAAA,EAAA,CAAA;AAC9B,IAAM,aAAA,GAAgB,QAAQ,SAAA,EAAA,CAAA;AAC9B,IAAM,iBAAA,GAAoB,GAAG,QAAA,IAAY,SAAA,EAAA,CAAA;AACzC,IAAM,iBAAA,GAAoB,GAAG,QAAA,IAAY,SAAA,EAAA,CAAA;AAEzC,IAAA,cAAA,GAAA;IAGL,YAAmB,IAAA;QAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA;QAFnB,IAAA,CAAA,IAAA,GAAO,gBAAA,CAAA;QAGL,IAAA,CAAK,OAAA,GAAU,GAAG,IAAA,IAAQ,SAAA,aAAsB,IAAA,GAAA,CAAA;IAAA,CAAA;CAAA,CAAA;;ACP7C,IAAM,cAAA,GAAiB,CAAC,MAAA,EAAA,EAAA;IAC7B,IAAI,MAAA,CAAO,OAAA,EAAS;QAClB,MAAM,IAAI,cAAA,CAAgB,MAAA,CAAyC,MAAA,CAAA,CAAA;KAAA;AAAA,CAAA,CAAA;AAShE,SAAA,cAAA,CACL,MAAA,EACA,QAAA;IAEA,IAAI,OAAA,GAAU,IAAA,CAAA;IACd,OAAO,IAAI,OAAA,CAAW,CAAC,OAAA,EAAS,MAAA,EAAA,EAAA;QAC9B,MAAM,eAAA,GAAkB,GAAA,EAAA,CAAM,MAAA,CAAO,IAAI,cAAA,CAAe,MAAA,CAAO,MAAA,CAAA,CAAA,CAAA;QAE/D,IAAI,MAAA,CAAO,OAAA,EAAS;YAClB,eAAA,EAAA,CAAA;YACA,OAAA;SAAA;QAGF,OAAA,GAAU,sBAAA,CAAuB,MAAA,EAAQ,eAAA,CAAA,CAAA;QACzC,QAAA,CAAQ,OAAA,CAAQ,GAAA,EAAA,CAAM,OAAA,EAAA,CAAA,CAAW,IAAA,CAAK,OAAA,EAAS,MAAA,CAAA,CAAA;IAAA,CAAA,CAAA,CAC9C,OAAA,CAAQ,GAAA,EAAA;QAET,OAAA,GAAU,IAAA,CAAA;IAAA,CAAA,CAAA,CAAA;AAAA,CAAA;AAWP,IAAM,OAAA,GAAU,KAAA,EACrB,KAAA,EACA,OAAA,EAAA,EAAA;IAEA,IAAI;QACF,MAAM,OAAA,CAAQ,OAAA,EAAA,CAAA;QACd,MAAM,KAAA,GAAQ,MAAM,KAAA,EAAA,CAAA;QACpB,OAAO;YACL,MAAA,EAAQ,IAAA;YACR,KAAA;SAAA,CAAA;KAAA;IAAA,OAEK,KAAA,EAAP;QACA,OAAO;YACL,MAAA,EAAQ,KAAA,YAAiB,cAAA,CAAA,CAAA,CAAiB,WAAA,CAAA,CAAA,CAAc,UAAA;YACxD,KAAA;SAAA,CAAA;KAAA;YAEF;QACA,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA;KAAA;AAAA,CAAA,CAAA;AAWG,IAAM,WAAA,GAAc,CAAI,MAAA,EAAA,EAAA;IAC7B,OAAO,CAAC,QAAA,EAAA,EAAA;QACN,OAAO,cAAA,CACL,cAAA,CAAe,MAAA,EAAQ,QAAA,CAAA,CAAS,IAAA,CAAK,CAAC,MAAA,EAAA,EAAA;YACpC,cAAA,CAAe,MAAA,CAAA,CAAA;YACf,OAAO,MAAA,CAAA;QAAA,CAAA,CAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAYR,IAAM,WAAA,GAAc,CAAC,MAAA,EAAA,EAAA;IAC1B,MAAM,KAAA,GAAQ,WAAA,CAAkB,MAAA,CAAA,CAAA;IAChC,OAAO,CAAC,SAAA,EAAA,EAAA;QACN,OAAO,KAAA,CAAM,IAAI,OAAA,CAAc,CAAC,OAAA,EAAA,EAAA,CAAY,UAAA,CAAW,OAAA,EAAS,SAAA,CAAA,CAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA,CAAA;;ACxBpE,IAAM,EAAE,MAAA,EAAA,GAAW,MAAA,CAAA;AAInB,IAAM,kBAAA,GAAqB,EAAA,CAAA;AAE3B,IAAM,GAAA,GAAM,oBAAA,CAAA;AAEZ,IAAM,UAAA,GAAa,CACjB,iBAAA,EACA,sBAAA,EAAA,EAAA;IAEA,MAAM,eAAA,GAAkB,CAAC,UAAA,EAAA,EAAA,CACvB,sBAAA,CAAuB,iBAAA,EAAmB,GAAA,EAAA,CACxC,yBAAA,CAA0B,UAAA,EAAY,iBAAA,CAAkB,MAAA,CAAA,CAAA,CAAA;IAG5D,OAAO,CACL,YAAA,EACA,IAAA,EAAA,EAAA;QAEA,cAAA,CAAe,YAAA,EAAc,cAAA,CAAA,CAAA;QAC7B,MAAM,oBAAA,GAAuB,IAAI,eAAA,EAAA,CAAA;QAEjC,eAAA,CAAgB,oBAAA,CAAA,CAAA;QAEhB,MAAM,MAAA,GAAS,OAAA,CACb,KAAA,IAAA,EAAA;YACE,cAAA,CAAe,iBAAA,CAAA,CAAA;YACf,cAAA,CAAe,oBAAA,CAAqB,MAAA,CAAA,CAAA;YACpC,MAAM,OAAA,GAAU,MAAM,YAAA,CAAa;gBACjC,KAAA,EAAO,WAAA,CAAY,oBAAA,CAAqB,MAAA,CAAA;gBACxC,KAAA,EAAO,WAAA,CAAY,oBAAA,CAAqB,MAAA,CAAA;gBACxC,MAAA,EAAQ,oBAAA,CAAqB,MAAA;aAAA,CAAA,CAAA;YAE/B,cAAA,CAAe,oBAAA,CAAqB,MAAA,CAAA,CAAA;YACpC,OAAO,OAAA,CAAA;QAAA,CAAA,EAET,GAAA,EAAA,CAAM,yBAAA,CAA0B,oBAAA,EAAsB,aAAA,CAAA,CAAA,CAAA;QAGxD,IAAI,IAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAM,QAAA,EAAU;YAClB,sBAAA,CAAuB,IAAA,CAAK,MAAA,CAAA,CAAA;SAAA;QAG9B,OAAO;YACL,MAAA,EAAQ,WAAA,CAA2B,iBAAA,CAAA,CAAmB,MAAA,CAAA;YACtD,MAAA;gBACE,yBAAA,CAA0B,oBAAA,EAAsB,aAAA,CAAA,CAAA;YAAA,CAAA;SAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAMxD,IAAM,iBAAA,GAAoB,CACxB,cAAA,EAKA,MAAA,EAAA,EAAA;IASA,MAAM,IAAA,GAAO,KAAA,EACX,SAAA,EACA,OAAA,EAAA,EAAA;QAEA,cAAA,CAAe,MAAA,CAAA,CAAA;QAGf,IAAI,WAAA,GAAmC,GAAA,EAAA;QAAM,CAAA,CAAA;QAE7C,MAAM,YAAA,GAAe,IAAI,OAAA,CAA2B,CAAC,OAAA,EAAS,MAAA,EAAA,EAAA;YAE5D,IAAI,aAAA,GAAgB,cAAA,CAAe;gBACjC,SAAA;gBACA,MAAA,EAAQ,CAAC,MAAA,EAAQ,WAAA,EAAA,EAAA;oBAEf,WAAA,CAAY,WAAA,EAAA,CAAA;oBAEZ,OAAA,CAAQ;wBACN,MAAA;wBACA,WAAA,CAAY,QAAA,EAAA;wBACZ,WAAA,CAAY,gBAAA,EAAA;qBAAA,CAAA,CAAA;gBAAA,CAAA;aAAA,CAAA,CAAA;YAIlB,WAAA,GAAc,GAAA,EAAA;gBACZ,aAAA,EAAA,CAAA;gBACA,MAAA,EAAA,CAAA;YAAA,CAAA,CAAA;QAAA,CAAA,CAAA,CAAA;QAIJ,MAAM,QAAA,GAA2D;YAC/D,YAAA;SAAA,CAAA;QAGF,IAAI,OAAA,IAAW,IAAA,EAAM;YACnB,QAAA,CAAS,IAAA,CACP,IAAI,OAAA,CAAc,CAAC,OAAA,EAAA,EAAA,CAAY,UAAA,CAAW,OAAA,EAAS,OAAA,EAAS,IAAA,CAAA,CAAA,CAAA,CAAA;SAAA;QAIhE,IAAI;YACF,MAAM,MAAA,GAAS,MAAM,cAAA,CAAe,MAAA,EAAQ,OAAA,CAAQ,IAAA,CAAK,QAAA,CAAA,CAAA,CAAA;YAEzD,cAAA,CAAe,MAAA,CAAA,CAAA;YACf,OAAO,MAAA,CAAA;SAAA;gBACP;YAEA,WAAA,EAAA,CAAA;SAAA;IAAA,CAAA,CAAA;IAIJ,OAAQ,CAAC,SAAA,EAAoC,OAAA,EAAA,EAAA,CAC3C,cAAA,CAAe,IAAA,CAAK,SAAA,EAAW,OAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAGnC,IAAM,yBAAA,GAA4B,CAAC,OAAA,EAAA,EAAA;IACjC,IAAI,EAAE,IAAA,EAAM,aAAA,EAAe,OAAA,EAAS,SAAA,EAAW,MAAA,EAAA,GAAW,OAAA,CAAA;IAE1D,IAAI,IAAA,EAAM;QACR,SAAA,GAAY,YAAA,CAAa,IAAA,CAAA,CAAM,KAAA,CAAA;KAAA;SAAA,IACtB,aAAA,EAAe;QACxB,IAAA,GAAO,aAAA,CAAe,IAAA,CAAA;QACtB,SAAA,GAAY,aAAA,CAAc,KAAA,CAAA;KAAA;SAAA,IACjB,OAAA,EAAS;QAClB,SAAA,GAAY,OAAA,CAAA;KAAA;SAAA,IACH,SAAA,EAAW;KAAA;SAEf;QACL,MAAM,IAAI,KAAA,CACR,yFAAA,CAAA,CAAA;KAAA;IAIJ,cAAA,CAAe,MAAA,EAAQ,kBAAA,CAAA,CAAA;IAEvB,OAAO,EAAE,SAAA,EAAW,IAAA,EAAM,MAAA,EAAA,CAAA;AAAA,CAAA,CAAA;AAIrB,IAAM,mBAAA,GAAyD,CACpE,OAAA,EAAA,EAAA;IAEA,MAAM,EAAE,IAAA,EAAM,SAAA,EAAW,MAAA,EAAA,GAAW,yBAAA,CAA0B,OAAA,CAAA,CAAA;IAE9D,MAAM,EAAA,GAAK,MAAA,EAAA,CAAA;IACX,MAAM,KAAA,GAAgC;QACpC,EAAA;QACA,MAAA;QACA,IAAA;QACA,SAAA;QACA,OAAA,EAAS,IAAI,GAAA,EAAA;QACb,WAAA,EAAa,GAAA,EAAA;YACX,MAAM,IAAI,KAAA,CAAM,6BAAA,CAAA,CAAA;QAAA,CAAA;KAAA,CAAA;IAIpB,OAAO,KAAA,CAAA;AAAA,CAAA,CAAA;AAGT,IAAM,qBAAA,GAAwB,CAC5B,KAAA,EAAA,EAAA;IAEA,KAAA,CAAM,OAAA,CAAQ,OAAA,CAAQ,CAAC,UAAA,EAAA,EAAA;QACrB,yBAAA,CAA0B,UAAA,EAAY,iBAAA,CAAA,CAAA;IAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAI1C,IAAM,6BAAA,GAAgC,CACpC,WAAA,EAAA,EAAA;IAEA,OAAO,GAAA,EAAA;QACL,WAAA,CAAY,OAAA,CAAQ,qBAAA,CAAA,CAAA;QAEpB,WAAA,CAAY,KAAA,EAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAWhB,IAAM,iBAAA,GAAoB,CACxB,YAAA,EACA,aAAA,EACA,SAAA,EAAA,EAAA;IAEA,IAAI;QACF,YAAA,CAAa,aAAA,EAAe,SAAA,CAAA,CAAA;KAAA;IAAA,OACrB,iBAAA,EAAP;QAGA,UAAA,CAAW,GAAA,EAAA;YACT,MAAM,iBAAA,CAAA;QAAA,CAAA,EACL,CAAA,CAAA,CAAA;KAAA;AAAA,CAAA,CAAA;AAOA,IAAM,WAAA,GAAc,YAAA,CACzB,GAAG,GAAA,MAAA,CAAA,CAAA;AAME,IAAM,iBAAA,GAAoB,YAAA,CAAa,GAAG,GAAA,YAAA,CAAA,CAAA;AAK1C,IAAM,cAAA,GAAiB,YAAA,CAC5B,GAAG,GAAA,SAAA,CAAA,CAAA;AAGL,IAAM,mBAAA,GAA4C,CAAA,GAAI,IAAA,EAAA,EAAA;IACpD,OAAA,CAAQ,KAAA,CAAM,GAAG,GAAA,QAAA,EAAa,GAAG,IAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAM5B,SAAA,wBAAA,CAIL,iBAAA,GAAoE,EAAA;IACpE,MAAM,WAAA,GAAc,IAAI,GAAA,EAAA,CAAA;IACxB,MAAM,EAAE,KAAA,EAAO,OAAA,GAAU,mBAAA,EAAA,GAAwB,iBAAA,CAAA;IAEjD,cAAA,CAAe,OAAA,EAAS,SAAA,CAAA,CAAA;IAExB,MAAM,WAAA,GAAc,CAAC,KAAA,EAAA,EAAA;QACnB,KAAA,CAAM,WAAA,GAAc,GAAA,EAAA,CAAM,WAAA,CAAY,MAAA,CAAO,KAAA,CAAO,EAAA,CAAA,CAAA;QAEpD,WAAA,CAAY,GAAA,CAAI,KAAA,CAAM,EAAA,EAAI,KAAA,CAAA,CAAA;QAC1B,OAAO,CAAC,aAAA,EAAA,EAAA;YACN,KAAA,CAAM,WAAA,EAAA,CAAA;YACN,IAAI,aAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAe,YAAA,EAAc;gBAC/B,qBAAA,CAAsB,KAAA,CAAA,CAAA;aAAA;QAAA,CAAA,CAAA;IAAA,CAAA,CAAA;IAK5B,MAAM,iBAAA,GAAoB,CACxB,UAAA,EAAA,EAAA;QAEA,KAAA,MAAW,KAAA,IAAS,KAAA,CAAM,IAAA,CAAK,WAAA,CAAY,MAAA,EAAA,CAAA,EAAW;YACpD,IAAI,UAAA,CAAW,KAAA,CAAA,EAAQ;gBACrB,OAAO,KAAA,CAAA;aAAA;SAAA;QAIX,OAAO,KAAA,CAAA,CAAA;IAAA,CAAA,CAAA;IAGT,MAAM,cAAA,GAAiB,CAAC,OAAA,EAAA,EAAA;QACtB,IAAI,KAAA,GAAQ,iBAAA,CACV,CAAC,aAAA,EAAA,EAAA,CAAkB,aAAA,CAAc,MAAA,KAAW,OAAA,CAAQ,MAAA,CAAA,CAAA;QAGtD,IAAI,CAAC,KAAA,EAAO;YACV,KAAA,GAAQ,mBAAA,CAAoB,OAAA,CAAA,CAAA;SAAA;QAG9B,OAAO,WAAA,CAAY,KAAA,CAAA,CAAA;IAAA,CAAA,CAAA;IAGrB,MAAM,aAAA,GAAgB,CACpB,OAAA,EAAA,EAAA;QAEA,MAAM,EAAE,IAAA,EAAM,MAAA,EAAQ,SAAA,EAAA,GAAc,yBAAA,CAA0B,OAAA,CAAA,CAAA;QAE9D,MAAM,KAAA,GAAQ,iBAAA,CAAkB,CAAC,MAAA,EAAA,EAAA;YAC/B,MAAM,oBAAA,GACJ,OAAO,IAAA,KAAS,QAAA,CAAA,CAAA,CACZ,MAAA,CAAM,IAAA,KAAS,IAAA,CAAA,CAAA,CACf,MAAA,CAAM,SAAA,KAAc,SAAA,CAAA;YAE1B,OAAO,oBAAA,IAAwB,MAAA,CAAM,MAAA,KAAW,MAAA,CAAA;QAAA,CAAA,CAAA,CAAA;QAGlD,IAAI,KAAA,EAAO;YACT,KAAA,CAAM,WAAA,EAAA,CAAA;YACN,IAAI,OAAA,CAAQ,YAAA,EAAc;gBACxB,qBAAA,CAAsB,KAAA,CAAA,CAAA;aAAA;SAAA;QAI1B,OAAO,CAAC,CAAC,KAAA,CAAA;IAAA,CAAA,CAAA;IAGX,MAAM,cAAA,GAAiB,KAAA,EACrB,KAAA,EACA,MAAA,EACA,GAAA,EACA,gBAAA,EAAA,EAAA;QAEA,MAAM,sBAAA,GAAyB,IAAI,eAAA,EAAA,CAAA;QACnC,MAAM,IAAA,GAAO,iBAAA,CACX,cAAA,EACA,sBAAA,CAAuB,MAAA,CAAA,CAAA;QAEzB,MAAM,gBAAA,GAAmC,EAAA,CAAA;QAEzC,IAAI;YACF,KAAA,CAAM,OAAA,CAAQ,GAAA,CAAI,sBAAA,CAAA,CAAA;YAClB,MAAM,OAAA,CAAQ,OAAA,CACZ,KAAA,CAAM,MAAA,CACJ,MAAA,EAEA,MAAA,CAAO,EAAA,EAAI,GAAA,EAAK;gBACd,gBAAA;gBACA,SAAA,EAAW,CACT,SAAA,EACA,OAAA,EAAA,EAAA,CACG,IAAA,CAAK,SAAA,EAAW,OAAA,CAAA,CAAS,IAAA,CAAK,OAAA,CAAA;gBACnC,IAAA;gBACA,KAAA,EAAO,WAAA,CAAY,sBAAA,CAAuB,MAAA,CAAA;gBAC1C,KAAA,EAAO,WAAA,CAAiB,sBAAA,CAAuB,MAAA,CAAA;gBAC/C,KAAA;gBACA,MAAA,EAAQ,sBAAA,CAAuB,MAAA;gBAC/B,IAAA,EAAM,UAAA,CAAW,sBAAA,CAAuB,MAAA,EAAQ,gBAAA,CAAA;gBAChD,WAAA,EAAa,KAAA,CAAM,WAAA;gBACnB,SAAA,EAAW,GAAA,EAAA;oBACT,WAAA,CAAY,GAAA,CAAI,KAAA,CAAM,EAAA,EAAI,KAAA,CAAA,CAAA;gBAAA,CAAA;gBAE5B,qBAAA,EAAuB,GAAA,EAAA;oBACrB,KAAA,CAAM,OAAA,CAAQ,OAAA,CAAQ,CAAC,UAAA,EAAY,CAAA,EAAG,GAAA,EAAA,EAAA;wBACpC,IAAI,UAAA,KAAe,sBAAA,EAAwB;4BACzC,yBAAA,CAA0B,UAAA,EAAY,iBAAA,CAAA,CAAA;4BACtC,GAAA,CAAI,MAAA,CAAO,UAAA,CAAA,CAAA;yBAAA;oBAAA,CAAA,CAAA,CAAA;gBAAA,CAAA;aAAA,CAAA,CAAA,CAAA,CAAA;SAAA;QAAA,OAOhB,aAAA,EAAP;YACA,IAAI,CAAE,CAAA,aAAA,YAAyB,cAAA,CAAA,EAAiB;gBAC9C,iBAAA,CAAkB,OAAA,EAAS,aAAA,EAAe;oBACxC,QAAA,EAAU,QAAA;iBAAA,CAAA,CAAA;aAAA;SAAA;gBAGd;YACA,MAAM,OAAA,CAAQ,UAAA,CAAW,gBAAA,CAAA,CAAA;YAEzB,yBAAA,CAA0B,sBAAA,EAAwB,iBAAA,CAAA,CAAA;YAClD,KAAA,CAAM,OAAA,CAAQ,MAAA,CAAO,sBAAA,CAAA,CAAA;SAAA;IAAA,CAAA,CAAA;IAIzB,MAAM,uBAAA,GAA0B,6BAAA,CAA8B,WAAA,CAAA,CAAA;IAE9D,MAAM,UAAA,GACJ,CAAC,GAAA,EAAA,EAAA,CAAQ,CAAC,IAAA,EAAA,EAAA,CAAS,CAAC,MAAA,EAAA,EAAA;QAClB,IAAI,CAAC,QAAA,CAAS,MAAA,CAAA,EAAS;YAErB,OAAO,IAAA,CAAK,MAAA,CAAA,CAAA;SAAA;QAGd,IAAI,WAAA,CAAY,KAAA,CAAM,MAAA,CAAA,EAAS;YAC7B,OAAO,cAAA,CAAe,MAAA,CAAO,OAAA,CAAA,CAAA;SAAA;QAG/B,IAAI,iBAAA,CAAkB,KAAA,CAAM,MAAA,CAAA,EAAS;YACnC,uBAAA,EAAA,CAAA;YACA,OAAA;SAAA;QAGF,IAAI,cAAA,CAAe,KAAA,CAAM,MAAA,CAAA,EAAS;YAChC,OAAO,aAAA,CAAc,MAAA,CAAO,OAAA,CAAA,CAAA;SAAA;QAI9B,IAAI,aAAA,GAA+C,GAAA,CAAI,QAAA,EAAA,CAAA;QAIvD,MAAM,gBAAA,GAAmB,GAAA,EAAA;YACvB,IAAI,aAAA,KAAkB,kBAAA,EAAoB;gBACxC,MAAM,IAAI,KAAA,CACR,GAAG,GAAA,qDAAA,CAAA,CAAA;aAAA;YAIP,OAAO,aAAA,CAAA;QAAA,CAAA,CAAA;QAGT,IAAI,MAAA,CAAA;QAEJ,IAAI;YAEF,MAAA,GAAS,IAAA,CAAK,MAAA,CAAA,CAAA;YAEd,IAAI,WAAA,CAAY,IAAA,GAAO,CAAA,EAAG;gBACxB,IAAI,YAAA,GAAe,GAAA,CAAI,QAAA,EAAA,CAAA;gBAEvB,MAAM,eAAA,GAAkB,KAAA,CAAM,IAAA,CAAK,WAAA,CAAY,MAAA,EAAA,CAAA,CAAA;gBAC/C,KAAA,IAAS,KAAA,IAAS,eAAA,EAAiB;oBACjC,IAAI,WAAA,GAAc,KAAA,CAAA;oBAElB,IAAI;wBACF,WAAA,GAAc,KAAA,CAAM,SAAA,CAAU,MAAA,EAAQ,YAAA,EAAc,aAAA,CAAA,CAAA;qBAAA;oBAAA,OAC7C,cAAA,EAAP;wBACA,WAAA,GAAc,KAAA,CAAA;wBAEd,iBAAA,CAAkB,OAAA,EAAS,cAAA,EAAgB;4BACzC,QAAA,EAAU,WAAA;yBAAA,CAAA,CAAA;qBAAA;oBAId,IAAI,CAAC,WAAA,EAAa;wBAChB,SAAA;qBAAA;oBAGF,cAAA,CAAe,KAAA,EAAO,MAAA,EAAQ,GAAA,EAAK,gBAAA,CAAA,CAAA;iBAAA;aAAA;SAAA;gBAGvC;YAEA,aAAA,GAAgB,kBAAA,CAAA;SAAA;QAGlB,OAAO,MAAA,CAAA;IAAA,CAAA,CAAA;IAGX,OAAO;QACL,UAAA;QACA,cAAA;QACA,aAAA;QACA,cAAA,EAAgB,uBAAA;KAAA,CAAA;AAAA,CAAA;;ACngBb,IAAM,gBAAA,GAAmB,eAAA,CAAA;AAEzB,IAAM,kBAAA,GACX,GAAA,EAAA,CACA,CAAC,OAAA,EAAA,EAAA,CAA+C,CAAA;IAC9C,OAAA;IACA,IAAA,EAAM,EAAA,CAAG,gBAAA,CAAA,EAAmB,IAAA,EAAA;CAAA,CAAA,CAAA;AAKhC,IAAI,OAAA,CAAA;AACJ,IAAM,kBAAA,GACJ,OAAO,cAAA,KAAmB,UAAA,CAAA,CAAA,CACtB,cAAA,CAAe,IAAA,CACb,OAAO,MAAA,KAAW,WAAA,CAAA,CAAA,CACd,MAAA,CAAA,CAAA,CACA,OAAO,MAAA,KAAW,WAAA,CAAA,CAAA,CAClB,MAAA,CAAA,CAAA,CACA,UAAA,CAAA,CAAA,CAAA,CAGN,CAAC,EAAA,EAAA,EAAA,CACE,CAAA,OAAA,IAAY,CAAA,OAAA,GAAU,OAAA,CAAQ,OAAA,EAAA,CAAA,CAAA,CAAY,IAAA,CAAK,EAAA,CAAA,CAAI,KAAA,CAAM,CAAC,GAAA,EAAA,EAAA,CACzD,UAAA,CAAW,GAAA,EAAA;IACT,MAAM,GAAA,CAAA;AAAA,CAAA,EACL,CAAA,CAAA,CAAA,CAAA;AAGb,IAAM,oBAAA,GAAuB,CAAC,OAAA,EAAA,EAAA;IAC5B,OAAO,CAAC,MAAA,EAAA,EAAA;QACN,UAAA,CAAW,MAAA,EAAQ,OAAA,CAAA,CAAA;IAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAMvB,IAAM,GAAA,GACJ,OAAO,MAAA,KAAW,WAAA,IAAe,MAAA,CAAO,qBAAA,CAAA,CAAA,CACpC,MAAA,CAAO,qBAAA,CAAA,CAAA,CACP,oBAAA,CAAqB,EAAA,CAAA,CAAA;AA8BpB,IAAM,iBAAA,GACX,CAAC,OAAA,GAA4B,EAAE,IAAA,EAAM,KAAA,EAAA,EAAA,EAAA,CACrC,CAAC,IAAA,EAAA,EAAA,CACD,CAAA,GAAI,IAAA,EAAA,EAAA;IACF,MAAM,KAAA,GAAQ,IAAA,CAAK,GAAG,IAAA,CAAA,CAAA;IAEtB,IAAI,SAAA,GAAY,IAAA,CAAA;IAChB,IAAI,uBAAA,GAA0B,KAAA,CAAA;IAC9B,IAAI,kBAAA,GAAqB,KAAA,CAAA;IAEzB,MAAM,SAAA,GAAY,IAAI,GAAA,EAAA,CAAA;IAEtB,MAAM,aAAA,GACJ,OAAA,CAAQ,IAAA,KAAS,MAAA,CAAA,CAAA,CACb,kBAAA,CAAA,CAAA,CACA,OAAA,CAAQ,IAAA,KAAS,KAAA,CAAA,CAAA,CACjB,GAAA,CAAA,CAAA,CACA,OAAA,CAAQ,IAAA,KAAS,UAAA,CAAA,CAAA,CACjB,OAAA,CAAQ,iBAAA,CAAA,CAAA,CACR,oBAAA,CAAqB,OAAA,CAAQ,OAAA,CAAA,CAAA;IAEnC,MAAM,eAAA,GAAkB,GAAA,EAAA;QAGtB,kBAAA,GAAqB,KAAA,CAAA;QACrB,IAAI,uBAAA,EAAyB;YAC3B,uBAAA,GAA0B,KAAA,CAAA;YAC1B,SAAA,CAAU,OAAA,CAAQ,CAAC,CAAA,EAAA,EAAA,CAAM,CAAA,EAAA,CAAA,CAAA;SAAA;IAAA,CAAA,CAAA;IAI7B,OAAO,MAAA,CAAO,MAAA,CAAO,EAAA,EAAI,KAAA,EAAO;QAG9B,SAAA,CAAU,SAAA;YAKR,MAAM,eAAA,GAAmC,GAAA,EAAA,CAAM,SAAA,IAAa,SAAA,EAAA,CAAA;YAC5D,MAAM,WAAA,GAAc,KAAA,CAAM,SAAA,CAAU,eAAA,CAAA,CAAA;YACpC,SAAA,CAAU,GAAA,CAAI,SAAA,CAAA,CAAA;YACd,OAAO,GAAA,EAAA;gBACL,WAAA,EAAA,CAAA;gBACA,SAAA,CAAU,MAAA,CAAO,SAAA,CAAA,CAAA;YAAA,CAAA,CAAA;QAAA,CAAA;QAKrB,QAAA,CAAS,MAAA;YAzHf,IAAA,EAAA,CAAA;YA0HQ,IAAI;gBAGF,SAAA,GAAY,CAAC,CAAA,CAAA,EAAA,GAAA,MAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAQ,IAAA,CAAA,IAAR,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAe,gBAAA,CAAA,CAAA,CAAA;gBAG5B,uBAAA,GAA0B,CAAC,SAAA,CAAA;gBAC3B,IAAI,uBAAA,EAAyB;oBAI3B,IAAI,CAAC,kBAAA,EAAoB;wBACvB,kBAAA,GAAqB,IAAA,CAAA;wBACrB,aAAA,CAAc,eAAA,CAAA,CAAA;qBAAA;iBAAA;gBASlB,OAAO,KAAA,CAAM,QAAA,CAAS,MAAA,CAAA,CAAA;aAAA;oBACtB;gBAEA,SAAA,GAAY,IAAA,CAAA;aAAA;QAAA,CAAA;KAAA,CAAA,CAAA;AAAA,CAAA,CAAA;;A7B3HtB,SAAA,EAAA,CAAA", "sourcesContent": ["import { enableES5 } from 'immer'\r\nexport * from 'redux'\r\nexport {\r\n  default as createNextState,\r\n  current,\r\n  freeze,\r\n  original,\r\n  isDraft,\r\n} from 'immer'\r\nexport type { Draft } from 'immer'\r\nexport { createSelector } from 'reselect'\r\nexport type {\r\n  Selector,\r\n  OutputParametricSelector,\r\n  OutputSelector,\r\n  ParametricSelector,\r\n} from 'reselect'\r\nexport { createDraftSafeSelector } from './createDraftSafeSelector'\r\nexport type { ThunkAction, ThunkDispatch, ThunkMiddleware } from 'redux-thunk'\r\n\r\n// We deliberately enable Immer's ES5 support, on the grounds that\r\n// we assume RTK will be used with React Native and other Proxy-less\r\n// environments.  In addition, that's how Immer 4 behaved, and since\r\n// we want to ship this in an RTK minor, we should keep the same behavior.\r\nenableES5()\r\n\r\nexport {\r\n  // js\r\n  configureStore,\r\n} from './configureStore'\r\nexport type {\r\n  // types\r\n  ConfigureEnhancersCallback,\r\n  ConfigureStoreOptions,\r\n  EnhancedStore,\r\n} from './configureStore'\r\nexport type { DevToolsEnhancerOptions } from './devtoolsExtension'\r\nexport {\r\n  // js\r\n  createAction,\r\n  getType,\r\n  isAction,\r\n  isActionCreator,\r\n  isFSA as isFluxStandardAction,\r\n} from './createAction'\r\nexport type {\r\n  // types\r\n  PayloadAction,\r\n  PayloadActionCreator,\r\n  ActionCreatorWithNonInferrablePayload,\r\n  ActionCreatorWithOptionalPayload,\r\n  ActionCreatorWithPayload,\r\n  ActionCreatorWithoutPayload,\r\n  ActionCreatorWithPreparedPayload,\r\n  PrepareAction,\r\n} from './createAction'\r\nexport {\r\n  // js\r\n  createReducer,\r\n} from './createReducer'\r\nexport type {\r\n  // types\r\n  Actions,\r\n  CaseReducer,\r\n  CaseReducers,\r\n} from './createReducer'\r\nexport {\r\n  // js\r\n  createSlice,\r\n} from './createSlice'\r\n\r\nexport type {\r\n  // types\r\n  CreateSliceOptions,\r\n  Slice,\r\n  CaseReducerActions,\r\n  SliceCaseReducers,\r\n  ValidateSliceCaseReducers,\r\n  CaseReducerWithPrepare,\r\n  SliceActionCreator,\r\n} from './createSlice'\r\nexport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware'\r\nexport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware'\r\nexport {\r\n  // js\r\n  createImmutableStateInvariantMiddleware,\r\n  isImmutableDefault,\r\n} from './immutableStateInvariantMiddleware'\r\nexport type {\r\n  // types\r\n  ImmutableStateInvariantMiddlewareOptions,\r\n} from './immutableStateInvariantMiddleware'\r\nexport {\r\n  // js\r\n  createSerializableStateInvariantMiddleware,\r\n  findNonSerializableValue,\r\n  isPlain,\r\n} from './serializableStateInvariantMiddleware'\r\nexport type {\r\n  // types\r\n  SerializableStateInvariantMiddlewareOptions,\r\n} from './serializableStateInvariantMiddleware'\r\nexport {\r\n  // js\r\n  getDefaultMiddleware,\r\n} from './getDefaultMiddleware'\r\nexport type {\r\n  // types\r\n  ActionReducerMapBuilder,\r\n} from './mapBuilders'\r\nexport { MiddlewareArray, EnhancerArray } from './utils'\r\n\r\nexport { createEntityAdapter } from './entities/create_adapter'\r\nexport type {\r\n  Dictionary,\r\n  EntityState,\r\n  EntityAdapter,\r\n  EntitySelectors,\r\n  EntityStateAdapter,\r\n  EntityId,\r\n  Update,\r\n  IdSelector,\r\n  Comparer,\r\n} from './entities/models'\r\n\r\nexport {\r\n  createAsyncThunk,\r\n  unwrapResult,\r\n  miniSerializeError,\r\n} from './createAsyncThunk'\r\nexport type {\r\n  AsyncThunk,\r\n  AsyncThunkOptions,\r\n  AsyncThunkAction,\r\n  AsyncThunkPayloadCreatorReturnValue,\r\n  AsyncThunkPayloadCreator,\r\n  SerializedError,\r\n} from './createAsyncThunk'\r\n\r\nexport {\r\n  // js\r\n  isAllOf,\r\n  isAnyOf,\r\n  isPending,\r\n  isRejected,\r\n  isFulfilled,\r\n  isAsyncThunkAction,\r\n  isRejectedWithValue,\r\n} from './matchers'\r\nexport type {\r\n  // types\r\n  ActionMatchingAllOf,\r\n  ActionMatchingAnyOf,\r\n} from './matchers'\r\n\r\nexport { nanoid } from './nanoid'\r\n\r\nexport { default as isPlainObject } from './isPlainObject'\r\n\r\nexport type {\r\n  ListenerEffect,\r\n  ListenerMiddleware,\r\n  ListenerEffectAPI,\r\n  ListenerMiddlewareInstance,\r\n  CreateListenerMiddlewareOptions,\r\n  ListenerErrorHandler,\r\n  TypedStartListening,\r\n  TypedAddListener,\r\n  TypedStopListening,\r\n  TypedRemoveListener,\r\n  UnsubscribeListener,\r\n  UnsubscribeListenerOptions,\r\n  ForkedTaskExecutor,\r\n  ForkedTask,\r\n  ForkedTaskAPI,\r\n  AsyncTaskExecutor,\r\n  SyncTaskExecutor,\r\n  TaskCancelled,\r\n  TaskRejected,\r\n  TaskResolved,\r\n  TaskResult,\r\n} from './listenerMiddleware/index'\r\nexport type { AnyListenerPredicate } from './listenerMiddleware/types'\r\n\r\nexport {\r\n  createListenerMiddleware,\r\n  addListener,\r\n  removeListener,\r\n  clearAllListeners,\r\n  TaskAbortError,\r\n} from './listenerMiddleware/index'\r\n\r\nexport {\r\n  SHOULD_AUTOBATCH,\r\n  prepareAutoBatched,\r\n  autoBatchEnhancer,\r\n} from './autoBatchEnhancer'\r\nexport type { AutoBatchOptions } from './autoBatchEnhancer'\r\n\r\nexport type { ExtractDispatchExtensions as TSHelpersExtractDispatchExtensions } from './tsHelpers'\r\n", "import { current, isDraft } from 'immer'\r\nimport { createSelector } from 'reselect'\r\n\r\n/**\r\n * \"Draft-Safe\" version of `reselect`'s `createSelector`:\r\n * If an `immer`-drafted object is passed into the resulting selector's first argument,\r\n * the selector will act on the current draft value, instead of returning a cached value\r\n * that might be possibly outdated if the draft has been modified since.\r\n * @public\r\n */\r\nexport const createDraftSafeSelector: typeof createSelector = (\r\n  ...args: unknown[]\r\n) => {\r\n  const selector = (createSelector as any)(...args)\r\n  const wrappedSelector = (value: unknown, ...rest: unknown[]) =>\r\n    selector(isDraft(value) ? current(value) : value, ...rest)\r\n  return wrappedSelector as any\r\n}\r\n", "import type {\r\n  Reducer,\r\n  ReducersMapObject,\r\n  Middleware,\r\n  Action,\r\n  AnyAction,\r\n  StoreEnhancer,\r\n  Store,\r\n  Dispatch,\r\n  PreloadedState,\r\n  CombinedState,\r\n} from 'redux'\r\nimport { createStore, compose, applyMiddleware, combineReducers } from 'redux'\r\nimport type { DevToolsEnhancerOptions as DevToolsOptions } from './devtoolsExtension'\r\nimport { composeWithDevTools } from './devtoolsExtension'\r\n\r\nimport isPlainObject from './isPlainObject'\r\nimport type {\r\n  ThunkMiddlewareFor,\r\n  CurriedGetDefaultMiddleware,\r\n} from './getDefaultMiddleware'\r\nimport { curryGetDefaultMiddleware } from './getDefaultMiddleware'\r\nimport type {\r\n  NoInfer,\r\n  ExtractDispatchExtensions,\r\n  ExtractStoreExtensions,\r\n  ExtractStateExtensions,\r\n} from './tsHelpers'\r\nimport { EnhancerArray } from './utils'\r\n\r\nconst IS_PRODUCTION = process.env.NODE_ENV === 'production'\r\n\r\n/**\r\n * Callback function type, to be used in `ConfigureStoreOptions.enhancers`\r\n *\r\n * @public\r\n */\r\nexport type ConfigureEnhancersCallback<E extends Enhancers = Enhancers> = (\r\n  defaultEnhancers: EnhancerArray<[StoreEnhancer<{}, {}>]>\r\n) => E\r\n\r\n/**\r\n * Options for `configureStore()`.\r\n *\r\n * @public\r\n */\r\nexport interface ConfigureStoreOptions<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = Middlewares<S>,\r\n  E extends Enhancers = Enhancers\r\n> {\r\n  /**\r\n   * A single reducer function that will be used as the root reducer, or an\r\n   * object of slice reducers that will be passed to `combineReducers()`.\r\n   */\r\n  reducer: Reducer<S, A> | ReducersMapObject<S, A>\r\n\r\n  /**\r\n   * An array of Redux middleware to install. If not supplied, defaults to\r\n   * the set of middleware returned by `getDefaultMiddleware()`.\r\n   *\r\n   * @example `middleware: (gDM) => gDM().concat(logger, apiMiddleware, yourCustomMiddleware)`\r\n   * @see https://redux-toolkit.js.org/api/getDefaultMiddleware#intended-usage\r\n   */\r\n  middleware?: ((getDefaultMiddleware: CurriedGetDefaultMiddleware<S>) => M) | M\r\n\r\n  /**\r\n   * Whether to enable Redux DevTools integration. Defaults to `true`.\r\n   *\r\n   * Additional configuration can be done by passing Redux DevTools options\r\n   */\r\n  devTools?: boolean | DevToolsOptions\r\n\r\n  /**\r\n   * The initial state, same as Redux's createStore.\r\n   * You may optionally specify it to hydrate the state\r\n   * from the server in universal apps, or to restore a previously serialized\r\n   * user session. If you use `combineReducers()` to produce the root reducer\r\n   * function (either directly or indirectly by passing an object as `reducer`),\r\n   * this must be an object with the same shape as the reducer map keys.\r\n   */\r\n  /*\r\n  Not 100% correct but the best approximation we can get:\r\n  - if S is a `CombinedState` applying a second `CombinedState` on it does not change anything.\r\n  - if it is not, there could be two cases:\r\n    - `ReducersMapObject<S, A>` is being passed in. In this case, we will call `combineReducers` on it and `CombinedState<S>` is correct\r\n    - `Reducer<S, A>` is being passed in. In this case, actually `CombinedState<S>` is wrong and `S` would be correct.\r\n    As we cannot distinguish between those two cases without adding another generic parameter,\r\n    we just make the pragmatic assumption that the latter almost never happens.\r\n  */\r\n  preloadedState?: PreloadedState<CombinedState<NoInfer<S>>>\r\n\r\n  /**\r\n   * The store enhancers to apply. See Redux's `createStore()`.\r\n   * All enhancers will be included before the DevTools Extension enhancer.\r\n   * If you need to customize the order of enhancers, supply a callback\r\n   * function that will receive the original array (ie, `[applyMiddleware]`),\r\n   * and should return a new array (such as `[applyMiddleware, offline]`).\r\n   * If you only need to add middleware, you can use the `middleware` parameter instead.\r\n   */\r\n  enhancers?: E | ConfigureEnhancersCallback<E>\r\n}\r\n\r\ntype Middlewares<S> = ReadonlyArray<Middleware<{}, S>>\r\n\r\ntype Enhancers = ReadonlyArray<StoreEnhancer>\r\n\r\nexport interface ToolkitStore<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = Middlewares<S>\r\n> extends Store<S, A> {\r\n  /**\r\n   * The `dispatch` method of your store, enhanced by all its middlewares.\r\n   *\r\n   * @inheritdoc\r\n   */\r\n  dispatch: ExtractDispatchExtensions<M> & Dispatch<A>\r\n}\r\n\r\n/**\r\n * A Redux store returned by `configureStore()`. Supports dispatching\r\n * side-effectful _thunks_ in addition to plain actions.\r\n *\r\n * @public\r\n */\r\nexport type EnhancedStore<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = Middlewares<S>,\r\n  E extends Enhancers = Enhancers\r\n> = ToolkitStore<S & ExtractStateExtensions<E>, A, M> &\r\n  ExtractStoreExtensions<E>\r\n\r\n/**\r\n * A friendly abstraction over the standard Redux `createStore()` function.\r\n *\r\n * @param options The store configuration.\r\n * @returns A configured Redux store.\r\n *\r\n * @public\r\n */\r\nexport function configureStore<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = [ThunkMiddlewareFor<S>],\r\n  E extends Enhancers = [StoreEnhancer]\r\n>(options: ConfigureStoreOptions<S, A, M, E>): EnhancedStore<S, A, M, E> {\r\n  const curriedGetDefaultMiddleware = curryGetDefaultMiddleware<S>()\r\n\r\n  const {\r\n    reducer = undefined,\r\n    middleware = curriedGetDefaultMiddleware(),\r\n    devTools = true,\r\n    preloadedState = undefined,\r\n    enhancers = undefined,\r\n  } = options || {}\r\n\r\n  let rootReducer: Reducer<S, A>\r\n\r\n  if (typeof reducer === 'function') {\r\n    rootReducer = reducer\r\n  } else if (isPlainObject(reducer)) {\r\n    rootReducer = combineReducers(reducer) as unknown as Reducer<S, A>\r\n  } else {\r\n    throw new Error(\r\n      '\"reducer\" is a required argument, and must be a function or an object of functions that can be passed to combineReducers'\r\n    )\r\n  }\r\n\r\n  let finalMiddleware = middleware\r\n  if (typeof finalMiddleware === 'function') {\r\n    finalMiddleware = finalMiddleware(curriedGetDefaultMiddleware)\r\n\r\n    if (!IS_PRODUCTION && !Array.isArray(finalMiddleware)) {\r\n      throw new Error(\r\n        'when using a middleware builder function, an array of middleware must be returned'\r\n      )\r\n    }\r\n  }\r\n  if (\r\n    !IS_PRODUCTION &&\r\n    finalMiddleware.some((item: any) => typeof item !== 'function')\r\n  ) {\r\n    throw new Error(\r\n      'each middleware provided to configureStore must be a function'\r\n    )\r\n  }\r\n\r\n  const middlewareEnhancer: StoreEnhancer = applyMiddleware(...finalMiddleware)\r\n\r\n  let finalCompose = compose\r\n\r\n  if (devTools) {\r\n    finalCompose = composeWithDevTools({\r\n      // Enable capture of stack traces for dispatched Redux actions\r\n      trace: !IS_PRODUCTION,\r\n      ...(typeof devTools === 'object' && devTools),\r\n    })\r\n  }\r\n\r\n  const defaultEnhancers = new EnhancerArray(middlewareEnhancer)\r\n  let storeEnhancers: Enhancers = defaultEnhancers\r\n\r\n  if (Array.isArray(enhancers)) {\r\n    storeEnhancers = [middlewareEnhancer, ...enhancers]\r\n  } else if (typeof enhancers === 'function') {\r\n    storeEnhancers = enhancers(defaultEnhancers)\r\n  }\r\n\r\n  const composedEnhancer = finalCompose(...storeEnhancers) as StoreEnhancer<any>\r\n\r\n  return createStore(rootReducer, preloadedState, composedEnhancer)\r\n}\r\n", "import type { Action, ActionCreator, StoreEnhancer } from 'redux'\r\nimport { compose } from 'redux'\r\n\r\n/**\r\n * @public\r\n */\r\nexport interface DevToolsEnhancerOptions {\r\n  /**\r\n   * the instance name to be showed on the monitor page. Default value is `document.title`.\r\n   * If not specified and there's no document title, it will consist of `tabId` and `instanceId`.\r\n   */\r\n  name?: string\r\n  /**\r\n   * action creators functions to be available in the Dispatcher.\r\n   */\r\n  actionCreators?: ActionCreator<any>[] | { [key: string]: ActionCreator<any> }\r\n  /**\r\n   * if more than one action is dispatched in the indicated interval, all new actions will be collected and sent at once.\r\n   * It is the joint between performance and speed. When set to `0`, all actions will be sent instantly.\r\n   * Set it to a higher value when experiencing perf issues (also `maxAge` to a lower value).\r\n   *\r\n   * @default 500 ms.\r\n   */\r\n  latency?: number\r\n  /**\r\n   * (> 1) - maximum allowed actions to be stored in the history tree. The oldest actions are removed once maxAge is reached. It's critical for performance.\r\n   *\r\n   * @default 50\r\n   */\r\n  maxAge?: number\r\n  /**\r\n   * Customizes how actions and state are serialized and deserialized. Can be a boolean or object. If given a boolean, the behavior is the same as if you\r\n   * were to pass an object and specify `options` as a boolean. Giving an object allows fine-grained customization using the `replacer` and `reviver`\r\n   * functions.\r\n   */\r\n  serialize?:\r\n    | boolean\r\n    | {\r\n        /**\r\n         * - `undefined` - will use regular `JSON.stringify` to send data (it's the fast mode).\r\n         * - `false` - will handle also circular references.\r\n         * - `true` - will handle also date, regex, undefined, error objects, symbols, maps, sets and functions.\r\n         * - object, which contains `date`, `regex`, `undefined`, `error`, `symbol`, `map`, `set` and `function` keys.\r\n         *   For each of them you can indicate if to include (by setting as `true`).\r\n         *   For `function` key you can also specify a custom function which handles serialization.\r\n         *   See [`jsan`](https://github.com/kolodny/jsan) for more details.\r\n         */\r\n        options?:\r\n          | undefined\r\n          | boolean\r\n          | {\r\n              date?: true\r\n              regex?: true\r\n              undefined?: true\r\n              error?: true\r\n              symbol?: true\r\n              map?: true\r\n              set?: true\r\n              function?: true | ((fn: (...args: any[]) => any) => string)\r\n            }\r\n        /**\r\n         * [JSON replacer function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The_replacer_parameter) used for both actions and states stringify.\r\n         * In addition, you can specify a data type by adding a [`__serializedType__`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/helpers/index.js#L4)\r\n         * key. So you can deserialize it back while importing or persisting data.\r\n         * Moreover, it will also [show a nice preview showing the provided custom type](https://cloud.githubusercontent.com/assets/7957859/21814330/a17d556a-d761-11e6-85ef-159dd12f36c5.png):\r\n         */\r\n        replacer?: (key: string, value: unknown) => any\r\n        /**\r\n         * [JSON `reviver` function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Using_the_reviver_parameter)\r\n         * used for parsing the imported actions and states. See [`remotedev-serialize`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/immutable/serialize.js#L8-L41)\r\n         * as an example on how to serialize special data types and get them back.\r\n         */\r\n        reviver?: (key: string, value: unknown) => any\r\n        /**\r\n         * Automatically serialize/deserialize immutablejs via [remotedev-serialize](https://github.com/zalmoxisus/remotedev-serialize).\r\n         * Just pass the Immutable library. It will support all ImmutableJS structures. You can even export them into a file and get them back.\r\n         * The only exception is `Record` class, for which you should pass this in addition the references to your classes in `refs`.\r\n         */\r\n        immutable?: any\r\n        /**\r\n         * ImmutableJS `Record` classes used to make possible restore its instances back when importing, persisting...\r\n         */\r\n        refs?: any\r\n      }\r\n  /**\r\n   * function which takes `action` object and id number as arguments, and should return `action` object back.\r\n   */\r\n  actionSanitizer?: <A extends Action>(action: A, id: number) => A\r\n  /**\r\n   * function which takes `state` object and index as arguments, and should return `state` object back.\r\n   */\r\n  stateSanitizer?: <S>(state: S, index: number) => S\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsWhitelist` specified, `actionsBlacklist` is ignored.\r\n   * @deprecated Use actionsDenylist instead.\r\n   */\r\n  actionsBlacklist?: string | string[]\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsWhitelist` specified, `actionsBlacklist` is ignored.\r\n   * @deprecated Use actionsAllowlist instead.\r\n   */\r\n  actionsWhitelist?: string | string[]\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\r\n  actionsDenylist?: string | string[]\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\r\n  actionsAllowlist?: string | string[]\r\n  /**\r\n   * called for every action before sending, takes `state` and `action` object, and returns `true` in case it allows sending the current data to the monitor.\r\n   * Use it as a more advanced version of `actionsDenylist`/`actionsAllowlist` parameters.\r\n   */\r\n  predicate?: <S, A extends Action>(state: S, action: A) => boolean\r\n  /**\r\n   * if specified as `false`, it will not record the changes till clicking on `Start recording` button.\r\n   * Available only for Redux enhancer, for others use `autoPause`.\r\n   *\r\n   * @default true\r\n   */\r\n  shouldRecordChanges?: boolean\r\n  /**\r\n   * if specified, whenever clicking on `Pause recording` button and there are actions in the history log, will add this action type.\r\n   * If not specified, will commit when paused. Available only for Redux enhancer.\r\n   *\r\n   * @default \"@@PAUSED\"\"\r\n   */\r\n  pauseActionType?: string\r\n  /**\r\n   * auto pauses when the extension’s window is not opened, and so has zero impact on your app when not in use.\r\n   * Not available for Redux enhancer (as it already does it but storing the data to be sent).\r\n   *\r\n   * @default false\r\n   */\r\n  autoPause?: boolean\r\n  /**\r\n   * if specified as `true`, it will not allow any non-monitor actions to be dispatched till clicking on `Unlock changes` button.\r\n   * Available only for Redux enhancer.\r\n   *\r\n   * @default false\r\n   */\r\n  shouldStartLocked?: boolean\r\n  /**\r\n   * if set to `false`, will not recompute the states on hot reloading (or on replacing the reducers). Available only for Redux enhancer.\r\n   *\r\n   * @default true\r\n   */\r\n  shouldHotReload?: boolean\r\n  /**\r\n   * if specified as `true`, whenever there's an exception in reducers, the monitors will show the error message, and next actions will not be dispatched.\r\n   *\r\n   * @default false\r\n   */\r\n  shouldCatchErrors?: boolean\r\n  /**\r\n   * If you want to restrict the extension, specify the features you allow.\r\n   * If not specified, all of the features are enabled. When set as an object, only those included as `true` will be allowed.\r\n   * Note that except `true`/`false`, `import` and `export` can be set as `custom` (which is by default for Redux enhancer), meaning that the importing/exporting occurs on the client side.\r\n   * Otherwise, you'll get/set the data right from the monitor part.\r\n   */\r\n  features?: {\r\n    /**\r\n     * start/pause recording of dispatched actions\r\n     */\r\n    pause?: boolean\r\n    /**\r\n     * lock/unlock dispatching actions and side effects\r\n     */\r\n    lock?: boolean\r\n    /**\r\n     * persist states on page reloading\r\n     */\r\n    persist?: boolean\r\n    /**\r\n     * export history of actions in a file\r\n     */\r\n    export?: boolean | 'custom'\r\n    /**\r\n     * import history of actions from a file\r\n     */\r\n    import?: boolean | 'custom'\r\n    /**\r\n     * jump back and forth (time travelling)\r\n     */\r\n    jump?: boolean\r\n    /**\r\n     * skip (cancel) actions\r\n     */\r\n    skip?: boolean\r\n    /**\r\n     * drag and drop actions in the history list\r\n     */\r\n    reorder?: boolean\r\n    /**\r\n     * dispatch custom actions or action creators\r\n     */\r\n    dispatch?: boolean\r\n    /**\r\n     * generate tests for the selected actions\r\n     */\r\n    test?: boolean\r\n  }\r\n  /**\r\n   * Set to true or a stacktrace-returning function to record call stack traces for dispatched actions.\r\n   * Defaults to false.\r\n   */\r\n  trace?: boolean | (<A extends Action>(action: A) => string)\r\n  /**\r\n   * The maximum number of stack trace entries to record per action. Defaults to 10.\r\n   */\r\n  traceLimit?: number\r\n}\r\n\r\ntype Compose = typeof compose\r\n\r\ninterface ComposeWithDevTools {\r\n  (options: DevToolsEnhancerOptions): Compose\r\n  <StoreExt>(...funcs: StoreEnhancer<StoreExt>[]): StoreEnhancer<StoreExt>\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport const composeWithDevTools: ComposeWithDevTools =\r\n  typeof window !== 'undefined' &&\r\n  (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\r\n    ? (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\r\n    : function () {\r\n        if (arguments.length === 0) return undefined\r\n        if (typeof arguments[0] === 'object') return compose\r\n        return compose.apply(null, arguments as any as Function[])\r\n      }\r\n\r\n/**\r\n * @public\r\n */\r\nexport const devToolsEnhancer: {\r\n  (options: DevToolsEnhancerOptions): StoreEnhancer<any>\r\n} =\r\n  typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__\r\n    ? (window as any).__REDUX_DEVTOOLS_EXTENSION__\r\n    : function () {\r\n        return function (noop) {\r\n          return noop\r\n        }\r\n      }\r\n", "/**\r\n * Returns true if the passed value is \"plain\" object, i.e. an object whose\r\n * prototype is the root `Object.prototype`. This includes objects created\r\n * using object literals, but not for instance for class instances.\r\n *\r\n * @param {any} value The value to inspect.\r\n * @returns {boolean} True if the argument appears to be a plain object.\r\n *\r\n * @public\r\n */\r\nexport default function isPlainObject(value: unknown): value is object {\r\n  if (typeof value !== 'object' || value === null) return false\r\n\r\n  let proto = Object.getPrototypeOf(value)\r\n  if (proto === null) return true\r\n\r\n  let baseProto = proto\r\n  while (Object.getPrototypeOf(baseProto) !== null) {\r\n    baseProto = Object.getPrototypeOf(baseProto)\r\n  }\r\n\r\n  return proto === baseProto\r\n}\r\n", "import type { Middleware, AnyAction } from 'redux'\r\nimport type { ThunkMiddleware } from 'redux-thunk'\r\nimport thunkMiddleware from 'redux-thunk'\r\nimport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware'\r\nimport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware'\r\nimport type { ImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware'\r\n/* PROD_START_REMOVE_UMD */\r\nimport { createImmutableStateInvariantMiddleware } from './immutableStateInvariantMiddleware'\r\n/* PROD_STOP_REMOVE_UMD */\r\n\r\nimport type { SerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware'\r\nimport { createSerializableStateInvariantMiddleware } from './serializableStateInvariantMiddleware'\r\nimport type { ExcludeFromTuple } from './tsHelpers'\r\nimport { MiddlewareArray } from './utils'\r\n\r\nfunction isBoolean(x: any): x is boolean {\r\n  return typeof x === 'boolean'\r\n}\r\n\r\ninterface ThunkOptions<E = any> {\r\n  extraArgument: E\r\n}\r\n\r\ninterface GetDefaultMiddlewareOptions {\r\n  thunk?: boolean | ThunkOptions\r\n  immutableCheck?: boolean | ImmutableStateInvariantMiddlewareOptions\r\n  serializableCheck?: boolean | SerializableStateInvariantMiddlewareOptions\r\n  actionCreatorCheck?: boolean | ActionCreatorInvariantMiddlewareOptions\r\n}\r\n\r\nexport type ThunkMiddlewareFor<\r\n  S,\r\n  O extends GetDefaultMiddlewareOptions = {}\r\n> = O extends {\r\n  thunk: false\r\n}\r\n  ? never\r\n  : O extends { thunk: { extraArgument: infer E } }\r\n  ? ThunkMiddleware<S, AnyAction, E>\r\n  : ThunkMiddleware<S, AnyAction>\r\n\r\nexport type CurriedGetDefaultMiddleware<S = any> = <\r\n  O extends Partial<GetDefaultMiddlewareOptions> = {\r\n    thunk: true\r\n    immutableCheck: true\r\n    serializableCheck: true\r\n    actionCreatorCheck: true\r\n  }\r\n>(\r\n  options?: O\r\n) => MiddlewareArray<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>>\r\n\r\nexport function curryGetDefaultMiddleware<\r\n  S = any\r\n>(): CurriedGetDefaultMiddleware<S> {\r\n  return function curriedGetDefaultMiddleware(options) {\r\n    return getDefaultMiddleware(options)\r\n  }\r\n}\r\n\r\n/**\r\n * Returns any array containing the default middleware installed by\r\n * `configureStore()`. Useful if you want to configure your store with a custom\r\n * `middleware` array but still keep the default set.\r\n *\r\n * @return The default middleware used by `configureStore()`.\r\n *\r\n * @public\r\n *\r\n * @deprecated Prefer to use the callback notation for the `middleware` option in `configureStore`\r\n * to access a pre-typed `getDefaultMiddleware` instead.\r\n */\r\nexport function getDefaultMiddleware<\r\n  S = any,\r\n  O extends Partial<GetDefaultMiddlewareOptions> = {\r\n    thunk: true\r\n    immutableCheck: true\r\n    serializableCheck: true\r\n    actionCreatorCheck: true\r\n  }\r\n>(\r\n  options: O = {} as O\r\n): MiddlewareArray<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>> {\r\n  const {\r\n    thunk = true,\r\n    immutableCheck = true,\r\n    serializableCheck = true,\r\n    actionCreatorCheck = true,\r\n  } = options\r\n\r\n  let middlewareArray = new MiddlewareArray<Middleware[]>()\r\n\r\n  if (thunk) {\r\n    if (isBoolean(thunk)) {\r\n      middlewareArray.push(thunkMiddleware)\r\n    } else {\r\n      middlewareArray.push(\r\n        thunkMiddleware.withExtraArgument(thunk.extraArgument)\r\n      )\r\n    }\r\n  }\r\n\r\n  if (process.env.NODE_ENV !== 'production') {\r\n    if (immutableCheck) {\r\n      /* PROD_START_REMOVE_UMD */\r\n      let immutableOptions: ImmutableStateInvariantMiddlewareOptions = {}\r\n\r\n      if (!isBoolean(immutableCheck)) {\r\n        immutableOptions = immutableCheck\r\n      }\r\n\r\n      middlewareArray.unshift(\r\n        createImmutableStateInvariantMiddleware(immutableOptions)\r\n      )\r\n      /* PROD_STOP_REMOVE_UMD */\r\n    }\r\n\r\n    if (serializableCheck) {\r\n      let serializableOptions: SerializableStateInvariantMiddlewareOptions = {}\r\n\r\n      if (!isBoolean(serializableCheck)) {\r\n        serializableOptions = serializableCheck\r\n      }\r\n\r\n      middlewareArray.push(\r\n        createSerializableStateInvariantMiddleware(serializableOptions)\r\n      )\r\n    }\r\n    if (actionCreatorCheck) {\r\n      let actionCreatorOptions: ActionCreatorInvariantMiddlewareOptions = {}\r\n\r\n      if (!isBoolean(actionCreatorCheck)) {\r\n        actionCreatorOptions = actionCreatorCheck\r\n      }\r\n\r\n      middlewareArray.unshift(\r\n        createActionCreatorInvariantMiddleware(actionCreatorOptions)\r\n      )\r\n    }\r\n  }\r\n\r\n  return middlewareArray as any\r\n}\r\n", "import type { Middleware, StoreEnhancer } from 'redux'\r\nimport type { EnhancerArray, MiddlewareArray } from './utils'\r\n\r\n/**\r\n * return True if T is `any`, otherwise return False\r\n * taken from https://github.com/joonhocho/tsdef\r\n *\r\n * @internal\r\n */\r\nexport type IsAny<T, True, False = never> =\r\n  // test if we are going the left AND right path in the condition\r\n  true | false extends (T extends never ? true : false) ? True : False\r\n\r\n/**\r\n * return True if T is `unknown`, otherwise return False\r\n * taken from https://github.com/joonhocho/tsdef\r\n *\r\n * @internal\r\n */\r\nexport type IsUnknown<T, True, False = never> = unknown extends T\r\n  ? IsAny<T, False, True>\r\n  : False\r\n\r\nexport type FallbackIfUnknown<T, Fallback> = IsUnknown<T, Fallback, T>\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IfMaybeUndefined<P, True, False> = [undefined] extends [P]\r\n  ? True\r\n  : False\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IfVoid<P, True, False> = [void] extends [P] ? True : False\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IsEmptyObj<T, True, False = never> = T extends any\r\n  ? keyof T extends never\r\n    ? IsUnknown<T, False, IfMaybeUndefined<T, False, IfVoid<T, False, True>>>\r\n    : False\r\n  : never\r\n\r\n/**\r\n * returns True if TS version is above 3.5, False if below.\r\n * uses feature detection to detect TS version >= 3.5\r\n * * versions below 3.5 will return `{}` for unresolvable interference\r\n * * versions above will return `unknown`\r\n *\r\n * @internal\r\n */\r\nexport type AtLeastTS35<True, False> = [True, False][IsUnknown<\r\n  ReturnType<<T>() => T>,\r\n  0,\r\n  1\r\n>]\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IsUnknownOrNonInferrable<T, True, False> = AtLeastTS35<\r\n  IsUnknown<T, True, False>,\r\n  IsEmptyObj<T, True, IsUnknown<T, True, False>>\r\n>\r\n\r\n/**\r\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\r\n */\r\nexport type UnionToIntersection<U> = (\r\n  U extends any ? (k: U) => void : never\r\n) extends (k: infer I) => void\r\n  ? I\r\n  : never\r\n\r\n// Appears to have a convenient side effect of ignoring `never` even if that's not what you specified\r\nexport type ExcludeFromTuple<T, E, Acc extends unknown[] = []> = T extends [\r\n  infer Head,\r\n  ...infer Tail\r\n]\r\n  ? ExcludeFromTuple<Tail, E, [...Acc, ...([Head] extends [E] ? [] : [Head])]>\r\n  : Acc\r\n\r\ntype ExtractDispatchFromMiddlewareTuple<\r\n  MiddlewareTuple extends any[],\r\n  Acc extends {}\r\n> = MiddlewareTuple extends [infer Head, ...infer Tail]\r\n  ? ExtractDispatchFromMiddlewareTuple<\r\n      Tail,\r\n      Acc & (Head extends Middleware<infer D> ? IsAny<D, {}, D> : {})\r\n    >\r\n  : Acc\r\n\r\nexport type ExtractDispatchExtensions<M> = M extends MiddlewareArray<\r\n  infer MiddlewareTuple\r\n>\r\n  ? ExtractDispatchFromMiddlewareTuple<MiddlewareTuple, {}>\r\n  : M extends ReadonlyArray<Middleware>\r\n  ? ExtractDispatchFromMiddlewareTuple<[...M], {}>\r\n  : never\r\n\r\ntype ExtractStoreExtensionsFromEnhancerTuple<\r\n  EnhancerTuple extends any[],\r\n  Acc extends {}\r\n> = EnhancerTuple extends [infer Head, ...infer Tail]\r\n  ? ExtractStoreExtensionsFromEnhancerTuple<\r\n      Tail,\r\n      Acc & (Head extends StoreEnhancer<infer Ext> ? IsAny<Ext, {}, Ext> : {})\r\n    >\r\n  : Acc\r\n\r\nexport type ExtractStoreExtensions<E> = E extends EnhancerArray<\r\n  infer EnhancerTuple\r\n>\r\n  ? ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple, {}>\r\n  : E extends ReadonlyArray<StoreEnhancer>\r\n  ? UnionToIntersection<\r\n      E[number] extends StoreEnhancer<infer Ext>\r\n        ? Ext extends {}\r\n          ? IsAny<Ext, {}, Ext>\r\n          : {}\r\n        : {}\r\n    >\r\n  : never\r\n\r\ntype ExtractStateExtensionsFromEnhancerTuple<\r\n  EnhancerTuple extends any[],\r\n  Acc extends {}\r\n> = EnhancerTuple extends [infer Head, ...infer Tail]\r\n  ? ExtractStateExtensionsFromEnhancerTuple<\r\n      Tail,\r\n      Acc &\r\n        (Head extends StoreEnhancer<any, infer StateExt>\r\n          ? IsAny<StateExt, {}, StateExt>\r\n          : {})\r\n    >\r\n  : Acc\r\n\r\nexport type ExtractStateExtensions<E> = E extends EnhancerArray<\r\n  infer EnhancerTuple\r\n>\r\n  ? ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple, {}>\r\n  : E extends ReadonlyArray<StoreEnhancer>\r\n  ? UnionToIntersection<\r\n      E[number] extends StoreEnhancer<any, infer StateExt>\r\n        ? StateExt extends {}\r\n          ? IsAny<StateExt, {}, StateExt>\r\n          : {}\r\n        : {}\r\n    >\r\n  : never\r\n\r\n/**\r\n * Helper type. Passes T out again, but boxes it in a way that it cannot\r\n * \"widen\" the type by accident if it is a generic that should be inferred\r\n * from elsewhere.\r\n *\r\n * @internal\r\n */\r\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\r\n\r\nexport type Omit<T, K extends keyof any> = Pick<T, Exclude<keyof T, K>>\r\n\r\nexport interface TypeGuard<T> {\r\n  (value: any): value is T\r\n}\r\n\r\nexport interface HasMatchFunction<T> {\r\n  match: TypeGuard<T>\r\n}\r\n\r\nexport const hasMatchFunction = <T>(\r\n  v: Matcher<T>\r\n): v is HasMatchFunction<T> => {\r\n  return v && typeof (v as HasMatchFunction<T>).match === 'function'\r\n}\r\n\r\n/** @public */\r\nexport type Matcher<T> = HasMatchFunction<T> | TypeGuard<T>\r\n\r\n/** @public */\r\nexport type ActionFromMatcher<M extends Matcher<any>> = M extends Matcher<\r\n  infer T\r\n>\r\n  ? T\r\n  : never\r\n\r\nexport type Id<T> = { [K in keyof T]: T[K] } & {}\r\n", "import type { Action } from 'redux'\r\nimport type {\r\n  IsUnknownOrNonInferrable,\r\n  IfMaybeUndefined,\r\n  IfVoid,\r\n  IsAny,\r\n} from './tsHelpers'\r\nimport { hasMatchFunction } from './tsHelpers'\r\nimport isPlainObject from './isPlainObject'\r\n\r\n/**\r\n * An action with a string type and an associated payload. This is the\r\n * type of action returned by `createAction()` action creators.\r\n *\r\n * @template P The type of the action's payload.\r\n * @template T the type used for the action type.\r\n * @template M The type of the action's meta (optional)\r\n * @template E The type of the action's error (optional)\r\n *\r\n * @public\r\n */\r\nexport type PayloadAction<\r\n  P = void,\r\n  T extends string = string,\r\n  M = never,\r\n  E = never\r\n> = {\r\n  payload: P\r\n  type: T\r\n} & ([M] extends [never]\r\n  ? {}\r\n  : {\r\n      meta: M\r\n    }) &\r\n  ([E] extends [never]\r\n    ? {}\r\n    : {\r\n        error: E\r\n      })\r\n\r\n/**\r\n * A \"prepare\" method to be used as the second parameter of `createAction`.\r\n * Takes any number of arguments and returns a Flux Standard Action without\r\n * type (will be added later) that *must* contain a payload (might be undefined).\r\n *\r\n * @public\r\n */\r\nexport type PrepareAction<P> =\r\n  | ((...args: any[]) => { payload: P })\r\n  | ((...args: any[]) => { payload: P; meta: any })\r\n  | ((...args: any[]) => { payload: P; error: any })\r\n  | ((...args: any[]) => { payload: P; meta: any; error: any })\r\n\r\n/**\r\n * Internal version of `ActionCreatorWithPreparedPayload`. Not to be used externally.\r\n *\r\n * @internal\r\n */\r\nexport type _ActionCreatorWithPreparedPayload<\r\n  PA extends PrepareAction<any> | void,\r\n  T extends string = string\r\n> = PA extends PrepareAction<infer P>\r\n  ? ActionCreatorWithPreparedPayload<\r\n      Parameters<PA>,\r\n      P,\r\n      T,\r\n      ReturnType<PA> extends {\r\n        error: infer E\r\n      }\r\n        ? E\r\n        : never,\r\n      ReturnType<PA> extends {\r\n        meta: infer M\r\n      }\r\n        ? M\r\n        : never\r\n    >\r\n  : void\r\n\r\n/**\r\n * Basic type for all action creators.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n */\r\nexport interface BaseActionCreator<P, T extends string, M = never, E = never> {\r\n  type: T\r\n  match: (action: Action<unknown>) => action is PayloadAction<P, T, M, E>\r\n}\r\n\r\n/**\r\n * An action creator that takes multiple arguments that are passed\r\n * to a `PrepareAction` method to create the final Action.\r\n * @typeParam Args arguments for the action creator function\r\n * @typeParam P `payload` type\r\n * @typeParam T `type` name\r\n * @typeParam E optional `error` type\r\n * @typeParam M optional `meta` type\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithPreparedPayload<\r\n  Args extends unknown[],\r\n  P,\r\n  T extends string = string,\r\n  E = never,\r\n  M = never\r\n> extends BaseActionCreator<P, T, M, E> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with `Args` will return\r\n   * an Action with a payload of type `P` and (depending on the `PrepareAction`\r\n   * method used) a `meta`- and `error` property of types `M` and `E` respectively.\r\n   */\r\n  (...args: Args): PayloadAction<P, T, M, E>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` that takes an optional payload of type `P`.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithOptionalPayload<P, T extends string = string>\r\n  extends BaseActionCreator<P, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with an argument will\r\n   * return a {@link PayloadAction} of type `T` with a payload of `P`.\r\n   * Calling it without an argument will return a PayloadAction with a payload of `undefined`.\r\n   */\r\n  (payload?: P): PayloadAction<P, T>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` that takes no payload.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithoutPayload<T extends string = string>\r\n  extends BaseActionCreator<undefined, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} will\r\n   * return a {@link PayloadAction} of type `T` with a payload of `undefined`\r\n   */\r\n  (noArgument: void): PayloadAction<undefined, T>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` that requires a payload of type P.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithPayload<P, T extends string = string>\r\n  extends BaseActionCreator<P, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with an argument will\r\n   * return a {@link PayloadAction} of type `T` with a payload of `P`\r\n   */\r\n  (payload: P): PayloadAction<P, T>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` whose `payload` type could not be inferred. Accepts everything as `payload`.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithNonInferrablePayload<\r\n  T extends string = string\r\n> extends BaseActionCreator<unknown, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with an argument will\r\n   * return a {@link PayloadAction} of type `T` with a payload\r\n   * of exactly the type of the argument.\r\n   */\r\n  <PT extends unknown>(payload: PT): PayloadAction<PT, T>\r\n}\r\n\r\n/**\r\n * An action creator that produces actions with a `payload` attribute.\r\n *\r\n * @typeParam P the `payload` type\r\n * @typeParam T the `type` of the resulting action\r\n * @typeParam PA if the resulting action is preprocessed by a `prepare` method, the signature of said method.\r\n *\r\n * @public\r\n */\r\nexport type PayloadActionCreator<\r\n  P = void,\r\n  T extends string = string,\r\n  PA extends PrepareAction<P> | void = void\r\n> = IfPrepareActionMethodProvided<\r\n  PA,\r\n  _ActionCreatorWithPreparedPayload<PA, T>,\r\n  // else\r\n  IsAny<\r\n    P,\r\n    ActionCreatorWithPayload<any, T>,\r\n    IsUnknownOrNonInferrable<\r\n      P,\r\n      ActionCreatorWithNonInferrablePayload<T>,\r\n      // else\r\n      IfVoid<\r\n        P,\r\n        ActionCreatorWithoutPayload<T>,\r\n        // else\r\n        IfMaybeUndefined<\r\n          P,\r\n          ActionCreatorWithOptionalPayload<P, T>,\r\n          // else\r\n          ActionCreatorWithPayload<P, T>\r\n        >\r\n      >\r\n    >\r\n  >\r\n>\r\n\r\n/**\r\n * A utility function to create an action creator for the given action type\r\n * string. The action creator accepts a single argument, which will be included\r\n * in the action object as a field called payload. The action creator function\r\n * will also have its toString() overridden so that it returns the action type,\r\n * allowing it to be used in reducer logic that is looking for that action type.\r\n *\r\n * @param type The action type to use for created actions.\r\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\r\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\r\n *\r\n * @public\r\n */\r\nexport function createAction<P = void, T extends string = string>(\r\n  type: T\r\n): PayloadActionCreator<P, T>\r\n\r\n/**\r\n * A utility function to create an action creator for the given action type\r\n * string. The action creator accepts a single argument, which will be included\r\n * in the action object as a field called payload. The action creator function\r\n * will also have its toString() overridden so that it returns the action type,\r\n * allowing it to be used in reducer logic that is looking for that action type.\r\n *\r\n * @param type The action type to use for created actions.\r\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\r\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\r\n *\r\n * @public\r\n */\r\nexport function createAction<\r\n  PA extends PrepareAction<any>,\r\n  T extends string = string\r\n>(\r\n  type: T,\r\n  prepareAction: PA\r\n): PayloadActionCreator<ReturnType<PA>['payload'], T, PA>\r\n\r\nexport function createAction(type: string, prepareAction?: Function): any {\r\n  function actionCreator(...args: any[]) {\r\n    if (prepareAction) {\r\n      let prepared = prepareAction(...args)\r\n      if (!prepared) {\r\n        throw new Error('prepareAction did not return an object')\r\n      }\r\n\r\n      return {\r\n        type,\r\n        payload: prepared.payload,\r\n        ...('meta' in prepared && { meta: prepared.meta }),\r\n        ...('error' in prepared && { error: prepared.error }),\r\n      }\r\n    }\r\n    return { type, payload: args[0] }\r\n  }\r\n\r\n  actionCreator.toString = () => `${type}`\r\n\r\n  actionCreator.type = type\r\n\r\n  actionCreator.match = (action: Action<unknown>): action is PayloadAction =>\r\n    action.type === type\r\n\r\n  return actionCreator\r\n}\r\n\r\n/**\r\n * Returns true if value is a plain object with a `type` property.\r\n */\r\nexport function isAction(action: unknown): action is Action<unknown> {\r\n  return isPlainObject(action) && 'type' in action\r\n}\r\n\r\n/**\r\n * Returns true if value is an RTK-like action creator, with a static type property and match method.\r\n */\r\nexport function isActionCreator(\r\n  action: unknown\r\n): action is BaseActionCreator<unknown, string> & Function {\r\n  return (\r\n    typeof action === 'function' &&\r\n    'type' in action &&\r\n    // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\r\n    hasMatchFunction(action as any)\r\n  )\r\n}\r\n\r\n/**\r\n * Returns true if value is an action with a string type and valid Flux Standard Action keys.\r\n */\r\nexport function isFSA(action: unknown): action is {\r\n  type: string\r\n  payload?: unknown\r\n  error?: unknown\r\n  meta?: unknown\r\n} {\r\n  return (\r\n    isAction(action) &&\r\n    typeof action.type === 'string' &&\r\n    Object.keys(action).every(isValidKey)\r\n  )\r\n}\r\n\r\nfunction isValidKey(key: string) {\r\n  return ['type', 'payload', 'error', 'meta'].indexOf(key) > -1\r\n}\r\n\r\n/**\r\n * Returns the action type of the actions created by the passed\r\n * `createAction()`-generated action creator (arbitrary action creators\r\n * are not supported).\r\n *\r\n * @param action The action creator whose action type to get.\r\n * @returns The action type used by the action creator.\r\n *\r\n * @public\r\n */\r\nexport function getType<T extends string>(\r\n  actionCreator: PayloadActionCreator<any, T>\r\n): T {\r\n  return `${actionCreator}` as T\r\n}\r\n\r\n// helper types for more readable typings\r\n\r\ntype IfPrepareActionMethodProvided<\r\n  PA extends PrepareAction<any> | void,\r\n  True,\r\n  False\r\n> = PA extends (...args: any[]) => any ? True : False\r\n", "import type { Middleware } from 'redux'\r\nimport { isActionCreator as isRTKAction } from './createAction'\r\n\r\nexport interface ActionCreatorInvariantMiddlewareOptions {\r\n  /**\r\n   * The function to identify whether a value is an action creator.\r\n   * The default checks for a function with a static type property and match method.\r\n   */\r\n  isActionCreator?: (action: unknown) => action is Function & { type?: unknown }\r\n}\r\n\r\nexport function getMessage(type?: unknown) {\r\n  const splitType = type ? `${type}`.split('/') : []\r\n  const actionName = splitType[splitType.length - 1] || 'actionCreator'\r\n  return `Detected an action creator with type \"${\r\n    type || 'unknown'\r\n  }\" being dispatched. \r\nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`\r\n}\r\n\r\nexport function createActionCreatorInvariantMiddleware(\r\n  options: ActionCreatorInvariantMiddlewareOptions = {}\r\n): Middleware {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return () => (next) => (action) => next(action)\r\n  }\r\n  const { isActionCreator = isRTKAction } = options\r\n  return () => (next) => (action) => {\r\n    if (isActionCreator(action)) {\r\n      console.warn(getMessage(action.type))\r\n    }\r\n    return next(action)\r\n  }\r\n}\r\n", "import createNextState, { isDraftable } from 'immer'\r\nimport type { Middleware, StoreEnhancer } from 'redux'\r\n\r\nexport function getTimeMeasureUtils(maxDelay: number, fnName: string) {\r\n  let elapsed = 0\r\n  return {\r\n    measureTime<T>(fn: () => T): T {\r\n      const started = Date.now()\r\n      try {\r\n        return fn()\r\n      } finally {\r\n        const finished = Date.now()\r\n        elapsed += finished - started\r\n      }\r\n    },\r\n    warnIfExceeded() {\r\n      if (elapsed > maxDelay) {\r\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \r\nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\r\nIt is disabled in production builds, so you don't need to worry about that.`)\r\n      }\r\n    },\r\n  }\r\n}\r\n\r\nexport function delay(ms: number) {\r\n  return new Promise((resolve) => setTimeout(resolve, ms))\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport class MiddlewareArray<\r\n  Middlewares extends Middleware<any, any>[]\r\n> extends Array<Middlewares[number]> {\r\n  constructor(...items: Middlewares)\r\n  constructor(...args: any[]) {\r\n    super(...args)\r\n    Object.setPrototypeOf(this, MiddlewareArray.prototype)\r\n  }\r\n\r\n  static get [Symbol.species]() {\r\n    return MiddlewareArray as any\r\n  }\r\n\r\n  concat<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...Middlewares, ...AdditionalMiddlewares]>\r\n\r\n  concat<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    ...items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...Middlewares, ...AdditionalMiddlewares]>\r\n  concat(...arr: any[]) {\r\n    return super.concat.apply(this, arr)\r\n  }\r\n\r\n  prepend<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...AdditionalMiddlewares, ...Middlewares]>\r\n\r\n  prepend<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    ...items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...AdditionalMiddlewares, ...Middlewares]>\r\n\r\n  prepend(...arr: any[]) {\r\n    if (arr.length === 1 && Array.isArray(arr[0])) {\r\n      return new MiddlewareArray(...arr[0].concat(this))\r\n    }\r\n    return new MiddlewareArray(...arr.concat(this))\r\n  }\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport class EnhancerArray<\r\n  Enhancers extends StoreEnhancer<any, any>[]\r\n> extends Array<Enhancers[number]> {\r\n  constructor(...items: Enhancers)\r\n  constructor(...args: any[]) {\r\n    super(...args)\r\n    Object.setPrototypeOf(this, EnhancerArray.prototype)\r\n  }\r\n\r\n  static get [Symbol.species]() {\r\n    return EnhancerArray as any\r\n  }\r\n\r\n  concat<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    items: AdditionalEnhancers\r\n  ): EnhancerArray<[...Enhancers, ...AdditionalEnhancers]>\r\n\r\n  concat<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    ...items: AdditionalEnhancers\r\n  ): EnhancerArray<[...Enhancers, ...AdditionalEnhancers]>\r\n  concat(...arr: any[]) {\r\n    return super.concat.apply(this, arr)\r\n  }\r\n\r\n  prepend<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    items: AdditionalEnhancers\r\n  ): EnhancerArray<[...AdditionalEnhancers, ...Enhancers]>\r\n\r\n  prepend<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    ...items: AdditionalEnhancers\r\n  ): EnhancerArray<[...AdditionalEnhancers, ...Enhancers]>\r\n\r\n  prepend(...arr: any[]) {\r\n    if (arr.length === 1 && Array.isArray(arr[0])) {\r\n      return new EnhancerArray(...arr[0].concat(this))\r\n    }\r\n    return new EnhancerArray(...arr.concat(this))\r\n  }\r\n}\r\n\r\nexport function freezeDraftable<T>(val: T) {\r\n  return isDraftable(val) ? createNextState(val, () => {}) : val\r\n}\r\n", "import type { Middleware } from 'redux'\r\nimport { getTimeMeasureUtils } from './utils'\r\n\r\ntype EntryProcessor = (key: string, value: any) => any\r\n\r\nconst isProduction: boolean = process.env.NODE_ENV === 'production'\r\nconst prefix: string = 'Invariant failed'\r\n\r\n// Throw an error if the condition fails\r\n// Strip out error messages for production\r\n// > Not providing an inline default argument for message as the result is smaller\r\nfunction invariant(condition: any, message?: string) {\r\n  if (condition) {\r\n    return\r\n  }\r\n  // Condition not passed\r\n\r\n  // In production we strip the message but still throw\r\n  if (isProduction) {\r\n    throw new Error(prefix)\r\n  }\r\n\r\n  // When not in production we allow the message to pass through\r\n  // *This block will be removed in production builds*\r\n  throw new Error(`${prefix}: ${message || ''}`)\r\n}\r\n\r\nfunction stringify(\r\n  obj: any,\r\n  serializer?: EntryProcessor,\r\n  indent?: string | number,\r\n  decycler?: EntryProcessor\r\n): string {\r\n  return JSON.stringify(obj, getSerialize(serializer, decycler), indent)\r\n}\r\n\r\nfunction getSerialize(\r\n  serializer?: EntryProcessor,\r\n  decycler?: EntryProcessor\r\n): EntryProcessor {\r\n  let stack: any[] = [],\r\n    keys: any[] = []\r\n\r\n  if (!decycler)\r\n    decycler = function (_: string, value: any) {\r\n      if (stack[0] === value) return '[Circular ~]'\r\n      return (\r\n        '[Circular ~.' + keys.slice(0, stack.indexOf(value)).join('.') + ']'\r\n      )\r\n    }\r\n\r\n  return function (this: any, key: string, value: any) {\r\n    if (stack.length > 0) {\r\n      var thisPos = stack.indexOf(this)\r\n      ~thisPos ? stack.splice(thisPos + 1) : stack.push(this)\r\n      ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key)\r\n      if (~stack.indexOf(value)) value = decycler!.call(this, key, value)\r\n    } else stack.push(value)\r\n\r\n    return serializer == null ? value : serializer.call(this, key, value)\r\n  }\r\n}\r\n\r\n/**\r\n * The default `isImmutable` function.\r\n *\r\n * @public\r\n */\r\nexport function isImmutableDefault(value: unknown): boolean {\r\n  return typeof value !== 'object' || value == null || Object.isFrozen(value)\r\n}\r\n\r\nexport function trackForMutations(\r\n  isImmutable: IsImmutableFunc,\r\n  ignorePaths: IgnorePaths | undefined,\r\n  obj: any\r\n) {\r\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj)\r\n  return {\r\n    detectMutations() {\r\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj)\r\n    },\r\n  }\r\n}\r\n\r\ninterface TrackedProperty {\r\n  value: any\r\n  children: Record<string, any>\r\n}\r\n\r\nfunction trackProperties(\r\n  isImmutable: IsImmutableFunc,\r\n  ignorePaths: IgnorePaths = [],\r\n  obj: Record<string, any>,\r\n  path: string = '',\r\n  checkedObjects: Set<Record<string, any>> = new Set()\r\n) {\r\n  const tracked: Partial<TrackedProperty> = { value: obj }\r\n\r\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\r\n    checkedObjects.add(obj);\r\n    tracked.children = {}\r\n\r\n    for (const key in obj) {\r\n      const childPath = path ? path + '.' + key : key\r\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\r\n        continue\r\n      }\r\n\r\n      tracked.children[key] = trackProperties(\r\n        isImmutable,\r\n        ignorePaths,\r\n        obj[key],\r\n        childPath\r\n      )\r\n    }\r\n  }\r\n  return tracked as TrackedProperty\r\n}\r\n\r\ntype IgnorePaths = readonly (string | RegExp)[]\r\n\r\nfunction detectMutations(\r\n  isImmutable: IsImmutableFunc,\r\n  ignoredPaths: IgnorePaths = [],\r\n  trackedProperty: TrackedProperty,\r\n  obj: any,\r\n  sameParentRef: boolean = false,\r\n  path: string = ''\r\n): { wasMutated: boolean; path?: string } {\r\n  const prevObj = trackedProperty ? trackedProperty.value : undefined\r\n\r\n  const sameRef = prevObj === obj\r\n\r\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\r\n    return { wasMutated: true, path }\r\n  }\r\n\r\n  if (isImmutable(prevObj) || isImmutable(obj)) {\r\n    return { wasMutated: false }\r\n  }\r\n\r\n  // Gather all keys from prev (tracked) and after objs\r\n  const keysToDetect: Record<string, boolean> = {}\r\n  for (let key in trackedProperty.children) {\r\n    keysToDetect[key] = true\r\n  }\r\n  for (let key in obj) {\r\n    keysToDetect[key] = true\r\n  }\r\n\r\n  const hasIgnoredPaths = ignoredPaths.length > 0\r\n\r\n  for (let key in keysToDetect) {\r\n    const nestedPath = path ? path + '.' + key : key\r\n\r\n    if (hasIgnoredPaths) {\r\n      const hasMatches = ignoredPaths.some((ignored) => {\r\n        if (ignored instanceof RegExp) {\r\n          return ignored.test(nestedPath)\r\n        }\r\n        return nestedPath === ignored\r\n      })\r\n      if (hasMatches) {\r\n        continue\r\n      }\r\n    }\r\n\r\n    const result = detectMutations(\r\n      isImmutable,\r\n      ignoredPaths,\r\n      trackedProperty.children[key],\r\n      obj[key],\r\n      sameRef,\r\n      nestedPath\r\n    )\r\n\r\n    if (result.wasMutated) {\r\n      return result\r\n    }\r\n  }\r\n  return { wasMutated: false }\r\n}\r\n\r\ntype IsImmutableFunc = (value: any) => boolean\r\n\r\n/**\r\n * Options for `createImmutableStateInvariantMiddleware()`.\r\n *\r\n * @public\r\n */\r\nexport interface ImmutableStateInvariantMiddlewareOptions {\r\n  /**\r\n    Callback function to check if a value is considered to be immutable.\r\n    This function is applied recursively to every value contained in the state.\r\n    The default implementation will return true for primitive types \r\n    (like numbers, strings, booleans, null and undefined).\r\n   */\r\n  isImmutable?: IsImmutableFunc\r\n  /** \r\n    An array of dot-separated path strings that match named nodes from \r\n    the root state to ignore when checking for immutability.\r\n    Defaults to undefined\r\n   */\r\n  ignoredPaths?: IgnorePaths\r\n  /** Print a warning if checks take longer than N ms. Default: 32ms */\r\n  warnAfter?: number\r\n  // @deprecated. Use ignoredPaths\r\n  ignore?: string[]\r\n}\r\n\r\n/**\r\n * Creates a middleware that checks whether any state was mutated in between\r\n * dispatches or during a dispatch. If any mutations are detected, an error is\r\n * thrown.\r\n *\r\n * @param options Middleware options.\r\n *\r\n * @public\r\n */\r\nexport function createImmutableStateInvariantMiddleware(\r\n  options: ImmutableStateInvariantMiddlewareOptions = {}\r\n): Middleware {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return () => (next) => (action) => next(action)\r\n  }\r\n\r\n  let {\r\n    isImmutable = isImmutableDefault,\r\n    ignoredPaths,\r\n    warnAfter = 32,\r\n    ignore,\r\n  } = options\r\n\r\n  // Alias ignore->ignoredPaths, but prefer ignoredPaths if present\r\n  ignoredPaths = ignoredPaths || ignore\r\n\r\n  const track = trackForMutations.bind(null, isImmutable, ignoredPaths)\r\n\r\n  return ({ getState }) => {\r\n    let state = getState()\r\n    let tracker = track(state)\r\n\r\n    let result\r\n    return (next) => (action) => {\r\n      const measureUtils = getTimeMeasureUtils(\r\n        warnAfter,\r\n        'ImmutableStateInvariantMiddleware'\r\n      )\r\n\r\n      measureUtils.measureTime(() => {\r\n        state = getState()\r\n\r\n        result = tracker.detectMutations()\r\n        // Track before potentially not meeting the invariant\r\n        tracker = track(state)\r\n\r\n        invariant(\r\n          !result.wasMutated,\r\n          `A state mutation was detected between dispatches, in the path '${\r\n            result.path || ''\r\n          }'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`\r\n        )\r\n      })\r\n\r\n      const dispatchedAction = next(action)\r\n\r\n      measureUtils.measureTime(() => {\r\n        state = getState()\r\n\r\n        result = tracker.detectMutations()\r\n        // Track before potentially not meeting the invariant\r\n        tracker = track(state)\r\n\r\n        result.wasMutated &&\r\n          invariant(\r\n            !result.wasMutated,\r\n            `A state mutation was detected inside a dispatch, in the path: ${\r\n              result.path || ''\r\n            }. Take a look at the reducer(s) handling the action ${stringify(\r\n              action\r\n            )}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`\r\n          )\r\n      })\r\n\r\n      measureUtils.warnIfExceeded()\r\n\r\n      return dispatchedAction\r\n    }\r\n  }\r\n}\r\n", "import isPlainObject from './isPlainObject'\r\nimport type { Middleware } from 'redux'\r\nimport { getTimeMeasureUtils } from './utils'\r\n\r\n/**\r\n * Returns true if the passed value is \"plain\", i.e. a value that is either\r\n * directly JSON-serializable (boolean, number, string, array, plain object)\r\n * or `undefined`.\r\n *\r\n * @param val The value to check.\r\n *\r\n * @public\r\n */\r\nexport function isPlain(val: any) {\r\n  const type = typeof val\r\n  return (\r\n    val == null ||\r\n    type === 'string' ||\r\n    type === 'boolean' ||\r\n    type === 'number' ||\r\n    Array.isArray(val) ||\r\n    isPlainObject(val)\r\n  )\r\n}\r\n\r\ninterface NonSerializableValue {\r\n  keyPath: string\r\n  value: unknown\r\n}\r\n\r\ntype IgnorePaths = readonly (string | RegExp)[]\r\n\r\n/**\r\n * @public\r\n */\r\nexport function findNonSerializableValue(\r\n  value: unknown,\r\n  path: string = '',\r\n  isSerializable: (value: unknown) => boolean = isPlain,\r\n  getEntries?: (value: unknown) => [string, any][],\r\n  ignoredPaths: IgnorePaths = [],\r\n  cache?: WeakSet<object>\r\n): NonSerializableValue | false {\r\n  let foundNestedSerializable: NonSerializableValue | false\r\n\r\n  if (!isSerializable(value)) {\r\n    return {\r\n      keyPath: path || '<root>',\r\n      value: value,\r\n    }\r\n  }\r\n\r\n  if (typeof value !== 'object' || value === null) {\r\n    return false\r\n  }\r\n\r\n  if (cache?.has(value)) return false\r\n\r\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value)\r\n\r\n  const hasIgnoredPaths = ignoredPaths.length > 0\r\n\r\n  for (const [key, nestedValue] of entries) {\r\n    const nestedPath = path ? path + '.' + key : key\r\n\r\n    if (hasIgnoredPaths) {\r\n      const hasMatches = ignoredPaths.some((ignored) => {\r\n        if (ignored instanceof RegExp) {\r\n          return ignored.test(nestedPath)\r\n        }\r\n        return nestedPath === ignored\r\n      })\r\n      if (hasMatches) {\r\n        continue\r\n      }\r\n    }\r\n\r\n    if (!isSerializable(nestedValue)) {\r\n      return {\r\n        keyPath: nestedPath,\r\n        value: nestedValue,\r\n      }\r\n    }\r\n\r\n    if (typeof nestedValue === 'object') {\r\n      foundNestedSerializable = findNonSerializableValue(\r\n        nestedValue,\r\n        nestedPath,\r\n        isSerializable,\r\n        getEntries,\r\n        ignoredPaths,\r\n        cache\r\n      )\r\n\r\n      if (foundNestedSerializable) {\r\n        return foundNestedSerializable\r\n      }\r\n    }\r\n  }\r\n\r\n  if (cache && isNestedFrozen(value)) cache.add(value)\r\n\r\n  return false\r\n}\r\n\r\nexport function isNestedFrozen(value: object) {\r\n  if (!Object.isFrozen(value)) return false\r\n\r\n  for (const nestedValue of Object.values(value)) {\r\n    if (typeof nestedValue !== 'object' || nestedValue === null) continue\r\n\r\n    if (!isNestedFrozen(nestedValue)) return false\r\n  }\r\n\r\n  return true\r\n}\r\n\r\n/**\r\n * Options for `createSerializableStateInvariantMiddleware()`.\r\n *\r\n * @public\r\n */\r\nexport interface SerializableStateInvariantMiddlewareOptions {\r\n  /**\r\n   * The function to check if a value is considered serializable. This\r\n   * function is applied recursively to every value contained in the\r\n   * state. Defaults to `isPlain()`.\r\n   */\r\n  isSerializable?: (value: any) => boolean\r\n  /**\r\n   * The function that will be used to retrieve entries from each\r\n   * value.  If unspecified, `Object.entries` will be used. Defaults\r\n   * to `undefined`.\r\n   */\r\n  getEntries?: (value: any) => [string, any][]\r\n\r\n  /**\r\n   * An array of action types to ignore when checking for serializability.\r\n   * Defaults to []\r\n   */\r\n  ignoredActions?: string[]\r\n\r\n  /**\r\n   * An array of dot-separated path strings or regular expressions to ignore\r\n   * when checking for serializability, Defaults to\r\n   * ['meta.arg', 'meta.baseQueryMeta']\r\n   */\r\n  ignoredActionPaths?: (string | RegExp)[]\r\n\r\n  /**\r\n   * An array of dot-separated path strings or regular expressions to ignore\r\n   * when checking for serializability, Defaults to []\r\n   */\r\n  ignoredPaths?: (string | RegExp)[]\r\n  /**\r\n   * Execution time warning threshold. If the middleware takes longer\r\n   * than `warnAfter` ms, a warning will be displayed in the console.\r\n   * Defaults to 32ms.\r\n   */\r\n  warnAfter?: number\r\n\r\n  /**\r\n   * Opt out of checking state. When set to `true`, other state-related params will be ignored.\r\n   */\r\n  ignoreState?: boolean\r\n\r\n  /**\r\n   * Opt out of checking actions. When set to `true`, other action-related params will be ignored.\r\n   */\r\n  ignoreActions?: boolean\r\n\r\n  /**\r\n   * Opt out of caching the results. The cache uses a WeakSet and speeds up repeated checking processes.\r\n   * The cache is automatically disabled if no browser support for WeakSet is present.\r\n   */\r\n  disableCache?: boolean\r\n}\r\n\r\n/**\r\n * Creates a middleware that, after every state change, checks if the new\r\n * state is serializable. If a non-serializable value is found within the\r\n * state, an error is printed to the console.\r\n *\r\n * @param options Middleware options.\r\n *\r\n * @public\r\n */\r\nexport function createSerializableStateInvariantMiddleware(\r\n  options: SerializableStateInvariantMiddlewareOptions = {}\r\n): Middleware {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return () => (next) => (action) => next(action)\r\n  }\r\n  const {\r\n    isSerializable = isPlain,\r\n    getEntries,\r\n    ignoredActions = [],\r\n    ignoredActionPaths = ['meta.arg', 'meta.baseQueryMeta'],\r\n    ignoredPaths = [],\r\n    warnAfter = 32,\r\n    ignoreState = false,\r\n    ignoreActions = false,\r\n    disableCache = false,\r\n  } = options\r\n\r\n  const cache: WeakSet<object> | undefined =\r\n    !disableCache && WeakSet ? new WeakSet() : undefined\r\n\r\n  return (storeAPI) => (next) => (action) => {\r\n    const result = next(action)\r\n\r\n    const measureUtils = getTimeMeasureUtils(\r\n      warnAfter,\r\n      'SerializableStateInvariantMiddleware'\r\n    )\r\n\r\n    if (\r\n      !ignoreActions &&\r\n      !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)\r\n    ) {\r\n      measureUtils.measureTime(() => {\r\n        const foundActionNonSerializableValue = findNonSerializableValue(\r\n          action,\r\n          '',\r\n          isSerializable,\r\n          getEntries,\r\n          ignoredActionPaths,\r\n          cache\r\n        )\r\n\r\n        if (foundActionNonSerializableValue) {\r\n          const { keyPath, value } = foundActionNonSerializableValue\r\n\r\n          console.error(\r\n            `A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`,\r\n            value,\r\n            '\\nTake a look at the logic that dispatched this action: ',\r\n            action,\r\n            '\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)',\r\n            '\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)'\r\n          )\r\n        }\r\n      })\r\n    }\r\n\r\n    if (!ignoreState) {\r\n      measureUtils.measureTime(() => {\r\n        const state = storeAPI.getState()\r\n\r\n        const foundStateNonSerializableValue = findNonSerializableValue(\r\n          state,\r\n          '',\r\n          isSerializable,\r\n          getEntries,\r\n          ignoredPaths,\r\n          cache\r\n        )\r\n\r\n        if (foundStateNonSerializableValue) {\r\n          const { keyPath, value } = foundStateNonSerializableValue\r\n\r\n          console.error(\r\n            `A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`,\r\n            value,\r\n            `\r\nTake a look at the reducer(s) handling this action type: ${action.type}.\r\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`\r\n          )\r\n        }\r\n      })\r\n\r\n      measureUtils.warnIfExceeded()\r\n    }\r\n\r\n    return result\r\n  }\r\n}\r\n", "import type { Draft } from 'immer'\r\nimport createNextState, { isDraft, isDraftable } from 'immer'\r\nimport type { AnyAction, Action, Reducer } from 'redux'\r\nimport type { ActionReducerMapBuilder } from './mapBuilders'\r\nimport { executeReducerBuilderCallback } from './mapBuilders'\r\nimport type { NoInfer } from './tsHelpers'\r\nimport { freezeDraftable } from './utils'\r\n\r\n/**\r\n * Defines a mapping from action types to corresponding action object shapes.\r\n *\r\n * @deprecated This should not be used manually - it is only used for internal\r\n *             inference purposes and should not have any further value.\r\n *             It might be removed in the future.\r\n * @public\r\n */\r\nexport type Actions<T extends keyof any = string> = Record<T, Action>\r\n\r\n/**\r\n * @deprecated use `TypeGuard` instead\r\n */\r\nexport interface ActionMatcher<A extends AnyAction> {\r\n  (action: AnyAction): action is A\r\n}\r\n\r\nexport type ActionMatcherDescription<S, A extends AnyAction> = {\r\n  matcher: ActionMatcher<A>\r\n  reducer: CaseReducer<S, NoInfer<A>>\r\n}\r\n\r\nexport type ReadonlyActionMatcherDescriptionCollection<S> = ReadonlyArray<\r\n  ActionMatcherDescription<S, any>\r\n>\r\n\r\nexport type ActionMatcherDescriptionCollection<S> = Array<\r\n  ActionMatcherDescription<S, any>\r\n>\r\n\r\n/**\r\n * A *case reducer* is a reducer function for a specific action type. Case\r\n * reducers can be composed to full reducers using `createReducer()`.\r\n *\r\n * Unlike a normal Redux reducer, a case reducer is never called with an\r\n * `undefined` state to determine the initial state. Instead, the initial\r\n * state is explicitly specified as an argument to `createReducer()`.\r\n *\r\n * In addition, a case reducer can choose to mutate the passed-in `state`\r\n * value directly instead of returning a new state. This does not actually\r\n * cause the store state to be mutated directly; instead, thanks to\r\n * [immer](https://github.com/mweststrate/immer), the mutations are\r\n * translated to copy operations that result in a new state.\r\n *\r\n * @public\r\n */\r\nexport type CaseReducer<S = any, A extends Action = AnyAction> = (\r\n  state: Draft<S>,\r\n  action: A\r\n) => NoInfer<S> | void | Draft<NoInfer<S>>\r\n\r\n/**\r\n * A mapping from action types to case reducers for `createReducer()`.\r\n *\r\n * @deprecated This should not be used manually - it is only used\r\n *             for internal inference purposes and using it manually\r\n *             would lead to type erasure.\r\n *             It might be removed in the future.\r\n * @public\r\n */\r\nexport type CaseReducers<S, AS extends Actions> = {\r\n  [T in keyof AS]: AS[T] extends Action ? CaseReducer<S, AS[T]> : void\r\n}\r\n\r\nexport type NotFunction<T> = T extends Function ? never : T\r\n\r\nfunction isStateFunction<S>(x: unknown): x is () => S {\r\n  return typeof x === 'function'\r\n}\r\n\r\nexport type ReducerWithInitialState<S extends NotFunction<any>> = Reducer<S> & {\r\n  getInitialState: () => S\r\n}\r\n\r\nlet hasWarnedAboutObjectNotation = false\r\n\r\n/**\r\n * A utility function that allows defining a reducer as a mapping from action\r\n * type to *case reducer* functions that handle these action types. The\r\n * reducer's initial state is passed as the first argument.\r\n *\r\n * @remarks\r\n * The body of every case reducer is implicitly wrapped with a call to\r\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\r\n * This means that rather than returning a new state object, you can also\r\n * mutate the passed-in state object directly; these mutations will then be\r\n * automatically and efficiently translated into copies, giving you both\r\n * convenience and immutability.\r\n *\r\n * @overloadSummary\r\n * This overload accepts a callback function that receives a `builder` object as its argument.\r\n * That builder provides `addCase`, `addMatcher` and `addDefaultCase` functions that may be\r\n * called to define what actions this reducer will handle.\r\n *\r\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\r\n * @param builderCallback - `(builder: Builder) => void` A callback that receives a *builder* object to define\r\n *   case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\r\n * @example\r\n```ts\r\nimport {\r\n  createAction,\r\n  createReducer,\r\n  AnyAction,\r\n  PayloadAction,\r\n} from \"@reduxjs/toolkit\";\r\n\r\nconst increment = createAction<number>(\"increment\");\r\nconst decrement = createAction<number>(\"decrement\");\r\n\r\nfunction isActionWithNumberPayload(\r\n  action: AnyAction\r\n): action is PayloadAction<number> {\r\n  return typeof action.payload === \"number\";\r\n}\r\n\r\nconst reducer = createReducer(\r\n  {\r\n    counter: 0,\r\n    sumOfNumberPayloads: 0,\r\n    unhandledActions: 0,\r\n  },\r\n  (builder) => {\r\n    builder\r\n      .addCase(increment, (state, action) => {\r\n        // action is inferred correctly here\r\n        state.counter += action.payload;\r\n      })\r\n      // You can chain calls, or have separate `builder.addCase()` lines each time\r\n      .addCase(decrement, (state, action) => {\r\n        state.counter -= action.payload;\r\n      })\r\n      // You can apply a \"matcher function\" to incoming actions\r\n      .addMatcher(isActionWithNumberPayload, (state, action) => {})\r\n      // and provide a default case if no other handlers matched\r\n      .addDefaultCase((state, action) => {});\r\n  }\r\n);\r\n```\r\n * @public\r\n */\r\nexport function createReducer<S extends NotFunction<any>>(\r\n  initialState: S | (() => S),\r\n  builderCallback: (builder: ActionReducerMapBuilder<S>) => void\r\n): ReducerWithInitialState<S>\r\n\r\n/**\r\n * A utility function that allows defining a reducer as a mapping from action\r\n * type to *case reducer* functions that handle these action types. The\r\n * reducer's initial state is passed as the first argument.\r\n *\r\n * The body of every case reducer is implicitly wrapped with a call to\r\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\r\n * This means that rather than returning a new state object, you can also\r\n * mutate the passed-in state object directly; these mutations will then be\r\n * automatically and efficiently translated into copies, giving you both\r\n * convenience and immutability.\r\n * \r\n * @overloadSummary\r\n * This overload accepts an object where the keys are string action types, and the values\r\n * are case reducer functions to handle those action types.\r\n *\r\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\r\n * @param actionsMap - An object mapping from action types to _case reducers_, each of which handles one specific action type.\r\n * @param actionMatchers - An array of matcher definitions in the form `{matcher, reducer}`.\r\n *   All matching reducers will be executed in order, independently if a case reducer matched or not.\r\n * @param defaultCaseReducer - A \"default case\" reducer that is executed if no case reducer and no matcher\r\n *   reducer was executed for this action.\r\n *\r\n * @example\r\n```js\r\nconst counterReducer = createReducer(0, {\r\n  increment: (state, action) => state + action.payload,\r\n  decrement: (state, action) => state - action.payload\r\n})\r\n\r\n// Alternately, use a \"lazy initializer\" to provide the initial state\r\n// (works with either form of createReducer)\r\nconst initialState = () => 0\r\nconst counterReducer = createReducer(initialState, {\r\n  increment: (state, action) => state + action.payload,\r\n  decrement: (state, action) => state - action.payload\r\n})\r\n```\r\n \r\n * Action creators that were generated using [`createAction`](./createAction) may be used directly as the keys here, using computed property syntax:\r\n\r\n```js\r\nconst increment = createAction('increment')\r\nconst decrement = createAction('decrement')\r\n\r\nconst counterReducer = createReducer(0, {\r\n  [increment]: (state, action) => state + action.payload,\r\n  [decrement.type]: (state, action) => state - action.payload\r\n})\r\n```\r\n * @public\r\n */\r\nexport function createReducer<\r\n  S extends NotFunction<any>,\r\n  CR extends CaseReducers<S, any> = CaseReducers<S, any>\r\n>(\r\n  initialState: S | (() => S),\r\n  actionsMap: CR,\r\n  actionMatchers?: ActionMatcherDescriptionCollection<S>,\r\n  defaultCaseReducer?: CaseReducer<S>\r\n): ReducerWithInitialState<S>\r\n\r\nexport function createReducer<S extends NotFunction<any>>(\r\n  initialState: S | (() => S),\r\n  mapOrBuilderCallback:\r\n    | CaseReducers<S, any>\r\n    | ((builder: ActionReducerMapBuilder<S>) => void),\r\n  actionMatchers: ReadonlyActionMatcherDescriptionCollection<S> = [],\r\n  defaultCaseReducer?: CaseReducer<S>\r\n): ReducerWithInitialState<S> {\r\n  if (process.env.NODE_ENV !== 'production') {\r\n    if (typeof mapOrBuilderCallback === 'object') {\r\n      if (!hasWarnedAboutObjectNotation) {\r\n        hasWarnedAboutObjectNotation = true\r\n        console.warn(\r\n          \"The object notation for `createReducer` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\"\r\n        )\r\n      }\r\n    }\r\n  }\r\n\r\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] =\r\n    typeof mapOrBuilderCallback === 'function'\r\n      ? executeReducerBuilderCallback(mapOrBuilderCallback)\r\n      : [mapOrBuilderCallback, actionMatchers, defaultCaseReducer]\r\n\r\n  // Ensure the initial state gets frozen either way (if draftable)\r\n  let getInitialState: () => S\r\n  if (isStateFunction(initialState)) {\r\n    getInitialState = () => freezeDraftable(initialState())\r\n  } else {\r\n    const frozenInitialState = freezeDraftable(initialState)\r\n    getInitialState = () => frozenInitialState\r\n  }\r\n\r\n  function reducer(state = getInitialState(), action: any): S {\r\n    let caseReducers = [\r\n      actionsMap[action.type],\r\n      ...finalActionMatchers\r\n        .filter(({ matcher }) => matcher(action))\r\n        .map(({ reducer }) => reducer),\r\n    ]\r\n    if (caseReducers.filter((cr) => !!cr).length === 0) {\r\n      caseReducers = [finalDefaultCaseReducer]\r\n    }\r\n\r\n    return caseReducers.reduce((previousState, caseReducer): S => {\r\n      if (caseReducer) {\r\n        if (isDraft(previousState)) {\r\n          // If it's already a draft, we must already be inside a `createNextState` call,\r\n          // likely because this is being wrapped in `createReducer`, `createSlice`, or nested\r\n          // inside an existing draft. It's safe to just pass the draft to the mutator.\r\n          const draft = previousState as Draft<S> // We can assume this is already a draft\r\n          const result = caseReducer(draft, action)\r\n\r\n          if (result === undefined) {\r\n            return previousState\r\n          }\r\n\r\n          return result as S\r\n        } else if (!isDraftable(previousState)) {\r\n          // If state is not draftable (ex: a primitive, such as 0), we want to directly\r\n          // return the caseReducer func and not wrap it with produce.\r\n          const result = caseReducer(previousState as any, action)\r\n\r\n          if (result === undefined) {\r\n            if (previousState === null) {\r\n              return previousState\r\n            }\r\n            throw Error(\r\n              'A case reducer on a non-draftable value must not return undefined'\r\n            )\r\n          }\r\n\r\n          return result as S\r\n        } else {\r\n          // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\r\n          // than an Immutable<S>, and TypeScript cannot find out how to reconcile\r\n          // these two types.\r\n          return createNextState(previousState, (draft: Draft<S>) => {\r\n            return caseReducer(draft, action)\r\n          })\r\n        }\r\n      }\r\n\r\n      return previousState\r\n    }, state)\r\n  }\r\n\r\n  reducer.getInitialState = getInitialState\r\n\r\n  return reducer as ReducerWithInitialState<S>\r\n}\r\n", "import type { Action, AnyAction } from 'redux'\r\nimport type {\r\n  CaseReducer,\r\n  CaseReducers,\r\n  ActionMatcherDescriptionCollection,\r\n} from './createReducer'\r\nimport type { TypeGuard } from './tsHelpers'\r\n\r\nexport interface TypedActionCreator<Type extends string> {\r\n  (...args: any[]): Action<Type>\r\n  type: Type\r\n}\r\n\r\n/**\r\n * A builder for an action <-> reducer map.\r\n *\r\n * @public\r\n */\r\nexport interface ActionReducerMapBuilder<State> {\r\n  /**\r\n   * Adds a case reducer to handle a single exact action type.\r\n   * @remarks\r\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\r\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\r\n   * @param reducer - The actual case reducer function.\r\n   */\r\n  addCase<ActionCreator extends TypedActionCreator<string>>(\r\n    actionCreator: ActionCreator,\r\n    reducer: CaseReducer<State, ReturnType<ActionCreator>>\r\n  ): ActionReducerMapBuilder<State>\r\n  /**\r\n   * Adds a case reducer to handle a single exact action type.\r\n   * @remarks\r\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\r\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\r\n   * @param reducer - The actual case reducer function.\r\n   */\r\n  addCase<Type extends string, A extends Action<Type>>(\r\n    type: Type,\r\n    reducer: CaseReducer<State, A>\r\n  ): ActionReducerMapBuilder<State>\r\n\r\n  /**\r\n   * Allows you to match your incoming actions against your own filter function instead of only the `action.type` property.\r\n   * @remarks\r\n   * If multiple matcher reducers match, all of them will be executed in the order\r\n   * they were defined in - even if a case reducer already matched.\r\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\r\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\r\n   *   function\r\n   * @param reducer - The actual case reducer function.\r\n   *\r\n   * @example\r\n```ts\r\nimport {\r\n  createAction,\r\n  createReducer,\r\n  AsyncThunk,\r\n  AnyAction,\r\n} from \"@reduxjs/toolkit\";\r\n\r\ntype GenericAsyncThunk = AsyncThunk<unknown, unknown, any>;\r\n\r\ntype PendingAction = ReturnType<GenericAsyncThunk[\"pending\"]>;\r\ntype RejectedAction = ReturnType<GenericAsyncThunk[\"rejected\"]>;\r\ntype FulfilledAction = ReturnType<GenericAsyncThunk[\"fulfilled\"]>;\r\n\r\nconst initialState: Record<string, string> = {};\r\nconst resetAction = createAction(\"reset-tracked-loading-state\");\r\n\r\nfunction isPendingAction(action: AnyAction): action is PendingAction {\r\n  return action.type.endsWith(\"/pending\");\r\n}\r\n\r\nconst reducer = createReducer(initialState, (builder) => {\r\n  builder\r\n    .addCase(resetAction, () => initialState)\r\n    // matcher can be defined outside as a type predicate function\r\n    .addMatcher(isPendingAction, (state, action) => {\r\n      state[action.meta.requestId] = \"pending\";\r\n    })\r\n    .addMatcher(\r\n      // matcher can be defined inline as a type predicate function\r\n      (action): action is RejectedAction => action.type.endsWith(\"/rejected\"),\r\n      (state, action) => {\r\n        state[action.meta.requestId] = \"rejected\";\r\n      }\r\n    )\r\n    // matcher can just return boolean and the matcher can receive a generic argument\r\n    .addMatcher<FulfilledAction>(\r\n      (action) => action.type.endsWith(\"/fulfilled\"),\r\n      (state, action) => {\r\n        state[action.meta.requestId] = \"fulfilled\";\r\n      }\r\n    );\r\n});\r\n```\r\n   */\r\n  addMatcher<A>(\r\n    matcher: TypeGuard<A> | ((action: any) => boolean),\r\n    reducer: CaseReducer<State, A extends AnyAction ? A : A & AnyAction>\r\n  ): Omit<ActionReducerMapBuilder<State>, 'addCase'>\r\n\r\n  /**\r\n   * Adds a \"default case\" reducer that is executed if no case reducer and no matcher\r\n   * reducer was executed for this action.\r\n   * @param reducer - The fallback \"default case\" reducer function.\r\n   *\r\n   * @example\r\n```ts\r\nimport { createReducer } from '@reduxjs/toolkit'\r\nconst initialState = { otherActions: 0 }\r\nconst reducer = createReducer(initialState, builder => {\r\n  builder\r\n    // .addCase(...)\r\n    // .addMatcher(...)\r\n    .addDefaultCase((state, action) => {\r\n      state.otherActions++\r\n    })\r\n})\r\n```\r\n   */\r\n  addDefaultCase(reducer: CaseReducer<State, AnyAction>): {}\r\n}\r\n\r\nexport function executeReducerBuilderCallback<S>(\r\n  builderCallback: (builder: ActionReducerMapBuilder<S>) => void\r\n): [\r\n  CaseReducers<S, any>,\r\n  ActionMatcherDescriptionCollection<S>,\r\n  CaseReducer<S, AnyAction> | undefined\r\n] {\r\n  const actionsMap: CaseReducers<S, any> = {}\r\n  const actionMatchers: ActionMatcherDescriptionCollection<S> = []\r\n  let defaultCaseReducer: CaseReducer<S, AnyAction> | undefined\r\n  const builder = {\r\n    addCase(\r\n      typeOrActionCreator: string | TypedActionCreator<any>,\r\n      reducer: CaseReducer<S>\r\n    ) {\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        /*\r\n         to keep the definition by the user in line with actual behavior,\r\n         we enforce `addCase` to always be called before calling `addMatcher`\r\n         as matching cases take precedence over matchers\r\n         */\r\n        if (actionMatchers.length > 0) {\r\n          throw new Error(\r\n            '`builder.addCase` should only be called before calling `builder.addMatcher`'\r\n          )\r\n        }\r\n        if (defaultCaseReducer) {\r\n          throw new Error(\r\n            '`builder.addCase` should only be called before calling `builder.addDefaultCase`'\r\n          )\r\n        }\r\n      }\r\n      const type =\r\n        typeof typeOrActionCreator === 'string'\r\n          ? typeOrActionCreator\r\n          : typeOrActionCreator.type\r\n      if (!type) {\r\n        throw new Error(\r\n          '`builder.addCase` cannot be called with an empty action type'\r\n        )\r\n      }\r\n      if (type in actionsMap) {\r\n        throw new Error(\r\n          '`builder.addCase` cannot be called with two reducers for the same action type'\r\n        )\r\n      }\r\n      actionsMap[type] = reducer\r\n      return builder\r\n    },\r\n    addMatcher<A>(\r\n      matcher: TypeGuard<A>,\r\n      reducer: CaseReducer<S, A extends AnyAction ? A : A & AnyAction>\r\n    ) {\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        if (defaultCaseReducer) {\r\n          throw new Error(\r\n            '`builder.addMatcher` should only be called before calling `builder.addDefaultCase`'\r\n          )\r\n        }\r\n      }\r\n      actionMatchers.push({ matcher, reducer })\r\n      return builder\r\n    },\r\n    addDefaultCase(reducer: CaseReducer<S, AnyAction>) {\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        if (defaultCaseReducer) {\r\n          throw new Error('`builder.addDefaultCase` can only be called once')\r\n        }\r\n      }\r\n      defaultCaseReducer = reducer\r\n      return builder\r\n    },\r\n  }\r\n  builderCallback(builder)\r\n  return [actionsMap, actionMatchers, defaultCaseReducer]\r\n}\r\n", "import type { AnyAction, Reducer } from 'redux'\r\nimport { createNextState } from '.'\r\nimport type {\r\n  ActionCreatorWithoutPayload,\r\n  PayloadAction,\r\n  PayloadActionCreator,\r\n  PrepareAction,\r\n  _ActionCreatorWithPreparedPayload,\r\n} from './createAction'\r\nimport { createAction } from './createAction'\r\nimport type {\r\n  CaseReducer,\r\n  CaseReducers,\r\n  ReducerWithInitialState,\r\n} from './createReducer'\r\nimport { createReducer, NotFunction } from './createReducer'\r\nimport type { ActionReducerMapBuilder } from './mapBuilders'\r\nimport { executeReducerBuilderCallback } from './mapBuilders'\r\nimport type { NoInfer } from './tsHelpers'\r\nimport { freezeDraftable } from './utils'\r\n\r\nlet hasWarnedAboutObjectNotation = false\r\n\r\n/**\r\n * An action creator attached to a slice.\r\n *\r\n * @deprecated please use PayloadActionCreator directly\r\n *\r\n * @public\r\n */\r\nexport type SliceActionCreator<P> = PayloadActionCreator<P>\r\n\r\n/**\r\n * The return value of `createSlice`\r\n *\r\n * @public\r\n */\r\nexport interface Slice<\r\n  State = any,\r\n  CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>,\r\n  Name extends string = string\r\n> {\r\n  /**\r\n   * The slice name.\r\n   */\r\n  name: Name\r\n\r\n  /**\r\n   * The slice's reducer.\r\n   */\r\n  reducer: Reducer<State>\r\n\r\n  /**\r\n   * Action creators for the types of actions that are handled by the slice\r\n   * reducer.\r\n   */\r\n  actions: CaseReducerActions<CaseReducers, Name>\r\n\r\n  /**\r\n   * The individual case reducer functions that were passed in the `reducers` parameter.\r\n   * This enables reuse and testing if they were defined inline when calling `createSlice`.\r\n   */\r\n  caseReducers: SliceDefinedCaseReducers<CaseReducers>\r\n\r\n  /**\r\n   * Provides access to the initial state value given to the slice.\r\n   * If a lazy state initializer was provided, it will be called and a fresh value returned.\r\n   */\r\n  getInitialState: () => State\r\n}\r\n\r\n/**\r\n * Options for `createSlice()`.\r\n *\r\n * @public\r\n */\r\nexport interface CreateSliceOptions<\r\n  State = any,\r\n  CR extends SliceCaseReducers<State> = SliceCaseReducers<State>,\r\n  Name extends string = string\r\n> {\r\n  /**\r\n   * The slice's name. Used to namespace the generated action types.\r\n   */\r\n  name: Name\r\n\r\n  /**\r\n   * The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\r\n   */\r\n  initialState: State | (() => State)\r\n\r\n  /**\r\n   * A mapping from action types to action-type-specific *case reducer*\r\n   * functions. For every action type, a matching action creator will be\r\n   * generated using `createAction()`.\r\n   */\r\n  reducers: ValidateSliceCaseReducers<State, CR>\r\n\r\n  /**\r\n   * A callback that receives a *builder* object to define\r\n   * case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\r\n   * \r\n   * Alternatively, a mapping from action types to action-type-specific *case reducer*\r\n   * functions. These reducers should have existing action types used\r\n   * as the keys, and action creators will _not_ be generated.\r\n   * \r\n   * @example\r\n```ts\r\nimport { createAction, createSlice, Action, AnyAction } from '@reduxjs/toolkit'\r\nconst incrementBy = createAction<number>('incrementBy')\r\nconst decrement = createAction('decrement')\r\n\r\ninterface RejectedAction extends Action {\r\n  error: Error\r\n}\r\n\r\nfunction isRejectedAction(action: AnyAction): action is RejectedAction {\r\n  return action.type.endsWith('rejected')\r\n}\r\n\r\ncreateSlice({\r\n  name: 'counter',\r\n  initialState: 0,\r\n  reducers: {},\r\n  extraReducers: builder => {\r\n    builder\r\n      .addCase(incrementBy, (state, action) => {\r\n        // action is inferred correctly here if using TS\r\n      })\r\n      // You can chain calls, or have separate `builder.addCase()` lines each time\r\n      .addCase(decrement, (state, action) => {})\r\n      // You can match a range of action types\r\n      .addMatcher(\r\n        isRejectedAction,\r\n        // `action` will be inferred as a RejectedAction due to isRejectedAction being defined as a type guard\r\n        (state, action) => {}\r\n      )\r\n      // and provide a default case if no other handlers matched\r\n      .addDefaultCase((state, action) => {})\r\n    }\r\n})\r\n```\r\n   */\r\n  extraReducers?:\r\n    | CaseReducers<NoInfer<State>, any>\r\n    | ((builder: ActionReducerMapBuilder<NoInfer<State>>) => void)\r\n}\r\n\r\n/**\r\n * A CaseReducer with a `prepare` method.\r\n *\r\n * @public\r\n */\r\nexport type CaseReducerWithPrepare<State, Action extends PayloadAction> = {\r\n  reducer: CaseReducer<State, Action>\r\n  prepare: PrepareAction<Action['payload']>\r\n}\r\n\r\n/**\r\n * The type describing a slice's `reducers` option.\r\n *\r\n * @public\r\n */\r\nexport type SliceCaseReducers<State> = {\r\n  [K: string]:\r\n    | CaseReducer<State, PayloadAction<any>>\r\n    | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>\r\n}\r\n\r\ntype SliceActionType<\r\n  SliceName extends string,\r\n  ActionName extends keyof any\r\n> = ActionName extends string | number ? `${SliceName}/${ActionName}` : string\r\n\r\n/**\r\n * Derives the slice's `actions` property from the `reducers` options\r\n *\r\n * @public\r\n */\r\nexport type CaseReducerActions<\r\n  CaseReducers extends SliceCaseReducers<any>,\r\n  SliceName extends string\r\n> = {\r\n  [Type in keyof CaseReducers]: CaseReducers[Type] extends { prepare: any }\r\n    ? ActionCreatorForCaseReducerWithPrepare<\r\n        CaseReducers[Type],\r\n        SliceActionType<SliceName, Type>\r\n      >\r\n    : ActionCreatorForCaseReducer<\r\n        CaseReducers[Type],\r\n        SliceActionType<SliceName, Type>\r\n      >\r\n}\r\n\r\n/**\r\n * Get a `PayloadActionCreator` type for a passed `CaseReducerWithPrepare`\r\n *\r\n * @internal\r\n */\r\ntype ActionCreatorForCaseReducerWithPrepare<\r\n  CR extends { prepare: any },\r\n  Type extends string\r\n> = _ActionCreatorWithPreparedPayload<CR['prepare'], Type>\r\n\r\n/**\r\n * Get a `PayloadActionCreator` type for a passed `CaseReducer`\r\n *\r\n * @internal\r\n */\r\ntype ActionCreatorForCaseReducer<CR, Type extends string> = CR extends (\r\n  state: any,\r\n  action: infer Action\r\n) => any\r\n  ? Action extends { payload: infer P }\r\n    ? PayloadActionCreator<P, Type>\r\n    : ActionCreatorWithoutPayload<Type>\r\n  : ActionCreatorWithoutPayload<Type>\r\n\r\n/**\r\n * Extracts the CaseReducers out of a `reducers` object, even if they are\r\n * tested into a `CaseReducerWithPrepare`.\r\n *\r\n * @internal\r\n */\r\ntype SliceDefinedCaseReducers<CaseReducers extends SliceCaseReducers<any>> = {\r\n  [Type in keyof CaseReducers]: CaseReducers[Type] extends {\r\n    reducer: infer Reducer\r\n  }\r\n    ? Reducer\r\n    : CaseReducers[Type]\r\n}\r\n\r\n/**\r\n * Used on a SliceCaseReducers object.\r\n * Ensures that if a CaseReducer is a `CaseReducerWithPrepare`, that\r\n * the `reducer` and the `prepare` function use the same type of `payload`.\r\n *\r\n * Might do additional such checks in the future.\r\n *\r\n * This type is only ever useful if you want to write your own wrapper around\r\n * `createSlice`. Please don't use it otherwise!\r\n *\r\n * @public\r\n */\r\nexport type ValidateSliceCaseReducers<\r\n  S,\r\n  ACR extends SliceCaseReducers<S>\r\n> = ACR &\r\n  {\r\n    [T in keyof ACR]: ACR[T] extends {\r\n      reducer(s: S, action?: infer A): any\r\n    }\r\n      ? {\r\n          prepare(...a: never[]): Omit<A, 'type'>\r\n        }\r\n      : {}\r\n  }\r\n\r\nfunction getType(slice: string, actionKey: string): string {\r\n  return `${slice}/${actionKey}`\r\n}\r\n\r\n/**\r\n * A function that accepts an initial state, an object full of reducer\r\n * functions, and a \"slice name\", and automatically generates\r\n * action creators and action types that correspond to the\r\n * reducers and state.\r\n *\r\n * The `reducer` argument is passed to `createReducer()`.\r\n *\r\n * @public\r\n */\r\nexport function createSlice<\r\n  State,\r\n  CaseReducers extends SliceCaseReducers<State>,\r\n  Name extends string = string\r\n>(\r\n  options: CreateSliceOptions<State, CaseReducers, Name>\r\n): Slice<State, CaseReducers, Name> {\r\n  const { name } = options\r\n  if (!name) {\r\n    throw new Error('`name` is a required option for createSlice')\r\n  }\r\n\r\n  if (\r\n    typeof process !== 'undefined' &&\r\n    process.env.NODE_ENV === 'development'\r\n  ) {\r\n    if (options.initialState === undefined) {\r\n      console.error(\r\n        'You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`'\r\n      )\r\n    }\r\n  }\r\n\r\n  const initialState =\r\n    typeof options.initialState == 'function'\r\n      ? options.initialState\r\n      : freezeDraftable(options.initialState)\r\n\r\n  const reducers = options.reducers || {}\r\n\r\n  const reducerNames = Object.keys(reducers)\r\n\r\n  const sliceCaseReducersByName: Record<string, CaseReducer> = {}\r\n  const sliceCaseReducersByType: Record<string, CaseReducer> = {}\r\n  const actionCreators: Record<string, Function> = {}\r\n\r\n  reducerNames.forEach((reducerName) => {\r\n    const maybeReducerWithPrepare = reducers[reducerName]\r\n    const type = getType(name, reducerName)\r\n\r\n    let caseReducer: CaseReducer<State, any>\r\n    let prepareCallback: PrepareAction<any> | undefined\r\n\r\n    if ('reducer' in maybeReducerWithPrepare) {\r\n      caseReducer = maybeReducerWithPrepare.reducer\r\n      prepareCallback = maybeReducerWithPrepare.prepare\r\n    } else {\r\n      caseReducer = maybeReducerWithPrepare\r\n    }\r\n\r\n    sliceCaseReducersByName[reducerName] = caseReducer\r\n    sliceCaseReducersByType[type] = caseReducer\r\n    actionCreators[reducerName] = prepareCallback\r\n      ? createAction(type, prepareCallback)\r\n      : createAction(type)\r\n  })\r\n\r\n  function buildReducer() {\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      if (typeof options.extraReducers === 'object') {\r\n        if (!hasWarnedAboutObjectNotation) {\r\n          hasWarnedAboutObjectNotation = true\r\n          console.warn(\r\n            \"The object notation for `createSlice.extraReducers` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\"\r\n          )\r\n        }\r\n      }\r\n    }\r\n    const [\r\n      extraReducers = {},\r\n      actionMatchers = [],\r\n      defaultCaseReducer = undefined,\r\n    ] =\r\n      typeof options.extraReducers === 'function'\r\n        ? executeReducerBuilderCallback(options.extraReducers)\r\n        : [options.extraReducers]\r\n\r\n    const finalCaseReducers = { ...extraReducers, ...sliceCaseReducersByType }\r\n\r\n    return createReducer(initialState, (builder) => {\r\n      for (let key in finalCaseReducers) {\r\n        builder.addCase(key, finalCaseReducers[key] as CaseReducer<any>)\r\n      }\r\n      for (let m of actionMatchers) {\r\n        builder.addMatcher(m.matcher, m.reducer)\r\n      }\r\n      if (defaultCaseReducer) {\r\n        builder.addDefaultCase(defaultCaseReducer)\r\n      }\r\n    })\r\n  }\r\n\r\n  let _reducer: ReducerWithInitialState<State>\r\n\r\n  return {\r\n    name,\r\n    reducer(state, action) {\r\n      if (!_reducer) _reducer = buildReducer()\r\n\r\n      return _reducer(state, action)\r\n    },\r\n    actions: actionCreators as any,\r\n    caseReducers: sliceCaseReducersByName as any,\r\n    getInitialState() {\r\n      if (!_reducer) _reducer = buildReducer()\r\n\r\n      return _reducer.getInitialState()\r\n    },\r\n  }\r\n}\r\n", "import type { EntityState } from './models'\r\n\r\nexport function getInitialEntityState<V>(): EntityState<V> {\r\n  return {\r\n    ids: [],\r\n    entities: {},\r\n  }\r\n}\r\n\r\nexport function createInitialStateFactory<V>() {\r\n  function getInitialState(): EntityState<V>\r\n  function getInitialState<S extends object>(\r\n    additionalState: S\r\n  ): EntityState<V> & S\r\n  function getInitialState(additionalState: any = {}): any {\r\n    return Object.assign(getInitialEntityState(), additionalState)\r\n  }\r\n\r\n  return { getInitialState }\r\n}\r\n", "import type { Selector } from 'reselect'\r\nimport { createDraftSafeSelector } from '../createDraftSafeSelector'\r\nimport type {\r\n  EntityState,\r\n  EntitySelectors,\r\n  Dictionary,\r\n  EntityId,\r\n} from './models'\r\n\r\nexport function createSelectorsFactory<T>() {\r\n  function getSelectors(): EntitySelectors<T, EntityState<T>>\r\n  function getSelectors<V>(\r\n    selectState: (state: V) => EntityState<T>\r\n  ): EntitySelectors<T, V>\r\n  function getSelectors<V>(\r\n    selectState?: (state: V) => EntityState<T>\r\n  ): EntitySelectors<T, any> {\r\n    const selectIds = (state: EntityState<T>) => state.ids\r\n\r\n    const selectEntities = (state: EntityState<T>) => state.entities\r\n\r\n    const selectAll = createDraftSafeSelector(\r\n      selectIds,\r\n      selectEntities,\r\n      (ids, entities): T[] => ids.map((id) => entities[id]!)\r\n    )\r\n\r\n    const selectId = (_: unknown, id: EntityId) => id\r\n\r\n    const selectById = (entities: Dictionary<T>, id: EntityId) => entities[id]\r\n\r\n    const selectTotal = createDraftSafeSelector(selectIds, (ids) => ids.length)\r\n\r\n    if (!selectState) {\r\n      return {\r\n        selectIds,\r\n        selectEntities,\r\n        selectAll,\r\n        selectTotal,\r\n        selectById: createDraftSafeSelector(\r\n          selectEntities,\r\n          selectId,\r\n          selectById\r\n        ),\r\n      }\r\n    }\r\n\r\n    const selectGlobalizedEntities = createDraftSafeSelector(\r\n      selectState as Selector<V, EntityState<T>>,\r\n      selectEntities\r\n    )\r\n\r\n    return {\r\n      selectIds: createDraftSafeSelector(selectState, selectIds),\r\n      selectEntities: selectGlobalizedEntities,\r\n      selectAll: createDraftSafeSelector(selectState, selectAll),\r\n      selectTotal: createDraftSafeSelector(selectState, selectTotal),\r\n      selectById: createDraftSafeSelector(\r\n        selectGlobalizedEntities,\r\n        selectId,\r\n        selectById\r\n      ),\r\n    }\r\n  }\r\n\r\n  return { getSelectors }\r\n}\r\n", "import createNextState, { isDraft } from 'immer'\r\nimport type { EntityState, PreventAny } from './models'\r\nimport type { PayloadAction } from '../createAction'\r\nimport { isFSA } from '../createAction'\r\nimport { IsAny } from '../tsHelpers'\r\n\r\nexport function createSingleArgumentStateOperator<V>(\r\n  mutator: (state: EntityState<V>) => void\r\n) {\r\n  const operator = createStateOperator((_: undefined, state: EntityState<V>) =>\r\n    mutator(state)\r\n  )\r\n\r\n  return function operation<S extends EntityState<V>>(\r\n    state: PreventAny<S, V>\r\n  ): S {\r\n    return operator(state as S, undefined)\r\n  }\r\n}\r\n\r\nexport function createStateOperator<V, R>(\r\n  mutator: (arg: R, state: EntityState<V>) => void\r\n) {\r\n  return function operation<S extends EntityState<V>>(\r\n    state: S,\r\n    arg: R | PayloadAction<R>\r\n  ): S {\r\n    function isPayloadActionArgument(\r\n      arg: R | PayloadAction<R>\r\n    ): arg is PayloadAction<R> {\r\n      return isFSA(arg)\r\n    }\r\n\r\n    const runMutator = (draft: EntityState<V>) => {\r\n      if (isPayloadActionArgument(arg)) {\r\n        mutator(arg.payload, draft)\r\n      } else {\r\n        mutator(arg, draft)\r\n      }\r\n    }\r\n\r\n    if (isDraft(state)) {\r\n      // we must already be inside a `createNextState` call, likely because\r\n      // this is being wrapped in `createReducer` or `createSlice`.\r\n      // It's safe to just pass the draft to the mutator.\r\n      runMutator(state)\r\n\r\n      // since it's a draft, we'll just return it\r\n      return state\r\n    } else {\r\n      // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\r\n      // than an Immutable<S>, and TypeScript cannot find out how to reconcile\r\n      // these two types.\r\n      return createNextState(state, runMutator)\r\n    }\r\n  }\r\n}\r\n", "import type { EntityState, IdSelector, Update, EntityId } from './models'\r\n\r\nexport function selectIdValue<T>(entity: T, selectId: IdSelector<T>) {\r\n  const key = selectId(entity)\r\n\r\n  if (process.env.NODE_ENV !== 'production' && key === undefined) {\r\n    console.warn(\r\n      'The entity passed to the `selectId` implementation returned undefined.',\r\n      'You should probably provide your own `selectId` implementation.',\r\n      'The entity that was passed:',\r\n      entity,\r\n      'The `selectId` implementation:',\r\n      selectId.toString()\r\n    )\r\n  }\r\n\r\n  return key\r\n}\r\n\r\nexport function ensureEntitiesArray<T>(\r\n  entities: readonly T[] | Record<EntityId, T>\r\n): readonly T[] {\r\n  if (!Array.isArray(entities)) {\r\n    entities = Object.values(entities)\r\n  }\r\n\r\n  return entities\r\n}\r\n\r\nexport function splitAddedUpdatedEntities<T>(\r\n  newEntities: readonly T[] | Record<EntityId, T>,\r\n  selectId: IdSelector<T>,\r\n  state: EntityState<T>\r\n): [T[], Update<T>[]] {\r\n  newEntities = ensureEntitiesArray(newEntities)\r\n\r\n  const added: T[] = []\r\n  const updated: Update<T>[] = []\r\n\r\n  for (const entity of newEntities) {\r\n    const id = selectIdValue(entity, selectId)\r\n    if (id in state.entities) {\r\n      updated.push({ id, changes: entity })\r\n    } else {\r\n      added.push(entity)\r\n    }\r\n  }\r\n  return [added, updated]\r\n}\r\n", "import type {\r\n  EntityState,\r\n  EntityStateAdapter,\r\n  IdSelector,\r\n  Update,\r\n  EntityId,\r\n} from './models'\r\nimport {\r\n  createStateOperator,\r\n  createSingleArgumentStateOperator,\r\n} from './state_adapter'\r\nimport {\r\n  selectIdValue,\r\n  ensureEntitiesArray,\r\n  splitAddedUpdatedEntities,\r\n} from './utils'\r\n\r\nexport function createUnsortedStateAdapter<T>(\r\n  selectId: IdSelector<T>\r\n): EntityStateAdapter<T> {\r\n  type R = EntityState<T>\r\n\r\n  function addOneMutably(entity: T, state: R): void {\r\n    const key = selectIdValue(entity, selectId)\r\n\r\n    if (key in state.entities) {\r\n      return\r\n    }\r\n\r\n    state.ids.push(key)\r\n    state.entities[key] = entity\r\n  }\r\n\r\n  function addManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n\r\n    for (const entity of newEntities) {\r\n      addOneMutably(entity, state)\r\n    }\r\n  }\r\n\r\n  function setOneMutably(entity: T, state: R): void {\r\n    const key = selectIdValue(entity, selectId)\r\n    if (!(key in state.entities)) {\r\n      state.ids.push(key)\r\n    }\r\n    state.entities[key] = entity\r\n  }\r\n\r\n  function setManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n    for (const entity of newEntities) {\r\n      setOneMutably(entity, state)\r\n    }\r\n  }\r\n\r\n  function setAllMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n\r\n    state.ids = []\r\n    state.entities = {}\r\n\r\n    addManyMutably(newEntities, state)\r\n  }\r\n\r\n  function removeOneMutably(key: EntityId, state: R): void {\r\n    return removeManyMutably([key], state)\r\n  }\r\n\r\n  function removeManyMutably(keys: readonly EntityId[], state: R): void {\r\n    let didMutate = false\r\n\r\n    keys.forEach((key) => {\r\n      if (key in state.entities) {\r\n        delete state.entities[key]\r\n        didMutate = true\r\n      }\r\n    })\r\n\r\n    if (didMutate) {\r\n      state.ids = state.ids.filter((id) => id in state.entities)\r\n    }\r\n  }\r\n\r\n  function removeAllMutably(state: R): void {\r\n    Object.assign(state, {\r\n      ids: [],\r\n      entities: {},\r\n    })\r\n  }\r\n\r\n  function takeNewKey(\r\n    keys: { [id: string]: EntityId },\r\n    update: Update<T>,\r\n    state: R\r\n  ): boolean {\r\n    const original = state.entities[update.id]\r\n    const updated: T = Object.assign({}, original, update.changes)\r\n    const newKey = selectIdValue(updated, selectId)\r\n    const hasNewKey = newKey !== update.id\r\n\r\n    if (hasNewKey) {\r\n      keys[update.id] = newKey\r\n      delete state.entities[update.id]\r\n    }\r\n\r\n    state.entities[newKey] = updated\r\n\r\n    return hasNewKey\r\n  }\r\n\r\n  function updateOneMutably(update: Update<T>, state: R): void {\r\n    return updateManyMutably([update], state)\r\n  }\r\n\r\n  function updateManyMutably(\r\n    updates: ReadonlyArray<Update<T>>,\r\n    state: R\r\n  ): void {\r\n    const newKeys: { [id: string]: EntityId } = {}\r\n\r\n    const updatesPerEntity: { [id: string]: Update<T> } = {}\r\n\r\n    updates.forEach((update) => {\r\n      // Only apply updates to entities that currently exist\r\n      if (update.id in state.entities) {\r\n        // If there are multiple updates to one entity, merge them together\r\n        updatesPerEntity[update.id] = {\r\n          id: update.id,\r\n          // Spreads ignore falsy values, so this works even if there isn't\r\n          // an existing update already at this key\r\n          changes: {\r\n            ...(updatesPerEntity[update.id]\r\n              ? updatesPerEntity[update.id].changes\r\n              : null),\r\n            ...update.changes,\r\n          },\r\n        }\r\n      }\r\n    })\r\n\r\n    updates = Object.values(updatesPerEntity)\r\n\r\n    const didMutateEntities = updates.length > 0\r\n\r\n    if (didMutateEntities) {\r\n      const didMutateIds =\r\n        updates.filter((update) => takeNewKey(newKeys, update, state)).length >\r\n        0\r\n\r\n      if (didMutateIds) {\r\n        state.ids = Object.keys(state.entities)\r\n      }\r\n    }\r\n  }\r\n\r\n  function upsertOneMutably(entity: T, state: R): void {\r\n    return upsertManyMutably([entity], state)\r\n  }\r\n\r\n  function upsertManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    const [added, updated] = splitAddedUpdatedEntities<T>(\r\n      newEntities,\r\n      selectId,\r\n      state\r\n    )\r\n\r\n    updateManyMutably(updated, state)\r\n    addManyMutably(added, state)\r\n  }\r\n\r\n  return {\r\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\r\n    addOne: createStateOperator(addOneMutably),\r\n    addMany: createStateOperator(addManyMutably),\r\n    setOne: createStateOperator(setOneMutably),\r\n    setMany: createStateOperator(setManyMutably),\r\n    setAll: createStateOperator(setAllMutably),\r\n    updateOne: createStateOperator(updateOneMutably),\r\n    updateMany: createStateOperator(updateManyMutably),\r\n    upsertOne: createStateOperator(upsertOneMutably),\r\n    upsertMany: createStateOperator(upsertManyMutably),\r\n    removeOne: createStateOperator(removeOneMutably),\r\n    removeMany: createStateOperator(removeManyMutably),\r\n  }\r\n}\r\n", "import type {\r\n  EntityState,\r\n  IdSelector,\r\n  Comparer,\r\n  EntityStateAdapter,\r\n  Update,\r\n  EntityId,\r\n} from './models'\r\nimport { createStateOperator } from './state_adapter'\r\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter'\r\nimport {\r\n  selectIdValue,\r\n  ensureEntitiesArray,\r\n  splitAddedUpdatedEntities,\r\n} from './utils'\r\n\r\nexport function createSortedStateAdapter<T>(\r\n  selectId: IdSelector<T>,\r\n  sort: Comparer<T>\r\n): EntityStateAdapter<T> {\r\n  type R = EntityState<T>\r\n\r\n  const { removeOne, removeMany, removeAll } =\r\n    createUnsortedStateAdapter(selectId)\r\n\r\n  function addOneMutably(entity: T, state: R): void {\r\n    return addManyMutably([entity], state)\r\n  }\r\n\r\n  function addManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n\r\n    const models = newEntities.filter(\r\n      (model) => !(selectIdValue(model, selectId) in state.entities)\r\n    )\r\n\r\n    if (models.length !== 0) {\r\n      merge(models, state)\r\n    }\r\n  }\r\n\r\n  function setOneMutably(entity: T, state: R): void {\r\n    return setManyMutably([entity], state)\r\n  }\r\n\r\n  function setManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n    if (newEntities.length !== 0) {\r\n      merge(newEntities, state)\r\n    }\r\n  }\r\n\r\n  function setAllMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n    state.entities = {}\r\n    state.ids = []\r\n\r\n    addManyMutably(newEntities, state)\r\n  }\r\n\r\n  function updateOneMutably(update: Update<T>, state: R): void {\r\n    return updateManyMutably([update], state)\r\n  }\r\n\r\n  function updateManyMutably(\r\n    updates: ReadonlyArray<Update<T>>,\r\n    state: R\r\n  ): void {\r\n    let appliedUpdates = false\r\n\r\n    for (let update of updates) {\r\n      const entity = state.entities[update.id]\r\n      if (!entity) {\r\n        continue\r\n      }\r\n\r\n      appliedUpdates = true\r\n\r\n      Object.assign(entity, update.changes)\r\n      const newId = selectId(entity)\r\n      if (update.id !== newId) {\r\n        delete state.entities[update.id]\r\n        state.entities[newId] = entity\r\n      }\r\n    }\r\n\r\n    if (appliedUpdates) {\r\n      resortEntities(state)\r\n    }\r\n  }\r\n\r\n  function upsertOneMutably(entity: T, state: R): void {\r\n    return upsertManyMutably([entity], state)\r\n  }\r\n\r\n  function upsertManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    const [added, updated] = splitAddedUpdatedEntities<T>(\r\n      newEntities,\r\n      selectId,\r\n      state\r\n    )\r\n\r\n    updateManyMutably(updated, state)\r\n    addManyMutably(added, state)\r\n  }\r\n\r\n  function areArraysEqual(a: readonly unknown[], b: readonly unknown[]) {\r\n    if (a.length !== b.length) {\r\n      return false\r\n    }\r\n\r\n    for (let i = 0; i < a.length && i < b.length; i++) {\r\n      if (a[i] === b[i]) {\r\n        continue\r\n      }\r\n      return false\r\n    }\r\n    return true\r\n  }\r\n\r\n  function merge(models: readonly T[], state: R): void {\r\n    // Insert/overwrite all new/updated\r\n    models.forEach((model) => {\r\n      state.entities[selectId(model)] = model\r\n    })\r\n\r\n    resortEntities(state)\r\n  }\r\n\r\n  function resortEntities(state: R) {\r\n    const allEntities = Object.values(state.entities) as T[]\r\n    allEntities.sort(sort)\r\n\r\n    const newSortedIds = allEntities.map(selectId)\r\n    const { ids } = state\r\n\r\n    if (!areArraysEqual(ids, newSortedIds)) {\r\n      state.ids = newSortedIds\r\n    }\r\n  }\r\n\r\n  return {\r\n    removeOne,\r\n    removeMany,\r\n    removeAll,\r\n    addOne: createStateOperator(addOneMutably),\r\n    updateOne: createStateOperator(updateOneMutably),\r\n    upsertOne: createStateOperator(upsertOneMutably),\r\n    setOne: createStateOperator(setOneMutably),\r\n    setMany: createStateOperator(setManyMutably),\r\n    setAll: createStateOperator(setAllMutably),\r\n    addMany: createStateOperator(addManyMutably),\r\n    updateMany: createStateOperator(updateManyMutably),\r\n    upsertMany: createStateOperator(upsertManyMutably),\r\n  }\r\n}\r\n", "import type {\r\n  EntityDefinition,\r\n  Comparer,\r\n  IdSelector,\r\n  EntityAdapter,\r\n} from './models'\r\nimport { createInitialStateFactory } from './entity_state'\r\nimport { createSelectorsFactory } from './state_selectors'\r\nimport { createSortedStateAdapter } from './sorted_state_adapter'\r\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter'\r\n\r\n/**\r\n *\r\n * @param options\r\n *\r\n * @public\r\n */\r\nexport function createEntityAdapter<T>(\r\n  options: {\r\n    selectId?: IdSelector<T>\r\n    sortComparer?: false | Comparer<T>\r\n  } = {}\r\n): EntityAdapter<T> {\r\n  const { selectId, sortComparer }: EntityDefinition<T> = {\r\n    sortComparer: false,\r\n    selectId: (instance: any) => instance.id,\r\n    ...options,\r\n  }\r\n\r\n  const stateFactory = createInitialStateFactory<T>()\r\n  const selectorsFactory = createSelectorsFactory<T>()\r\n  const stateAdapter = sortComparer\r\n    ? createSortedStateAdapter(selectId, sortComparer)\r\n    : createUnsortedStateAdapter(selectId)\r\n\r\n  return {\r\n    selectId,\r\n    sortComparer,\r\n    ...stateFactory,\r\n    ...selectorsFactory,\r\n    ...stateAdapter,\r\n  }\r\n}\r\n", "// Borrowed from https://github.com/ai/nanoid/blob/3.0.2/non-secure/index.js\r\n// This alphabet uses `A-Za-z0-9_-` symbols. A genetic algorithm helped\r\n// optimize the gzip compression for this alphabet.\r\nlet urlAlphabet =\r\n  'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW'\r\n\r\n/**\r\n *\r\n * @public\r\n */\r\nexport let nanoid = (size = 21) => {\r\n  let id = ''\r\n  // A compact alternative for `for (var i = 0; i < step; i++)`.\r\n  let i = size\r\n  while (i--) {\r\n    // `| 0` is more compact and faster than `Math.floor()`.\r\n    id += urlAlphabet[(Math.random() * 64) | 0]\r\n  }\r\n  return id\r\n}\r\n", "import type { Dispatch, AnyAction } from 'redux'\r\nimport type {\r\n  PayloadAction,\r\n  ActionCreatorWithPreparedPayload,\r\n} from './createAction'\r\nimport { createAction } from './createAction'\r\nimport type { ThunkDispatch } from 'redux-thunk'\r\nimport type { FallbackIfUnknown, Id, IsAny, IsUnknown } from './tsHelpers'\r\nimport { nanoid } from './nanoid'\r\n\r\n// @ts-ignore we need the import of these types due to a bundling issue.\r\ntype _Keep = PayloadAction | ActionCreatorWithPreparedPayload<any, unknown>\r\n\r\nexport type BaseThunkAPI<\r\n  S,\r\n  E,\r\n  D extends Dispatch = Dispatch,\r\n  RejectedValue = unknown,\r\n  RejectedMeta = unknown,\r\n  FulfilledMeta = unknown\r\n> = {\r\n  dispatch: D\r\n  getState: () => S\r\n  extra: E\r\n  requestId: string\r\n  signal: AbortSignal\r\n  abort: (reason?: string) => void\r\n  rejectWithValue: IsUnknown<\r\n    RejectedMeta,\r\n    (value: RejectedValue) => RejectWithValue<RejectedValue, RejectedMeta>,\r\n    (\r\n      value: RejectedValue,\r\n      meta: RejectedMeta\r\n    ) => RejectWithValue<RejectedValue, RejectedMeta>\r\n  >\r\n  fulfillWithValue: IsUnknown<\r\n    FulfilledMeta,\r\n    <FulfilledValue>(value: FulfilledValue) => FulfilledValue,\r\n    <FulfilledValue>(\r\n      value: FulfilledValue,\r\n      meta: FulfilledMeta\r\n    ) => FulfillWithMeta<FulfilledValue, FulfilledMeta>\r\n  >\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport interface SerializedError {\r\n  name?: string\r\n  message?: string\r\n  stack?: string\r\n  code?: string\r\n}\r\n\r\nconst commonProperties: Array<keyof SerializedError> = [\r\n  'name',\r\n  'message',\r\n  'stack',\r\n  'code',\r\n]\r\n\r\nclass RejectWithValue<Payload, RejectedMeta> {\r\n  /*\r\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\r\n  does not exist at runtime\r\n  */\r\n  private readonly _type!: 'RejectWithValue'\r\n  constructor(\r\n    public readonly payload: Payload,\r\n    public readonly meta: RejectedMeta\r\n  ) {}\r\n}\r\n\r\nclass FulfillWithMeta<Payload, FulfilledMeta> {\r\n  /*\r\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\r\n  does not exist at runtime\r\n  */\r\n  private readonly _type!: 'FulfillWithMeta'\r\n  constructor(\r\n    public readonly payload: Payload,\r\n    public readonly meta: FulfilledMeta\r\n  ) {}\r\n}\r\n\r\n/**\r\n * Serializes an error into a plain object.\r\n * Reworked from https://github.com/sindresorhus/serialize-error\r\n *\r\n * @public\r\n */\r\nexport const miniSerializeError = (value: any): SerializedError => {\r\n  if (typeof value === 'object' && value !== null) {\r\n    const simpleError: SerializedError = {}\r\n    for (const property of commonProperties) {\r\n      if (typeof value[property] === 'string') {\r\n        simpleError[property] = value[property]\r\n      }\r\n    }\r\n\r\n    return simpleError\r\n  }\r\n\r\n  return { message: String(value) }\r\n}\r\n\r\ntype AsyncThunkConfig = {\r\n  state?: unknown\r\n  dispatch?: Dispatch\r\n  extra?: unknown\r\n  rejectValue?: unknown\r\n  serializedErrorType?: unknown\r\n  pendingMeta?: unknown\r\n  fulfilledMeta?: unknown\r\n  rejectedMeta?: unknown\r\n}\r\n\r\ntype GetState<ThunkApiConfig> = ThunkApiConfig extends {\r\n  state: infer State\r\n}\r\n  ? State\r\n  : unknown\r\ntype GetExtra<ThunkApiConfig> = ThunkApiConfig extends { extra: infer Extra }\r\n  ? Extra\r\n  : unknown\r\ntype GetDispatch<ThunkApiConfig> = ThunkApiConfig extends {\r\n  dispatch: infer Dispatch\r\n}\r\n  ? FallbackIfUnknown<\r\n      Dispatch,\r\n      ThunkDispatch<\r\n        GetState<ThunkApiConfig>,\r\n        GetExtra<ThunkApiConfig>,\r\n        AnyAction\r\n      >\r\n    >\r\n  : ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, AnyAction>\r\n\r\nexport type GetThunkAPI<ThunkApiConfig> = BaseThunkAPI<\r\n  GetState<ThunkApiConfig>,\r\n  GetExtra<ThunkApiConfig>,\r\n  GetDispatch<ThunkApiConfig>,\r\n  GetRejectValue<ThunkApiConfig>,\r\n  GetRejectedMeta<ThunkApiConfig>,\r\n  GetFulfilledMeta<ThunkApiConfig>\r\n>\r\n\r\ntype GetRejectValue<ThunkApiConfig> = ThunkApiConfig extends {\r\n  rejectValue: infer RejectValue\r\n}\r\n  ? RejectValue\r\n  : unknown\r\n\r\ntype GetPendingMeta<ThunkApiConfig> = ThunkApiConfig extends {\r\n  pendingMeta: infer PendingMeta\r\n}\r\n  ? PendingMeta\r\n  : unknown\r\n\r\ntype GetFulfilledMeta<ThunkApiConfig> = ThunkApiConfig extends {\r\n  fulfilledMeta: infer FulfilledMeta\r\n}\r\n  ? FulfilledMeta\r\n  : unknown\r\n\r\ntype GetRejectedMeta<ThunkApiConfig> = ThunkApiConfig extends {\r\n  rejectedMeta: infer RejectedMeta\r\n}\r\n  ? RejectedMeta\r\n  : unknown\r\n\r\ntype GetSerializedErrorType<ThunkApiConfig> = ThunkApiConfig extends {\r\n  serializedErrorType: infer GetSerializedErrorType\r\n}\r\n  ? GetSerializedErrorType\r\n  : SerializedError\r\n\r\ntype MaybePromise<T> = T | Promise<T> | (T extends any ? Promise<T> : never)\r\n\r\n/**\r\n * A type describing the return value of the `payloadCreator` argument to `createAsyncThunk`.\r\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkPayloadCreatorReturnValue<\r\n  Returned,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = MaybePromise<\r\n  | IsUnknown<\r\n      GetFulfilledMeta<ThunkApiConfig>,\r\n      Returned,\r\n      FulfillWithMeta<Returned, GetFulfilledMeta<ThunkApiConfig>>\r\n    >\r\n  | RejectWithValue<\r\n      GetRejectValue<ThunkApiConfig>,\r\n      GetRejectedMeta<ThunkApiConfig>\r\n    >\r\n>\r\n/**\r\n * A type describing the `payloadCreator` argument to `createAsyncThunk`.\r\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkPayloadCreator<\r\n  Returned,\r\n  ThunkArg = void,\r\n  ThunkApiConfig extends AsyncThunkConfig = {}\r\n> = (\r\n  arg: ThunkArg,\r\n  thunkAPI: GetThunkAPI<ThunkApiConfig>\r\n) => AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig>\r\n\r\n/**\r\n * A ThunkAction created by `createAsyncThunk`.\r\n * Dispatching it returns a Promise for either a\r\n * fulfilled or rejected action.\r\n * Also, the returned value contains an `abort()` method\r\n * that allows the asyncAction to be cancelled from the outside.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkAction<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = (\r\n  dispatch: GetDispatch<ThunkApiConfig>,\r\n  getState: () => GetState<ThunkApiConfig>,\r\n  extra: GetExtra<ThunkApiConfig>\r\n) => Promise<\r\n  | ReturnType<AsyncThunkFulfilledActionCreator<Returned, ThunkArg>>\r\n  | ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>>\r\n> & {\r\n  abort: (reason?: string) => void\r\n  requestId: string\r\n  arg: ThunkArg\r\n  unwrap: () => Promise<Returned>\r\n}\r\n\r\ntype AsyncThunkActionCreator<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = IsAny<\r\n  ThunkArg,\r\n  // any handling\r\n  (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\r\n  // unknown handling\r\n  unknown extends ThunkArg\r\n    ? (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument not specified or specified as void or undefined\r\n    : [ThunkArg] extends [void] | [undefined]\r\n    ? () => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains void\r\n    : [void] extends [ThunkArg] // make optional\r\n    ? (arg?: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains undefined\r\n    : [undefined] extends [ThunkArg]\r\n    ? WithStrictNullChecks<\r\n        // with strict nullChecks: make optional\r\n        (\r\n          arg?: ThunkArg\r\n        ) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\r\n        // without strict null checks this will match everything, so don't make it optional\r\n        (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>\r\n      > // default case: normal argument\r\n    : (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>\r\n>\r\n\r\n/**\r\n * Options object for `createAsyncThunk`.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkOptions<\r\n  ThunkArg = void,\r\n  ThunkApiConfig extends AsyncThunkConfig = {}\r\n> = {\r\n  /**\r\n   * A method to control whether the asyncThunk should be executed. Has access to the\r\n   * `arg`, `api.getState()` and `api.extra` arguments.\r\n   *\r\n   * @returns `false` if it should be skipped\r\n   */\r\n  condition?(\r\n    arg: ThunkArg,\r\n    api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>\r\n  ): MaybePromise<boolean | undefined>\r\n  /**\r\n   * If `condition` returns `false`, the asyncThunk will be skipped.\r\n   * This option allows you to control whether a `rejected` action with `meta.condition == false`\r\n   * will be dispatched or not.\r\n   *\r\n   * @default `false`\r\n   */\r\n  dispatchConditionRejection?: boolean\r\n\r\n  serializeError?: (x: unknown) => GetSerializedErrorType<ThunkApiConfig>\r\n\r\n  /**\r\n   * A function to use when generating the `requestId` for the request sequence.\r\n   *\r\n   * @default `nanoid`\r\n   */\r\n  idGenerator?: (arg: ThunkArg) => string\r\n} & IsUnknown<\r\n  GetPendingMeta<ThunkApiConfig>,\r\n  {\r\n    /**\r\n     * A method to generate additional properties to be added to `meta` of the pending action.\r\n     *\r\n     * Using this optional overload will not modify the types correctly, this overload is only in place to support JavaScript users.\r\n     * Please use the `ThunkApiConfig` parameter `pendingMeta` to get access to a correctly typed overload\r\n     */\r\n    getPendingMeta?(\r\n      base: {\r\n        arg: ThunkArg\r\n        requestId: string\r\n      },\r\n      api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>\r\n    ): GetPendingMeta<ThunkApiConfig>\r\n  },\r\n  {\r\n    /**\r\n     * A method to generate additional properties to be added to `meta` of the pending action.\r\n     */\r\n    getPendingMeta(\r\n      base: {\r\n        arg: ThunkArg\r\n        requestId: string\r\n      },\r\n      api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>\r\n    ): GetPendingMeta<ThunkApiConfig>\r\n  }\r\n>\r\n\r\nexport type AsyncThunkPendingActionCreator<\r\n  ThunkArg,\r\n  ThunkApiConfig = {}\r\n> = ActionCreatorWithPreparedPayload<\r\n  [string, ThunkArg, GetPendingMeta<ThunkApiConfig>?],\r\n  undefined,\r\n  string,\r\n  never,\r\n  {\r\n    arg: ThunkArg\r\n    requestId: string\r\n    requestStatus: 'pending'\r\n  } & GetPendingMeta<ThunkApiConfig>\r\n>\r\n\r\nexport type AsyncThunkRejectedActionCreator<\r\n  ThunkArg,\r\n  ThunkApiConfig = {}\r\n> = ActionCreatorWithPreparedPayload<\r\n  [\r\n    Error | null,\r\n    string,\r\n    ThunkArg,\r\n    GetRejectValue<ThunkApiConfig>?,\r\n    GetRejectedMeta<ThunkApiConfig>?\r\n  ],\r\n  GetRejectValue<ThunkApiConfig> | undefined,\r\n  string,\r\n  GetSerializedErrorType<ThunkApiConfig>,\r\n  {\r\n    arg: ThunkArg\r\n    requestId: string\r\n    requestStatus: 'rejected'\r\n    aborted: boolean\r\n    condition: boolean\r\n  } & (\r\n    | ({ rejectedWithValue: false } & {\r\n        [K in keyof GetRejectedMeta<ThunkApiConfig>]?: undefined\r\n      })\r\n    | ({ rejectedWithValue: true } & GetRejectedMeta<ThunkApiConfig>)\r\n  )\r\n>\r\n\r\nexport type AsyncThunkFulfilledActionCreator<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig = {}\r\n> = ActionCreatorWithPreparedPayload<\r\n  [Returned, string, ThunkArg, GetFulfilledMeta<ThunkApiConfig>?],\r\n  Returned,\r\n  string,\r\n  never,\r\n  {\r\n    arg: ThunkArg\r\n    requestId: string\r\n    requestStatus: 'fulfilled'\r\n  } & GetFulfilledMeta<ThunkApiConfig>\r\n>\r\n\r\n/**\r\n * A type describing the return value of `createAsyncThunk`.\r\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunk<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig> & {\r\n  pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig>\r\n  rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>\r\n  fulfilled: AsyncThunkFulfilledActionCreator<\r\n    Returned,\r\n    ThunkArg,\r\n    ThunkApiConfig\r\n  >\r\n  typePrefix: string\r\n}\r\n\r\ntype OverrideThunkApiConfigs<OldConfig, NewConfig> = Id<\r\n  NewConfig & Omit<OldConfig, keyof NewConfig>\r\n>\r\n\r\ntype CreateAsyncThunk<CurriedThunkApiConfig extends AsyncThunkConfig> = {\r\n  /**\r\n   *\r\n   * @param typePrefix\r\n   * @param payloadCreator\r\n   * @param options\r\n   *\r\n   * @public\r\n   */\r\n  // separate signature without `AsyncThunkConfig` for better inference\r\n  <Returned, ThunkArg = void>(\r\n    typePrefix: string,\r\n    payloadCreator: AsyncThunkPayloadCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      CurriedThunkApiConfig\r\n    >,\r\n    options?: AsyncThunkOptions<ThunkArg, CurriedThunkApiConfig>\r\n  ): AsyncThunk<Returned, ThunkArg, CurriedThunkApiConfig>\r\n\r\n  /**\r\n   *\r\n   * @param typePrefix\r\n   * @param payloadCreator\r\n   * @param options\r\n   *\r\n   * @public\r\n   */\r\n  <Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(\r\n    typePrefix: string,\r\n    payloadCreator: AsyncThunkPayloadCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n    >,\r\n    options?: AsyncThunkOptions<\r\n      ThunkArg,\r\n      OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n    >\r\n  ): AsyncThunk<\r\n    Returned,\r\n    ThunkArg,\r\n    OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n  >\r\n\r\n  withTypes<ThunkApiConfig extends AsyncThunkConfig>(): CreateAsyncThunk<\r\n    OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n  >\r\n}\r\n\r\nexport const createAsyncThunk = (() => {\r\n  function createAsyncThunk<\r\n    Returned,\r\n    ThunkArg,\r\n    ThunkApiConfig extends AsyncThunkConfig\r\n  >(\r\n    typePrefix: string,\r\n    payloadCreator: AsyncThunkPayloadCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      ThunkApiConfig\r\n    >,\r\n    options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>\r\n  ): AsyncThunk<Returned, ThunkArg, ThunkApiConfig> {\r\n    type RejectedValue = GetRejectValue<ThunkApiConfig>\r\n    type PendingMeta = GetPendingMeta<ThunkApiConfig>\r\n    type FulfilledMeta = GetFulfilledMeta<ThunkApiConfig>\r\n    type RejectedMeta = GetRejectedMeta<ThunkApiConfig>\r\n\r\n    const fulfilled: AsyncThunkFulfilledActionCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      ThunkApiConfig\r\n    > = createAction(\r\n      typePrefix + '/fulfilled',\r\n      (\r\n        payload: Returned,\r\n        requestId: string,\r\n        arg: ThunkArg,\r\n        meta?: FulfilledMeta\r\n      ) => ({\r\n        payload,\r\n        meta: {\r\n          ...((meta as any) || {}),\r\n          arg,\r\n          requestId,\r\n          requestStatus: 'fulfilled' as const,\r\n        },\r\n      })\r\n    )\r\n\r\n    const pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig> =\r\n      createAction(\r\n        typePrefix + '/pending',\r\n        (requestId: string, arg: ThunkArg, meta?: PendingMeta) => ({\r\n          payload: undefined,\r\n          meta: {\r\n            ...((meta as any) || {}),\r\n            arg,\r\n            requestId,\r\n            requestStatus: 'pending' as const,\r\n          },\r\n        })\r\n      )\r\n\r\n    const rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> =\r\n      createAction(\r\n        typePrefix + '/rejected',\r\n        (\r\n          error: Error | null,\r\n          requestId: string,\r\n          arg: ThunkArg,\r\n          payload?: RejectedValue,\r\n          meta?: RejectedMeta\r\n        ) => ({\r\n          payload,\r\n          error: ((options && options.serializeError) || miniSerializeError)(\r\n            error || 'Rejected'\r\n          ) as GetSerializedErrorType<ThunkApiConfig>,\r\n          meta: {\r\n            ...((meta as any) || {}),\r\n            arg,\r\n            requestId,\r\n            rejectedWithValue: !!payload,\r\n            requestStatus: 'rejected' as const,\r\n            aborted: error?.name === 'AbortError',\r\n            condition: error?.name === 'ConditionError',\r\n          },\r\n        })\r\n      )\r\n\r\n    let displayedWarning = false\r\n\r\n    const AC =\r\n      typeof AbortController !== 'undefined'\r\n        ? AbortController\r\n        : class implements AbortController {\r\n            signal = {\r\n              aborted: false,\r\n              addEventListener() {},\r\n              dispatchEvent() {\r\n                return false\r\n              },\r\n              onabort() {},\r\n              removeEventListener() {},\r\n              reason: undefined,\r\n              throwIfAborted() {},\r\n            }\r\n            abort() {\r\n              if (process.env.NODE_ENV !== 'production') {\r\n                if (!displayedWarning) {\r\n                  displayedWarning = true\r\n                  console.info(\r\n                    `This platform does not implement AbortController. \r\nIf you want to use the AbortController to react to \\`abort\\` events, please consider importing a polyfill like 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'.`\r\n                  )\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n    function actionCreator(\r\n      arg: ThunkArg\r\n    ): AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> {\r\n      return (dispatch, getState, extra) => {\r\n        const requestId = options?.idGenerator\r\n          ? options.idGenerator(arg)\r\n          : nanoid()\r\n\r\n        const abortController = new AC()\r\n        let abortReason: string | undefined\r\n\r\n        let started = false\r\n        function abort(reason?: string) {\r\n          abortReason = reason\r\n          abortController.abort()\r\n        }\r\n\r\n        const promise = (async function () {\r\n          let finalAction: ReturnType<typeof fulfilled | typeof rejected>\r\n          try {\r\n            let conditionResult = options?.condition?.(arg, { getState, extra })\r\n            if (isThenable(conditionResult)) {\r\n              conditionResult = await conditionResult\r\n            }\r\n\r\n            if (conditionResult === false || abortController.signal.aborted) {\r\n              // eslint-disable-next-line no-throw-literal\r\n              throw {\r\n                name: 'ConditionError',\r\n                message: 'Aborted due to condition callback returning false.',\r\n              }\r\n            }\r\n            started = true\r\n\r\n            const abortedPromise = new Promise<never>((_, reject) =>\r\n              abortController.signal.addEventListener('abort', () =>\r\n                reject({\r\n                  name: 'AbortError',\r\n                  message: abortReason || 'Aborted',\r\n                })\r\n              )\r\n            )\r\n            dispatch(\r\n              pending(\r\n                requestId,\r\n                arg,\r\n                options?.getPendingMeta?.(\r\n                  { requestId, arg },\r\n                  { getState, extra }\r\n                )\r\n              )\r\n            )\r\n            finalAction = await Promise.race([\r\n              abortedPromise,\r\n              Promise.resolve(\r\n                payloadCreator(arg, {\r\n                  dispatch,\r\n                  getState,\r\n                  extra,\r\n                  requestId,\r\n                  signal: abortController.signal,\r\n                  abort,\r\n                  rejectWithValue: ((\r\n                    value: RejectedValue,\r\n                    meta?: RejectedMeta\r\n                  ) => {\r\n                    return new RejectWithValue(value, meta)\r\n                  }) as any,\r\n                  fulfillWithValue: ((value: unknown, meta?: FulfilledMeta) => {\r\n                    return new FulfillWithMeta(value, meta)\r\n                  }) as any,\r\n                })\r\n              ).then((result) => {\r\n                if (result instanceof RejectWithValue) {\r\n                  throw result\r\n                }\r\n                if (result instanceof FulfillWithMeta) {\r\n                  return fulfilled(result.payload, requestId, arg, result.meta)\r\n                }\r\n                return fulfilled(result as any, requestId, arg)\r\n              }),\r\n            ])\r\n          } catch (err) {\r\n            finalAction =\r\n              err instanceof RejectWithValue\r\n                ? rejected(null, requestId, arg, err.payload, err.meta)\r\n                : rejected(err as any, requestId, arg)\r\n          }\r\n          // We dispatch the result action _after_ the catch, to avoid having any errors\r\n          // here get swallowed by the try/catch block,\r\n          // per https://twitter.com/dan_abramov/status/770914221638942720\r\n          // and https://github.com/reduxjs/redux-toolkit/blob/e85eb17b39a2118d859f7b7746e0f3fee523e089/docs/tutorials/advanced-tutorial.md#async-error-handling-logic-in-thunks\r\n\r\n          const skipDispatch =\r\n            options &&\r\n            !options.dispatchConditionRejection &&\r\n            rejected.match(finalAction) &&\r\n            (finalAction as any).meta.condition\r\n\r\n          if (!skipDispatch) {\r\n            dispatch(finalAction)\r\n          }\r\n          return finalAction\r\n        })()\r\n        return Object.assign(promise as Promise<any>, {\r\n          abort,\r\n          requestId,\r\n          arg,\r\n          unwrap() {\r\n            return promise.then<any>(unwrapResult)\r\n          },\r\n        })\r\n      }\r\n    }\r\n\r\n    return Object.assign(\r\n      actionCreator as AsyncThunkActionCreator<\r\n        Returned,\r\n        ThunkArg,\r\n        ThunkApiConfig\r\n      >,\r\n      {\r\n        pending,\r\n        rejected,\r\n        fulfilled,\r\n        typePrefix,\r\n      }\r\n    )\r\n  }\r\n  createAsyncThunk.withTypes = () => createAsyncThunk\r\n\r\n  return createAsyncThunk as CreateAsyncThunk<AsyncThunkConfig>\r\n})()\r\n\r\ninterface UnwrappableAction {\r\n  payload: any\r\n  meta?: any\r\n  error?: any\r\n}\r\n\r\ntype UnwrappedActionPayload<T extends UnwrappableAction> = Exclude<\r\n  T,\r\n  { error: any }\r\n>['payload']\r\n\r\n/**\r\n * @public\r\n */\r\nexport function unwrapResult<R extends UnwrappableAction>(\r\n  action: R\r\n): UnwrappedActionPayload<R> {\r\n  if (action.meta && action.meta.rejectedWithValue) {\r\n    throw action.payload\r\n  }\r\n  if (action.error) {\r\n    throw action.error\r\n  }\r\n  return action.payload\r\n}\r\n\r\ntype WithStrictNullChecks<True, False> = undefined extends boolean\r\n  ? False\r\n  : True\r\n\r\nfunction isThenable(value: any): value is PromiseLike<any> {\r\n  return (\r\n    value !== null &&\r\n    typeof value === 'object' &&\r\n    typeof value.then === 'function'\r\n  )\r\n}\r\n", "import type {\r\n  Action<PERSON>romMatcher,\r\n  Matcher,\r\n  UnionToIntersection,\r\n} from './tsHelpers'\r\nimport { hasMatchFunction } from './tsHelpers'\r\nimport type {\r\n  AsyncThunk,\r\n  AsyncThunkFulfilledActionCreator,\r\n  AsyncThunkPendingActionCreator,\r\n  AsyncThunkRejectedActionCreator,\r\n} from './createAsyncThunk'\r\n\r\n/** @public */\r\nexport type ActionMatchingAnyOf<Matchers extends [...Matcher<any>[]]> =\r\n  ActionFromMatcher<Matchers[number]>\r\n\r\n/** @public */\r\nexport type ActionMatchingAllOf<Matchers extends [...Matcher<any>[]]> =\r\n  UnionToIntersection<ActionMatchingAnyOf<Matchers>>\r\n\r\nconst matches = (matcher: Matcher<any>, action: any) => {\r\n  if (hasMatchFunction(matcher)) {\r\n    return matcher.match(action)\r\n  } else {\r\n    return matcher(action)\r\n  }\r\n}\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action matches any one of the supplied type guards or action\r\n * creators.\r\n *\r\n * @param matchers The type guards or action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isAnyOf<Matchers extends [...Matcher<any>[]]>(\r\n  ...matchers: Matchers\r\n) {\r\n  return (action: any): action is ActionMatchingAnyOf<Matchers> => {\r\n    return matchers.some((matcher) => matches(matcher, action))\r\n  }\r\n}\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action matches all of the supplied type guards or action\r\n * creators.\r\n *\r\n * @param matchers The type guards or action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isAllOf<Matchers extends [...Matcher<any>[]]>(\r\n  ...matchers: Matchers\r\n) {\r\n  return (action: any): action is ActionMatchingAllOf<Matchers> => {\r\n    return matchers.every((matcher) => matches(matcher, action))\r\n  }\r\n}\r\n\r\n/**\r\n * @param action A redux action\r\n * @param validStatus An array of valid meta.requestStatus values\r\n *\r\n * @internal\r\n */\r\nexport function hasExpectedRequestMetadata(\r\n  action: any,\r\n  validStatus: readonly string[]\r\n) {\r\n  if (!action || !action.meta) return false\r\n\r\n  const hasValidRequestId = typeof action.meta.requestId === 'string'\r\n  const hasValidRequestStatus =\r\n    validStatus.indexOf(action.meta.requestStatus) > -1\r\n\r\n  return hasValidRequestId && hasValidRequestStatus\r\n}\r\n\r\nfunction isAsyncThunkArray(a: [any] | AnyAsyncThunk[]): a is AnyAsyncThunk[] {\r\n  return (\r\n    typeof a[0] === 'function' &&\r\n    'pending' in a[0] &&\r\n    'fulfilled' in a[0] &&\r\n    'rejected' in a[0]\r\n  )\r\n}\r\n\r\nexport type UnknownAsyncThunkPendingAction = ReturnType<\r\n  AsyncThunkPendingActionCreator<unknown>\r\n>\r\n\r\nexport type PendingActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['pending']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is pending.\r\n *\r\n * @public\r\n */\r\nexport function isPending(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkPendingAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is pending.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isPending<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is PendingActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a pending thunk action\r\n * @public\r\n */\r\nexport function isPending(action: any): action is UnknownAsyncThunkPendingAction\r\nexport function isPending<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isPending()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is PendingActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = asyncThunks.map(\r\n      (asyncThunk) => asyncThunk.pending\r\n    ) as any\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkRejectedAction = ReturnType<\r\n  AsyncThunkRejectedActionCreator<unknown, unknown>\r\n>\r\n\r\nexport type RejectedActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['rejected']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is rejected.\r\n *\r\n * @public\r\n */\r\nexport function isRejected(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkRejectedAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is rejected.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isRejected<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is RejectedActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a rejected thunk action\r\n * @public\r\n */\r\nexport function isRejected(\r\n  action: any\r\n): action is UnknownAsyncThunkRejectedAction\r\nexport function isRejected<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => hasExpectedRequestMetadata(action, ['rejected'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isRejected()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is RejectedActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = asyncThunks.map(\r\n      (asyncThunk) => asyncThunk.rejected\r\n    ) as any\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkRejectedWithValueAction = ReturnType<\r\n  AsyncThunkRejectedActionCreator<unknown, unknown>\r\n>\r\n\r\nexport type RejectedWithValueActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['rejected']> &\r\n    (T extends AsyncThunk<any, any, { rejectValue: infer RejectedValue }>\r\n      ? { payload: RejectedValue }\r\n      : unknown)\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is rejected with value.\r\n *\r\n * @public\r\n */\r\nexport function isRejectedWithValue(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkRejectedAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is rejected with value.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isRejectedWithValue<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (\r\n  action: any\r\n) => action is RejectedWithValueActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a rejected thunk action with value\r\n * @public\r\n */\r\nexport function isRejectedWithValue(\r\n  action: any\r\n): action is UnknownAsyncThunkRejectedAction\r\nexport function isRejectedWithValue<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  const hasFlag = (action: any): action is any => {\r\n    return action && action.meta && action.meta.rejectedWithValue\r\n  }\r\n\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => {\r\n      const combinedMatcher = isAllOf(isRejected(...asyncThunks), hasFlag)\r\n\r\n      return combinedMatcher(action)\r\n    }\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isRejectedWithValue()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is RejectedActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    const combinedMatcher = isAllOf(isRejected(...asyncThunks), hasFlag)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkFulfilledAction = ReturnType<\r\n  AsyncThunkFulfilledActionCreator<unknown, unknown>\r\n>\r\n\r\nexport type FulfilledActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['fulfilled']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is fulfilled.\r\n *\r\n * @public\r\n */\r\nexport function isFulfilled(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkFulfilledAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is fulfilled.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isFulfilled<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is FulfilledActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a fulfilled thunk action\r\n * @public\r\n */\r\nexport function isFulfilled(\r\n  action: any\r\n): action is UnknownAsyncThunkFulfilledAction\r\nexport function isFulfilled<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => hasExpectedRequestMetadata(action, ['fulfilled'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isFulfilled()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is FulfilledActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = asyncThunks.map(\r\n      (asyncThunk) => asyncThunk.fulfilled\r\n    ) as any\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkAction =\r\n  | UnknownAsyncThunkPendingAction\r\n  | UnknownAsyncThunkRejectedAction\r\n  | UnknownAsyncThunkFulfilledAction\r\n\r\nexport type AnyAsyncThunk = {\r\n  pending: { match: (action: any) => action is any }\r\n  fulfilled: { match: (action: any) => action is any }\r\n  rejected: { match: (action: any) => action is any }\r\n}\r\n\r\nexport type ActionsFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  | ActionFromMatcher<T['pending']>\r\n  | ActionFromMatcher<T['fulfilled']>\r\n  | ActionFromMatcher<T['rejected']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator.\r\n *\r\n * @public\r\n */\r\nexport function isAsyncThunkAction(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isAsyncThunkAction<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is ActionsFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a thunk action\r\n * @public\r\n */\r\nexport function isAsyncThunkAction(\r\n  action: any\r\n): action is UnknownAsyncThunkAction\r\nexport function isAsyncThunkAction<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) =>\r\n      hasExpectedRequestMetadata(action, ['pending', 'fulfilled', 'rejected'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isAsyncThunkAction()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is ActionsFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = [] as any\r\n\r\n    for (const asyncThunk of asyncThunks) {\r\n      matchers.push(\r\n        asyncThunk.pending,\r\n        asyncThunk.rejected,\r\n        asyncThunk.fulfilled\r\n      )\r\n    }\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n", "import type { AbortSignalWithReason } from './types'\r\n\r\nexport const assertFunction: (\r\n  func: unknown,\r\n  expected: string\r\n) => asserts func is (...args: unknown[]) => unknown = (\r\n  func: unknown,\r\n  expected: string\r\n) => {\r\n  if (typeof func !== 'function') {\r\n    throw new TypeError(`${expected} is not a function`)\r\n  }\r\n}\r\n\r\nexport const noop = () => {}\r\n\r\nexport const catchRejection = <T>(\r\n  promise: Promise<T>,\r\n  onError = noop\r\n): Promise<T> => {\r\n  promise.catch(onError)\r\n\r\n  return promise\r\n}\r\n\r\nexport const addAbortSignalListener = (\r\n  abortSignal: AbortSignal,\r\n  callback: (evt: Event) => void\r\n) => {\r\n  abortSignal.addEventListener('abort', callback, { once: true })\r\n  return () => abortSignal.removeEventListener('abort', callback)\r\n}\r\n\r\n/**\r\n * Calls `abortController.abort(reason)` and patches `signal.reason`.\r\n * if it is not supported.\r\n *\r\n * At the time of writing `signal.reason` is available in FF chrome, edge node 17 and deno.\r\n * @param abortController\r\n * @param reason\r\n * @returns\r\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/reason\r\n */\r\nexport const abortControllerWithReason = <T>(\r\n  abortController: AbortController,\r\n  reason: T\r\n): void => {\r\n  type Consumer<T> = (val: T) => void\r\n\r\n  const signal = abortController.signal as AbortSignalWithReason<T>\r\n\r\n  if (signal.aborted) {\r\n    return\r\n  }\r\n\r\n  // Patch `reason` if necessary.\r\n  // - We use defineProperty here because reason is a getter of `AbortSignal.__proto__`.\r\n  // - We need to patch 'reason' before calling `.abort()` because listeners to the 'abort'\r\n  // event are are notified immediately.\r\n  if (!('reason' in signal)) {\r\n    Object.defineProperty(signal, 'reason', {\r\n      enumerable: true,\r\n      value: reason,\r\n      configurable: true,\r\n      writable: true,\r\n    })\r\n  }\r\n\r\n  ;(abortController.abort as Consumer<typeof reason>)(reason)\r\n}\r\n", "import type { SerializedError } from '@reduxjs/toolkit'\r\n\r\nconst task = 'task'\r\nconst listener = 'listener'\r\nconst completed = 'completed'\r\nconst cancelled = 'cancelled'\r\n\r\n/* TaskAbortError error codes  */\r\nexport const taskCancelled = `task-${cancelled}` as const\r\nexport const taskCompleted = `task-${completed}` as const\r\nexport const listenerCancelled = `${listener}-${cancelled}` as const\r\nexport const listenerCompleted = `${listener}-${completed}` as const\r\n\r\nexport class TaskAbortError implements SerializedError {\r\n  name = 'TaskAbortError'\r\n  message: string\r\n  constructor(public code: string | undefined) {\r\n    this.message = `${task} ${cancelled} (reason: ${code})`\r\n  }\r\n}\r\n", "import { TaskAbortError } from './exceptions'\r\nimport type { AbortSignalWithReason, TaskResult } from './types'\r\nimport { addAbortSignalListener, catchRejection, noop } from './utils'\r\n\r\n/**\r\n * Synchronously raises {@link TaskAbortError} if the task tied to the input `signal` has been cancelled.\r\n * @param signal\r\n * @param reason\r\n * @see {TaskAbortError}\r\n */\r\nexport const validateActive = (signal: AbortSignal): void => {\r\n  if (signal.aborted) {\r\n    throw new TaskAbortError((signal as AbortSignalWithReason<string>).reason)\r\n  }\r\n}\r\n\r\n/**\r\n * Generates a race between the promise(s) and the AbortSignal\r\n * This avoids `Promise.race()`-related memory leaks:\r\n * https://github.com/nodejs/node/issues/17469#issuecomment-349794909\r\n */\r\nexport function raceWithSignal<T>(\r\n  signal: AbortSignalWithReason<string>,\r\n  promise: Promise<T>\r\n): Promise<T> {\r\n  let cleanup = noop\r\n  return new Promise<T>((resolve, reject) => {\r\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason))\r\n\r\n    if (signal.aborted) {\r\n      notifyRejection()\r\n      return\r\n    }\r\n\r\n    cleanup = addAbortSignalListener(signal, notifyRejection)\r\n    promise.finally(() => cleanup()).then(resolve, reject)\r\n  }).finally(() => {\r\n    // after this point, replace `cleanup` with a noop, so there is no reference to `signal` any more\r\n    cleanup = noop\r\n  })\r\n}\r\n\r\n/**\r\n * Runs a task and returns promise that resolves to {@link TaskResult}.\r\n * Second argument is an optional `cleanUp` function that always runs after task.\r\n *\r\n * **Note:** `runTask` runs the executor in the next microtask.\r\n * @returns\r\n */\r\nexport const runTask = async <T>(\r\n  task: () => Promise<T>,\r\n  cleanUp?: () => void\r\n): Promise<TaskResult<T>> => {\r\n  try {\r\n    await Promise.resolve()\r\n    const value = await task()\r\n    return {\r\n      status: 'ok',\r\n      value,\r\n    }\r\n  } catch (error: any) {\r\n    return {\r\n      status: error instanceof TaskAbortError ? 'cancelled' : 'rejected',\r\n      error,\r\n    }\r\n  } finally {\r\n    cleanUp?.()\r\n  }\r\n}\r\n\r\n/**\r\n * Given an input `AbortSignal` and a promise returns another promise that resolves\r\n * as soon the input promise is provided or rejects as soon as\r\n * `AbortSignal.abort` is `true`.\r\n * @param signal\r\n * @returns\r\n */\r\nexport const createPause = <T>(signal: AbortSignal) => {\r\n  return (promise: Promise<T>): Promise<T> => {\r\n    return catchRejection(\r\n      raceWithSignal(signal, promise).then((output) => {\r\n        validateActive(signal)\r\n        return output\r\n      })\r\n    )\r\n  }\r\n}\r\n\r\n/**\r\n * Given an input `AbortSignal` and `timeoutMs` returns a promise that resolves\r\n * after `timeoutMs` or rejects as soon as `AbortSignal.abort` is `true`.\r\n * @param signal\r\n * @returns\r\n */\r\nexport const createDelay = (signal: AbortSignal) => {\r\n  const pause = createPause<void>(signal)\r\n  return (timeoutMs: number): Promise<void> => {\r\n    return pause(new Promise<void>((resolve) => setTimeout(resolve, timeoutMs)))\r\n  }\r\n}\r\n", "import type { Dispatch, AnyAction, MiddlewareAPI } from 'redux'\r\nimport type { ThunkDispatch } from 'redux-thunk'\r\nimport { createAction, isAction } from '../createAction'\r\nimport { nanoid } from '../nanoid'\r\n\r\nimport type {\r\n  ListenerMiddleware,\r\n  ListenerMiddlewareInstance,\r\n  AddListenerOverloads,\r\n  AnyListenerPredicate,\r\n  CreateListenerMiddlewareOptions,\r\n  TypedAddListener,\r\n  TypedCreateListenerEntry,\r\n  FallbackAddListenerOptions,\r\n  ListenerEntry,\r\n  ListenerErrorHandler,\r\n  UnsubscribeListener,\r\n  TakePattern,\r\n  ListenerErrorInfo,\r\n  ForkedTaskExecutor,\r\n  ForkedTask,\r\n  TypedRemoveListener,\r\n  TaskResult,\r\n  AbortSignalWithReason,\r\n  UnsubscribeListenerOptions,\r\n  ForkOptions,\r\n} from './types'\r\nimport {\r\n  abortControllerWithReason,\r\n  addAbortSignalListener,\r\n  assertFunction,\r\n  catchRejection,\r\n} from './utils'\r\nimport {\r\n  listenerCancelled,\r\n  listenerCompleted,\r\n  TaskAbortError,\r\n  taskCancelled,\r\n  taskCompleted,\r\n} from './exceptions'\r\nimport {\r\n  runTask,\r\n  validateActive,\r\n  createPause,\r\n  createDelay,\r\n  raceWithSignal,\r\n} from './task'\r\nexport { TaskAbortError } from './exceptions'\r\nexport type {\r\n  ListenerEffect,\r\n  ListenerMiddleware,\r\n  ListenerEffectAPI,\r\n  ListenerMiddlewareInstance,\r\n  CreateListenerMiddlewareOptions,\r\n  ListenerErrorHandler,\r\n  TypedStartListening,\r\n  TypedAddListener,\r\n  TypedStopListening,\r\n  TypedRemoveListener,\r\n  UnsubscribeListener,\r\n  UnsubscribeListenerOptions,\r\n  ForkedTaskExecutor,\r\n  ForkedTask,\r\n  ForkedTaskAPI,\r\n  AsyncTaskExecutor,\r\n  SyncTaskExecutor,\r\n  TaskCancelled,\r\n  TaskRejected,\r\n  TaskResolved,\r\n  TaskResult,\r\n} from './types'\r\n\r\n//Overly-aggressive byte-shaving\r\nconst { assign } = Object\r\n/**\r\n * @internal\r\n */\r\nconst INTERNAL_NIL_TOKEN = {} as const\r\n\r\nconst alm = 'listenerMiddleware' as const\r\n\r\nconst createFork = (\r\n  parentAbortSignal: AbortSignalWithReason<unknown>,\r\n  parentBlockingPromises: Promise<any>[]\r\n) => {\r\n  const linkControllers = (controller: AbortController) =>\r\n    addAbortSignalListener(parentAbortSignal, () =>\r\n      abortControllerWithReason(controller, parentAbortSignal.reason)\r\n    )\r\n\r\n  return <T>(\r\n    taskExecutor: ForkedTaskExecutor<T>,\r\n    opts?: ForkOptions\r\n  ): ForkedTask<T> => {\r\n    assertFunction(taskExecutor, 'taskExecutor')\r\n    const childAbortController = new AbortController()\r\n\r\n    linkControllers(childAbortController)\r\n\r\n    const result = runTask<T>(\r\n      async (): Promise<T> => {\r\n        validateActive(parentAbortSignal)\r\n        validateActive(childAbortController.signal)\r\n        const result = (await taskExecutor({\r\n          pause: createPause(childAbortController.signal),\r\n          delay: createDelay(childAbortController.signal),\r\n          signal: childAbortController.signal,\r\n        })) as T\r\n        validateActive(childAbortController.signal)\r\n        return result\r\n      },\r\n      () => abortControllerWithReason(childAbortController, taskCompleted)\r\n    )\r\n\r\n    if (opts?.autoJoin) {\r\n      parentBlockingPromises.push(result)\r\n    }\r\n\r\n    return {\r\n      result: createPause<TaskResult<T>>(parentAbortSignal)(result),\r\n      cancel() {\r\n        abortControllerWithReason(childAbortController, taskCancelled)\r\n      },\r\n    }\r\n  }\r\n}\r\n\r\nconst createTakePattern = <S>(\r\n  startListening: AddListenerOverloads<\r\n    UnsubscribeListener,\r\n    S,\r\n    Dispatch<AnyAction>\r\n  >,\r\n  signal: AbortSignal\r\n): TakePattern<S> => {\r\n  /**\r\n   * A function that takes a ListenerPredicate and an optional timeout,\r\n   * and resolves when either the predicate returns `true` based on an action\r\n   * state combination or when the timeout expires.\r\n   * If the parent listener is canceled while waiting, this will throw a\r\n   * TaskAbortError.\r\n   */\r\n  const take = async <P extends AnyListenerPredicate<S>>(\r\n    predicate: P,\r\n    timeout: number | undefined\r\n  ) => {\r\n    validateActive(signal)\r\n\r\n    // Placeholder unsubscribe function until the listener is added\r\n    let unsubscribe: UnsubscribeListener = () => {}\r\n\r\n    const tuplePromise = new Promise<[AnyAction, S, S]>((resolve, reject) => {\r\n      // Inside the Promise, we synchronously add the listener.\r\n      let stopListening = startListening({\r\n        predicate: predicate as any,\r\n        effect: (action, listenerApi): void => {\r\n          // One-shot listener that cleans up as soon as the predicate passes\r\n          listenerApi.unsubscribe()\r\n          // Resolve the promise with the same arguments the predicate saw\r\n          resolve([\r\n            action,\r\n            listenerApi.getState(),\r\n            listenerApi.getOriginalState(),\r\n          ])\r\n        },\r\n      })\r\n      unsubscribe = () => {\r\n        stopListening()\r\n        reject()\r\n      }\r\n    })\r\n\r\n    const promises: (Promise<null> | Promise<[AnyAction, S, S]>)[] = [\r\n      tuplePromise,\r\n    ]\r\n\r\n    if (timeout != null) {\r\n      promises.push(\r\n        new Promise<null>((resolve) => setTimeout(resolve, timeout, null))\r\n      )\r\n    }\r\n\r\n    try {\r\n      const output = await raceWithSignal(signal, Promise.race(promises))\r\n\r\n      validateActive(signal)\r\n      return output\r\n    } finally {\r\n      // Always clean up the listener\r\n      unsubscribe()\r\n    }\r\n  }\r\n\r\n  return ((predicate: AnyListenerPredicate<S>, timeout: number | undefined) =>\r\n    catchRejection(take(predicate, timeout))) as TakePattern<S>\r\n}\r\n\r\nconst getListenerEntryPropsFrom = (options: FallbackAddListenerOptions) => {\r\n  let { type, actionCreator, matcher, predicate, effect } = options\r\n\r\n  if (type) {\r\n    predicate = createAction(type).match\r\n  } else if (actionCreator) {\r\n    type = actionCreator!.type\r\n    predicate = actionCreator.match\r\n  } else if (matcher) {\r\n    predicate = matcher\r\n  } else if (predicate) {\r\n    // pass\r\n  } else {\r\n    throw new Error(\r\n      'Creating or removing a listener requires one of the known fields for matching an action'\r\n    )\r\n  }\r\n\r\n  assertFunction(effect, 'options.listener')\r\n\r\n  return { predicate, type, effect }\r\n}\r\n\r\n/** Accepts the possible options for creating a listener, and returns a formatted listener entry */\r\nexport const createListenerEntry: TypedCreateListenerEntry<unknown> = (\r\n  options: FallbackAddListenerOptions\r\n) => {\r\n  const { type, predicate, effect } = getListenerEntryPropsFrom(options)\r\n\r\n  const id = nanoid()\r\n  const entry: ListenerEntry<unknown> = {\r\n    id,\r\n    effect,\r\n    type,\r\n    predicate,\r\n    pending: new Set<AbortController>(),\r\n    unsubscribe: () => {\r\n      throw new Error('Unsubscribe not initialized')\r\n    },\r\n  }\r\n\r\n  return entry\r\n}\r\n\r\nconst cancelActiveListeners = (\r\n  entry: ListenerEntry<unknown, Dispatch<AnyAction>>\r\n) => {\r\n  entry.pending.forEach((controller) => {\r\n    abortControllerWithReason(controller, listenerCancelled)\r\n  })\r\n}\r\n\r\nconst createClearListenerMiddleware = (\r\n  listenerMap: Map<string, ListenerEntry>\r\n) => {\r\n  return () => {\r\n    listenerMap.forEach(cancelActiveListeners)\r\n\r\n    listenerMap.clear()\r\n  }\r\n}\r\n\r\n/**\r\n * Safely reports errors to the `errorHandler` provided.\r\n * Errors that occur inside `errorHandler` are notified in a new task.\r\n * Inspired by [rxjs reportUnhandledError](https://github.com/ReactiveX/rxjs/blob/6fafcf53dc9e557439b25debaeadfd224b245a66/src/internal/util/reportUnhandledError.ts)\r\n * @param errorHandler\r\n * @param errorToNotify\r\n */\r\nconst safelyNotifyError = (\r\n  errorHandler: ListenerErrorHandler,\r\n  errorToNotify: unknown,\r\n  errorInfo: ListenerErrorInfo\r\n): void => {\r\n  try {\r\n    errorHandler(errorToNotify, errorInfo)\r\n  } catch (errorHandlerError) {\r\n    // We cannot let an error raised here block the listener queue.\r\n    // The error raised here will be picked up by `window.onerror`, `process.on('error')` etc...\r\n    setTimeout(() => {\r\n      throw errorHandlerError\r\n    }, 0)\r\n  }\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport const addListener = createAction(\r\n  `${alm}/add`\r\n) as TypedAddListener<unknown>\r\n\r\n/**\r\n * @public\r\n */\r\nexport const clearAllListeners = createAction(`${alm}/removeAll`)\r\n\r\n/**\r\n * @public\r\n */\r\nexport const removeListener = createAction(\r\n  `${alm}/remove`\r\n) as TypedRemoveListener<unknown>\r\n\r\nconst defaultErrorHandler: ListenerErrorHandler = (...args: unknown[]) => {\r\n  console.error(`${alm}/error`, ...args)\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport function createListenerMiddleware<\r\n  S = unknown,\r\n  D extends Dispatch<AnyAction> = ThunkDispatch<S, unknown, AnyAction>,\r\n  ExtraArgument = unknown\r\n>(middlewareOptions: CreateListenerMiddlewareOptions<ExtraArgument> = {}) {\r\n  const listenerMap = new Map<string, ListenerEntry>()\r\n  const { extra, onError = defaultErrorHandler } = middlewareOptions\r\n\r\n  assertFunction(onError, 'onError')\r\n\r\n  const insertEntry = (entry: ListenerEntry) => {\r\n    entry.unsubscribe = () => listenerMap.delete(entry!.id)\r\n\r\n    listenerMap.set(entry.id, entry)\r\n    return (cancelOptions?: UnsubscribeListenerOptions) => {\r\n      entry.unsubscribe()\r\n      if (cancelOptions?.cancelActive) {\r\n        cancelActiveListeners(entry)\r\n      }\r\n    }\r\n  }\r\n\r\n  const findListenerEntry = (\r\n    comparator: (entry: ListenerEntry) => boolean\r\n  ): ListenerEntry | undefined => {\r\n    for (const entry of Array.from(listenerMap.values())) {\r\n      if (comparator(entry)) {\r\n        return entry\r\n      }\r\n    }\r\n\r\n    return undefined\r\n  }\r\n\r\n  const startListening = (options: FallbackAddListenerOptions) => {\r\n    let entry = findListenerEntry(\r\n      (existingEntry) => existingEntry.effect === options.effect\r\n    )\r\n\r\n    if (!entry) {\r\n      entry = createListenerEntry(options as any)\r\n    }\r\n\r\n    return insertEntry(entry)\r\n  }\r\n\r\n  const stopListening = (\r\n    options: FallbackAddListenerOptions & UnsubscribeListenerOptions\r\n  ): boolean => {\r\n    const { type, effect, predicate } = getListenerEntryPropsFrom(options)\r\n\r\n    const entry = findListenerEntry((entry) => {\r\n      const matchPredicateOrType =\r\n        typeof type === 'string'\r\n          ? entry.type === type\r\n          : entry.predicate === predicate\r\n\r\n      return matchPredicateOrType && entry.effect === effect\r\n    })\r\n\r\n    if (entry) {\r\n      entry.unsubscribe()\r\n      if (options.cancelActive) {\r\n        cancelActiveListeners(entry)\r\n      }\r\n    }\r\n\r\n    return !!entry\r\n  }\r\n\r\n  const notifyListener = async (\r\n    entry: ListenerEntry<unknown, Dispatch<AnyAction>>,\r\n    action: AnyAction,\r\n    api: MiddlewareAPI,\r\n    getOriginalState: () => S\r\n  ) => {\r\n    const internalTaskController = new AbortController()\r\n    const take = createTakePattern(\r\n      startListening,\r\n      internalTaskController.signal\r\n    )\r\n    const autoJoinPromises: Promise<any>[] = []\r\n\r\n    try {\r\n      entry.pending.add(internalTaskController)\r\n      await Promise.resolve(\r\n        entry.effect(\r\n          action,\r\n          // Use assign() rather than ... to avoid extra helper functions added to bundle\r\n          assign({}, api, {\r\n            getOriginalState,\r\n            condition: (\r\n              predicate: AnyListenerPredicate<any>,\r\n              timeout?: number\r\n            ) => take(predicate, timeout).then(Boolean),\r\n            take,\r\n            delay: createDelay(internalTaskController.signal),\r\n            pause: createPause<any>(internalTaskController.signal),\r\n            extra,\r\n            signal: internalTaskController.signal,\r\n            fork: createFork(internalTaskController.signal, autoJoinPromises),\r\n            unsubscribe: entry.unsubscribe,\r\n            subscribe: () => {\r\n              listenerMap.set(entry.id, entry)\r\n            },\r\n            cancelActiveListeners: () => {\r\n              entry.pending.forEach((controller, _, set) => {\r\n                if (controller !== internalTaskController) {\r\n                  abortControllerWithReason(controller, listenerCancelled)\r\n                  set.delete(controller)\r\n                }\r\n              })\r\n            },\r\n          })\r\n        )\r\n      )\r\n    } catch (listenerError) {\r\n      if (!(listenerError instanceof TaskAbortError)) {\r\n        safelyNotifyError(onError, listenerError, {\r\n          raisedBy: 'effect',\r\n        })\r\n      }\r\n    } finally {\r\n      await Promise.allSettled(autoJoinPromises)\r\n\r\n      abortControllerWithReason(internalTaskController, listenerCompleted) // Notify that the task has completed\r\n      entry.pending.delete(internalTaskController)\r\n    }\r\n  }\r\n\r\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap)\r\n\r\n  const middleware: ListenerMiddleware<S, D, ExtraArgument> =\r\n    (api) => (next) => (action) => {\r\n      if (!isAction(action)) {\r\n        // we only want to notify listeners for action objects\r\n        return next(action)\r\n      }\r\n\r\n      if (addListener.match(action)) {\r\n        return startListening(action.payload)\r\n      }\r\n\r\n      if (clearAllListeners.match(action)) {\r\n        clearListenerMiddleware()\r\n        return\r\n      }\r\n\r\n      if (removeListener.match(action)) {\r\n        return stopListening(action.payload)\r\n      }\r\n\r\n      // Need to get this state _before_ the reducer processes the action\r\n      let originalState: S | typeof INTERNAL_NIL_TOKEN = api.getState()\r\n\r\n      // `getOriginalState` can only be called synchronously.\r\n      // @see https://github.com/reduxjs/redux-toolkit/discussions/1648#discussioncomment-1932820\r\n      const getOriginalState = (): S => {\r\n        if (originalState === INTERNAL_NIL_TOKEN) {\r\n          throw new Error(\r\n            `${alm}: getOriginalState can only be called synchronously`\r\n          )\r\n        }\r\n\r\n        return originalState as S\r\n      }\r\n\r\n      let result: unknown\r\n\r\n      try {\r\n        // Actually forward the action to the reducer before we handle listeners\r\n        result = next(action)\r\n\r\n        if (listenerMap.size > 0) {\r\n          let currentState = api.getState()\r\n          // Work around ESBuild+TS transpilation issue\r\n          const listenerEntries = Array.from(listenerMap.values())\r\n          for (let entry of listenerEntries) {\r\n            let runListener = false\r\n\r\n            try {\r\n              runListener = entry.predicate(action, currentState, originalState)\r\n            } catch (predicateError) {\r\n              runListener = false\r\n\r\n              safelyNotifyError(onError, predicateError, {\r\n                raisedBy: 'predicate',\r\n              })\r\n            }\r\n\r\n            if (!runListener) {\r\n              continue\r\n            }\r\n\r\n            notifyListener(entry, action, api, getOriginalState)\r\n          }\r\n        }\r\n      } finally {\r\n        // Remove `originalState` store from this scope.\r\n        originalState = INTERNAL_NIL_TOKEN\r\n      }\r\n\r\n      return result\r\n    }\r\n\r\n  return {\r\n    middleware,\r\n    startListening,\r\n    stopListening,\r\n    clearListeners: clearListenerMiddleware,\r\n  } as ListenerMiddlewareInstance<S, D, ExtraArgument>\r\n}\r\n", "import type { StoreEnhancer } from 'redux'\r\n\r\nexport const SHOULD_AUTOBATCH = 'RTK_autoBatch'\r\n\r\nexport const prepareAutoBatched =\r\n  <T>() =>\r\n  (payload: T): { payload: T; meta: unknown } => ({\r\n    payload,\r\n    meta: { [SHOULD_AUTOBATCH]: true },\r\n  })\r\n\r\n// TODO Remove this in 2.0\r\n// Copied from https://github.com/feross/queue-microtask\r\nlet promise: Promise<any>\r\nconst queueMicrotaskShim =\r\n  typeof queueMicrotask === 'function'\r\n    ? queueMicrotask.bind(\r\n        typeof window !== 'undefined'\r\n          ? window\r\n          : typeof global !== 'undefined'\r\n          ? global\r\n          : globalThis\r\n      )\r\n    : // reuse resolved promise, and allocate it lazily\r\n      (cb: () => void) =>\r\n        (promise || (promise = Promise.resolve())).then(cb).catch((err: any) =>\r\n          setTimeout(() => {\r\n            throw err\r\n          }, 0)\r\n        )\r\n\r\nconst createQueueWithTimer = (timeout: number) => {\r\n  return (notify: () => void) => {\r\n    setTimeout(notify, timeout)\r\n  }\r\n}\r\n\r\n// requestAnimationFrame won't exist in SSR environments.\r\n// Fall back to a vague approximation just to keep from erroring.\r\nconst rAF =\r\n  typeof window !== 'undefined' && window.requestAnimationFrame\r\n    ? window.requestAnimationFrame\r\n    : createQueueWithTimer(10)\r\n\r\nexport type AutoBatchOptions =\r\n  | { type: 'tick' }\r\n  | { type: 'timer'; timeout: number }\r\n  | { type: 'raf' }\r\n  | { type: 'callback'; queueNotification: (notify: () => void) => void }\r\n\r\n/**\r\n * A Redux store enhancer that watches for \"low-priority\" actions, and delays\r\n * notifying subscribers until either the queued callback executes or the\r\n * next \"standard-priority\" action is dispatched.\r\n *\r\n * This allows dispatching multiple \"low-priority\" actions in a row with only\r\n * a single subscriber notification to the UI after the sequence of actions\r\n * is finished, thus improving UI re-render performance.\r\n *\r\n * Watches for actions with the `action.meta[SHOULD_AUTOBATCH]` attribute.\r\n * This can be added to `action.meta` manually, or by using the\r\n * `prepareAutoBatched` helper.\r\n *\r\n * By default, it will queue a notification for the end of the event loop tick.\r\n * However, you can pass several other options to configure the behavior:\r\n * - `{type: 'tick'}: queues using `queueMicrotask` (default)\r\n * - `{type: 'timer, timeout: number}`: queues using `setTimeout`\r\n * - `{type: 'raf'}`: queues using `requestAnimationFrame`\r\n * - `{type: 'callback', queueNotification: (notify: () => void) => void}: lets you provide your own callback\r\n *\r\n *\r\n */\r\nexport const autoBatchEnhancer =\r\n  (options: AutoBatchOptions = { type: 'raf' }): StoreEnhancer =>\r\n  (next) =>\r\n  (...args) => {\r\n    const store = next(...args)\r\n\r\n    let notifying = true\r\n    let shouldNotifyAtEndOfTick = false\r\n    let notificationQueued = false\r\n\r\n    const listeners = new Set<() => void>()\r\n\r\n    const queueCallback =\r\n      options.type === 'tick'\r\n        ? queueMicrotaskShim\r\n        : options.type === 'raf'\r\n        ? rAF\r\n        : options.type === 'callback'\r\n        ? options.queueNotification\r\n        : createQueueWithTimer(options.timeout)\r\n\r\n    const notifyListeners = () => {\r\n      // We're running at the end of the event loop tick.\r\n      // Run the real listener callbacks to actually update the UI.\r\n      notificationQueued = false\r\n      if (shouldNotifyAtEndOfTick) {\r\n        shouldNotifyAtEndOfTick = false\r\n        listeners.forEach((l) => l())\r\n      }\r\n    }\r\n\r\n    return Object.assign({}, store, {\r\n      // Override the base `store.subscribe` method to keep original listeners\r\n      // from running if we're delaying notifications\r\n      subscribe(listener: () => void) {\r\n        // Each wrapped listener will only call the real listener if\r\n        // the `notifying` flag is currently active when it's called.\r\n        // This lets the base store work as normal, while the actual UI\r\n        // update becomes controlled by this enhancer.\r\n        const wrappedListener: typeof listener = () => notifying && listener()\r\n        const unsubscribe = store.subscribe(wrappedListener)\r\n        listeners.add(listener)\r\n        return () => {\r\n          unsubscribe()\r\n          listeners.delete(listener)\r\n        }\r\n      },\r\n      // Override the base `store.dispatch` method so that we can check actions\r\n      // for the `shouldAutoBatch` flag and determine if batching is active\r\n      dispatch(action: any) {\r\n        try {\r\n          // If the action does _not_ have the `shouldAutoBatch` flag,\r\n          // we resume/continue normal notify-after-each-dispatch behavior\r\n          notifying = !action?.meta?.[SHOULD_AUTOBATCH]\r\n          // If a `notifyListeners` microtask was queued, you can't cancel it.\r\n          // Instead, we set a flag so that it's a no-op when it does run\r\n          shouldNotifyAtEndOfTick = !notifying\r\n          if (shouldNotifyAtEndOfTick) {\r\n            // We've seen at least 1 action with `SHOULD_AUTOBATCH`. Try to queue\r\n            // a microtask to notify listeners at the end of the event loop tick.\r\n            // Make sure we only enqueue this _once_ per tick.\r\n            if (!notificationQueued) {\r\n              notificationQueued = true\r\n              queueCallback(notifyListeners)\r\n            }\r\n          }\r\n          // Go ahead and process the action as usual, including reducers.\r\n          // If normal notification behavior is enabled, the store will notify\r\n          // all of its own listeners, and the wrapper callbacks above will\r\n          // see `notifying` is true and pass on to the real listener callbacks.\r\n          // If we're \"batching\" behavior, then the wrapped callbacks will\r\n          // bail out, causing the base store notification behavior to be no-ops.\r\n          return store.dispatch(action)\r\n        } finally {\r\n          // Assume we're back to normal behavior after each action\r\n          notifying = true\r\n        }\r\n      },\r\n    })\r\n  }\r\n", null]}