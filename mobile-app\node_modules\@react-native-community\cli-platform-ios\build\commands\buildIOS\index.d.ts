/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { Config } from '@react-native-community/cli-types';
import { BuildFlags } from './buildProject';
export interface FlagsT extends BuildFlags {
    configuration?: string;
    simulator?: string;
    device?: string | true;
    udid?: string;
    scheme?: string;
}
declare function buildIOS(_: Array<string>, ctx: Config, args: FlagsT): Promise<string | void>;
export declare const iosBuildOptions: ({
    name: string;
    description: string;
    default?: undefined;
    parse?: undefined;
} | {
    name: string;
    default: string | number;
    parse: NumberConstructor;
    description?: undefined;
} | {
    name: string;
    description: string;
    default: string | undefined;
    parse?: undefined;
} | {
    name: string;
    description: string;
    parse: (val: string) => string[];
    default?: undefined;
})[];
declare const _default: {
    name: string;
    description: string;
    func: typeof buildIOS;
    examples: {
        desc: string;
        cmd: string;
    }[];
    options: ({
        name: string;
        description: string;
        default?: undefined;
        parse?: undefined;
    } | {
        name: string;
        default: string | number;
        parse: NumberConstructor;
        description?: undefined;
    } | {
        name: string;
        description: string;
        default: string | undefined;
        parse?: undefined;
    } | {
        name: string;
        description: string;
        parse: (val: string) => string[];
        default?: undefined;
    })[];
};
export default _default;
//# sourceMappingURL=index.d.ts.map