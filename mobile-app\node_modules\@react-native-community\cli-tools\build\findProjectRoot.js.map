{"version": 3, "names": ["findProjectRoot", "cwd", "process", "packageLocation", "findUp", "sync", "CLIError", "path", "dirname"], "sources": ["../src/findProjectRoot.ts"], "sourcesContent": ["import findUp from 'find-up';\nimport path from 'path';\nimport {CLIError} from './errors';\n\n/**\n * Finds project root by looking for a closest `package.json`.\n */\nexport default function findProjectRoot(cwd = process.cwd()): string {\n  const packageLocation = findUp.sync('package.json', {cwd});\n  /**\n   * It is possible that `package.json` doesn't exist\n   * in the tree. In that case, we want to throw an error.\n   *\n   * When executing via `npx`, this will never happen as `npm`\n   * requires that file to be present in order to run.\n   */\n  if (!packageLocation) {\n    throw new CLIError(`\n      We couldn't find a package.json in your project.\n      Are you sure you are running it inside a React Native project?\n    `);\n  }\n\n  return path.dirname(packageLocation);\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAkC;AAElC;AACA;AACA;AACe,SAASA,eAAe,CAACC,GAAG,GAAGC,OAAO,CAACD,GAAG,EAAE,EAAU;EACnE,MAAME,eAAe,GAAGC,iBAAM,CAACC,IAAI,CAAC,cAAc,EAAE;IAACJ;EAAG,CAAC,CAAC;EAC1D;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACE,eAAe,EAAE;IACpB,MAAM,IAAIG,gBAAQ,CAAE;AACxB;AACA;AACA,KAAK,CAAC;EACJ;EAEA,OAAOC,eAAI,CAACC,OAAO,CAACL,eAAe,CAAC;AACtC"}