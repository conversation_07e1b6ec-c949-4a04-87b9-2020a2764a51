{"name": "@react-native-community/cli-debugger-ui", "version": "11.3.6", "license": "MIT", "main": "./build/middleware", "scripts": {"build": "yarn build:ui && yarn build:middleware", "build:ui": "parcel build --no-content-hash src/ui/index.html --out-dir build/ui --public-url '/debugger-ui'", "build:middleware": "tsc"}, "files": ["build", "!*.d.ts", "!*.map"], "devDependencies": {"@babel/core": "^7.6.4", "parcel-bundler": "^1.12.5"}, "dependencies": {"serve-static": "^1.13.1"}, "homepage": "https://github.com/react-native-community/cli/tree/master/packages/debugger-ui", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/debugger-ui"}, "gitHead": "ae61a11bda5d5c1b495f7bf5745123d8df8ffaef"}