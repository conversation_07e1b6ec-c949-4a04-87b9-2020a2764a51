{"name": "appdirsjs", "version": "1.2.7", "description": "OS-dependent application paths for cache, data and config directories", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/index.js", "dist/index.d.ts"], "repository": {"type": "git", "url": "https://github.com/codingjerk/appdirsjs.git"}, "devDependencies": {"@types/jest": "^26.0.15", "@typescript-eslint/eslint-plugin": "^4.5.0", "@typescript-eslint/parser": "^4.5.0", "eslint": "^7.11.0", "jest": "^26.6.1", "ts-jest": "^26.4.2", "typescript": "^4.0.3"}}