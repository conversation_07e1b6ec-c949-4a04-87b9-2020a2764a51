{"version": 3, "names": ["CommonActions", "React", "NavigationBuilderContext", "useNavigationCache", "state", "getState", "navigation", "setOptions", "router", "emitter", "stackRef", "useContext", "cache", "useMemo", "current", "actions", "actionCreators", "routes", "reduce", "acc", "route", "previous", "key", "emit", "rest", "dispatch", "thunk", "action", "source", "withStack", "callback", "isStackSet", "process", "env", "NODE_ENV", "Error", "stack", "undefined", "helpers", "Object", "keys", "name", "args", "create", "getParent", "id", "getId", "options", "o", "isFocused", "index"], "sourceRoot": "../../src", "sources": ["useNavigationCache.tsx"], "mappings": "AAAA,SACEA,aAAa,QAKR,2BAA2B;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB,MAAM,4BAA4B;AAmCjE;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkB,OAWb;EAAA,IAP3B;IACAC,KAAK;IACLC,QAAQ;IACRC,UAAU;IACVC,UAAU;IACVC,MAAM;IACNC;EACwB,CAAC;EACzB,MAAM;IAAEC;EAAS,CAAC,GAAGT,KAAK,CAACU,UAAU,CAACT,wBAAwB,CAAC;;EAE/D;EACA;EACA;EACA,MAAMU,KAAK,GAAGX,KAAK,CAACY,OAAO,CACzB,OAAO;IAAEC,OAAO,EAAE,CAAC;EAAqD,CAAC,CAAC;EAC1E;EACA,CAACT,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,MAAM,EAAEC,OAAO,CAAC,CACpD;EAED,MAAMM,OAAO,GAAG;IACd,GAAGP,MAAM,CAACQ,cAAc;IACxB,GAAGhB;EACL,CAAC;EAEDY,KAAK,CAACE,OAAO,GAAGV,KAAK,CAACa,MAAM,CAACC,MAAM,CAEjC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAChB,MAAMC,QAAQ,GAAGT,KAAK,CAACE,OAAO,CAACM,KAAK,CAACE,GAAG,CAAC;IAMzC,IAAID,QAAQ,EAAE;MACZ;MACAF,GAAG,CAACC,KAAK,CAACE,GAAG,CAAC,GAAGD,QAAQ;IAC3B,CAAC,MAAM;MACL;MACA,MAAM;QAAEE,IAAI;QAAE,GAAGC;MAAK,CAAC,GAAGlB,UAAU;MAEpC,MAAMmB,QAAQ,GAAIC,KAAY,IAAK;QACjC,MAAMC,MAAM,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACrB,QAAQ,EAAE,CAAC,GAAGqB,KAAK;QAEtE,IAAIC,MAAM,IAAI,IAAI,EAAE;UAClBrB,UAAU,CAACmB,QAAQ,CAAC;YAAEG,MAAM,EAAER,KAAK,CAACE,GAAG;YAAE,GAAGK;UAAO,CAAC,CAAC;QACvD;MACF,CAAC;MAED,MAAME,SAAS,GAAIC,QAAoB,IAAK;QAC1C,IAAIC,UAAU,GAAG,KAAK;QAEtB,IAAI;UACF,IACEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCxB,QAAQ,IACR,CAACA,QAAQ,CAACI,OAAO,EACjB;YACA;YACAJ,QAAQ,CAACI,OAAO,GAAG,IAAIqB,KAAK,EAAE,CAACC,KAAK;YACpCL,UAAU,GAAG,IAAI;UACnB;UAEAD,QAAQ,EAAE;QACZ,CAAC,SAAS;UACR,IAAIC,UAAU,IAAIrB,QAAQ,EAAE;YAC1BA,QAAQ,CAACI,OAAO,GAAGuB,SAAS;UAC9B;QACF;MACF,CAAC;MAED,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACzB,OAAO,CAAC,CAACG,MAAM,CACzC,CAACC,GAAG,EAAEsB,IAAI,KAAK;QACbtB,GAAG,CAACsB,IAAI,CAAC,GAAG;UAAA,kCAAIC,IAAI;YAAJA,IAAI;UAAA;UAAA,OAClBb,SAAS,CAAC;UACR;UACAJ,QAAQ,CAACV,OAAO,CAAC0B,IAAI,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAC,CACjC;QAAA;QAEH,OAAOvB,GAAG;MACZ,CAAC,EACD,CAAC,CAAC,CACH;MAEDA,GAAG,CAACC,KAAK,CAACE,GAAG,CAAC,GAAG;QACf,GAAGE,IAAI;QACP,GAAGc,OAAO;QACV;QACA,GAAI7B,OAAO,CAACkC,MAAM,CAACvB,KAAK,CAACE,GAAG,CAAS;QACrCG,QAAQ,EAAGC,KAAY,IAAKG,SAAS,CAAC,MAAMJ,QAAQ,CAACC,KAAK,CAAC,CAAC;QAC5DkB,SAAS,EAAGC,EAAW,IAAK;UAC1B,IAAIA,EAAE,KAAKR,SAAS,IAAIQ,EAAE,KAAKrB,IAAI,CAACsB,KAAK,EAAE,EAAE;YAC3C;YACA;YACA,OAAO3B,GAAG,CAACC,KAAK,CAACE,GAAG,CAAC;UACvB;UAEA,OAAOE,IAAI,CAACoB,SAAS,CAACC,EAAE,CAAC;QAC3B,CAAC;QACDtC,UAAU,EAAGwC,OAAe,IAC1BxC,UAAU,CAAEyC,CAAC,KAAM;UACjB,GAAGA,CAAC;UACJ,CAAC5B,KAAK,CAACE,GAAG,GAAG;YAAE,GAAG0B,CAAC,CAAC5B,KAAK,CAACE,GAAG,CAAC;YAAE,GAAGyB;UAAQ;QAC7C,CAAC,CAAC,CAAC;QACLE,SAAS,EAAE,MAAM;UACf,MAAM7C,KAAK,GAAGC,QAAQ,EAAE;UAExB,IAAID,KAAK,CAACa,MAAM,CAACb,KAAK,CAAC8C,KAAK,CAAC,CAAC5B,GAAG,KAAKF,KAAK,CAACE,GAAG,EAAE;YAC/C,OAAO,KAAK;UACd;;UAEA;UACA;UACA,OAAOhB,UAAU,GAAGA,UAAU,CAAC2C,SAAS,EAAE,GAAG,IAAI;QACnD;MACF,CAAC;IACH;IAEA,OAAO9B,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,OAAOP,KAAK,CAACE,OAAO;AACtB"}