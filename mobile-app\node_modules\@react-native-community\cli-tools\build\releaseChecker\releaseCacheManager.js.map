{"version": 3, "names": ["loadCache", "name", "cacheRaw", "fs", "readFileSync", "path", "resolve", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cache", "JSON", "parse", "e", "code", "saveCache", "logger", "debug", "undefined", "writeFileSync", "stringify", "legacyPath", "os", "homedir", "cachePath", "appDirs", "appName", "existsSync", "mkdirSync", "recursive", "get", "key", "set", "value"], "sources": ["../../src/releaseChecker/releaseCacheManager.ts"], "sourcesContent": ["import path from 'path';\nimport fs from 'fs';\nimport os from 'os';\nimport appDirs from 'appdirsjs';\nimport logger from '../logger';\n\ntype ReleaseCacheKey = 'eTag' | 'lastChecked' | 'latestVersion';\ntype Cache = {[key in ReleaseCacheKey]?: string};\n\nfunction loadCache(name: string): Cache | undefined {\n  try {\n    const cacheRaw = fs.readFileSync(\n      path.resolve(getCacheRootPath(), name),\n      'utf8',\n    );\n    const cache = JSON.parse(cacheRaw);\n    return cache;\n  } catch (e) {\n    if ((e as any).code === 'ENOENT') {\n      // Create cache file since it doesn't exist.\n      saveCache(name, {});\n    }\n    logger.debug('No release cache found');\n    return undefined;\n  }\n}\n\nfunction saveCache(name: string, cache: Cache) {\n  fs.writeFileSync(\n    path.resolve(getCacheRootPath(), name),\n    JSON.stringify(cache, null, 2),\n  );\n}\n\n/**\n * Returns the path string of `$HOME/.react-native-cli`.\n *\n * In case it doesn't exist, it will be created.\n */\nfunction getCacheRootPath() {\n  const legacyPath = path.resolve(os.homedir(), '.react-native-cli', 'cache');\n  const cachePath = appDirs({appName: 'react-native-cli', legacyPath}).cache;\n\n  if (!fs.existsSync(cachePath)) {\n    fs.mkdirSync(cachePath, {recursive: true});\n  }\n\n  return cachePath;\n}\n\nfunction get(name: string, key: ReleaseCacheKey): string | undefined {\n  const cache = loadCache(name);\n  if (cache) {\n    return cache[key];\n  }\n  return undefined;\n}\n\nfunction set(name: string, key: ReleaseCacheKey, value: string) {\n  const cache = loadCache(name);\n  if (cache) {\n    cache[key] = value;\n    saveCache(name, cache);\n  }\n}\n\nexport default {\n  get,\n  set,\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA+B;AAK/B,SAASA,SAAS,CAACC,IAAY,EAAqB;EAClD,IAAI;IACF,MAAMC,QAAQ,GAAGC,aAAE,CAACC,YAAY,CAC9BC,eAAI,CAACC,OAAO,CAACC,gBAAgB,EAAE,EAAEN,IAAI,CAAC,EACtC,MAAM,CACP;IACD,MAAMO,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACR,QAAQ,CAAC;IAClC,OAAOM,KAAK;EACd,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,IAAKA,CAAC,CAASC,IAAI,KAAK,QAAQ,EAAE;MAChC;MACAC,SAAS,CAACZ,IAAI,EAAE,CAAC,CAAC,CAAC;IACrB;IACAa,eAAM,CAACC,KAAK,CAAC,wBAAwB,CAAC;IACtC,OAAOC,SAAS;EAClB;AACF;AAEA,SAASH,SAAS,CAACZ,IAAY,EAAEO,KAAY,EAAE;EAC7CL,aAAE,CAACc,aAAa,CACdZ,eAAI,CAACC,OAAO,CAACC,gBAAgB,EAAE,EAAEN,IAAI,CAAC,EACtCQ,IAAI,CAACS,SAAS,CAACV,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAC/B;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASD,gBAAgB,GAAG;EAC1B,MAAMY,UAAU,GAAGd,eAAI,CAACC,OAAO,CAACc,aAAE,CAACC,OAAO,EAAE,EAAE,mBAAmB,EAAE,OAAO,CAAC;EAC3E,MAAMC,SAAS,GAAG,IAAAC,oBAAO,EAAC;IAACC,OAAO,EAAE,kBAAkB;IAAEL;EAAU,CAAC,CAAC,CAACX,KAAK;EAE1E,IAAI,CAACL,aAAE,CAACsB,UAAU,CAACH,SAAS,CAAC,EAAE;IAC7BnB,aAAE,CAACuB,SAAS,CAACJ,SAAS,EAAE;MAACK,SAAS,EAAE;IAAI,CAAC,CAAC;EAC5C;EAEA,OAAOL,SAAS;AAClB;AAEA,SAASM,GAAG,CAAC3B,IAAY,EAAE4B,GAAoB,EAAsB;EACnE,MAAMrB,KAAK,GAAGR,SAAS,CAACC,IAAI,CAAC;EAC7B,IAAIO,KAAK,EAAE;IACT,OAAOA,KAAK,CAACqB,GAAG,CAAC;EACnB;EACA,OAAOb,SAAS;AAClB;AAEA,SAASc,GAAG,CAAC7B,IAAY,EAAE4B,GAAoB,EAAEE,KAAa,EAAE;EAC9D,MAAMvB,KAAK,GAAGR,SAAS,CAACC,IAAI,CAAC;EAC7B,IAAIO,KAAK,EAAE;IACTA,KAAK,CAACqB,GAAG,CAAC,GAAGE,KAAK;IAClBlB,SAAS,CAACZ,IAAI,EAAEO,KAAK,CAAC;EACxB;AACF;AAAC,eAEc;EACboB,GAAG;EACHE;AACF,CAAC;AAAA"}