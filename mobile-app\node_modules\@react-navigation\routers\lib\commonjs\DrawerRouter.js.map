{"version": 3, "names": ["DrawerActions", "TabActions", "openDrawer", "type", "closeDrawer", "toggle<PERSON>rawer", "DrawerRouter", "defaultStatus", "rest", "router", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDrawerInHistory", "state", "Boolean", "history", "some", "it", "addDrawerToHistory", "status", "removeDrawerFromHistory", "filter", "getInitialState", "routeNames", "routeParamList", "routeGetIdList", "default", "stale", "key", "nanoid", "getRehydratedState", "partialState", "getStateForRouteFocus", "result", "getStateForAction", "action", "options", "index", "actionCreators"], "sourceRoot": "../../src", "sources": ["DrawerRouter.tsx"], "mappings": ";;;;;;;AAAA;AAEA;AAMqB;AAAA;AA4Dd,MAAMA,aAAa,GAAG;EAC3B,GAAGC,qBAAU;EACbC,UAAU,GAAqB;IAC7B,OAAO;MAAEC,IAAI,EAAE;IAAc,CAAC;EAChC,CAAC;EACDC,WAAW,GAAqB;IAC9B,OAAO;MAAED,IAAI,EAAE;IAAe,CAAC;EACjC,CAAC;EACDE,YAAY,GAAqB;IAC/B,OAAO;MAAEF,IAAI,EAAE;IAAgB,CAAC;EAClC;AACF,CAAC;AAAC;AAEa,SAASG,YAAY,OAMlC;EAAA,IANmC;IACnCC,aAAa,GAAG,QAAQ;IACxB,GAAGC;EACgB,CAAC;EAIpB,MAAMC,MAAM,GAAG,IAAAC,kBAAS,EAACF,IAAI,CAG5B;EAED,MAAMG,iBAAiB,GACrBC,KAEsD;IAAA;IAAA,OACnDC,OAAO,mBAACD,KAAK,CAACE,OAAO,mDAAb,eAAeC,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACb,IAAI,KAAK,QAAQ,CAAC,CAAC;EAAA;EAE/D,MAAMc,kBAAkB,GACtBL,KAA2C,IACF;IACzC,IAAID,iBAAiB,CAACC,KAAK,CAAC,EAAE;MAC5B,OAAOA,KAAK;IACd;IAEA,OAAO;MACL,GAAGA,KAAK;MACRE,OAAO,EAAE,CACP,GAAGF,KAAK,CAACE,OAAO,EAChB;QACEX,IAAI,EAAE,QAAQ;QACde,MAAM,EAAEX,aAAa,KAAK,MAAM,GAAG,QAAQ,GAAG;MAChD,CAAC;IAEL,CAAC;EACH,CAAC;EAED,MAAMY,uBAAuB,GAC3BP,KAA2C,IACF;IACzC,IAAI,CAACD,iBAAiB,CAACC,KAAK,CAAC,EAAE;MAC7B,OAAOA,KAAK;IACd;IAEA,OAAO;MACL,GAAGA,KAAK;MACRE,OAAO,EAAEF,KAAK,CAACE,OAAO,CAACM,MAAM,CAAEJ,EAAE,IAAKA,EAAE,CAACb,IAAI,KAAK,QAAQ;IAC5D,CAAC;EACH,CAAC;EAED,MAAMD,UAAU,GACdU,KAA2C,IACF;IACzC,IAAIL,aAAa,KAAK,MAAM,EAAE;MAC5B,OAAOY,uBAAuB,CAACP,KAAK,CAAC;IACvC;IAEA,OAAOK,kBAAkB,CAACL,KAAK,CAAC;EAClC,CAAC;EAED,MAAMR,WAAW,GACfQ,KAA2C,IACF;IACzC,IAAIL,aAAa,KAAK,MAAM,EAAE;MAC5B,OAAOU,kBAAkB,CAACL,KAAK,CAAC;IAClC;IAEA,OAAOO,uBAAuB,CAACP,KAAK,CAAC;EACvC,CAAC;EAED,OAAO;IACL,GAAGH,MAAM;IAETN,IAAI,EAAE,QAAQ;IAEdkB,eAAe,QAAiD;MAAA,IAAhD;QAAEC,UAAU;QAAEC,cAAc;QAAEC;MAAe,CAAC;MAC5D,MAAMZ,KAAK,GAAGH,MAAM,CAACY,eAAe,CAAC;QACnCC,UAAU;QACVC,cAAc;QACdC;MACF,CAAC,CAAC;MAEF,OAAO;QACL,GAAGZ,KAAK;QACRa,OAAO,EAAElB,aAAa;QACtBmB,KAAK,EAAE,KAAK;QACZvB,IAAI,EAAE,QAAQ;QACdwB,GAAG,EAAG,UAAS,IAAAC,iBAAM,GAAG;MAC1B,CAAC;IACH,CAAC;IAEDC,kBAAkB,CAChBC,YAAY,SAEZ;MAAA,IADA;QAAER,UAAU;QAAEC,cAAc;QAAEC;MAAe,CAAC;MAE9C,IAAIM,YAAY,CAACJ,KAAK,KAAK,KAAK,EAAE;QAChC,OAAOI,YAAY;MACrB;MAEA,IAAIlB,KAAK,GAAGH,MAAM,CAACoB,kBAAkB,CAACC,YAAY,EAAE;QAClDR,UAAU;QACVC,cAAc;QACdC;MACF,CAAC,CAAC;MAEF,IAAIb,iBAAiB,CAACmB,YAAY,CAAC,EAAE;QACnC;QACAlB,KAAK,GAAGO,uBAAuB,CAACP,KAAK,CAAC;QACtCA,KAAK,GAAGK,kBAAkB,CAACL,KAAK,CAAC;MACnC;MAEA,OAAO;QACL,GAAGA,KAAK;QACRa,OAAO,EAAElB,aAAa;QACtBJ,IAAI,EAAE,QAAQ;QACdwB,GAAG,EAAG,UAAS,IAAAC,iBAAM,GAAG;MAC1B,CAAC;IACH,CAAC;IAEDG,qBAAqB,CAACnB,KAAK,EAAEe,GAAG,EAAE;MAChC,MAAMK,MAAM,GAAGvB,MAAM,CAACsB,qBAAqB,CAACnB,KAAK,EAAEe,GAAG,CAAC;MAEvD,OAAOvB,WAAW,CAAC4B,MAAM,CAAC;IAC5B,CAAC;IAEDC,iBAAiB,CAACrB,KAAK,EAAEsB,MAAM,EAAEC,OAAO,EAAE;MACxC,QAAQD,MAAM,CAAC/B,IAAI;QACjB,KAAK,aAAa;UAChB,OAAOD,UAAU,CAACU,KAAK,CAAC;QAE1B,KAAK,cAAc;UACjB,OAAOR,WAAW,CAACQ,KAAK,CAAC;QAE3B,KAAK,eAAe;UAClB,IAAID,iBAAiB,CAACC,KAAK,CAAC,EAAE;YAC5B,OAAOO,uBAAuB,CAACP,KAAK,CAAC;UACvC;UAEA,OAAOK,kBAAkB,CAACL,KAAK,CAAC;QAElC,KAAK,SAAS;QACd,KAAK,UAAU;UAAE;YACf,MAAMoB,MAAM,GAAGvB,MAAM,CAACwB,iBAAiB,CAACrB,KAAK,EAAEsB,MAAM,EAAEC,OAAO,CAAC;YAE/D,IAAIH,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACI,KAAK,KAAKxB,KAAK,CAACwB,KAAK,EAAE;cAClD,OAAOhC,WAAW,CAAC4B,MAAM,CAAyC;YACpE;YAEA,OAAOA,MAAM;UACf;QAEA,KAAK,SAAS;UACZ,IAAIrB,iBAAiB,CAACC,KAAK,CAAC,EAAE;YAC5B,OAAOO,uBAAuB,CAACP,KAAK,CAAC;UACvC;UAEA,OAAOH,MAAM,CAACwB,iBAAiB,CAACrB,KAAK,EAAEsB,MAAM,EAAEC,OAAO,CAAC;QAEzD;UACE,OAAO1B,MAAM,CAACwB,iBAAiB,CAACrB,KAAK,EAAEsB,MAAM,EAAEC,OAAO,CAAC;MAAC;IAE9D,CAAC;IAEDE,cAAc,EAAErC;EAClB,CAAC;AACH"}