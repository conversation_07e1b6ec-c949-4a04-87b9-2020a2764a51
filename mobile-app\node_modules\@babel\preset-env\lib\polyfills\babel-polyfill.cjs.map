{"version": 3, "names": ["getImportSource", "getRequireSource", "isPolyfillSource", "require", "BABEL_POLYFILL_DEPRECATION", "NO_DIRECT_POLYFILL_IMPORT", "module", "exports", "template", "regenerator", "deprecated", "usage", "name", "visitor", "ImportDeclaration", "path", "src", "console", "warn", "replace", "remove", "replaceWithMultiple", "ast", "replaceWith", "Program", "get", "for<PERSON>ach", "bodyPath"], "sources": ["../../src/polyfills/babel-polyfill.cjs"], "sourcesContent": ["// TODO(Babel 8) Remove this file\nif (process.env.BABEL_8_BREAKING) {\n  throw new Error(\n    \"Internal Babel error: This file should only be loaded in Babel 7\",\n  );\n}\n\nconst {\n  getImportSource,\n  getRequireSource,\n  isPolyfillSource,\n} = require(\"./utils.cjs\");\n\nconst BABEL_POLYFILL_DEPRECATION = `\n  \\`@babel/polyfill\\` is deprecated. Please, use required parts of \\`core-js\\`\n  and \\`regenerator-runtime/runtime\\` separately`;\n\nconst NO_DIRECT_POLYFILL_IMPORT = `\n  When setting \\`useBuiltIns: 'usage'\\`, polyfills are automatically imported when needed.\n  Please remove the direct import of \\`SPECIFIER\\` or use \\`useBuiltIns: 'entry'\\` instead.`;\n\nmodule.exports = function ({ template }, { regenerator, deprecated, usage }) {\n  return {\n    name: \"preset-env/replace-babel-polyfill\",\n    visitor: {\n      ImportDeclaration(path) {\n        const src = getImportSource(path);\n        if (usage && isPolyfillSource(src)) {\n          console.warn(NO_DIRECT_POLYFILL_IMPORT.replace(\"SPECIFIER\", src));\n          if (!deprecated) path.remove();\n        } else if (src === \"@babel/polyfill\") {\n          if (deprecated) {\n            console.warn(BABEL_POLYFILL_DEPRECATION);\n          } else if (regenerator) {\n            path.replaceWithMultiple(template.ast`\n              import \"core-js\";\n              import \"regenerator-runtime/runtime.js\";\n            `);\n          } else {\n            path.replaceWith(template.ast`\n              import \"core-js\";\n            `);\n          }\n        }\n      },\n      Program(path) {\n        path.get(\"body\").forEach(bodyPath => {\n          const src = getRequireSource(bodyPath);\n          if (usage && isPolyfillSource(src)) {\n            console.warn(NO_DIRECT_POLYFILL_IMPORT.replace(\"SPECIFIER\", src));\n            if (!deprecated) bodyPath.remove();\n          } else if (src === \"@babel/polyfill\") {\n            if (deprecated) {\n              console.warn(BABEL_POLYFILL_DEPRECATION);\n            } else if (regenerator) {\n              bodyPath.replaceWithMultiple(template.ast`\n                require(\"core-js\");\n                require(\"regenerator-runtime/runtime.js\");\n              `);\n            } else {\n              bodyPath.replaceWith(template.ast`\n                require(\"core-js\");\n              `);\n            }\n          }\n        });\n      },\n    },\n  };\n};\n"], "mappings": ";AAOA,MAAM;EACJA,eAAe;EACfC,gBAAgB;EAChBC;AACF,CAAC,GAAGC,OAAO,CAAC,aAAa,CAAC;AAE1B,MAAMC,0BAA0B,GAAG;AACnC;AACA,iDAAiD;AAEjD,MAAMC,yBAAyB,GAAG;AAClC;AACA,4FAA4F;AAE5FC,MAAM,CAACC,OAAO,GAAG,UAAU;EAAEC;AAAS,CAAC,EAAE;EAAEC,WAAW;EAAEC,UAAU;EAAEC;AAAM,CAAC,EAAE;EAC3E,OAAO;IACLC,IAAI,EAAE,mCAAmC;IACzCC,OAAO,EAAE;MACPC,iBAAiBA,CAACC,IAAI,EAAE;QACtB,MAAMC,GAAG,GAAGhB,eAAe,CAACe,IAAI,CAAC;QACjC,IAAIJ,KAAK,IAAIT,gBAAgB,CAACc,GAAG,CAAC,EAAE;UAClCC,OAAO,CAACC,IAAI,CAACb,yBAAyB,CAACc,OAAO,CAAC,WAAW,EAAEH,GAAG,CAAC,CAAC;UACjE,IAAI,CAACN,UAAU,EAAEK,IAAI,CAACK,MAAM,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIJ,GAAG,KAAK,iBAAiB,EAAE;UACpC,IAAIN,UAAU,EAAE;YACdO,OAAO,CAACC,IAAI,CAACd,0BAA0B,CAAC;UAC1C,CAAC,MAAM,IAAIK,WAAW,EAAE;YACtBM,IAAI,CAACM,mBAAmB,CAACb,QAAQ,CAACc,GAAG;AACjD;AACA;AACA,aAAa,CAAC;UACJ,CAAC,MAAM;YACLP,IAAI,CAACQ,WAAW,CAACf,QAAQ,CAACc,GAAG;AACzC;AACA,aAAa,CAAC;UACJ;QACF;MACF,CAAC;MACDE,OAAOA,CAACT,IAAI,EAAE;QACZA,IAAI,CAACU,GAAG,CAAC,MAAM,CAAC,CAACC,OAAO,CAACC,QAAQ,IAAI;UACnC,MAAMX,GAAG,GAAGf,gBAAgB,CAAC0B,QAAQ,CAAC;UACtC,IAAIhB,KAAK,IAAIT,gBAAgB,CAACc,GAAG,CAAC,EAAE;YAClCC,OAAO,CAACC,IAAI,CAACb,yBAAyB,CAACc,OAAO,CAAC,WAAW,EAAEH,GAAG,CAAC,CAAC;YACjE,IAAI,CAACN,UAAU,EAAEiB,QAAQ,CAACP,MAAM,CAAC,CAAC;UACpC,CAAC,MAAM,IAAIJ,GAAG,KAAK,iBAAiB,EAAE;YACpC,IAAIN,UAAU,EAAE;cACdO,OAAO,CAACC,IAAI,CAACd,0BAA0B,CAAC;YAC1C,CAAC,MAAM,IAAIK,WAAW,EAAE;cACtBkB,QAAQ,CAACN,mBAAmB,CAACb,QAAQ,CAACc,GAAG;AACvD;AACA;AACA,eAAe,CAAC;YACJ,CAAC,MAAM;cACLK,QAAQ,CAACJ,WAAW,CAACf,QAAQ,CAACc,GAAG;AAC/C;AACA,eAAe,CAAC;YACJ;UACF;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC;AACH,CAAC", "ignoreList": []}