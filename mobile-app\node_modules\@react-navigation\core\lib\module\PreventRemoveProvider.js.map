{"version": 3, "names": ["nanoid", "React", "useLatestCallback", "NavigationHelpersContext", "NavigationRouteContext", "PreventRemoveContext", "transformPreventedRoutes", "preventedRoutesMap", "preventedRoutesToTransform", "values", "preventedRoutes", "reduce", "acc", "routeKey", "preventRemove", "PreventRemoveProvider", "children", "parentId", "useState", "setPreventedRoutesMap", "Map", "navigation", "useContext", "route", "preventRemoveContextValue", "setParentPrevented", "setPreventRemove", "id", "getState", "routes", "every", "key", "Error", "prevPrevented", "get", "nextPrevented", "set", "delete", "isPrevented", "some", "useEffect", "undefined", "value", "useMemo"], "sourceRoot": "../../src", "sources": ["PreventRemoveProvider.tsx"], "mappings": "AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,oBAAoB,MAA2B,wBAAwB;AAc9E;AACA;AACA;AACA,MAAMC,wBAAwB,GAC5BC,kBAAsC,IAClB;EACpB,MAAMC,0BAA0B,GAAG,CAAC,GAAGD,kBAAkB,CAACE,MAAM,EAAE,CAAC;EAEnE,MAAMC,eAAe,GAAGF,0BAA0B,CAACG,MAAM,CACvD,CAACC,GAAG,WAAkC;IAAA;IAAA,IAAhC;MAAEC,QAAQ;MAAEC;IAAc,CAAC;IAC/BF,GAAG,CAACC,QAAQ,CAAC,GAAG;MACdC,aAAa,EAAE,kBAAAF,GAAG,CAACC,QAAQ,CAAC,kDAAb,cAAeC,aAAa,KAAIA;IACjD,CAAC;IACD,OAAOF,GAAG;EACZ,CAAC,EACD,CAAC,CAAC,CACH;EAED,OAAOF,eAAe;AACxB,CAAC;;AAED;AACA;AACA;AACA,eAAe,SAASK,qBAAqB,QAAsB;EAAA,IAArB;IAAEC;EAAgB,CAAC;EAC/D,MAAM,CAACC,QAAQ,CAAC,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,MAAMlB,MAAM,EAAE,CAAC;EACjD,MAAM,CAACO,kBAAkB,EAAEY,qBAAqB,CAAC,GAC/ClB,KAAK,CAACiB,QAAQ,CAAqB,IAAIE,GAAG,EAAE,CAAC;EAE/C,MAAMC,UAAU,GAAGpB,KAAK,CAACqB,UAAU,CAACnB,wBAAwB,CAAC;EAC7D,MAAMoB,KAAK,GAAGtB,KAAK,CAACqB,UAAU,CAAClB,sBAAsB,CAAC;EAEtD,MAAMoB,yBAAyB,GAAGvB,KAAK,CAACqB,UAAU,CAACjB,oBAAoB,CAAC;EACxE;EACA,MAAMoB,kBAAkB,GAAGD,yBAAyB,aAAzBA,yBAAyB,uBAAzBA,yBAAyB,CAAEE,gBAAgB;EAEtE,MAAMA,gBAAgB,GAAGxB,iBAAiB,CACxC,CAACyB,EAAU,EAAEd,QAAgB,EAAEC,aAAsB,KAAW;IAC9D,IACEA,aAAa,KACZO,UAAU,IAAI,IAAI,IACjBA,UAAU,aAAVA,UAAU,eAAVA,UAAU,CACNO,QAAQ,EAAE,CACXC,MAAM,CAACC,KAAK,CAAEP,KAAK,IAAKA,KAAK,CAACQ,GAAG,KAAKlB,QAAQ,CAAC,CAAC,EACrD;MACA,MAAM,IAAImB,KAAK,CACZ,sCAAqCnB,QAAS,+CAA8C,CAC9F;IACH;IAEAM,qBAAqB,CAAEc,aAAa,IAAK;MAAA;MACvC;MACA,IACEpB,QAAQ,4BAAKoB,aAAa,CAACC,GAAG,CAACP,EAAE,CAAC,uDAArB,mBAAuBd,QAAQ,KAC5CC,aAAa,6BAAKmB,aAAa,CAACC,GAAG,CAACP,EAAE,CAAC,wDAArB,oBAAuBb,aAAa,GACtD;QACA,OAAOmB,aAAa;MACtB;MAEA,MAAME,aAAa,GAAG,IAAIf,GAAG,CAACa,aAAa,CAAC;MAE5C,IAAInB,aAAa,EAAE;QACjBqB,aAAa,CAACC,GAAG,CAACT,EAAE,EAAE;UACpBd,QAAQ;UACRC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLqB,aAAa,CAACE,MAAM,CAACV,EAAE,CAAC;MAC1B;MAEA,OAAOQ,aAAa;IACtB,CAAC,CAAC;EACJ,CAAC,CACF;EAED,MAAMG,WAAW,GAAG,CAAC,GAAG/B,kBAAkB,CAACE,MAAM,EAAE,CAAC,CAAC8B,IAAI,CACvD;IAAA,IAAC;MAAEzB;IAAc,CAAC;IAAA,OAAKA,aAAa;EAAA,EACrC;EAEDb,KAAK,CAACuC,SAAS,CAAC,MAAM;IACpB,IAAI,CAAAjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,GAAG,MAAKU,SAAS,IAAIhB,kBAAkB,KAAKgB,SAAS,EAAE;MAChE;MACA;MACAhB,kBAAkB,CAACR,QAAQ,EAAEM,KAAK,CAACQ,GAAG,EAAEO,WAAW,CAAC;MACpD,OAAO,MAAM;QACXb,kBAAkB,CAACR,QAAQ,EAAEM,KAAK,CAACQ,GAAG,EAAE,KAAK,CAAC;MAChD,CAAC;IACH;IAEA;EACF,CAAC,EAAE,CAACd,QAAQ,EAAEqB,WAAW,EAAEf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,GAAG,EAAEN,kBAAkB,CAAC,CAAC;EAE3D,MAAMiB,KAAK,GAAGzC,KAAK,CAAC0C,OAAO,CACzB,OAAO;IACLjB,gBAAgB;IAChBhB,eAAe,EAAEJ,wBAAwB,CAACC,kBAAkB;EAC9D,CAAC,CAAC,EACF,CAACmB,gBAAgB,EAAEnB,kBAAkB,CAAC,CACvC;EAED,oBACE,oBAAC,oBAAoB,CAAC,QAAQ;IAAC,KAAK,EAAEmC;EAAM,GACzC1B,QAAQ,CACqB;AAEpC"}