var e,t,n=this&&this.__generator||function(e,t){var n,r,i,a,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function o(a){return function(o){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return u.label++,{value:a[1],done:!1};case 5:u.label++,r=a[1],a=[0];continue;case 7:a=u.ops.pop(),u.trys.pop();continue;default:if(!((i=(i=u.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){u=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){u.label=a[1];break}if(6===a[0]&&u.label<i[1]){u.label=i[1],i=a;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(a);break}i[2]&&u.ops.pop(),u.trys.pop();continue}a=t.call(e,u)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,o])}}},r=this&&this.__spreadArray||function(e,t){for(var n=0,r=t.length,i=e.length;n<r;n++,i++)e[i]=t[n];return e},i=Object.create,a=Object.defineProperty,u=Object.defineProperties,o=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyDescriptors,c=Object.getOwnPropertyNames,l=Object.getOwnPropertySymbols,d=Object.getPrototypeOf,f=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable,h=function(e,t,n){return t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},v=function(e,t){for(var n in t||(t={}))f.call(t,n)&&h(e,n,t[n]);if(l)for(var r=0,i=l(t);r<i.length;r++)p.call(t,n=i[r])&&h(e,n,t[n]);return e},y=function(e,t){return u(e,s(t))},m=function(e){return a(e,"__esModule",{value:!0})},g=function(e,t){var n={};for(var r in e)f.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&l)for(var i=0,a=l(e);i<a.length;i++)t.indexOf(r=a[i])<0&&p.call(e,r)&&(n[r]=e[r]);return n},b=function(e){return function(e,t,n){if(t&&"object"==typeof t||"function"==typeof t)for(var r=function(r){f.call(e,r)||"default"===r||a(e,r,{get:function(){return t[r]},enumerable:!(n=o(t,r))||n.enumerable})},i=0,u=c(t);i<u.length;i++)r(u[i]);return e}(m(a(null!=e?i(d(e)):{},"default",e&&e.__esModule&&"default"in e?{get:function(){return e.default},enumerable:!0}:{value:e,enumerable:!0})),e)},q=function(e,t,n){return new Promise((function(r,i){var a=function(e){try{o(n.next(e))}catch(e){i(e)}},u=function(e){try{o(n.throw(e))}catch(e){i(e)}},o=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(a,u)};o((n=n.apply(e,t)).next())}))};m(exports),function(e,t){for(var n in t)a(e,n,{get:t[n],enumerable:!0})}(exports,{QueryStatus:function(){return e},buildCreateApi:function(){return ge},copyWithStructuralSharing:function(){return A},coreModule:function(){return Ke},coreModuleName:function(){return Ne},createApi:function(){return Ee},defaultSerializeQueryArgs:function(){return ve},fakeBaseQuery:function(){return be},fetchBaseQuery:function(){return x},retry:function(){return I},setupListeners:function(){return F},skipSelector:function(){return ce},skipToken:function(){return se}}),(t=e||(e={})).uninitialized="uninitialized",t.pending="pending",t.fulfilled="fulfilled",t.rejected="rejected";var S=function(e){return[].concat.apply([],e)},O=b(require("@reduxjs/toolkit")).isPlainObject;function A(e,t){if(e===t||!(O(e)&&O(t)||Array.isArray(e)&&Array.isArray(t)))return t;for(var n=Object.keys(t),r=Object.keys(e),i=n.length===r.length,a=Array.isArray(t)?[]:{},u=0,o=n;u<o.length;u++){var s=o[u];a[s]=A(e[s],t[s]),i&&(i=e[s]===a[s])}return i?e:a}var T=b(require("@reduxjs/toolkit")),R=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return fetch.apply(void 0,e)},j=function(e){return e.status>=200&&e.status<=299},w=function(e){return/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"")};function k(e){if(!(0,T.isPlainObject)(e))return e;for(var t=v({},e),n=0,r=Object.entries(t);n<r.length;n++){var i=r[n];void 0===i[1]&&delete t[i[0]]}return t}function x(e){var t=this;void 0===e&&(e={});var r=e.baseUrl,i=e.prepareHeaders,a=void 0===i?function(e){return e}:i,u=e.fetchFn,o=void 0===u?R:u,s=e.paramsSerializer,c=e.isJsonContentType,l=void 0===c?w:c,d=e.jsonContentType,f=void 0===d?"application/json":d,p=e.jsonReplacer,h=e.timeout,m=e.responseHandler,b=e.validateStatus,S=g(e,["baseUrl","prepareHeaders","fetchFn","paramsSerializer","isJsonContentType","jsonContentType","jsonReplacer","timeout","responseHandler","validateStatus"]);return"undefined"==typeof fetch&&o===R&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),function(e,i){return q(t,null,(function(){var t,u,c,d,q,A,R,w,x,P,Q,C,I,M,D,N,K,E,_,F,z,U,B,L,W,H,J,V,G,Y,$,X,Z,ee,te,ne;return n(this,(function(n){switch(n.label){case 0:return t=i.signal,u=i.getState,c=i.extra,d=i.endpoint,q=i.forced,A=i.type,x=(w="string"==typeof e?{url:e}:e).url,Q=void 0===(P=w.headers)?new Headers(S.headers):P,I=void 0===(C=w.params)?void 0:C,D=void 0===(M=w.responseHandler)?null!=m?m:"json":M,K=void 0===(N=w.validateStatus)?null!=b?b:j:N,_=void 0===(E=w.timeout)?h:E,F=g(w,["url","headers","params","responseHandler","validateStatus","timeout"]),z=v(y(v({},S),{signal:t}),F),Q=new Headers(k(Q)),U=z,[4,a(Q,{getState:u,extra:c,endpoint:d,forced:q,type:A})];case 1:U.headers=n.sent()||Q,B=function(e){return"object"==typeof e&&((0,T.isPlainObject)(e)||Array.isArray(e)||"function"==typeof e.toJSON)},!z.headers.has("content-type")&&B(z.body)&&z.headers.set("content-type",f),B(z.body)&&l(z.headers)&&(z.body=JSON.stringify(z.body,p)),I&&(L=~x.indexOf("?")?"&":"?",W=s?s(I):new URLSearchParams(k(I)),x+=L+W),x=function(e,t){if(!e)return t;if(!t)return e;if(function(e){return new RegExp("(^|:)//").test(e)}(t))return t;var n=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=function(e){return e.replace(/\/$/,"")}(e),""+e+n+function(e){return e.replace(/^\//,"")}(t)}(r,x),H=new Request(x,z),J=new Request(x,z),R={request:J},G=!1,Y=_&&setTimeout((function(){G=!0,i.abort()}),_),n.label=2;case 2:return n.trys.push([2,4,5,6]),[4,o(H)];case 3:return V=n.sent(),[3,6];case 4:return $=n.sent(),[2,{error:{status:G?"TIMEOUT_ERROR":"FETCH_ERROR",error:String($)},meta:R}];case 5:return Y&&clearTimeout(Y),[7];case 6:X=V.clone(),R.response=X,ee="",n.label=7;case 7:return n.trys.push([7,9,,10]),[4,Promise.all([O(V,D).then((function(e){return Z=e}),(function(e){return te=e})),X.text().then((function(e){return ee=e}),(function(){}))])];case 8:if(n.sent(),te)throw te;return[3,10];case 9:return ne=n.sent(),[2,{error:{status:"PARSING_ERROR",originalStatus:V.status,data:ee,error:String(ne)},meta:R}];case 10:return[2,K(V,Z)?{data:Z,meta:R}:{error:{status:V.status,data:Z},meta:R}]}}))}))};function O(e,t){return q(this,null,(function(){var r;return n(this,(function(n){switch(n.label){case 0:return"function"==typeof t?[2,t(e)]:("content-type"===t&&(t=l(e.headers)?"json":"text"),"json"!==t?[3,2]:[4,e.text()]);case 1:return[2,(r=n.sent()).length?JSON.parse(r):null];case 2:return[2,e.text()]}}))}))}}var P=function(e,t){void 0===t&&(t=void 0),this.value=e,this.meta=t};function Q(e,t){return void 0===e&&(e=0),void 0===t&&(t=5),q(this,null,(function(){var r,i;return n(this,(function(n){switch(n.label){case 0:return r=Math.min(e,t),i=~~((Math.random()+.4)*(300<<r)),[4,new Promise((function(e){return setTimeout((function(t){return e(t)}),i)}))];case 1:return n.sent(),[2]}}))}))}var C={},I=Object.assign((function(e,t){return function(r,i,a){return q(void 0,null,(function(){var u,o,s,c,l,d,f;return n(this,(function(n){switch(n.label){case 0:u=[5,(t||C).maxRetries,(a||C).maxRetries].filter((function(e){return void 0!==e})),o=u.slice(-1)[0],s=function(e,t,n){return n.attempt<=o},c=v(v({maxRetries:o,backoff:Q,retryCondition:s},t),a),l=0,n.label=1;case 1:n.label=2;case 2:return n.trys.push([2,4,,6]),[4,e(r,i,a)];case 3:if((d=n.sent()).error)throw new P(d);return[2,d];case 4:if(f=n.sent(),l++,f.throwImmediately){if(f instanceof P)return[2,f.value];throw f}return f instanceof P&&!c.retryCondition(f.value.error,r,{attempt:l,baseQueryApi:i,extraOptions:a})?[2,f.value]:[4,c.backoff(l,c.maxRetries)];case 5:return n.sent(),[3,6];case 6:return[3,1];case 7:return[2]}}))}))}}),{fail:function(e){throw Object.assign(new P({error:e}),{throwImmediately:!0})}}),M=b(require("@reduxjs/toolkit")),D=(0,M.createAction)("__rtkq/focused"),N=(0,M.createAction)("__rtkq/unfocused"),K=(0,M.createAction)("__rtkq/online"),E=(0,M.createAction)("__rtkq/offline"),_=!1;function F(e,t){return t?t(e,{onFocus:D,onFocusLost:N,onOffline:E,onOnline:K}):(n=function(){return e(D())},r=function(){return e(K())},i=function(){return e(E())},a=function(){"visible"===window.document.visibilityState?n():e(N())},_||"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("visibilitychange",a,!1),window.addEventListener("focus",n,!1),window.addEventListener("online",r,!1),window.addEventListener("offline",i,!1),_=!0),function(){window.removeEventListener("focus",n),window.removeEventListener("visibilitychange",a),window.removeEventListener("online",r),window.removeEventListener("offline",i),_=!1});var n,r,i,a}var z,U,B=b(require("@reduxjs/toolkit"));function L(e){return e.type===z.query}function W(e,t,n,r,i,a){return"function"==typeof e?e(t,n,r,i).map(H).map(a):Array.isArray(e)?e.map(H).map(a):[]}function H(e){return"string"==typeof e?{type:e}:e}(U=z||(z={})).query="query",U.mutation="mutation";var J=b(require("@reduxjs/toolkit"));function V(e){return null!=e}var G=Symbol("forceQueryFn"),Y=function(e){return"function"==typeof e[G]},$=b(require("@reduxjs/toolkit")),X=b(require("immer")),Z=b(require("@reduxjs/toolkit"));function ee(e){return e}function te(e,t,n,r){return W(n[e.meta.arg.endpointName][t],(0,$.isFulfilled)(e)?e.payload:void 0,(0,$.isRejectedWithValue)(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,r)}var ne=b(require("immer")),re=b(require("immer"));function ie(e,t,n){var r=e[t];r&&n(r)}function ae(e){var t;return null!=(t="arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)?t:e.requestId}function ue(e,t,n){var r=e[ae(t)];r&&n(r)}var oe={},se=Symbol.for("RTKQ/skipToken"),ce=se,le={status:e.uninitialized},de=(0,B.createNextState)(le,(function(){})),fe=(0,B.createNextState)(le,(function(){})),pe=b(require("@reduxjs/toolkit")),he=WeakMap?new WeakMap:void 0,ve=function(e){var t=e.endpointName,n=e.queryArgs,r="",i=null==he?void 0:he.get(n);if("string"==typeof i)r=i;else{var a=JSON.stringify(n,(function(e,t){return(0,pe.isPlainObject)(t)?Object.keys(t).sort().reduce((function(e,n){return e[n]=t[n],e}),{}):t}));(0,pe.isPlainObject)(n)&&(null==he||he.set(n,a)),r=a}return t+"("+r+")"},ye=b(require("@reduxjs/toolkit")),me=b(require("reselect"));function ge(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){var n=(0,me.defaultMemoize)((function(e){var n,r;return null==(r=t.extractRehydrationInfo)?void 0:r.call(t,e,{reducerPath:null!=(n=t.reducerPath)?n:"api"})})),i=y(v({reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1},t),{extractRehydrationInfo:n,serializeQueryArgs:function(e){var n=ve;if("serializeQueryArgs"in e.endpointDefinition){var r=e.endpointDefinition.serializeQueryArgs;n=function(e){var t=r(e);return"string"==typeof t?t:ve(y(v({},e),{queryArgs:t}))}}else t.serializeQueryArgs&&(n=t.serializeQueryArgs);return n(e)},tagTypes:r([],t.tagTypes||[])}),a={endpointDefinitions:{},batch:function(e){e()},apiUid:(0,ye.nanoid)(),extractRehydrationInfo:n,hasRehydrationInfo:(0,me.defaultMemoize)((function(e){return null!=n(e)}))},u={injectEndpoints:function(e){for(var t=e.endpoints({query:function(e){return y(v({},e),{type:z.query})},mutation:function(e){return y(v({},e),{type:z.mutation})}}),n=0,r=Object.entries(t);n<r.length;n++){var i=r[n],s=i[0],c=i[1];if(e.overrideExisting||!(s in a.endpointDefinitions)){a.endpointDefinitions[s]=c;for(var l=0,d=o;l<d.length;l++)d[l].injectEndpoint(s,c)}}return u},enhanceEndpoints:function(e){var t=e.addTagTypes,n=e.endpoints;if(t)for(var r=0,o=t;r<o.length;r++){var s=o[r];i.tagTypes.includes(s)||i.tagTypes.push(s)}if(n)for(var c=0,l=Object.entries(n);c<l.length;c++){var d=l[c],f=d[0],p=d[1];"function"==typeof p?p(a.endpointDefinitions[f]):Object.assign(a.endpointDefinitions[f]||{},p)}return u}},o=e.map((function(e){return e.init(u,i,a)}));return u.injectEndpoints({endpoints:t.endpoints})}}function be(){return function(){throw new Error("When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.")}}var qe,Se=b(require("@reduxjs/toolkit")),Oe=function(e){var t=e.reducerPath,n=e.api,r=e.context,i=e.internalState,a=n.internalActions,u=a.removeQueryResult,o=a.unsubscribeQueryResult;function s(e){var t=i.currentSubscriptions[e];return!!t&&!function(e){for(var t in e)return!1;return!0}(t)}var c={};function l(e,t,n,i){var a,o=r.endpointDefinitions[t],l=null!=(a=null==o?void 0:o.keepUnusedDataFor)?a:i.keepUnusedDataFor;if(Infinity!==l){var d=Math.max(0,Math.min(l,2147482.647));if(!s(e)){var f=c[e];f&&clearTimeout(f),c[e]=setTimeout((function(){s(e)||n.dispatch(u({queryCacheKey:e})),delete c[e]}),1e3*d)}}}return function(e,i,a){var u;if(o.match(e)){var s=i.getState()[t];l(b=e.payload.queryCacheKey,null==(u=s.queries[b])?void 0:u.endpointName,i,s.config)}if(n.util.resetApiState.match(e))for(var d=0,f=Object.entries(c);d<f.length;d++){var p=f[d],h=p[0],v=p[1];v&&clearTimeout(v),delete c[h]}if(r.hasRehydrationInfo(e)){s=i.getState()[t];for(var y=r.extractRehydrationInfo(e).queries,m=0,g=Object.entries(y);m<g.length;m++){var b,q=g[m],S=q[1];l(b=q[0],null==S?void 0:S.endpointName,i,s.config)}}}},Ae=b(require("@reduxjs/toolkit")),Te=function(t){var n=t.reducerPath,r=t.context,i=t.context.endpointDefinitions,a=t.mutationThunk,u=t.api,o=t.assertTagType,s=t.refetchQuery,c=u.internalActions.removeQueryResult,l=(0,Ae.isAnyOf)((0,Ae.isFulfilled)(a),(0,Ae.isRejectedWithValue)(a));function d(t,i){var a=i.getState(),o=a[n],l=u.util.selectInvalidatedBy(a,t);r.batch((function(){for(var t,n=0,r=Array.from(l.values());n<r.length;n++){var a=r[n].queryCacheKey,u=o.queries[a],d=null!=(t=o.subscriptions[a])?t:{};u&&(0===Object.keys(d).length?i.dispatch(c({queryCacheKey:a})):u.status!==e.uninitialized&&i.dispatch(s(u,a)))}}))}return function(e,t){l(e)&&d(te(e,"invalidatesTags",i,o),t),u.util.invalidateTags.match(e)&&d(W(e.payload,void 0,void 0,void 0,void 0,o),t)}},Re=function(t){var n=t.reducerPath,r=t.queryThunk,i=t.api,a=t.refetchQuery,u=t.internalState,o={};function s(t,r){var i=t.queryCacheKey,s=r.getState()[n].queries[i];if(s&&s.status!==e.uninitialized){var c=d(u.currentSubscriptions[i]);if(Number.isFinite(c)){var l=o[i];(null==l?void 0:l.timeout)&&(clearTimeout(l.timeout),l.timeout=void 0);var f=Date.now()+c,p=o[i]={nextPollTimestamp:f,pollingInterval:c,timeout:setTimeout((function(){p.timeout=void 0,r.dispatch(a(s,i))}),c)}}}}function c(t,r){var i=t.queryCacheKey,a=r.getState()[n].queries[i];if(a&&a.status!==e.uninitialized){var c=d(u.currentSubscriptions[i]);if(Number.isFinite(c)){var f=o[i],p=Date.now()+c;(!f||p<f.nextPollTimestamp)&&s({queryCacheKey:i},r)}else l(i)}}function l(e){var t=o[e];(null==t?void 0:t.timeout)&&clearTimeout(t.timeout),delete o[e]}function d(e){void 0===e&&(e={});var t=Number.POSITIVE_INFINITY;for(var n in e)e[n].pollingInterval&&(t=Math.min(e[n].pollingInterval,t));return t}return function(e,t){(i.internalActions.updateSubscriptionOptions.match(e)||i.internalActions.unsubscribeQueryResult.match(e))&&c(e.payload,t),(r.pending.match(e)||r.rejected.match(e)&&e.meta.condition)&&c(e.meta.arg,t),(r.fulfilled.match(e)||r.rejected.match(e)&&!e.meta.condition)&&s(e.meta.arg,t),i.util.resetApiState.match(e)&&function(){for(var e=0,t=Object.keys(o);e<t.length;e++)l(t[e])}()}},je=b(require("@reduxjs/toolkit")),we=new Error("Promise never resolved before cacheEntryRemoved."),ke=function(e){var t=e.api,n=e.reducerPath,r=e.context,i=e.queryThunk,a=e.mutationThunk,u=(0,je.isAsyncThunkAction)(i),o=(0,je.isAsyncThunkAction)(a),s=(0,je.isFulfilled)(i,a),c={};function l(e,n,i,a,u){var o=r.endpointDefinitions[e],s=null==o?void 0:o.onCacheEntryAdded;if(s){var l={},d=new Promise((function(e){l.cacheEntryRemoved=e})),f=Promise.race([new Promise((function(e){l.valueResolved=e})),d.then((function(){throw we}))]);f.catch((function(){})),c[i]=l;var p=t.endpoints[e].select(o.type===z.query?n:i),h=a.dispatch((function(e,t,n){return n})),m=y(v({},a),{getCacheEntry:function(){return p(a.getState())},requestId:u,extra:h,updateCachedData:o.type===z.query?function(r){return a.dispatch(t.util.updateQueryData(e,n,r))}:void 0,cacheDataLoaded:f,cacheEntryRemoved:d}),g=s(n,m);Promise.resolve(g).catch((function(e){if(e!==we)throw e}))}}return function(e,r,d){var f=function(e){return u(e)?e.meta.arg.queryCacheKey:o(e)?e.meta.requestId:t.internalActions.removeQueryResult.match(e)?e.payload.queryCacheKey:t.internalActions.removeMutationResult.match(e)?ae(e.payload):""}(e);if(i.pending.match(e)){var p=d[n].queries[f],h=r.getState()[n].queries[f];!p&&h&&l(e.meta.arg.endpointName,e.meta.arg.originalArgs,f,r,e.meta.requestId)}else if(a.pending.match(e))(h=r.getState()[n].mutations[f])&&l(e.meta.arg.endpointName,e.meta.arg.originalArgs,f,r,e.meta.requestId);else if(s(e))(null==(g=c[f])?void 0:g.valueResolved)&&(g.valueResolved({data:e.payload,meta:e.meta.baseQueryMeta}),delete g.valueResolved);else if(t.internalActions.removeQueryResult.match(e)||t.internalActions.removeMutationResult.match(e))(g=c[f])&&(delete c[f],g.cacheEntryRemoved());else if(t.util.resetApiState.match(e))for(var v=0,y=Object.entries(c);v<y.length;v++){var m=y[v],g=m[1];delete c[m[0]],g.cacheEntryRemoved()}}},xe=b(require("@reduxjs/toolkit")),Pe=function(e){var t=e.api,n=e.context,r=e.queryThunk,i=e.mutationThunk,a=(0,xe.isPending)(r,i),u=(0,xe.isRejected)(r,i),o=(0,xe.isFulfilled)(r,i),s={};return function(e,r){var i,c,l;if(a(e)){var d=e.meta,f=d.requestId,p=d.arg,h=p.endpointName,m=p.originalArgs,g=n.endpointDefinitions[h],b=null==g?void 0:g.onQueryStarted;if(b){var q={},S=new Promise((function(e,t){q.resolve=e,q.reject=t}));S.catch((function(){})),s[f]=q;var O=t.endpoints[h].select(g.type===z.query?m:f),A=r.dispatch((function(e,t,n){return n})),T=y(v({},r),{getCacheEntry:function(){return O(r.getState())},requestId:f,extra:A,updateCachedData:g.type===z.query?function(e){return r.dispatch(t.util.updateQueryData(h,m,e))}:void 0,queryFulfilled:S});b(m,T)}}else if(o(e)){var R=e.meta,j=R.baseQueryMeta;null==(i=s[f=R.requestId])||i.resolve({data:e.payload,meta:j}),delete s[f]}else if(u(e)){var w=e.meta;j=w.baseQueryMeta,null==(l=s[f=w.requestId])||l.reject({error:null!=(c=e.payload)?c:e.error,isUnhandledError:!w.rejectedWithValue,meta:j}),delete s[f]}}},Qe=function(e){var t=e.api,n=e.context.apiUid;return function(e,r){t.util.resetApiState.match(e)&&r.dispatch(t.internalActions.middlewareRegistered(n))}},Ce=b(require("immer")),Ie="function"==typeof queueMicrotask?queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis):function(e){return(qe||(qe=Promise.resolve())).then(e).catch((function(e){return setTimeout((function(){throw e}),0)}))};function Me(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];Object.assign.apply(Object,r([e],t))}var De=b(require("immer")),Ne=Symbol(),Ke=function(){return{name:Ne,init:function(t,i,a){var u=i.baseQuery,o=i.reducerPath,s=i.serializeQueryArgs,c=i.keepUnusedDataFor,l=i.refetchOnMountOrArgChange,d=i.refetchOnFocus,f=i.refetchOnReconnect;(0,De.enablePatches)();var p=function(e){return e};Object.assign(t,{reducerPath:o,endpoints:{},internalActions:{onOnline:K,onOffline:E,onFocus:D,onFocusLost:N},util:{}});var h=function(t){var r=this,i=t.reducerPath,a=t.baseQuery,u=t.context.endpointDefinitions,o=t.serializeQueryArgs,s=t.api,c=t.assertTagType,l=function(e,t){return q(r,[e,t],(function(e,t){var r,i,o,s,c,l,f,p,h,v,y,m,g,b=t.signal,q=t.abort,S=t.rejectWithValue,O=t.fulfillWithValue,A=t.dispatch,T=t.getState,R=t.extra;return n(this,(function(t){switch(t.label){case 0:r=u[e.endpointName],t.label=1;case 1:return t.trys.push([1,8,,13]),i=ee,o=void 0,s={signal:b,abort:q,dispatch:A,getState:T,extra:R,endpoint:e.endpointName,type:e.type,forced:"query"===e.type?d(e,T()):void 0},(c="query"===e.type?e[G]:void 0)?(o=c(),[3,6]):[3,2];case 2:return r.query?[4,a(r.query(e.originalArgs),s,r.extraOptions)]:[3,4];case 3:return o=t.sent(),r.transformResponse&&(i=r.transformResponse),[3,6];case 4:return[4,r.queryFn(e.originalArgs,s,r.extraOptions,(function(e){return a(e,s,r.extraOptions)}))];case 5:o=t.sent(),t.label=6;case 6:if(o.error)throw new P(o.error,o.meta);return l=O,[4,i(o.data,o.meta,e.originalArgs)];case 7:return[2,l.apply(void 0,[t.sent(),(m={fulfilledTimeStamp:Date.now(),baseQueryMeta:o.meta},m[Z.SHOULD_AUTOBATCH]=!0,m)])];case 8:if(f=t.sent(),!((p=f)instanceof P))return[3,12];h=ee,r.query&&r.transformErrorResponse&&(h=r.transformErrorResponse),t.label=9;case 9:return t.trys.push([9,11,,12]),v=S,[4,h(p.value,p.meta,e.originalArgs)];case 10:return[2,v.apply(void 0,[t.sent(),(g={baseQueryMeta:p.meta},g[Z.SHOULD_AUTOBATCH]=!0,g)])];case 11:return y=t.sent(),p=y,[3,12];case 12:throw console.error(p),p;case 13:return[2]}}))}))};function d(e,t){var n,r,a,u,o=null==(r=null==(n=t[i])?void 0:n.queries)?void 0:r[e.queryCacheKey],s=null==(a=t[i])?void 0:a.config.refetchOnMountOrArgChange,c=null==o?void 0:o.fulfilledTimeStamp,l=null!=(u=e.forceRefetch)?u:e.subscribe&&s;return!!l&&(!0===l||(Number(new Date)-Number(c))/1e3>=l)}var f=(0,Z.createAsyncThunk)(i+"/executeQuery",l,{getPendingMeta:function(){var e;return(e={startedTimeStamp:Date.now()})[Z.SHOULD_AUTOBATCH]=!0,e},condition:function(e,t){var n,r,a,o=(0,t.getState)(),s=null==(r=null==(n=o[i])?void 0:n.queries)?void 0:r[e.queryCacheKey],c=null==s?void 0:s.fulfilledTimeStamp,l=e.originalArgs,f=null==s?void 0:s.originalArgs,p=u[e.endpointName];return!(!Y(e)&&("pending"===(null==s?void 0:s.status)||!d(e,o)&&(!L(p)||!(null==(a=null==p?void 0:p.forceRefetch)?void 0:a.call(p,{currentArg:l,previousArg:f,endpointState:s,state:o})))&&c))},dispatchConditionRejection:!0}),p=(0,Z.createAsyncThunk)(i+"/executeMutation",l,{getPendingMeta:function(){var e;return(e={startedTimeStamp:Date.now()})[Z.SHOULD_AUTOBATCH]=!0,e}});function h(e){return function(t){var n,r;return(null==(r=null==(n=null==t?void 0:t.meta)?void 0:n.arg)?void 0:r.endpointName)===e}}return{queryThunk:f,mutationThunk:p,prefetch:function(e,t,n){return function(r,i){var a=function(e){return"force"in e}(n)&&n.force,u=function(e){return"ifOlderThan"in e}(n)&&n.ifOlderThan,o=function(n){return void 0===n&&(n=!0),s.endpoints[e].initiate(t,{forceRefetch:n})},c=s.endpoints[e].select(t)(i());if(a)r(o());else if(u){var l=null==c?void 0:c.fulfilledTimeStamp;if(!l)return void r(o());(Number(new Date)-Number(new Date(l)))/1e3>=u&&r(o())}else r(o(!1))}},updateQueryData:function(t,n,r,i){return void 0===i&&(i=!0),function(a,u){var o,c,l,d=s.endpoints[t].select(n)(u()),f={patches:[],inversePatches:[],undo:function(){return a(s.util.patchQueryData(t,n,f.inversePatches,i))}};if(d.status===e.uninitialized)return f;if("data"in d)if((0,X.isDraftable)(d.data)){var p=(0,X.produceWithPatches)(d.data,r),h=p[0],v=p[2];(o=f.patches).push.apply(o,p[1]),(c=f.inversePatches).push.apply(c,v),l=h}else l=r(d.data),f.patches.push({op:"replace",path:[],value:l}),f.inversePatches.push({op:"replace",path:[],value:d.data});return a(s.util.patchQueryData(t,n,f.patches,i)),f}},upsertQueryData:function(e,t,n){return function(r){var i;return r(s.endpoints[e].initiate(t,((i={subscribe:!1,forceRefetch:!0})[G]=function(){return{data:n}},i)))}},patchQueryData:function(e,t,n,r){return function(i,a){var l=u[e],d=o({queryArgs:t,endpointDefinition:l,endpointName:e});if(i(s.internalActions.queryResultPatched({queryCacheKey:d,patches:n})),r){var f=s.endpoints[e].select(t)(a()),p=W(l.providesTags,f.data,void 0,t,{},c);i(s.internalActions.updateProvidedBy({queryCacheKey:d,providedTags:p}))}}},buildMatchThunkActions:function(e,t){return{matchPending:(0,$.isAllOf)((0,$.isPending)(e),h(t)),matchFulfilled:(0,$.isAllOf)((0,$.isFulfilled)(e),h(t)),matchRejected:(0,$.isAllOf)((0,$.isRejected)(e),h(t))}}}}({baseQuery:u,reducerPath:o,context:a,api:t,serializeQueryArgs:s,assertTagType:p}),m=h.queryThunk,g=h.mutationThunk,b=h.patchQueryData,O=h.updateQueryData,T=h.upsertQueryData,R=h.prefetch,j=h.buildMatchThunkActions,w=function(t){var n=t.reducerPath,r=t.queryThunk,i=t.mutationThunk,a=t.context,u=a.endpointDefinitions,o=a.apiUid,s=a.extractRehydrationInfo,c=a.hasRehydrationInfo,l=t.assertTagType,d=t.config,f=(0,J.createAction)(n+"/resetApiState"),p=(0,J.createSlice)({name:n+"/queries",initialState:oe,reducers:{removeQueryResult:{reducer:function(e,t){delete e[t.payload.queryCacheKey]},prepare:(0,J.prepareAutoBatched)()},queryResultPatched:{reducer:function(e,t){var n=t.payload,r=n.patches;ie(e,n.queryCacheKey,(function(e){e.data=(0,re.applyPatches)(e.data,r.concat())}))},prepare:(0,J.prepareAutoBatched)()}},extraReducers:function(t){t.addCase(r.pending,(function(t,n){var r,i=n.meta,a=n.meta.arg,u=Y(a);(a.subscribe||u)&&(null!=t[r=a.queryCacheKey]||(t[r]={status:e.uninitialized,endpointName:a.endpointName})),ie(t,a.queryCacheKey,(function(t){t.status=e.pending,t.requestId=u&&t.requestId?t.requestId:i.requestId,void 0!==a.originalArgs&&(t.originalArgs=a.originalArgs),t.startedTimeStamp=i.startedTimeStamp}))})).addCase(r.fulfilled,(function(t,n){var r=n.meta,i=n.payload;ie(t,r.arg.queryCacheKey,(function(t){var n;if(t.requestId===r.requestId||Y(r.arg)){var a=u[r.arg.endpointName].merge;if(t.status=e.fulfilled,a)if(void 0!==t.data){var o=r.fulfilledTimeStamp,s=r.arg,c=r.baseQueryMeta,l=r.requestId,d=(0,J.createNextState)(t.data,(function(e){return a(e,i,{arg:s.originalArgs,baseQueryMeta:c,fulfilledTimeStamp:o,requestId:l})}));t.data=d}else t.data=i;else t.data=null==(n=u[r.arg.endpointName].structuralSharing)||n?A((0,ne.isDraft)(t.data)?(0,re.original)(t.data):t.data,i):i;delete t.error,t.fulfilledTimeStamp=r.fulfilledTimeStamp}}))})).addCase(r.rejected,(function(t,n){var r=n.meta,i=r.condition,a=r.requestId,u=n.error,o=n.payload;ie(t,r.arg.queryCacheKey,(function(t){if(i);else{if(t.requestId!==a)return;t.status=e.rejected,t.error=null!=o?o:u}}))})).addMatcher(c,(function(t,n){for(var r=s(n).queries,i=0,a=Object.entries(r);i<a.length;i++){var u=a[i],o=u[1];(null==o?void 0:o.status)!==e.fulfilled&&(null==o?void 0:o.status)!==e.rejected||(t[u[0]]=o)}}))}}),h=(0,J.createSlice)({name:n+"/mutations",initialState:oe,reducers:{removeMutationResult:{reducer:function(e,t){var n=ae(t.payload);n in e&&delete e[n]},prepare:(0,J.prepareAutoBatched)()}},extraReducers:function(t){t.addCase(i.pending,(function(t,n){var r=n.meta,i=r.requestId,a=r.arg,u=r.startedTimeStamp;a.track&&(t[ae(n.meta)]={requestId:i,status:e.pending,endpointName:a.endpointName,startedTimeStamp:u})})).addCase(i.fulfilled,(function(t,n){var r=n.payload,i=n.meta;i.arg.track&&ue(t,i,(function(t){t.requestId===i.requestId&&(t.status=e.fulfilled,t.data=r,t.fulfilledTimeStamp=i.fulfilledTimeStamp)}))})).addCase(i.rejected,(function(t,n){var r=n.payload,i=n.error,a=n.meta;a.arg.track&&ue(t,a,(function(t){t.requestId===a.requestId&&(t.status=e.rejected,t.error=null!=r?r:i)}))})).addMatcher(c,(function(t,n){for(var r=s(n).mutations,i=0,a=Object.entries(r);i<a.length;i++){var u=a[i],o=u[0],c=u[1];(null==c?void 0:c.status)!==e.fulfilled&&(null==c?void 0:c.status)!==e.rejected||o===(null==c?void 0:c.requestId)||(t[o]=c)}}))}}),m=(0,J.createSlice)({name:n+"/invalidation",initialState:oe,reducers:{updateProvidedBy:{reducer:function(e,t){for(var n,r,i,a,u=t.payload,o=u.queryCacheKey,s=u.providedTags,c=0,l=Object.values(e);c<l.length;c++)for(var d=0,f=Object.values(l[c]);d<f.length;d++){var p=f[d],h=p.indexOf(o);-1!==h&&p.splice(h,1)}for(var v=0,y=s;v<y.length;v++){var m=y[v],g=m.type,b=m.id,q=null!=(a=(r=null!=(n=e[g])?n:e[g]={})[i=b||"__internal_without_id"])?a:r[i]=[];q.includes(o)||q.push(o)}},prepare:(0,J.prepareAutoBatched)()}},extraReducers:function(e){e.addCase(p.actions.removeQueryResult,(function(e,t){for(var n=t.payload.queryCacheKey,r=0,i=Object.values(e);r<i.length;r++)for(var a=0,u=Object.values(i[r]);a<u.length;a++){var o=u[a],s=o.indexOf(n);-1!==s&&o.splice(s,1)}})).addMatcher(c,(function(e,t){for(var n,r,i,a,u=s(t).provided,o=0,c=Object.entries(u);o<c.length;o++)for(var l=c[o],d=l[0],f=0,p=Object.entries(l[1]);f<p.length;f++)for(var h=p[f],v=h[0],y=h[1],m=null!=(a=(r=null!=(n=e[d])?n:e[d]={})[i=v||"__internal_without_id"])?a:r[i]=[],g=0,b=y;g<b.length;g++){var q=b[g];m.includes(q)||m.push(q)}})).addMatcher((0,J.isAnyOf)((0,J.isFulfilled)(r),(0,J.isRejectedWithValue)(r)),(function(e,t){var n=te(t,"providesTags",u,l);m.caseReducers.updateProvidedBy(e,m.actions.updateProvidedBy({queryCacheKey:t.meta.arg.queryCacheKey,providedTags:n}))}))}}),g=(0,J.createSlice)({name:n+"/subscriptions",initialState:oe,reducers:{updateSubscriptionOptions:function(e,t){},unsubscribeQueryResult:function(e,t){},internal_probeSubscription:function(e,t){}}}),b=(0,J.createSlice)({name:n+"/internalSubscriptions",initialState:oe,reducers:{subscriptionsUpdated:{reducer:function(e,t){return(0,re.applyPatches)(e,t.payload)},prepare:(0,J.prepareAutoBatched)()}}}),q=(0,J.createSlice)({name:n+"/config",initialState:v({online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine,focused:"undefined"==typeof document||"hidden"!==document.visibilityState,middlewareRegistered:!1},d),reducers:{middlewareRegistered:function(e,t){e.middlewareRegistered="conflict"!==e.middlewareRegistered&&o===t.payload||"conflict"}},extraReducers:function(e){e.addCase(K,(function(e){e.online=!0})).addCase(E,(function(e){e.online=!1})).addCase(D,(function(e){e.focused=!0})).addCase(N,(function(e){e.focused=!1})).addMatcher(c,(function(e){return v({},e)}))}}),S=(0,J.combineReducers)({queries:p.reducer,mutations:h.reducer,provided:m.reducer,subscriptions:b.reducer,config:q.reducer});return{reducer:function(e,t){return S(f.match(t)?void 0:e,t)},actions:y(v(v(v(v(v(v({},q.actions),p.actions),g.actions),b.actions),h.actions),m.actions),{unsubscribeMutationResult:h.actions.removeMutationResult,resetApiState:f})}}({context:a,queryThunk:m,mutationThunk:g,reducerPath:o,assertTagType:p,config:{refetchOnFocus:d,refetchOnReconnect:f,refetchOnMountOrArgChange:l,keepUnusedDataFor:c,reducerPath:o}}),k=w.reducer,x=w.actions;Me(t.util,{patchQueryData:b,updateQueryData:O,upsertQueryData:T,prefetch:R,resetApiState:x.resetApiState}),Me(t.internalActions,x);var Q=function(t){var n=t.reducerPath,r=t.queryThunk,i=t.api,a=t.context,u=a.apiUid,o={invalidateTags:(0,Se.createAction)(n+"/invalidateTags")},s=[Qe,Oe,Te,Re,ke,Pe];return{middleware:function(r){var o=!1,l=y(v({},t),{internalState:{currentSubscriptions:{}},refetchQuery:c}),d=s.map((function(e){return e(l)})),f=function(e){var t=e.api,n=e.queryThunk,r=e.internalState,i=t.reducerPath+"/subscriptions",a=null,u=!1,o=t.internalActions,s=o.updateSubscriptionOptions,c=o.unsubscribeQueryResult;return function(e,o){var l,d;if(a||(a=JSON.parse(JSON.stringify(r.currentSubscriptions))),t.util.resetApiState.match(e))return a=r.currentSubscriptions={},[!0,!1];if(t.internalActions.internal_probeSubscription.match(e)){var f=e.payload;return[!1,!!(null==(l=r.currentSubscriptions[f.queryCacheKey])?void 0:l[f.requestId])]}var p=function(e,r){var i,a,u,o,l,d,f,p,h;if(s.match(r)){var v=r.payload,y=v.queryCacheKey,m=v.requestId;return(null==(i=null==e?void 0:e[y])?void 0:i[m])&&(e[y][m]=v.options),!0}if(c.match(r)){var g=r.payload;return m=g.requestId,e[y=g.queryCacheKey]&&delete e[y][m],!0}if(t.internalActions.removeQueryResult.match(r))return delete e[r.payload.queryCacheKey],!0;if(n.pending.match(r)){var b=r.meta;if(m=b.requestId,(O=b.arg).subscribe)return(q=null!=(u=e[a=O.queryCacheKey])?u:e[a]={})[m]=null!=(l=null!=(o=O.subscriptionOptions)?o:q[m])?l:{},!0}if(n.rejected.match(r)){var q,S=r.meta,O=S.arg;if(m=S.requestId,S.condition&&O.subscribe)return(q=null!=(f=e[d=O.queryCacheKey])?f:e[d]={})[m]=null!=(h=null!=(p=O.subscriptionOptions)?p:q[m])?h:{},!0}return!1}(r.currentSubscriptions,e);if(p){u||(Ie((function(){var e=JSON.parse(JSON.stringify(r.currentSubscriptions)),n=(0,Ce.produceWithPatches)(a,(function(){return e}));o.next(t.internalActions.subscriptionsUpdated(n[1])),a=e,u=!1})),u=!0);var h=!!(null==(d=e.type)?void 0:d.startsWith(i)),v=n.rejected.match(e)&&e.meta.condition&&!!e.meta.arg.subscribe;return[!h&&!v,!1]}return[!0,!1]}}(l),p=function(t){var n=t.reducerPath,r=t.context,i=t.refetchQuery,a=t.internalState,u=t.api.internalActions.removeQueryResult;function o(t,o){var s=t.getState()[n],c=s.queries,l=a.currentSubscriptions;r.batch((function(){for(var n=0,r=Object.keys(l);n<r.length;n++){var a=r[n],d=c[a],f=l[a];f&&d&&(Object.values(f).some((function(e){return!0===e[o]}))||Object.values(f).every((function(e){return void 0===e[o]}))&&s.config[o])&&(0===Object.keys(f).length?t.dispatch(u({queryCacheKey:a})):d.status!==e.uninitialized&&t.dispatch(i(d,a)))}}))}return function(e,t){D.match(e)&&o(t,"refetchOnFocus"),K.match(e)&&o(t,"refetchOnReconnect")}}(l);return function(e){return function(t){o||(o=!0,r.dispatch(i.internalActions.middlewareRegistered(u)));var s,c=y(v({},r),{next:e}),l=r.getState(),h=f(t,c,l),m=h[1];if(s=h[0]?e(t):m,r.getState()[n]&&(p(t,c,l),function(e){return!!e&&"string"==typeof e.type&&e.type.startsWith(n+"/")}(t)||a.hasRehydrationInfo(t)))for(var g=0,b=d;g<b.length;g++)(0,b[g])(t,c,l);return s}}},actions:o};function c(e,t,n){return void 0===n&&(n={}),r(v({type:"query",endpointName:e.endpointName,originalArgs:e.originalArgs,subscribe:!1,forceRefetch:!0,queryCacheKey:t},n))}}({reducerPath:o,context:a,queryThunk:m,mutationThunk:g,api:t,assertTagType:p}),C=Q.middleware;Me(t.util,Q.actions),Me(t,{reducer:k,middleware:C});var I=function(t){var n=t.serializeQueryArgs,r=t.reducerPath,i=function(e){return de},a=function(e){return fe};return{buildQuerySelector:function(e,t){return function(r){var a=n({queryArgs:r,endpointDefinition:t,endpointName:e});return(0,B.createSelector)(r===se?i:function(e){var t,n,r;return null!=(r=null==(n=null==(t=o(e))?void 0:t.queries)?void 0:n[a])?r:de},u)}},buildMutationSelector:function(){return function(e){var t,n;return n="object"==typeof e?null!=(t=ae(e))?t:se:e,(0,B.createSelector)(n===se?a:function(e){var t,r,i;return null!=(i=null==(r=null==(t=o(e))?void 0:t.mutations)?void 0:r[n])?i:fe},u)}},selectInvalidatedBy:function(e,t){for(var n,i=e[r],a=new Set,u=0,o=t.map(H);u<o.length;u++){var s=o[u],c=i.provided[s.type];if(c)for(var l=0,d=null!=(n=void 0!==s.id?c[s.id]:S(Object.values(c)))?n:[];l<d.length;l++)a.add(d[l])}return S(Array.from(a.values()).map((function(e){var t=i.queries[e];return t?[{queryCacheKey:e,endpointName:t.endpointName,originalArgs:t.originalArgs}]:[]})))}};function u(t){return v(v({},t),{status:n=t.status,isUninitialized:n===e.uninitialized,isLoading:n===e.pending,isSuccess:n===e.fulfilled,isError:n===e.rejected});var n}function o(e){return e[r]}}({serializeQueryArgs:s,reducerPath:o}),M=I.buildQuerySelector,_=I.buildMutationSelector;Me(t.util,{selectInvalidatedBy:I.selectInvalidatedBy});var F=function(e){var t=e.serializeQueryArgs,i=e.queryThunk,a=e.mutationThunk,u=e.api,o=e.context,s=new Map,c=new Map,l=u.internalActions,d=l.unsubscribeQueryResult,f=l.removeMutationResult,p=l.updateSubscriptionOptions;return{buildInitiateQuery:function(e,r){var a=function(o,c){var l=void 0===c?{}:c,f=l.subscribe,h=void 0===f||f,v=l.forceRefetch,y=l.subscriptionOptions,m=l[G];return function(c,l){var f,g,b=t({queryArgs:o,endpointDefinition:r,endpointName:e}),S=i(((f={type:"query",subscribe:h,forceRefetch:v,subscriptionOptions:y,endpointName:e,originalArgs:o,queryCacheKey:b})[G]=m,f)),O=u.endpoints[e].select(o),A=c(S),T=O(l()),R=A.requestId,j=A.abort,w=T.requestId!==R,k=null==(g=s.get(c))?void 0:g[b],x=function(){return O(l())},P=Object.assign(m?A.then(x):w&&!k?Promise.resolve(T):Promise.all([k,A]).then(x),{arg:o,requestId:R,subscriptionOptions:y,queryCacheKey:b,abort:j,unwrap:function(){return q(this,null,(function(){var e;return n(this,(function(t){switch(t.label){case 0:return[4,P];case 1:if((e=t.sent()).isError)throw e.error;return[2,e.data]}}))}))},refetch:function(){return c(a(o,{subscribe:!1,forceRefetch:!0}))},unsubscribe:function(){h&&c(d({queryCacheKey:b,requestId:R}))},updateSubscriptionOptions:function(t){P.subscriptionOptions=t,c(p({endpointName:e,requestId:R,queryCacheKey:b,options:t}))}});if(!k&&!w&&!m){var Q=s.get(c)||{};Q[b]=P,s.set(c,Q),P.then((function(){delete Q[b],Object.keys(Q).length||s.delete(c)}))}return P}};return a},buildInitiateMutation:function(e){return function(t,n){var r=void 0===n?{}:n,i=r.track,u=void 0===i||i,o=r.fixedCacheKey;return function(n,r){var i=a({type:"mutation",endpointName:e,originalArgs:t,track:u,fixedCacheKey:o}),s=n(i),l=s.requestId,d=s.abort,p=s.unwrap,h=s.unwrap().then((function(e){return{data:e}})).catch((function(e){return{error:e}})),v=function(){n(f({requestId:l,fixedCacheKey:o}))},y=Object.assign(h,{arg:s.arg,requestId:l,abort:d,unwrap:p,unsubscribe:v,reset:v}),m=c.get(n)||{};return c.set(n,m),m[l]=y,y.then((function(){delete m[l],Object.keys(m).length||c.delete(n)})),o&&(m[o]=y,y.then((function(){m[o]===y&&(delete m[o],Object.keys(m).length||c.delete(n))}))),y}}},getRunningQueryThunk:function(e,n){return function(r){var i,a=t({queryArgs:n,endpointDefinition:o.endpointDefinitions[e],endpointName:e});return null==(i=s.get(r))?void 0:i[a]}},getRunningMutationThunk:function(e,t){return function(e){var n;return null==(n=c.get(e))?void 0:n[t]}},getRunningQueriesThunk:function(){return function(e){return Object.values(s.get(e)||{}).filter(V)}},getRunningMutationsThunk:function(){return function(e){return Object.values(c.get(e)||{}).filter(V)}},getRunningOperationPromises:function(){var e=function(e){return Array.from(e.values()).flatMap((function(e){return e?Object.values(e):[]}))};return r(r([],e(s)),e(c)).filter(V)},removalWarning:function(){throw new Error("This method had to be removed due to a conceptual bug in RTK.\n       Please see https://github.com/reduxjs/redux-toolkit/pull/2481 for details.\n       See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for new guidance on SSR.")}}}({queryThunk:m,mutationThunk:g,api:t,serializeQueryArgs:s,context:a}),U=F.buildInitiateQuery,ce=F.buildInitiateMutation;return Me(t.util,{getRunningOperationPromises:F.getRunningOperationPromises,getRunningOperationPromise:F.removalWarning,getRunningMutationThunk:F.getRunningMutationThunk,getRunningMutationsThunk:F.getRunningMutationsThunk,getRunningQueryThunk:F.getRunningQueryThunk,getRunningQueriesThunk:F.getRunningQueriesThunk}),{name:Ne,injectEndpoint:function(e,n){var r,i=t;null!=(r=i.endpoints)[e]||(r[e]={}),L(n)?Me(i.endpoints[e],{name:e,select:M(e,n),initiate:U(e,n)},j(m,e)):n.type===z.mutation&&Me(i.endpoints[e],{name:e,select:_(),initiate:ce(e)},j(g,e))}}}}},Ee=ge(Ke());
//# sourceMappingURL=rtk-query.cjs.production.min.js.map