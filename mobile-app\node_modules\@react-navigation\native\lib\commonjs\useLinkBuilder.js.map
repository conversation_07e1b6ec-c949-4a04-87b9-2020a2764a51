{"version": 3, "names": ["getRootStateForNavigate", "navigation", "state", "parent", "getParent", "parentState", "getState", "index", "routes", "useLinkBuilder", "React", "useContext", "NavigationHelpersContext", "linking", "LinkingContext", "buildLink", "useCallback", "name", "params", "options", "enabled", "undefined", "path", "getPathFromState", "config"], "sourceRoot": "../../src", "sources": ["useLinkBuilder.tsx"], "mappings": ";;;;;;AAAA;AAOA;AAEA;AAA8C;AAAA;AAAA;AAW9C,MAAMA,uBAAuB,GAAG,CAC9BC,UAA4B,EAC5BC,KAAmB,KACF;EACjB,MAAMC,MAAM,GAAGF,UAAU,CAACG,SAAS,EAAE;EAErC,IAAID,MAAM,EAAE;IACV,MAAME,WAAW,GAAGF,MAAM,CAACG,QAAQ,EAAE;IAErC,OAAON,uBAAuB,CAACG,MAAM,EAAE;MACrCI,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CACN;QACE,GAAGH,WAAW,CAACG,MAAM,CAACH,WAAW,CAACE,KAAK,CAAC;QACxCL,KAAK,EAAEA;MACT,CAAC;IAEL,CAAC,CAAC;EACJ;EAEA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACe,SAASO,cAAc,GAAG;EACvC,MAAMR,UAAU,GAAGS,KAAK,CAACC,UAAU,CAACC,8BAAwB,CAAC;EAC7D,MAAMC,OAAO,GAAGH,KAAK,CAACC,UAAU,CAACG,uBAAc,CAAC;EAEhD,MAAMC,SAAS,GAAGL,KAAK,CAACM,WAAW,CACjC,CAACC,IAAY,EAAEC,MAAe,KAAK;IACjC,MAAM;MAAEC;IAAQ,CAAC,GAAGN,OAAO;IAE3B,IAAI,CAAAM,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,OAAO,MAAK,KAAK,EAAE;MAC9B,OAAOC,SAAS;IAClB;IAEA,MAAMnB,KAAK,GAAGD,UAAU,GACpBD,uBAAuB,CAACC,UAAU,EAAE;MAClCM,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;QAAES,IAAI;QAAEC;MAAO,CAAC;IAC3B,CAAC,CAAC;IACF;IACA;IACA;MACEX,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;QAAES,IAAI;QAAEC;MAAO,CAAC;IAC3B,CAAC;IAEL,MAAMI,IAAI,GAAGH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEI,gBAAgB,GAClCJ,OAAO,CAACI,gBAAgB,CAACrB,KAAK,EAAEiB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,MAAM,CAAC,GAChD,IAAAD,sBAAgB,EAACrB,KAAK,EAAEiB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,MAAM,CAAC;IAE5C,OAAOF,IAAI;EACb,CAAC,EACD,CAACT,OAAO,EAAEZ,UAAU,CAAC,CACtB;EAED,OAAOc,SAAS;AAClB"}