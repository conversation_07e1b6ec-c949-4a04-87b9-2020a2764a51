{"version": 3, "sources": ["index.ts"], "names": ["_configuration", "DEFAULT_CONFIGURATION", "_state", "createState", "State", "configure", "configuration", "tearDown", "Platform", "OS", "NativeInterface", "fetch", "requestedInterface", "latest", "refresh", "_fetchCurrentState", "addEventListener", "listener", "add", "remove", "useNetInfo", "netInfo", "setNetInfo", "type", "Types", "NetInfoStateType", "unknown", "isConnected", "isInternetReachable", "details"], "mappings": ";;;;;;;;;;;;;;;;;;;AASA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAkHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAhIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA,IAAIA,cAAc,GAAGC,6BAArB,C,CAEA;;AACA,IAAIC,MAAoB,GAAG,IAA3B;;AACA,MAAMC,WAAW,GAAG,MAAa;AAC/B,SAAO,IAAIC,eAAJ,CAAUJ,cAAV,CAAP;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASK,SAAT,CACLC,aADK,EAEC;AACNN,EAAAA,cAAc,GAAG,EACf,GAAGC,6BADY;AAEf,OAAGK;AAFY,GAAjB;;AAKA,MAAIJ,MAAJ,EAAY;AACVA,IAAAA,MAAM,CAACK,QAAP;;AACAL,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AAED,MAAIK,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzBC,6BAAgBL,SAAhB,CAA0BC,aAA1B;AACD;AACF;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASK,KAAT,CACLC,kBADK,EAEwB;AAC7B,MAAI,CAACV,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AACD,SAAOD,MAAM,CAACW,MAAP,CAAcD,kBAAd,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;;AACO,SAASE,OAAT,GAAgD;AACrD,MAAI,CAACZ,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AACD,SAAOD,MAAM,CAACa,kBAAP,EAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASC,gBAAT,CACLC,QADK,EAEsB;AAC3B,MAAI,CAACf,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AAEDD,EAAAA,MAAM,CAACgB,GAAP,CAAWD,QAAX;;AACA,SAAO,MAAY;AACjBf,IAAAA,MAAM,IAAIA,MAAM,CAACiB,MAAP,CAAcF,QAAd,CAAV;AACD,GAFD;AAGD;AAED;AACA;AACA;AACA;AACA;;;AACO,SAASG,UAAT,CACLd,aADK,EAEe;AACpB,MAAIA,aAAJ,EAAmB;AACjBD,IAAAA,SAAS,CAACC,aAAD,CAAT;AACD;;AAED,QAAM,CAACe,OAAD,EAAUC,UAAV,IAAwB,qBAA6B;AACzDC,IAAAA,IAAI,EAAEC,KAAK,CAACC,gBAAN,CAAuBC,OAD4B;AAEzDC,IAAAA,WAAW,EAAE,IAF4C;AAGzDC,IAAAA,mBAAmB,EAAE,IAHoC;AAIzDC,IAAAA,OAAO,EAAE;AAJgD,GAA7B,CAA9B;AAOA,wBAAU,MAAoB;AAC5B,WAAOb,gBAAgB,CAACM,UAAD,CAAvB;AACD,GAFD,EAEG,EAFH;AAIA,SAAOD,OAAP;AACD;;eAIc;AACbhB,EAAAA,SADa;AAEbM,EAAAA,KAFa;AAGbG,EAAAA,OAHa;AAIbE,EAAAA,gBAJa;AAKbI,EAAAA;AALa,C", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {useState, useEffect} from 'react';\nimport {Platform} from 'react-native';\nimport DEFAULT_CONFIGURATION from './internal/defaultConfiguration';\nimport NativeInterface from './internal/nativeInterface';\nimport State from './internal/state';\nimport * as Types from './internal/types';\n\n// Stores the currently used configuration\nlet _configuration = DEFAULT_CONFIGURATION;\n\n// Stores the singleton reference to the state manager\nlet _state: State | null = null;\nconst createState = (): State => {\n  return new State(_configuration);\n};\n\n/**\n * Configures the library with the given configuration. Note that calling this will stop all\n * previously added listeners from being called again. It is best to call this right when your\n * application is started to avoid issues.\n *\n * @param configuration The new configuration to set.\n */\nexport function configure(\n  configuration: Partial<Types.NetInfoConfiguration>,\n): void {\n  _configuration = {\n    ...DEFAULT_CONFIGURATION,\n    ...configuration,\n  };\n\n  if (_state) {\n    _state.tearDown();\n    _state = createState();\n  }\n\n  if (Platform.OS === 'ios') {\n    NativeInterface.configure(configuration);\n  }\n}\n\n/**\n * Returns a `Promise` that resolves to a `NetInfoState` object.\n *\n * @param [requestedInterface] interface from which to obtain the information\n *\n * @returns A Promise which contains the current connection state.\n */\nexport function fetch(\n  requestedInterface?: string,\n): Promise<Types.NetInfoState> {\n  if (!_state) {\n    _state = createState();\n  }\n  return _state.latest(requestedInterface);\n}\n\n/**\n * Force-refreshes the internal state of the NetInfo library.\n *\n * @returns A Promise which contains the updated connection state.\n */\nexport function refresh(): Promise<Types.NetInfoState> {\n  if (!_state) {\n    _state = createState();\n  }\n  return _state._fetchCurrentState();\n}\n\n/**\n * Subscribe to connection information. The callback is called with a parameter of type\n * [`NetInfoState`](README.md#netinfostate) whenever the connection state changes. Your listener\n * will be called with the latest information soon after you subscribe and then with any\n * subsequent changes afterwards. You should not assume that the listener is called in the same\n * way across devices or platforms.\n *\n * @param listener The listener which is called when the network state changes.\n *\n * @returns A function which can be called to unsubscribe.\n */\nexport function addEventListener(\n  listener: Types.NetInfoChangeHandler,\n): Types.NetInfoSubscription {\n  if (!_state) {\n    _state = createState();\n  }\n\n  _state.add(listener);\n  return (): void => {\n    _state && _state.remove(listener);\n  };\n}\n\n/**\n * A React Hook which updates when the connection state changes.\n *\n * @returns The connection state.\n */\nexport function useNetInfo(\n  configuration?: Partial<Types.NetInfoConfiguration>,\n): Types.NetInfoState {\n  if (configuration) {\n    configure(configuration);\n  }\n\n  const [netInfo, setNetInfo] = useState<Types.NetInfoState>({\n    type: Types.NetInfoStateType.unknown,\n    isConnected: null,\n    isInternetReachable: null,\n    details: null,\n  });\n\n  useEffect((): (() => void) => {\n    return addEventListener(setNetInfo);\n  }, []);\n\n  return netInfo;\n}\n\nexport * from './internal/types';\n\nexport default {\n  configure,\n  fetch,\n  refresh,\n  addEventListener,\n  useNetInfo,\n};\n"]}