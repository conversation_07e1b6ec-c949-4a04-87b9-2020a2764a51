import type { NavigationState } from '@react-navigation/routers';
import type { NavigationProp } from './types';
/**
 * Hook to access the navigation prop of the parent screen anywhere.
 *
 * @returns Navigation prop of the parent screen.
 */
export default function useNavigation<T = Omit<NavigationProp<ReactNavigation.RootParamList>, 'getState'> & {
    getState(): NavigationState | undefined;
}>(): T;
//# sourceMappingURL=useNavigation.d.ts.map