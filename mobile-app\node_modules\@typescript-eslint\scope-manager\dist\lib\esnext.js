"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext = void 0;
const es2023_1 = require("./es2023");
const esnext_intl_1 = require("./esnext.intl");
exports.esnext = Object.assign(Object.assign({}, es2023_1.es2023), esnext_intl_1.esnext_intl);
//# sourceMappingURL=esnext.js.map