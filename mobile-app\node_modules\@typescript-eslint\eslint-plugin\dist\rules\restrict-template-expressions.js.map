{"version": 3, "file": "restrict-template-expressions.js", "sourceRoot": "", "sources": ["../../src/rules/restrict-template-expressions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,8CAAgC;AAehC,kBAAe,IAAI,CAAC,UAAU,CAAqB;IACjD,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,6DAA6D;YAC/D,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,yDAAyD;SACvE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,QAAQ,EAAE;wBACR,WAAW,EACT,8DAA8D;wBAChE,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EACT,kEAAkE;wBACpE,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EACT,kEAAkE;wBACpE,IAAI,EAAE,SAAS;qBAChB;oBACD,WAAW,EAAE;wBACX,WAAW,EACT,iEAAiE;wBACnE,IAAI,EAAE,SAAS;qBAChB;oBACD,WAAW,EAAE;wBACX,WAAW,EACT,iEAAiE;wBACnE,IAAI,EAAE,SAAS;qBAChB;oBACD,UAAU,EAAE;wBACV,WAAW,EACT,gEAAgE;wBAClE,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,WAAW,EAAE,IAAI;SAClB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAChD,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAErD,SAAS,yBAAyB,CAAC,IAAa;YAC9C,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;gBACrD,OAAO,IAAI,CAAC;aACb;YAED,IACE,OAAO,CAAC,WAAW;gBACnB,IAAI,CAAC,aAAa,CAChB,IAAI,EACJ,EAAE,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAClD,EACD;gBACA,OAAO,IAAI,CAAC;aACb;YAED,IACE,OAAO,CAAC,YAAY;gBACpB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAClD;gBACA,OAAO,IAAI,CAAC;aACb;YAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;gBAChD,OAAO,IAAI,CAAC;aACb;YAED,IACE,OAAO,CAAC,WAAW;gBACnB,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,QAAQ,EAChD;gBACA,OAAO,IAAI,CAAC;aACb;YAED,IACE,OAAO,CAAC,YAAY;gBACpB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,EACpE;gBACA,OAAO,IAAI,CAAC;aACb;YAED,IAAI,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;gBACpD,OAAO,IAAI,CAAC;aACb;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAA8B;gBAC5C,uCAAuC;gBACvC,IAAI,IAAI,CAAC,MAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAAE;oBACjE,OAAO;iBACR;gBAED,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE;oBACzC,MAAM,cAAc,GAAG,IAAI,CAAC,4BAA4B,CACtD,WAAW,EACX,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAC9C,CAAC;oBAEF,IACE,CAAC,sCAAsC,CACrC,cAAc,EACd,yBAAyB,CAC1B,EACD;wBACA,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,SAAS,EAAE,aAAa;4BACxB,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE;yBACzD,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;SACF,CAAC;QAEF,SAAS,sCAAsC,CAC7C,IAAa,EACb,SAA+C;YAE/C,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;YAEjB,SAAS,GAAG,CAAC,SAAkB;gBAC7B,IAAI,SAAS,CAAC,OAAO,EAAE,EAAE;oBACvB,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACnC;gBAED,IAAI,SAAS,CAAC,cAAc,EAAE,EAAE;oBAC9B,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAClC;gBAED,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}