{"version": 3, "names": ["nanoid", "BaseRouter", "TYPE_ROUTE", "TabActions", "jumpTo", "name", "params", "type", "payload", "getRouteHistory", "routes", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialRouteName", "history", "key", "initialRouteIndex", "i", "unshift", "findIndex", "route", "changeIndex", "state", "current<PERSON><PERSON>", "filter", "it", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "router", "getInitialState", "routeNames", "routeParamList", "undefined", "includes", "indexOf", "map", "stale", "getRehydratedState", "partialState", "find", "r", "Math", "min", "max", "length", "getStateForRouteNamesChange", "routeKeyChanges", "getStateForRouteFocus", "getStateForAction", "action", "routeGetIdList", "getId", "currentId", "nextId", "merge", "path", "previousKey", "slice", "shouldActionChangeFocus", "actionCreators"], "sourceRoot": "../../src", "sources": ["TabRouter.tsx"], "mappings": "AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAE1C,OAAOC,UAAU,MAAM,cAAc;AAyDrC,MAAMC,UAAU,GAAG,OAAgB;AAEnC,OAAO,MAAMC,UAAU,GAAG;EACxBC,MAAM,CAACC,IAAY,EAAEC,MAAe,EAAiB;IACnD,OAAO;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;QAAEH,IAAI;QAAEC;MAAO;IAAE,CAAC;EACvD;AACF,CAAC;AAED,MAAMG,eAAe,GAAG,CACtBC,MAAuB,EACvBC,KAAa,EACbC,YAA0B,EAC1BC,gBAAoC,KACjC;EACH,MAAMC,OAAO,GAAG,CAAC;IAAEP,IAAI,EAAEL,UAAU;IAAEa,GAAG,EAAEL,MAAM,CAACC,KAAK,CAAC,CAACI;EAAI,CAAC,CAAC;EAC9D,IAAIC,iBAAiB;EAErB,QAAQJ,YAAY;IAClB,KAAK,OAAO;MACV,KAAK,IAAIK,CAAC,GAAGN,KAAK,EAAEM,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9BH,OAAO,CAACI,OAAO,CAAC;UAAEX,IAAI,EAAEL,UAAU;UAAEa,GAAG,EAAEL,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,CAACF;QAAI,CAAC,CAAC;MAC/D;MACA;IACF,KAAK,YAAY;MACf,IAAIJ,KAAK,KAAK,CAAC,EAAE;QACfG,OAAO,CAACI,OAAO,CAAC;UACdX,IAAI,EAAEL,UAAU;UAChBa,GAAG,EAAEL,MAAM,CAAC,CAAC,CAAC,CAACK;QACjB,CAAC,CAAC;MACJ;MACA;IACF,KAAK,cAAc;MACjBC,iBAAiB,GAAGN,MAAM,CAACS,SAAS,CACjCC,KAAK,IAAKA,KAAK,CAACf,IAAI,KAAKQ,gBAAgB,CAC3C;MACDG,iBAAiB,GAAGA,iBAAiB,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,iBAAiB;MAEpE,IAAIL,KAAK,KAAKK,iBAAiB,EAAE;QAC/BF,OAAO,CAACI,OAAO,CAAC;UACdX,IAAI,EAAEL,UAAU;UAChBa,GAAG,EAAEL,MAAM,CAACM,iBAAiB,CAAC,CAACD;QACjC,CAAC,CAAC;MACJ;MACA;IACF,KAAK,SAAS;MACZ;MACA;EAAM;EAGV,OAAOD,OAAO;AAChB,CAAC;AAED,MAAMO,WAAW,GAAG,CAClBC,KAAwC,EACxCX,KAAa,EACbC,YAA0B,EAC1BC,gBAAoC,KACjC;EACH,IAAIC,OAAO;EAEX,IAAIF,YAAY,KAAK,SAAS,EAAE;IAC9B,MAAMW,UAAU,GAAGD,KAAK,CAACZ,MAAM,CAACC,KAAK,CAAC,CAACI,GAAG;IAE1CD,OAAO,GAAGQ,KAAK,CAACR,OAAO,CACpBU,MAAM,CAAEC,EAAE,IAAMA,EAAE,CAAClB,IAAI,KAAK,OAAO,GAAGkB,EAAE,CAACV,GAAG,KAAKQ,UAAU,GAAG,KAAM,CAAC,CACrEG,MAAM,CAAC;MAAEnB,IAAI,EAAEL,UAAU;MAAEa,GAAG,EAAEQ;IAAW,CAAC,CAAC;EAClD,CAAC,MAAM;IACLT,OAAO,GAAGL,eAAe,CACvBa,KAAK,CAACZ,MAAM,EACZC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;EACH;EAEA,OAAO;IACL,GAAGS,KAAK;IACRX,KAAK;IACLG;EACF,CAAC;AACH,CAAC;AAED,eAAe,SAASa,SAAS,OAGZ;EAAA,IAHa;IAChCd,gBAAgB;IAChBD,YAAY,GAAG;EACC,CAAC;EACjB,MAAMgB,MAGL,GAAG;IACF,GAAG3B,UAAU;IAEbM,IAAI,EAAE,KAAK;IAEXsB,eAAe,QAAiC;MAAA,IAAhC;QAAEC,UAAU;QAAEC;MAAe,CAAC;MAC5C,MAAMpB,KAAK,GACTE,gBAAgB,KAAKmB,SAAS,IAAIF,UAAU,CAACG,QAAQ,CAACpB,gBAAgB,CAAC,GACnEiB,UAAU,CAACI,OAAO,CAACrB,gBAAgB,CAAC,GACpC,CAAC;MAEP,MAAMH,MAAM,GAAGoB,UAAU,CAACK,GAAG,CAAE9B,IAAI,KAAM;QACvCA,IAAI;QACJU,GAAG,EAAG,GAAEV,IAAK,IAAGL,MAAM,EAAG,EAAC;QAC1BM,MAAM,EAAEyB,cAAc,CAAC1B,IAAI;MAC7B,CAAC,CAAC,CAAC;MAEH,MAAMS,OAAO,GAAGL,eAAe,CAC7BC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;MAED,OAAO;QACLuB,KAAK,EAAE,KAAK;QACZ7B,IAAI,EAAE,KAAK;QACXQ,GAAG,EAAG,OAAMf,MAAM,EAAG,EAAC;QACtBW,KAAK;QACLmB,UAAU;QACVhB,OAAO;QACPJ;MACF,CAAC;IACH,CAAC;IAED2B,kBAAkB,CAACC,YAAY,SAAkC;MAAA;MAAA,IAAhC;QAAER,UAAU;QAAEC;MAAe,CAAC;MAC7D,IAAIT,KAAK,GAAGgB,YAAY;MAExB,IAAIhB,KAAK,CAACc,KAAK,KAAK,KAAK,EAAE;QACzB,OAAOd,KAAK;MACd;MAEA,MAAMZ,MAAM,GAAGoB,UAAU,CAACK,GAAG,CAAE9B,IAAI,IAAK;QACtC,MAAMe,KAAK,GACTE,KAAK,CACLZ,MAAM,CAAC6B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACnC,IAAI,KAAKA,IAAI,CAAC;QAErC,OAAO;UACL,GAAGe,KAAK;UACRf,IAAI;UACJU,GAAG,EACDK,KAAK,IAAIA,KAAK,CAACf,IAAI,KAAKA,IAAI,IAAIe,KAAK,CAACL,GAAG,GACrCK,KAAK,CAACL,GAAG,GACR,GAAEV,IAAK,IAAGL,MAAM,EAAG,EAAC;UAC3BM,MAAM,EACJyB,cAAc,CAAC1B,IAAI,CAAC,KAAK2B,SAAS,GAC9B;YACE,GAAGD,cAAc,CAAC1B,IAAI,CAAC;YACvB,IAAIe,KAAK,GAAGA,KAAK,CAACd,MAAM,GAAG0B,SAAS;UACtC,CAAC,GACDZ,KAAK,GACLA,KAAK,CAACd,MAAM,GACZ0B;QACR,CAAC;MACH,CAAC,CAAC;MAEF,MAAMrB,KAAK,GAAG8B,IAAI,CAACC,GAAG,CACpBD,IAAI,CAACE,GAAG,CAACb,UAAU,CAACI,OAAO,kBAACZ,KAAK,CAACZ,MAAM,CAAC,CAAAY,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEX,KAAK,KAAI,CAAC,CAAC,kDAA/B,cAAiCN,IAAI,CAAC,EAAE,CAAC,CAAC,EACtEK,MAAM,CAACkC,MAAM,GAAG,CAAC,CAClB;MAED,MAAM9B,OAAO,GACX,mBAAAQ,KAAK,CAACR,OAAO,mDAAb,eAAeU,MAAM,CAAEC,EAAE,IAAKf,MAAM,CAAC6B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACzB,GAAG,KAAKU,EAAE,CAACV,GAAG,CAAC,CAAC,KACnE,EAAE;MAEJ,OAAOM,WAAW,CAChB;QACEe,KAAK,EAAE,KAAK;QACZ7B,IAAI,EAAE,KAAK;QACXQ,GAAG,EAAG,OAAMf,MAAM,EAAG,EAAC;QACtBW,KAAK;QACLmB,UAAU;QACVhB,OAAO;QACPJ;MACF,CAAC,EACDC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;IACH,CAAC;IAEDgC,2BAA2B,CACzBvB,KAAK,SAEL;MAAA,IADA;QAAEQ,UAAU;QAAEC,cAAc;QAAEe;MAAgB,CAAC;MAE/C,MAAMpC,MAAM,GAAGoB,UAAU,CAACK,GAAG,CAC1B9B,IAAI,IACHiB,KAAK,CAACZ,MAAM,CAAC6B,IAAI,CACdC,CAAC,IAAKA,CAAC,CAACnC,IAAI,KAAKA,IAAI,IAAI,CAACyC,eAAe,CAACb,QAAQ,CAACO,CAAC,CAACnC,IAAI,CAAC,CAC5D,IAAI;QACHA,IAAI;QACJU,GAAG,EAAG,GAAEV,IAAK,IAAGL,MAAM,EAAG,EAAC;QAC1BM,MAAM,EAAEyB,cAAc,CAAC1B,IAAI;MAC7B,CAAC,CACJ;MAED,MAAMM,KAAK,GAAG8B,IAAI,CAACE,GAAG,CACpB,CAAC,EACDb,UAAU,CAACI,OAAO,CAACZ,KAAK,CAACZ,MAAM,CAACY,KAAK,CAACX,KAAK,CAAC,CAACN,IAAI,CAAC,CACnD;MAED,IAAIS,OAAO,GAAGQ,KAAK,CAACR,OAAO,CAACU,MAAM;MAChC;MACCC,EAAE,IAAKA,EAAE,CAAClB,IAAI,KAAK,OAAO,IAAIG,MAAM,CAAC6B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACzB,GAAG,KAAKU,EAAE,CAACV,GAAG,CAAC,CACpE;MAED,IAAI,CAACD,OAAO,CAAC8B,MAAM,EAAE;QACnB9B,OAAO,GAAGL,eAAe,CACvBC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;MACH;MAEA,OAAO;QACL,GAAGS,KAAK;QACRR,OAAO;QACPgB,UAAU;QACVpB,MAAM;QACNC;MACF,CAAC;IACH,CAAC;IAEDoC,qBAAqB,CAACzB,KAAK,EAAEP,GAAG,EAAE;MAChC,MAAMJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAAEqB,CAAC,IAAKA,CAAC,CAACzB,GAAG,KAAKA,GAAG,CAAC;MAE1D,IAAIJ,KAAK,KAAK,CAAC,CAAC,IAAIA,KAAK,KAAKW,KAAK,CAACX,KAAK,EAAE;QACzC,OAAOW,KAAK;MACd;MAEA,OAAOD,WAAW,CAACC,KAAK,EAAEX,KAAK,EAAEC,YAAY,EAAEC,gBAAgB,CAAC;IAClE,CAAC;IAEDmC,iBAAiB,CAAC1B,KAAK,EAAE2B,MAAM,SAAsC;MAAA,IAApC;QAAElB,cAAc;QAAEmB;MAAe,CAAC;MACjE,QAAQD,MAAM,CAAC1C,IAAI;QACjB,KAAK,SAAS;QACd,KAAK,UAAU;UAAE;YACf,IAAII,KAAK,GAAG,CAAC,CAAC;YAEd,IAAIsC,MAAM,CAAC1C,IAAI,KAAK,UAAU,IAAI0C,MAAM,CAACzC,OAAO,CAACO,GAAG,EAAE;cACpDJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAC3BC,KAAK,IAAKA,KAAK,CAACL,GAAG,KAAKkC,MAAM,CAACzC,OAAO,CAACO,GAAG,CAC5C;YACH,CAAC,MAAM;cACLJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAC3BC,KAAK,IAAKA,KAAK,CAACf,IAAI,KAAK4C,MAAM,CAACzC,OAAO,CAACH,IAAI,CAC9C;YACH;YAEA,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,OAAO,IAAI;YACb;YAEA,OAAOU,WAAW,CAChB;cACE,GAAGC,KAAK;cACRZ,MAAM,EAAEY,KAAK,CAACZ,MAAM,CAACyB,GAAG,CAAC,CAACf,KAAK,EAAEH,CAAC,KAAK;gBACrC,IAAIA,CAAC,KAAKN,KAAK,EAAE;kBACf,OAAOS,KAAK;gBACd;gBAEA,MAAM+B,KAAK,GAAGD,cAAc,CAAC9B,KAAK,CAACf,IAAI,CAAC;gBAExC,MAAM+C,SAAS,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;kBAAE7C,MAAM,EAAEc,KAAK,CAACd;gBAAO,CAAC,CAAC;gBACnD,MAAM+C,MAAM,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;kBAAE7C,MAAM,EAAE2C,MAAM,CAACzC,OAAO,CAACF;gBAAO,CAAC,CAAC;gBAEzD,MAAMS,GAAG,GACPqC,SAAS,KAAKC,MAAM,GAChBjC,KAAK,CAACL,GAAG,GACR,GAAEK,KAAK,CAACf,IAAK,IAAGL,MAAM,EAAG,EAAC;gBAEjC,IAAIM,MAAM;gBAEV,IACE2C,MAAM,CAAC1C,IAAI,KAAK,UAAU,IAC1B0C,MAAM,CAACzC,OAAO,CAAC8C,KAAK,IACpBF,SAAS,KAAKC,MAAM,EACpB;kBACA/C,MAAM,GACJ2C,MAAM,CAACzC,OAAO,CAACF,MAAM,KAAK0B,SAAS,IACnCD,cAAc,CAACX,KAAK,CAACf,IAAI,CAAC,KAAK2B,SAAS,GACpC;oBACE,GAAGD,cAAc,CAACX,KAAK,CAACf,IAAI,CAAC;oBAC7B,GAAGe,KAAK,CAACd,MAAM;oBACf,GAAG2C,MAAM,CAACzC,OAAO,CAACF;kBACpB,CAAC,GACDc,KAAK,CAACd,MAAM;gBACpB,CAAC,MAAM;kBACLA,MAAM,GACJyB,cAAc,CAACX,KAAK,CAACf,IAAI,CAAC,KAAK2B,SAAS,GACpC;oBACE,GAAGD,cAAc,CAACX,KAAK,CAACf,IAAI,CAAC;oBAC7B,GAAG4C,MAAM,CAACzC,OAAO,CAACF;kBACpB,CAAC,GACD2C,MAAM,CAACzC,OAAO,CAACF,MAAM;gBAC7B;gBAEA,MAAMiD,IAAI,GACRN,MAAM,CAAC1C,IAAI,KAAK,UAAU,IAAI0C,MAAM,CAACzC,OAAO,CAAC+C,IAAI,IAAI,IAAI,GACrDN,MAAM,CAACzC,OAAO,CAAC+C,IAAI,GACnBnC,KAAK,CAACmC,IAAI;gBAEhB,OAAOjD,MAAM,KAAKc,KAAK,CAACd,MAAM,IAAIiD,IAAI,KAAKnC,KAAK,CAACmC,IAAI,GACjD;kBAAE,GAAGnC,KAAK;kBAAEL,GAAG;kBAAEwC,IAAI;kBAAEjD;gBAAO,CAAC,GAC/Bc,KAAK;cACX,CAAC;YACH,CAAC,EACDT,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;UACH;QAEA,KAAK,SAAS;UAAE;YACd,IAAIS,KAAK,CAACR,OAAO,CAAC8B,MAAM,KAAK,CAAC,EAAE;cAC9B,OAAO,IAAI;YACb;YAEA,MAAMY,WAAW,GAAGlC,KAAK,CAACR,OAAO,CAACQ,KAAK,CAACR,OAAO,CAAC8B,MAAM,GAAG,CAAC,CAAC,CAAC7B,GAAG;YAC/D,MAAMJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CACjCC,KAAK,IAAKA,KAAK,CAACL,GAAG,KAAKyC,WAAW,CACrC;YAED,IAAI7C,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,OAAO,IAAI;YACb;YAEA,OAAO;cACL,GAAGW,KAAK;cACRR,OAAO,EAAEQ,KAAK,CAACR,OAAO,CAAC2C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACnC9C;YACF,CAAC;UACH;QAEA;UACE,OAAOV,UAAU,CAAC+C,iBAAiB,CAAC1B,KAAK,EAAE2B,MAAM,CAAC;MAAC;IAEzD,CAAC;IAEDS,uBAAuB,CAACT,MAAM,EAAE;MAC9B,OAAOA,MAAM,CAAC1C,IAAI,KAAK,UAAU;IACnC,CAAC;IAEDoD,cAAc,EAAExD;EAClB,CAAC;EAED,OAAOyB,MAAM;AACf"}