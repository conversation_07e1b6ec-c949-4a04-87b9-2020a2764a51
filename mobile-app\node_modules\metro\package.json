{"name": "metro", "version": "0.76.9", "description": "🚇 The JavaScript bundler for React Native.", "main": "src/index.js", "bin": "src/cli.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "dependencies": {"@babel/code-frame": "^7.0.0", "@babel/core": "^7.20.0", "@babel/generator": "^7.20.0", "@babel/parser": "^7.20.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.20.0", "@babel/types": "^7.20.0", "accepts": "^1.3.7", "async": "^3.2.2", "chalk": "^4.0.0", "ci-info": "^2.0.0", "connect": "^3.6.5", "debug": "^2.2.0", "denodeify": "^1.2.1", "error-stack-parser": "^2.0.6", "graceful-fs": "^4.2.4", "hermes-parser": "0.12.0", "image-size": "^1.0.2", "invariant": "^2.2.4", "jest-worker": "^27.2.0", "jsc-safe-url": "^0.2.2", "lodash.throttle": "^4.1.1", "metro-babel-transformer": "0.76.9", "metro-cache": "0.76.9", "metro-cache-key": "0.76.9", "metro-config": "0.76.9", "metro-core": "0.76.9", "metro-file-map": "0.76.9", "metro-inspector-proxy": "0.76.9", "metro-minify-uglify": "0.76.9", "metro-react-native-babel-preset": "0.76.9", "metro-resolver": "0.76.9", "metro-runtime": "0.76.9", "metro-source-map": "0.76.9", "metro-symbolicate": "0.76.9", "metro-transform-plugins": "0.76.9", "metro-transform-worker": "0.76.9", "mime-types": "^2.1.27", "node-fetch": "^2.2.0", "nullthrows": "^1.1.1", "rimraf": "^3.0.2", "serialize-error": "^2.1.0", "source-map": "^0.5.6", "strip-ansi": "^6.0.0", "throat": "^5.0.0", "ws": "^7.5.1", "yargs": "^17.6.2"}, "devDependencies": {"@babel/plugin-transform-flow-strip-types": "^7.20.0", "babel-jest": "^29.2.1", "dedent": "^0.7.0", "jest-snapshot": "^26.5.2", "jest-snapshot-serializer-raw": "^1.2.0", "metro-babel-register": "0.76.9", "metro-memory-fs": "0.76.9", "metro-react-native-babel-preset": "0.76.9", "metro-react-native-babel-transformer": "0.76.9", "mock-req": "^0.2.0", "mock-res": "^0.6.0", "stack-trace": "^0.0.10"}, "license": "MIT", "engines": {"node": ">=16"}}