import type { NavigationAction, NavigationState, ParamListBase } from '@react-navigation/routers';
import * as React from 'react';
import type { NavigationHelpers } from './types';
export type ListenerMap = {
    action: ChildActionListener;
    focus: FocusedNavigationListener;
};
export type KeyedListenerMap = {
    getState: GetStateListener;
    beforeRemove: ChildBeforeRemoveListener;
};
export type AddListener = <T extends keyof ListenerMap>(type: T, listener: ListenerMap[T]) => void;
export type AddKeyedListener = <T extends keyof KeyedListenerMap>(type: T, key: string, listener: KeyedListenerMap[T]) => void;
export type ChildActionListener = (action: NavigationAction, visitedNavigators?: Set<string>) => boolean;
export type FocusedNavigationCallback<T> = (navigation: NavigationHelpers<ParamListBase>) => T;
export type FocusedNavigationListener = <T>(callback: FocusedNavigationCallback<T>) => {
    handled: boolean;
    result: T;
};
export type GetStateListener = () => NavigationState;
export type ChildBeforeRemoveListener = (action: NavigationAction) => boolean;
/**
 * Context which holds the required helpers needed to build nested navigators.
 */
declare const NavigationBuilderContext: React.Context<{
    onAction?: ((action: NavigationAction, visitedNavigators?: Set<string>) => boolean) | undefined;
    addListener?: AddListener | undefined;
    addKeyedListener?: AddKeyedListener | undefined;
    onRouteFocus?: ((key: string) => void) | undefined;
    onDispatchAction: (action: NavigationAction, noop: boolean) => void;
    onOptionsChange: (options: object) => void;
    stackRef?: React.MutableRefObject<string | undefined> | undefined;
}>;
export default NavigationBuilderContext;
//# sourceMappingURL=NavigationBuilderContext.d.ts.map