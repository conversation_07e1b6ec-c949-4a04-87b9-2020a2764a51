{"version": 3, "sources": ["../../src/ios/simulator.ts"], "names": ["runSimulatorLoggingProcess", "stdio", "error", "CodeError", "ERR_IOS_CANNOT_START_SYSLOG", "message"], "mappings": ";;;;;;;AAAA;;AACA;;AAEO,SAASA,0BAAT,GAAoD;AACzD,MAAI;AACF,WAAO,0BACL,OADK,EAEL,CACE,QADF,EAEE,OAFF,EAGE,QAHF,EAIE,KAJF,EAKE,QALF,EAME,QANF,EAOE,KAPF,EAQE,SARF,EASE,OATF,CAFK,EAaL;AACEC,MAAAA,KAAK,EAAE;AADT,KAbK,CAAP;AAiBD,GAlBD,CAkBE,OAAOC,KAAP,EAAc;AACd,UAAM,IAAIC,iBAAJ,CAAcC,mCAAd,EAA4CF,KAAD,CAAiBG,OAA5D,CAAN;AACD;AACF", "sourcesContent": ["import { ChildProcess, spawn } from 'child_process';\nimport { CodeError, ERR_IOS_CANNOT_START_SYSLOG } from '../errors';\n\nexport function runSimulatorLoggingProcess(): ChildProcess {\n  try {\n    return spawn(\n      'xcrun',\n      [\n        'simctl',\n        'spawn',\n        'booted',\n        'log',\n        'stream',\n        '--type',\n        'log',\n        '--level',\n        'debug',\n      ],\n      {\n        stdio: 'pipe',\n      }\n    );\n  } catch (error) {\n    throw new CodeError(ERR_IOS_CANNOT_START_SYSLOG, (error as Error).message);\n  }\n}\n"], "file": "simulator.js"}