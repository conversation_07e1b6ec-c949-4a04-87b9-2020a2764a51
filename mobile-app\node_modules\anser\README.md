<!-- Please do not edit this file. Edit the `blah` field in the `package.json` instead. If in doubt, open an issue. -->


















# anser

 [![Support me on Patreon][badge_patreon]][patreon] [![Buy me a book][badge_amazon]][amazon] [![PayPal][badge_paypal_donate]][paypal-donations] [![Ask me anything](https://img.shields.io/badge/ask%20me-anything-1abc9c.svg)](https://github.com/IonicaBizau/ama) [![Travis](https://img.shields.io/travis/IonicaBizau/anser.svg)](https://travis-ci.org/IonicaBizau/anser/) [![Version](https://img.shields.io/npm/v/anser.svg)](https://www.npmjs.com/package/anser) [![Downloads](https://img.shields.io/npm/dt/anser.svg)](https://www.npmjs.com/package/anser) [![Get help on Codementor](https://cdn.codementor.io/badges/get_help_github.svg)](https://www.codementor.io/johnnyb?utm_source=github&utm_medium=button&utm_term=johnnyb&utm_campaign=github)

<a href="https://www.buymeacoffee.com/H96WwChMy" target="_blank"><img src="https://www.buymeacoffee.com/assets/img/custom_images/yellow_img.png" alt="Buy Me A Coffee"></a>







> A low level parser for ANSI sequences.






## :rocket: Features


 - Converts text containing [ANSI color escape codes](http://en.wikipedia.org/wiki/ANSI_escape_code#Colors) into equivalent HTML elements.
 - Allows converting the input into JSON output.
 - HTML escaping
 - Converts links into HTML elements
 - Friendly APIs to use with  virtual dom libraries













## :cloud: Installation

```sh
# Using npm
npm install --save anser

# Using yarn
yarn add anser
```













## :clipboard: Example



```js
const Anser = require("anser");

const txt = "\u001b[38;5;196mHello\u001b[39m \u001b[48;5;226mWorld\u001b[49m";

console.log(Anser.ansiToHtml(txt));
// <span style="color:rgb(255, 0, 0)">Hello</span> <span style="background-color:rgb(255, 255, 0)">World</span>

console.log(Anser.ansiToHtml(txt, { use_classes: true }));
// <span class="ansi-palette-196-fg">Hello</span> <span class="ansi-palette-226-bg">World</span>

console.log(Anser.ansiToJson(txt));
// [ { content: '',
//     fg: null,
//     bg: null,
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: undefined,
//     decoration: null,
//     was_processed: false,
//     isEmpty: [Function: isEmpty] },
//   { content: 'Hello',
//     fg: '255, 0, 0',
//     bg: null,
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: false,
//     decoration: null,
//     was_processed: true,
//     isEmpty: [Function: isEmpty] },
//   { content: ' ',
//     fg: null,
//     bg: null,
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: false,
//     decoration: null,
//     was_processed: false,
//     isEmpty: [Function: isEmpty] },
//   { content: 'World',
//     fg: null,
//     bg: '255, 255, 0',
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: false,
//     decoration: null,
//     was_processed: true,
//     isEmpty: [Function: isEmpty] },
//   { content: '',
//     fg: null,
//     bg: null,
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: false,
//     decoration: null,
//     was_processed: false,
//     isEmpty: [Function: isEmpty] } ]
```






When using **TypeScript** you can do the following:
```ts
import * as Anser from 'anser';
const txt = "\u001b[38;5;196mHello\u001b[39m \u001b[48;5;226mWorld\u001b[49m";
console.log(Anser.ansiToHtml(txt));
// <span style="color:rgb(255, 0, 0)">Hello</span> <span style="background-color:rgb(255, 255, 0)">World</span>
```







## :question: Get Help

There are few ways to get help:



 1. Please [post questions on Stack Overflow](https://stackoverflow.com/questions/ask). You can open issues with questions, as long you add a link to your Stack Overflow question.
 2. For bug reports and feature requests, open issues. :bug:
 3. For direct and quick help, you can [use Codementor](https://www.codementor.io/johnnyb). :rocket:





## :memo: Documentation


### `Anser.escapeForHtml(txt)`
Escape the input HTML.

This does the minimum escaping of text to make it compliant with HTML.
In particular, the '&','<', and '>' characters are escaped. This should
be run prior to `ansiToHtml`.

#### Params

- **String** `txt`: The input text (containing the ANSI snippets).

#### Return
- **String** The escaped html.

### `Anser.linkify(txt)`
Adds the links in the HTML.

This replaces any links in the text with anchor tags that display the
link. The links should have at least one whitespace character
surrounding it. Also, you should apply this after you have run
`ansiToHtml` on the text.

#### Params

- **String** `txt`: The input text.

#### Return
- **String** The HTML containing the <a> tags (unescaped).

### `Anser.ansiToHtml(txt, options)`
This replaces ANSI terminal escape codes with SPAN tags that wrap the
content.

This function only interprets ANSI SGR (Select Graphic Rendition) codes
that can be represented in HTML.
For example, cursor movement codes are ignored and hidden from output.
The default style uses colors that are very close to the prescribed
standard. The standard assumes that the text will have a black
background. These colors are set as inline styles on the SPAN tags.

Another option is to set `use_classes: true` in the options argument.
This will instead set classes on the spans so the colors can be set via
CSS. The class names used are of the format `ansi-*-fg/bg` and
`ansi-bright-*-fg/bg` where `*` is the color name,
i.e black/red/green/yellow/blue/magenta/cyan/white.

#### Params

- **String** `txt`: The input text.
- **Object** `options`: The options passed to the ansiToHTML method.

#### Return
- **String** The HTML output.

### `Anser.ansiToJson(txt, options)`
Converts ANSI input into JSON output.

#### Params

- **String** `txt`: The input text.
- **Object** `options`: The options passed to the ansiToHTML method.

#### Return
- **String** The HTML output.

### `Anser.ansiToText(txt)`
Converts ANSI input into text output.

#### Params

- **String** `txt`: The input text.

#### Return
- **String** The text output.

### `Anser()`
The `Anser` class.

#### Return
- **Anser**

### `setupPalette()`
Sets up the palette.

### `escapeForHtml(txt)`
Escapes the input text.

#### Params

- **String** `txt`: The input text.

#### Return
- **String** The escpaed HTML output.

### `linkify(txt)`
Adds HTML link elements.

#### Params

- **String** `txt`: The input text.

#### Return
- **String** The HTML output containing link elements.

### `ansiToHtml(txt, options)`
Converts ANSI input into HTML output.

#### Params

- **String** `txt`: The input text.
- **Object** `options`: The options passed ot the `process` method.

#### Return
- **String** The HTML output.

### `ansiToJson(txt, options)`
Converts ANSI input into HTML output.

#### Params

- **String** `txt`: The input text.
- **Object** `options`: The options passed ot the `process` method.

#### Return
- **String** The JSON output.

### `ansiToText(txt)`
Converts ANSI input into HTML output.

#### Params

- **String** `txt`: The input text.

#### Return
- **String** The text output.

### `process(txt, options, markup)`
Processes the input.

#### Params

- **String** `txt`: The input text.
- **Object** `options`: An object passed to `processChunk` method, extended with:
 - `json` (Boolean): If `true`, the result will be an object.
 - `use_classes` (Boolean): If `true`, HTML classes will be appended to the HTML output.
- **Boolean** `markup`:

### `processChunkJson(text, options, markup)`
Processes the current chunk into json output.

#### Params

- **String** `text`: The input text.
- **Object** `options`: An object containing the following fields:
 - `json` (Boolean): If `true`, the result will be an object.
 - `use_classes` (Boolean): If `true`, HTML classes will be appended to the HTML output.
- **Boolean** `markup`: If false, the colors will not be parsed.

#### Return
- **Object** The result object:
 - `content` (String): The text.
 - `fg` (String|null): The foreground color.
 - `bg` (String|null): The background color.
 - `fg_truecolor` (String|null): The foreground true color (if 16m color is enabled).
 - `bg_truecolor` (String|null): The background true color (if 16m color is enabled).
 - `clearLine` (Boolean): `true` if a carriageReturn \r was fount at end of line.
 - `was_processed` (Bolean): `true` if the colors were processed, `false` otherwise.
 - `isEmpty` (Function): A function returning `true` if the content is empty, or `false` otherwise.

### `processChunk(text, options, markup)`
Processes the current chunk of text.

#### Params

- **String** `text`: The input text.
- **Object** `options`: An object containing the following fields:
 - `json` (Boolean): If `true`, the result will be an object.
 - `use_classes` (Boolean): If `true`, HTML classes will be appended to the HTML output.
- **Boolean** `markup`: If false, the colors will not be parsed.

#### Return
- **Object|String** The result (object if `json` is wanted back or string otherwise).














## :yum: How to contribute
Have an idea? Found a bug? See [how to contribute][contributing].


## :sparkling_heart: Support my projects
I open-source almost everything I can, and I try to reply to everyone needing help using these projects. Obviously,
this takes time. You can integrate and use these projects in your applications *for free*! You can even change the source code and redistribute (even resell it).

However, if you get some profit from this or just want to encourage me to continue creating stuff, there are few ways you can do it:


 - Starring and sharing the projects you like :rocket:
 - [![Buy me a book][badge_amazon]][amazon]—I love books! I will remember you after years if you buy me one. :grin: :book:
 - [![PayPal][badge_paypal]][paypal-donations]—You can make one-time donations via PayPal. I'll probably buy a ~~coffee~~ tea. :tea:
 - [![Support me on Patreon][badge_patreon]][patreon]—Set up a recurring monthly donation and you will get interesting news about what I'm doing (things that I don't share with everyone).
 - **Bitcoin**—You can send me bitcoins at this address (or scanning the code below): `**********************************`

    ![](https://i.imgur.com/z6OQI95.png)


Thanks! :heart:









## :cake: Thanks
This project is highly based on [`ansi_up`](https://github.com/drudru/ansi_up), by [@drudru](https://github.com/drudru/). Thanks! :cake:








## :dizzy: Where is this library used?
If you are using this library in one of your projects, add it in this list. :sparkles:

 - `react-native`
 - `ansi-to-react`
 - `mesh-devtool`
 - `nuclide-commons-ui`
 - `@next/react-dev-overlay`
 - `transformime`
 - `@viankakrisna/react-dev-utils`
 - `react-webpack-build-helper`
 - `redux-devtools-trace-monitor`
 - `timer-react-dev-utils`
 - `react-dev-utils-extra`
 - `ansi-html-themed`
 - `uyun-react-dev-utils`
 - `react-dev-utils-sebfl-update`
 - `@jjavery/react-error-overlay`
 - `lambda-dev-utils`
 - `react-error-overlay-canary`
 - `@axio/react-dev-utils`
 - `react-error-overlay-dangerous`
 - `@classflow/react-dev-utils`
 - `ansi-to-json`
 - `nuclide`
 - `react-native-okhttp-fork`
 - `@devpodio/console`
 - `ipynb2html`
 - `webpack-isomorphic-dev-middleware`
 - `@theia/console`
 - `stack-frame-overlay`
 - `cycle-dev-utils`
 - `@viankakrisna/react-error-overlay`
 - `callstack-task-react-error-overlay`
 - `callstack-task-react-dev-utils`
 - `ansi-to-react-with-options`
 - `@plansys/react-dev-utils`
 - `zc-react-dev-utils`
 - `@ehyland-org/react-error-overlay`
 - `react-dev-utils-custom-hmr`
 - `ansi-to-react-with-classes`
 - `linklog`
 - `@naze/error`
 - `react-error-guard`
 - `singularityui-tailer`
 - `@unforgiven/react-native`
 - `react-ansi`
 - `@digibear/socket-bridge`
 - `ada-pack`
 - `react-native-hlf-wrapper`
 - `webpack-universal-compiler`
 - `@prague-digi/react-error-overlay`
 - `kunai`
 - `@apardellass/react-native-audio-stream`
 - `react-native-tvos`
 - `react-native-plugpag-wrapper`
 - `@codewars/jest-reporter`











## :scroll: License

[MIT][license] © [Ionică Bizău][website]






[license]: /LICENSE
[website]: https://ionicabizau.net
[contributing]: /CONTRIBUTING.md
[docs]: /DOCUMENTATION.md
[badge_patreon]: https://ionicabizau.github.io/badges/patreon.svg
[badge_amazon]: https://ionicabizau.github.io/badges/amazon.svg
[badge_paypal]: https://ionicabizau.github.io/badges/paypal.svg
[badge_paypal_donate]: https://ionicabizau.github.io/badges/paypal_donate.svg
[patreon]: https://www.patreon.com/ionicabizau
[amazon]: http://amzn.eu/hRo9sIZ
[paypal-donations]: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=RVXDDLKKLQRJW
