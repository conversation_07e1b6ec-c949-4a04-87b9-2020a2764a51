{"version": 3, "names": ["React", "StackGestureRefContext", "useGestureHandlerRef", "ref", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useGestureHandlerRef.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,sBAAsB,MAAM,4BAA4B;AAE/D,eAAe,SAASC,oBAAoB,GAAG;EAC7C,MAAMC,GAAG,GAAGH,KAAK,CAACI,UAAU,CAACH,sBAAsB,CAAC;EAEpD,IAAIE,GAAG,KAAKE,SAAS,EAAE;IACrB,MAAM,IAAIC,KAAK,CACb,4EAA4E,CAC7E;EACH;EAEA,OAAOH,GAAG;AACZ"}