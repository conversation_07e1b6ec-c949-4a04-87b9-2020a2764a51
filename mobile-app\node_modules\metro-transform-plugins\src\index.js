/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

"use strict";

module.exports = {
  // $FlowIgnore[unsafe-getters-setters]
  get addParamsToDefineCall() {
    return require("./addParamsToDefineCall");
  },
  // $FlowIgnore[unsafe-getters-setters]
  get constantFoldingPlugin() {
    return require("./constant-folding-plugin");
  },
  // $FlowIgnore[unsafe-getters-setters]
  get importExportPlugin() {
    return require("./import-export-plugin");
  },
  // $FlowIgnore[unsafe-getters-setters]
  get inlinePlugin() {
    return require("./inline-plugin");
  },
  // $FlowIgnore[unsafe-getters-setters]
  get normalizePseudoGlobals() {
    return require("./normalizePseudoGlobals");
  },
  getTransformPluginCacheKeyFiles: () => [
    require.resolve(__filename),
    require.resolve("./constant-folding-plugin"),
    require.resolve("./import-export-plugin"),
    require.resolve("./inline-plugin"),
    require.resolve("./normalizePseudoGlobals"),
  ],
};
