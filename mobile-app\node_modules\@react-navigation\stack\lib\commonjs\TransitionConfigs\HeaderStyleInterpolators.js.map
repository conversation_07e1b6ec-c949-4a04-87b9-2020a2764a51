{"version": 3, "names": ["add", "Animated", "forUIKit", "current", "next", "layouts", "defaultOffset", "leftSpacing", "leftLabelOffset", "leftLabel", "screen", "width", "titleLeftOffset", "title", "rightOffset", "progress", "interpolate", "inputRange", "outputRange", "extrapolate", "leftButtonStyle", "opacity", "leftLabelStyle", "transform", "translateX", "I18nManager", "getConstants", "isRTL", "rightButtonStyle", "titleStyle", "backgroundStyle", "forFade", "forSlideLeft", "forSlideRight", "forSlideUp", "header", "translateY", "height", "forNoAnimation"], "sourceRoot": "../../../src", "sources": ["TransitionConfigs/HeaderStyleInterpolators.tsx"], "mappings": ";;;;;;;;;;;AAAA;AAOA,MAAM;EAAEA;AAAI,CAAC,GAAGC,qBAAQ;;AAExB;AACA;AACA;AACO,SAASC,QAAQ,OAIwC;EAAA,IAJvC;IACvBC,OAAO;IACPC,IAAI;IACJC;EAC6B,CAAC;EAC9B,MAAMC,aAAa,GAAG,GAAG;EACzB,MAAMC,WAAW,GAAG,EAAE;;EAEtB;EACA;EACA;EACA;EACA;EACA,MAAMC,eAAe,GAAGH,OAAO,CAACI,SAAS,GACrC,CAACJ,OAAO,CAACK,MAAM,CAACC,KAAK,GAAGN,OAAO,CAACI,SAAS,CAACE,KAAK,IAAI,CAAC,GAAGJ,WAAW,GAClED,aAAa;EACjB,MAAMM,eAAe,GAAGP,OAAO,CAACQ,KAAK,GACjC,CAACR,OAAO,CAACK,MAAM,CAACC,KAAK,GAAGN,OAAO,CAACQ,KAAK,CAACF,KAAK,IAAI,CAAC,GAAGJ,WAAW,GAC9DD,aAAa;;EAEjB;EACA;EACA,MAAMQ,WAAW,GAAGT,OAAO,CAACK,MAAM,CAACC,KAAK,GAAG,CAAC;EAE5C,MAAMI,QAAQ,GAAGf,GAAG,CAClBG,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,OAAO;IACLC,eAAe,EAAE;MACfC,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QACzBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvB,CAAC;IACH,CAAC;IACDI,cAAc,EAAE;MACdC,SAAS,EAAE,CACT;QACEC,UAAU,EAAET,QAAQ,CAACC,WAAW,CAAC;UAC/BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrBC,WAAW,EAAEO,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACb,WAAW,EAAE,CAAC,EAAEN,eAAe,CAAC,GAClC,CAACA,eAAe,EAAE,CAAC,EAAE,CAACM,WAAW;QACvC,CAAC;MACH,CAAC;IAEL,CAAC;IACDc,gBAAgB,EAAE;MAChBP,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QACzBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvB,CAAC;IACH,CAAC;IACDW,UAAU,EAAE;MACVR,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QAC5BC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;MAC5B,CAAC,CAAC;MACFK,SAAS,EAAE,CACT;QACEC,UAAU,EAAET,QAAQ,CAACC,WAAW,CAAC;UAC/BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAEO,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACf,eAAe,EAAE,CAAC,EAAEE,WAAW,CAAC,GAClC,CAACA,WAAW,EAAE,CAAC,EAAE,CAACF,eAAe;QACvC,CAAC;MACH,CAAC;IAEL,CAAC;IACDkB,eAAe,EAAE;MACfP,SAAS,EAAE,CACT;QACEC,UAAU,EAAET,QAAQ,CAACC,WAAW,CAAC;UAC/BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrBC,WAAW,EAAEO,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACtB,OAAO,CAACK,MAAM,CAACC,KAAK,EAAE,CAAC,EAAEN,OAAO,CAACK,MAAM,CAACC,KAAK,CAAC,GAChD,CAACN,OAAO,CAACK,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACN,OAAO,CAACK,MAAM,CAACC,KAAK;QACrD,CAAC;MACH,CAAC;IAEL;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAASoB,OAAO,QAGyC;EAAA,IAHxC;IACtB5B,OAAO;IACPC;EAC6B,CAAC;EAC9B,MAAMW,QAAQ,GAAGf,GAAG,CAClBG,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,MAAME,OAAO,GAAGN,QAAQ,CAACC,WAAW,CAAC;IACnCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;EAEF,OAAO;IACLE,eAAe,EAAE;MAAEC;IAAQ,CAAC;IAC5BO,gBAAgB,EAAE;MAAEP;IAAQ,CAAC;IAC7BQ,UAAU,EAAE;MAAER;IAAQ,CAAC;IACvBS,eAAe,EAAE;MACfT,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1BC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;MAC1B,CAAC;IACH;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAASc,YAAY,QAIoC;EAAA,IAJnC;IAC3B7B,OAAO;IACPC,IAAI;IACJC,OAAO,EAAE;MAAEK;IAAO;EACW,CAAC;EAC9B,MAAMK,QAAQ,GAAGf,GAAG,CAClBG,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,MAAMK,UAAU,GAAGT,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAEO,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACjB,MAAM,CAACC,KAAK,EAAE,CAAC,EAAED,MAAM,CAACC,KAAK,CAAC,GAChC,CAACD,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACD,MAAM,CAACC,KAAK;EACrC,CAAC,CAAC;EAEF,MAAMY,SAAS,GAAG,CAAC;IAAEC;EAAW,CAAC,CAAC;EAElC,OAAO;IACLJ,eAAe,EAAE;MAAEG;IAAU,CAAC;IAC9BK,gBAAgB,EAAE;MAAEL;IAAU,CAAC;IAC/BM,UAAU,EAAE;MAAEN;IAAU,CAAC;IACzBO,eAAe,EAAE;MAAEP;IAAU;EAC/B,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAASU,aAAa,QAImC;EAAA,IAJlC;IAC5B9B,OAAO;IACPC,IAAI;IACJC,OAAO,EAAE;MAAEK;IAAO;EACW,CAAC;EAC9B,MAAMK,QAAQ,GAAGf,GAAG,CAClBG,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,MAAMK,UAAU,GAAGT,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAEO,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAACjB,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACD,MAAM,CAACC,KAAK,CAAC,GAChC,CAAC,CAACD,MAAM,CAACC,KAAK,EAAE,CAAC,EAAED,MAAM,CAACC,KAAK;EACrC,CAAC,CAAC;EAEF,MAAMY,SAAS,GAAG,CAAC;IAAEC;EAAW,CAAC,CAAC;EAElC,OAAO;IACLJ,eAAe,EAAE;MAAEG;IAAU,CAAC;IAC9BK,gBAAgB,EAAE;MAAEL;IAAU,CAAC;IAC/BM,UAAU,EAAE;MAAEN;IAAU,CAAC;IACzBO,eAAe,EAAE;MAAEP;IAAU;EAC/B,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAASW,UAAU,QAIsC;EAAA,IAJrC;IACzB/B,OAAO;IACPC,IAAI;IACJC,OAAO,EAAE;MAAE8B;IAAO;EACW,CAAC;EAC9B,MAAMpB,QAAQ,GAAGf,GAAG,CAClBG,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,MAAMiB,UAAU,GAAGrB,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,CAAC,CAACiB,MAAM,CAACE,MAAM,EAAE,CAAC,EAAE,CAACF,MAAM,CAACE,MAAM;EACjD,CAAC,CAAC;EAEF,MAAMd,SAAS,GAAG,CAAC;IAAEa;EAAW,CAAC,CAAC;EAElC,OAAO;IACLhB,eAAe,EAAE;MAAEG;IAAU,CAAC;IAC9BK,gBAAgB,EAAE;MAAEL;IAAU,CAAC;IAC/BM,UAAU,EAAE;MAAEN;IAAU,CAAC;IACzBO,eAAe,EAAE;MAAEP;IAAU;EAC/B,CAAC;AACH;AAEO,SAASe,cAAc,GAAiC;EAC7D,OAAO,CAAC,CAAC;AACX"}