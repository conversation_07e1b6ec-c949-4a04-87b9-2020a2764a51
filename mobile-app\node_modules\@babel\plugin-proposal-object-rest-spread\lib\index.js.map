{"version": 3, "file": "index.js", "sources": ["../src/shouldStoreRHSInTemporaryVariable.ts", "../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\n\nconst {\n  isObjectProperty,\n  isArrayPattern,\n  isObjectPattern,\n  isAssignmentPattern,\n  isRestElement,\n  isIdentifier,\n} = t;\n/**\n * This is a helper function to determine if we should create an intermediate variable\n * such that the RHS of an assignment is not duplicated.\n *\n * See https://github.com/babel/babel/pull/13711#issuecomment-914388382 for discussion\n * on further optimizations.\n */\nexport default function shouldStoreRHSInTemporaryVariable(\n  node: t.LVal,\n): boolean {\n  if (isArrayPattern(node)) {\n    const nonNullElements = node.elements.filter(element => element !== null);\n    if (nonNullElements.length > 1) return true;\n    else return shouldStoreRHSInTemporaryVariable(nonNullElements[0]);\n  } else if (isObjectPattern(node)) {\n    const { properties } = node;\n    if (properties.length > 1) return true;\n    else if (properties.length === 0) return false;\n    else {\n      const firstProperty = properties[0];\n      if (isObjectProperty(firstProperty)) {\n        // the value of the property must be an LVal\n        return shouldStoreRHSInTemporaryVariable(firstProperty.value as t.LVal);\n      } else {\n        return shouldStoreRHSInTemporaryVariable(firstProperty);\n      }\n    }\n  } else if (isAssignmentPattern(node)) {\n    return shouldStoreRHSInTemporaryVariable(node.left);\n  } else if (isRestElement(node)) {\n    if (isIdentifier(node.argument)) return true;\n    return shouldStoreRHSInTemporaryVariable(node.argument);\n  } else {\n    // node is Identifier or MemberExpression\n    return false;\n  }\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxObjectRestSpread from \"@babel/plugin-syntax-object-rest-spread\";\nimport { types as t } from \"@babel/core\";\nimport type { PluginPass } from \"@babel/core\";\nimport type { NodePath, Scope } from \"@babel/traverse\";\nimport { convertFunctionParams } from \"@babel/plugin-transform-parameters\";\nimport { isRequired } from \"@babel/helper-compilation-targets\";\nimport compatData from \"@babel/compat-data/corejs2-built-ins\";\nimport shouldStoreRHSInTemporaryVariable from \"./shouldStoreRHSInTemporaryVariable\";\n\nconst { isAssignmentPattern, isObjectProperty } = t;\n// @babel/types <=7.3.3 counts FOO as referenced in var { x: FOO }.\n// We need to detect this bug to know if \"unused\" means 0 or 1 references.\nif (!process.env.BABEL_8_BREAKING) {\n  const node = t.identifier(\"a\");\n  const property = t.objectProperty(t.identifier(\"key\"), node);\n  const pattern = t.objectPattern([property]);\n\n  // eslint-disable-next-line no-var\n  var ZERO_REFS = t.isReferenced(node, property, pattern) ? 1 : 0;\n}\n\ntype Param = NodePath<t.Function[\"params\"][number]>;\nexport interface Options {\n  useBuiltIns?: boolean;\n  loose?: boolean;\n}\n\nexport default declare((api, opts: Options) => {\n  api.assertVersion(7);\n\n  const targets = api.targets();\n  const supportsObjectAssign = !isRequired(\"es6.object.assign\", targets, {\n    compatData,\n  });\n\n  const { useBuiltIns = supportsObjectAssign, loose = false } = opts;\n\n  if (typeof loose !== \"boolean\") {\n    throw new Error(\".loose must be a boolean, or undefined\");\n  }\n\n  const ignoreFunctionLength = api.assumption(\"ignoreFunctionLength\") ?? loose;\n  const objectRestNoSymbols = api.assumption(\"objectRestNoSymbols\") ?? loose;\n  const pureGetters = api.assumption(\"pureGetters\") ?? loose;\n  const setSpreadProperties = api.assumption(\"setSpreadProperties\") ?? loose;\n\n  function getExtendsHelper(\n    file: PluginPass,\n  ): t.MemberExpression | t.Identifier {\n    return useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : file.addHelper(\"extends\");\n  }\n\n  function hasRestElement(path: Param) {\n    let foundRestElement = false;\n    visitRestElements(path, restElement => {\n      foundRestElement = true;\n      restElement.stop();\n    });\n    return foundRestElement;\n  }\n\n  function hasObjectPatternRestElement(path: NodePath): boolean {\n    let foundRestElement = false;\n    visitRestElements(path, restElement => {\n      if (restElement.parentPath.isObjectPattern()) {\n        foundRestElement = true;\n        restElement.stop();\n      }\n    });\n    return foundRestElement;\n  }\n\n  function visitRestElements(\n    path: NodePath,\n    visitor: (path: NodePath<t.RestElement>) => any,\n  ) {\n    path.traverse({\n      Expression(path) {\n        const { parent, key } = path;\n        if (\n          (isAssignmentPattern(parent) && key === \"right\") ||\n          (isObjectProperty(parent) && parent.computed && key === \"key\")\n        ) {\n          path.skip();\n        }\n      },\n      RestElement: visitor,\n    });\n  }\n\n  function hasSpread(node: t.ObjectExpression): boolean {\n    for (const prop of node.properties) {\n      if (t.isSpreadElement(prop)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // returns an array of all keys of an object, and a status flag indicating if all extracted keys\n  // were converted to stringLiterals or not\n  // e.g. extracts {keys: [\"a\", \"b\", \"3\", ++x], allLiteral: false }\n  // from ast of {a: \"foo\", b, 3: \"bar\", [++x]: \"baz\"}\n  function extractNormalizedKeys(node: t.ObjectPattern) {\n    // RestElement has been removed in createObjectRest\n    const props = node.properties as t.ObjectProperty[];\n    const keys: t.Expression[] = [];\n    let allLiteral = true;\n    let hasTemplateLiteral = false;\n\n    for (const prop of props) {\n      if (t.isIdentifier(prop.key) && !prop.computed) {\n        // since a key {a: 3} is equivalent to {\"a\": 3}, use the latter\n        keys.push(t.stringLiteral(prop.key.name));\n      } else if (t.isTemplateLiteral(prop.key)) {\n        keys.push(t.cloneNode(prop.key));\n        hasTemplateLiteral = true;\n      } else if (t.isLiteral(prop.key)) {\n        keys.push(\n          t.stringLiteral(\n            String(\n              // @ts-expect-error prop.key can not be a NullLiteral\n              prop.key.value,\n            ),\n          ),\n        );\n      } else {\n        // @ts-expect-error private name has been handled by destructuring-private\n        keys.push(t.cloneNode(prop.key));\n        allLiteral = false;\n      }\n    }\n\n    return { keys, allLiteral, hasTemplateLiteral };\n  }\n\n  // replaces impure computed keys with new identifiers\n  // and returns variable declarators of these new identifiers\n  function replaceImpureComputedKeys(\n    properties: NodePath<t.ObjectProperty>[],\n    scope: Scope,\n  ) {\n    const impureComputedPropertyDeclarators: t.VariableDeclarator[] = [];\n    for (const propPath of properties) {\n      // PrivateName is handled in destructuring-private plugin\n      const key = propPath.get(\"key\") as NodePath<t.Expression>;\n      if (propPath.node.computed && !key.isPure()) {\n        const name = scope.generateUidBasedOnNode(key.node);\n        const declarator = t.variableDeclarator(t.identifier(name), key.node);\n        impureComputedPropertyDeclarators.push(declarator);\n        key.replaceWith(t.identifier(name));\n      }\n    }\n    return impureComputedPropertyDeclarators;\n  }\n\n  function removeUnusedExcludedKeys(path: NodePath<t.ObjectPattern>): void {\n    const bindings = path.getOuterBindingIdentifierPaths();\n\n    Object.keys(bindings).forEach(bindingName => {\n      const bindingParentPath = bindings[bindingName].parentPath;\n      if (\n        path.scope.getBinding(bindingName).references >\n          (process.env.BABEL_8_BREAKING ? 0 : ZERO_REFS) ||\n        !bindingParentPath.isObjectProperty()\n      ) {\n        return;\n      }\n      bindingParentPath.remove();\n    });\n  }\n\n  //expects path to an object pattern\n  function createObjectRest(\n    path: NodePath<t.ObjectPattern>,\n    file: PluginPass,\n    objRef: t.Identifier | t.MemberExpression,\n  ): [t.VariableDeclarator[], t.LVal, t.CallExpression] {\n    const props = path.get(\"properties\");\n    const last = props[props.length - 1];\n    t.assertRestElement(last.node);\n    const restElement = t.cloneNode(last.node);\n    last.remove();\n\n    const impureComputedPropertyDeclarators = replaceImpureComputedKeys(\n      path.get(\"properties\") as NodePath<t.ObjectProperty>[],\n      path.scope,\n    );\n    const { keys, allLiteral, hasTemplateLiteral } = extractNormalizedKeys(\n      path.node,\n    );\n\n    if (keys.length === 0) {\n      return [\n        impureComputedPropertyDeclarators,\n        restElement.argument,\n        t.callExpression(getExtendsHelper(file), [\n          t.objectExpression([]),\n          t.sequenceExpression([\n            t.callExpression(file.addHelper(\"objectDestructuringEmpty\"), [\n              t.cloneNode(objRef),\n            ]),\n            t.cloneNode(objRef),\n          ]),\n        ]),\n      ];\n    }\n\n    let keyExpression;\n    if (!allLiteral) {\n      // map to toPropertyKey to handle the possible non-string values\n      keyExpression = t.callExpression(\n        t.memberExpression(t.arrayExpression(keys), t.identifier(\"map\")),\n        [file.addHelper(\"toPropertyKey\")],\n      );\n    } else {\n      keyExpression = t.arrayExpression(keys);\n\n      if (!hasTemplateLiteral && !t.isProgram(path.scope.block)) {\n        // Hoist definition of excluded keys, so that it's not created each time.\n        const program = path.findParent(path => path.isProgram());\n        const id = path.scope.generateUidIdentifier(\"excluded\");\n\n        program.scope.push({\n          id,\n          init: keyExpression,\n          kind: \"const\",\n        });\n\n        keyExpression = t.cloneNode(id);\n      }\n    }\n\n    return [\n      impureComputedPropertyDeclarators,\n      restElement.argument,\n      t.callExpression(\n        file.addHelper(\n          `objectWithoutProperties${objectRestNoSymbols ? \"Loose\" : \"\"}`,\n        ),\n        [t.cloneNode(objRef), keyExpression],\n      ),\n    ];\n  }\n\n  function replaceRestElement(\n    parentPath: NodePath<t.Function | t.CatchClause>,\n    paramPath: NodePath<\n      t.Function[\"params\"][number] | t.AssignmentPattern[\"left\"]\n    >,\n    container?: t.VariableDeclaration[],\n  ): void {\n    if (paramPath.isAssignmentPattern()) {\n      replaceRestElement(parentPath, paramPath.get(\"left\"), container);\n      return;\n    }\n\n    if (paramPath.isArrayPattern() && hasRestElement(paramPath)) {\n      const elements = paramPath.get(\"elements\");\n\n      for (let i = 0; i < elements.length; i++) {\n        replaceRestElement(parentPath, elements[i], container);\n      }\n    }\n\n    if (paramPath.isObjectPattern() && hasRestElement(paramPath)) {\n      const uid = parentPath.scope.generateUidIdentifier(\"ref\");\n\n      const declar = t.variableDeclaration(\"let\", [\n        t.variableDeclarator(paramPath.node, uid),\n      ]);\n\n      if (container) {\n        container.push(declar);\n      } else {\n        parentPath.ensureBlock();\n        parentPath.get(\"body\").unshiftContainer(\"body\", declar);\n      }\n      paramPath.replaceWith(t.cloneNode(uid));\n    }\n  }\n\n  return {\n    name: \"proposal-object-rest-spread\",\n    inherits: syntaxObjectRestSpread.default,\n\n    visitor: {\n      // function a({ b, ...c }) {}\n      Function(path) {\n        const params = path.get(\"params\");\n        const paramsWithRestElement = new Set<number>();\n        const idsInRestParams = new Set();\n        for (let i = 0; i < params.length; ++i) {\n          const param = params[i];\n          if (hasRestElement(param)) {\n            paramsWithRestElement.add(i);\n            for (const name of Object.keys(param.getBindingIdentifiers())) {\n              idsInRestParams.add(name);\n            }\n          }\n        }\n\n        // if true, a parameter exists that has an id in its initializer\n        // that is also an id bound in a rest parameter\n        // example: f({...R}, a = R)\n        let idInRest = false;\n\n        const IdentifierHandler = function (\n          path: NodePath<t.Identifier>,\n          functionScope: Scope,\n        ) {\n          const name = path.node.name;\n          if (\n            path.scope.getBinding(name) === functionScope.getBinding(name) &&\n            idsInRestParams.has(name)\n          ) {\n            idInRest = true;\n            path.stop();\n          }\n        };\n\n        let i: number;\n        for (i = 0; i < params.length && !idInRest; ++i) {\n          const param = params[i];\n          if (!paramsWithRestElement.has(i)) {\n            if (param.isReferencedIdentifier() || param.isBindingIdentifier()) {\n              IdentifierHandler(param, path.scope);\n            } else {\n              param.traverse(\n                {\n                  \"Scope|TypeAnnotation|TSTypeAnnotation\": path => path.skip(),\n                  \"ReferencedIdentifier|BindingIdentifier\": IdentifierHandler,\n                },\n                path.scope,\n              );\n            }\n          }\n        }\n\n        if (!idInRest) {\n          for (let i = 0; i < params.length; ++i) {\n            const param = params[i];\n            if (paramsWithRestElement.has(i)) {\n              replaceRestElement(path, param);\n            }\n          }\n        } else {\n          const shouldTransformParam = (idx: number) =>\n            idx >= i - 1 || paramsWithRestElement.has(idx);\n          convertFunctionParams(\n            path,\n            ignoreFunctionLength,\n            shouldTransformParam,\n            replaceRestElement,\n          );\n        }\n      },\n\n      // adapted from transform-destructuring/src/index.js#pushObjectRest\n      // const { a, ...b } = c;\n      VariableDeclarator(path, file) {\n        if (!path.get(\"id\").isObjectPattern()) {\n          return;\n        }\n\n        let insertionPath = path;\n        const originalPath = path;\n\n        visitRestElements(path.get(\"id\"), path => {\n          if (!path.parentPath.isObjectPattern()) {\n            // Return early if the parent is not an ObjectPattern, but\n            // (for example) an ArrayPattern or Function, because that\n            // means this RestElement is an not an object property.\n            return;\n          }\n\n          if (\n            // skip single-property case, e.g.\n            // const { ...x } = foo();\n            // since the RHS will not be duplicated\n            shouldStoreRHSInTemporaryVariable(originalPath.node.id) &&\n            !t.isIdentifier(originalPath.node.init)\n          ) {\n            // const { a, ...b } = foo();\n            // to avoid calling foo() twice, as a first step convert it to:\n            // const _foo = foo(),\n            //       { a, ...b } = _foo;\n            const initRef = path.scope.generateUidIdentifierBasedOnNode(\n              originalPath.node.init,\n              \"ref\",\n            );\n            // insert _foo = foo()\n            originalPath.insertBefore(\n              t.variableDeclarator(initRef, originalPath.node.init),\n            );\n            // replace foo() with _foo\n            originalPath.replaceWith(\n              t.variableDeclarator(originalPath.node.id, t.cloneNode(initRef)),\n            );\n\n            return;\n          }\n\n          let ref = originalPath.node.init;\n          const refPropertyPath: NodePath<t.ObjectProperty>[] = [];\n          let kind;\n\n          path.findParent((path: NodePath): boolean => {\n            if (path.isObjectProperty()) {\n              refPropertyPath.unshift(path);\n            } else if (path.isVariableDeclarator()) {\n              kind = path.parentPath.node.kind;\n              return true;\n            }\n          });\n\n          const impureObjRefComputedDeclarators = replaceImpureComputedKeys(\n            refPropertyPath,\n            path.scope,\n          );\n          refPropertyPath.forEach(prop => {\n            const { node } = prop;\n            ref = t.memberExpression(\n              ref,\n              t.cloneNode(node.key),\n              node.computed || t.isLiteral(node.key),\n            );\n          });\n\n          //@ts-expect-error: findParent can not apply assertions on result shape\n          const objectPatternPath: NodePath<t.ObjectPattern> = path.findParent(\n            path => path.isObjectPattern(),\n          );\n\n          const [impureComputedPropertyDeclarators, argument, callExpression] =\n            createObjectRest(\n              objectPatternPath,\n              file,\n              ref as t.MemberExpression,\n            );\n\n          if (pureGetters) {\n            removeUnusedExcludedKeys(objectPatternPath);\n          }\n\n          t.assertIdentifier(argument);\n\n          insertionPath.insertBefore(impureComputedPropertyDeclarators);\n\n          insertionPath.insertBefore(impureObjRefComputedDeclarators);\n\n          insertionPath = insertionPath.insertAfter(\n            t.variableDeclarator(argument, callExpression),\n          )[0] as NodePath<t.VariableDeclarator>;\n\n          path.scope.registerBinding(kind, insertionPath);\n\n          if (objectPatternPath.node.properties.length === 0) {\n            objectPatternPath\n              .findParent(\n                path => path.isObjectProperty() || path.isVariableDeclarator(),\n              )\n              .remove();\n          }\n        });\n      },\n\n      // taken from transform-destructuring/src/index.js#visitor\n      // export var { a, ...b } = c;\n      ExportNamedDeclaration(path) {\n        const declaration = path.get(\"declaration\");\n        if (!declaration.isVariableDeclaration()) return;\n\n        const hasRest = declaration\n          .get(\"declarations\")\n          .some(path => hasObjectPatternRestElement(path.get(\"id\")));\n        if (!hasRest) return;\n\n        const specifiers = [];\n\n        for (const name of Object.keys(path.getOuterBindingIdentifiers(true))) {\n          specifiers.push(\n            t.exportSpecifier(t.identifier(name), t.identifier(name)),\n          );\n        }\n\n        // Split the declaration and export list into two declarations so that the variable\n        // declaration can be split up later without needing to worry about not being a\n        // top-level statement.\n        path.replaceWith(declaration.node);\n        path.insertAfter(t.exportNamedDeclaration(null, specifiers));\n      },\n\n      // try {} catch ({a, ...b}) {}\n      CatchClause(path) {\n        const paramPath = path.get(\"param\");\n        replaceRestElement(path, paramPath);\n      },\n\n      // ({a, ...b} = c);\n      AssignmentExpression(path, file) {\n        const leftPath = path.get(\"left\");\n        if (leftPath.isObjectPattern() && hasRestElement(leftPath)) {\n          const nodes = [];\n\n          const refName = path.scope.generateUidBasedOnNode(\n            path.node.right,\n            \"ref\",\n          );\n\n          nodes.push(\n            t.variableDeclaration(\"var\", [\n              t.variableDeclarator(t.identifier(refName), path.node.right),\n            ]),\n          );\n\n          const [impureComputedPropertyDeclarators, argument, callExpression] =\n            createObjectRest(leftPath, file, t.identifier(refName));\n\n          if (impureComputedPropertyDeclarators.length > 0) {\n            nodes.push(\n              t.variableDeclaration(\"var\", impureComputedPropertyDeclarators),\n            );\n          }\n\n          const nodeWithoutSpread = t.cloneNode(path.node);\n          nodeWithoutSpread.right = t.identifier(refName);\n          nodes.push(t.expressionStatement(nodeWithoutSpread));\n          nodes.push(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", argument, callExpression),\n            ),\n          );\n          nodes.push(t.expressionStatement(t.identifier(refName)));\n\n          path.replaceWithMultiple(nodes);\n        }\n      },\n\n      // taken from transform-destructuring/src/index.js#visitor\n      ForXStatement(path: NodePath<t.ForXStatement>) {\n        const { node, scope } = path;\n        const leftPath = path.get(\"left\");\n        const left = node.left;\n\n        if (!hasObjectPatternRestElement(leftPath)) {\n          return;\n        }\n\n        if (!t.isVariableDeclaration(left)) {\n          // for ({a, ...b} of []) {}\n          const temp = scope.generateUidIdentifier(\"ref\");\n\n          node.left = t.variableDeclaration(\"var\", [\n            t.variableDeclarator(temp),\n          ]);\n\n          path.ensureBlock();\n          const body = path.node.body;\n\n          if (body.body.length === 0 && path.isCompletionRecord()) {\n            body.body.unshift(\n              t.expressionStatement(scope.buildUndefinedNode()),\n            );\n          }\n\n          body.body.unshift(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", left, t.cloneNode(temp)),\n            ),\n          );\n        } else {\n          // for (var {a, ...b} of []) {}\n          const pattern = left.declarations[0].id;\n\n          const key = scope.generateUidIdentifier(\"ref\");\n          node.left = t.variableDeclaration(left.kind, [\n            t.variableDeclarator(key, null),\n          ]);\n\n          path.ensureBlock();\n          const body = node.body as t.BlockStatement;\n\n          body.body.unshift(\n            t.variableDeclaration(node.left.kind, [\n              t.variableDeclarator(pattern, t.cloneNode(key)),\n            ]),\n          );\n        }\n      },\n\n      // [{a, ...b}] = c;\n      ArrayPattern(path) {\n        const objectPatterns: t.VariableDeclarator[] = [];\n\n        visitRestElements(path, path => {\n          if (!path.parentPath.isObjectPattern()) {\n            // Return early if the parent is not an ObjectPattern, but\n            // (for example) an ArrayPattern or Function, because that\n            // means this RestElement is an not an object property.\n            return;\n          }\n\n          const objectPattern = path.parentPath;\n\n          const uid = path.scope.generateUidIdentifier(\"ref\");\n          objectPatterns.push(t.variableDeclarator(objectPattern.node, uid));\n\n          objectPattern.replaceWith(t.cloneNode(uid));\n          path.skip();\n        });\n\n        if (objectPatterns.length > 0) {\n          const statementPath = path.getStatementParent();\n          const statementNode = statementPath.node;\n          const kind =\n            statementNode.type === \"VariableDeclaration\"\n              ? statementNode.kind\n              : \"var\";\n          statementPath.insertAfter(\n            t.variableDeclaration(kind, objectPatterns),\n          );\n        }\n      },\n\n      // var a = { ...b, ...c }\n      ObjectExpression(path, file) {\n        if (!hasSpread(path.node)) return;\n\n        let helper: t.Identifier | t.MemberExpression;\n        if (setSpreadProperties) {\n          helper = getExtendsHelper(file);\n        } else {\n          try {\n            helper = file.addHelper(\"objectSpread2\");\n          } catch {\n            // TODO: This is needed to workaround https://github.com/babel/babel/issues/10187\n            // and https://github.com/babel/babel/issues/10179 for older @babel/core versions\n            // where #10187 isn't fixed.\n            this.file.declarations[\"objectSpread2\"] = null;\n\n            // objectSpread2 has been introduced in v7.5.0\n            // We have to maintain backward compatibility.\n            helper = file.addHelper(\"objectSpread\");\n          }\n        }\n\n        let exp: t.CallExpression = null;\n        let props: t.ObjectMember[] = [];\n\n        function make() {\n          const hadProps = props.length > 0;\n          const obj = t.objectExpression(props);\n          props = [];\n\n          if (!exp) {\n            exp = t.callExpression(helper, [obj]);\n            return;\n          }\n\n          // When we can assume that getters are pure and don't depend on\n          // the order of evaluation, we can avoid making multiple calls.\n          if (pureGetters) {\n            if (hadProps) {\n              exp.arguments.push(obj);\n            }\n            return;\n          }\n\n          exp = t.callExpression(t.cloneNode(helper), [\n            exp,\n            // If we have static props, we need to insert an empty object\n            // because the odd arguments are copied with [[Get]], not\n            // [[GetOwnProperty]]\n            ...(hadProps ? [t.objectExpression([]), obj] : []),\n          ]);\n        }\n\n        for (const prop of path.node.properties) {\n          if (t.isSpreadElement(prop)) {\n            make();\n            exp.arguments.push(prop.argument);\n          } else {\n            props.push(prop);\n          }\n        }\n\n        if (props.length) make();\n\n        path.replaceWith(exp);\n      },\n    },\n  };\n});\n"], "names": ["isObjectProperty", "isArrayPattern", "isObjectPattern", "isAssignmentPattern", "isRestElement", "isIdentifier", "t", "shouldStoreRHSInTemporaryVariable", "node", "nonNullElements", "elements", "filter", "element", "length", "properties", "firstProperty", "value", "left", "argument", "identifier", "property", "objectProperty", "pattern", "objectPattern", "ZERO_REFS", "isReferenced", "declare", "api", "opts", "assertVersion", "targets", "supportsObjectAssign", "isRequired", "compatData", "useBuiltIns", "loose", "Error", "ignoreFunctionLength", "assumption", "objectRestNoSymbols", "pureGetters", "setSpreadProperties", "getExtendsHelper", "file", "memberExpression", "addHelper", "hasRestElement", "path", "foundRestElement", "visitRestElements", "restElement", "stop", "hasObjectPatternRestElement", "parentPath", "visitor", "traverse", "Expression", "parent", "key", "computed", "skip", "RestElement", "hasSpread", "prop", "isSpreadElement", "extractNormalizedKeys", "props", "keys", "allLiteral", "hasTemplateLiteral", "push", "stringLiteral", "name", "isTemplateLiteral", "cloneNode", "isLiteral", "String", "replaceImpureComputedKeys", "scope", "impureComputedPropertyDeclarators", "prop<PERSON>ath", "get", "isPure", "generateUidBasedOnNode", "declarator", "variableDeclarator", "replaceWith", "removeUnusedExcludedKeys", "bindings", "getOuterBindingIdentifierPaths", "Object", "for<PERSON>ach", "bindingName", "bindingParentPath", "getBinding", "references", "remove", "createObjectRest", "objRef", "last", "assertRestElement", "callExpression", "objectExpression", "sequenceExpression", "keyExpression", "arrayExpression", "isProgram", "block", "program", "findParent", "id", "generateUidIdentifier", "init", "kind", "replaceRestElement", "<PERSON><PERSON><PERSON><PERSON>", "container", "i", "uid", "declar", "variableDeclaration", "ensureBlock", "unshiftContainer", "inherits", "syntaxObjectRestSpread", "default", "Function", "params", "paramsWithRestElement", "Set", "idsInRestParams", "param", "add", "getBindingIdentifiers", "idInRest", "IdentifierHandler", "functionScope", "has", "isReferencedIdentifier", "isBindingIdentifier", "shouldTransformParam", "idx", "convertFunctionParams", "VariableDeclarator", "insertionPath", "originalPath", "initRef", "generateUidIdentifierBasedOnNode", "insertBefore", "ref", "refProper<PERSON><PERSON>ath", "unshift", "isVariableDeclarator", "impureObjRefComputedDeclarators", "objectPatternPath", "assertIdentifier", "insertAfter", "registerBinding", "ExportNamedDeclaration", "declaration", "isVariableDeclaration", "hasRest", "some", "specifiers", "getOuterBindingIdentifiers", "exportSpecifier", "exportNamedDeclaration", "CatchClause", "AssignmentExpression", "leftPath", "nodes", "refName", "right", "nodeWithoutSpread", "expressionStatement", "assignmentExpression", "replaceWithMultiple", "ForXStatement", "temp", "body", "isCompletionRecord", "buildUndefinedNode", "declarations", "ArrayPattern", "objectPatterns", "statementPath", "getStatementParent", "statementNode", "type", "ObjectExpression", "helper", "exp", "make", "hadProps", "obj", "arguments"], "mappings": ";;;;;;;;;;;;;;;AAEA,MAAM;oBACJA,kBAAgB;EAChBC,cAAc;EACdC,eAAe;uBACfC,qBAAmB;EACnBC,aAAa;AACbC,EAAAA,YAAAA;AACF,CAAC,GAAGC,UAAC,CAAA;AAQU,SAASC,iCAAiC,CACvDC,IAAY,EACH;AACT,EAAA,IAAIP,cAAc,CAACO,IAAI,CAAC,EAAE;AACxB,IAAA,MAAMC,eAAe,GAAGD,IAAI,CAACE,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAK,IAAI,CAAC,CAAA;AACzE,IAAA,IAAIH,eAAe,CAACI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KACvC,OAAON,iCAAiC,CAACE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;AACnE,GAAC,MAAM,IAAIP,eAAe,CAACM,IAAI,CAAC,EAAE;IAChC,MAAM;AAAEM,MAAAA,UAAAA;AAAW,KAAC,GAAGN,IAAI,CAAA;IAC3B,IAAIM,UAAU,CAACD,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAClC,IAAIC,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,KAC1C;AACH,MAAA,MAAME,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC,CAAA;AACnC,MAAA,IAAId,kBAAgB,CAACe,aAAa,CAAC,EAAE;AAEnC,QAAA,OAAOR,iCAAiC,CAACQ,aAAa,CAACC,KAAK,CAAW,CAAA;AACzE,OAAC,MAAM;QACL,OAAOT,iCAAiC,CAACQ,aAAa,CAAC,CAAA;AACzD,OAAA;AACF,KAAA;AACF,GAAC,MAAM,IAAIZ,qBAAmB,CAACK,IAAI,CAAC,EAAE;AACpC,IAAA,OAAOD,iCAAiC,CAACC,IAAI,CAACS,IAAI,CAAC,CAAA;AACrD,GAAC,MAAM,IAAIb,aAAa,CAACI,IAAI,CAAC,EAAE;IAC9B,IAAIH,YAAY,CAACG,IAAI,CAACU,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAA;AAC5C,IAAA,OAAOX,iCAAiC,CAACC,IAAI,CAACU,QAAQ,CAAC,CAAA;AACzD,GAAC,MAAM;AAEL,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF;;ACpCA,MAAM;EAAEf,mBAAmB;AAAEH,EAAAA,gBAAAA;AAAiB,CAAC,GAAGM,UAAC,CAAA;AAGhB;AACjC,EAAA,MAAME,IAAI,GAAGF,UAAC,CAACa,UAAU,CAAC,GAAG,CAAC,CAAA;AAC9B,EAAA,MAAMC,QAAQ,GAAGd,UAAC,CAACe,cAAc,CAACf,UAAC,CAACa,UAAU,CAAC,KAAK,CAAC,EAAEX,IAAI,CAAC,CAAA;EAC5D,MAAMc,OAAO,GAAGhB,UAAC,CAACiB,aAAa,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAA;;AAG3C,EAAA,IAAII,SAAS,GAAGlB,UAAC,CAACmB,YAAY,CAACjB,IAAI,EAAEY,QAAQ,EAAEE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACjE,CAAA;AAQA,YAAeI,yBAAO,CAAC,CAACC,GAAG,EAAEC,IAAa,KAAK;AAAA,EAAA,IAAA,eAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,CAAA;AAC7CD,EAAAA,GAAG,CAACE,aAAa,CAAC,CAAC,CAAC,CAAA;AAEpB,EAAA,MAAMC,OAAO,GAAGH,GAAG,CAACG,OAAO,EAAE,CAAA;EAC7B,MAAMC,oBAAoB,GAAG,CAACC,mCAAU,CAAC,mBAAmB,EAAEF,OAAO,EAAE;AACrEG,gBAAAA,8BAAAA;AACF,GAAC,CAAC,CAAA;EAEF,MAAM;AAAEC,IAAAA,WAAW,GAAGH,oBAAoB;AAAEI,IAAAA,KAAK,GAAG,KAAA;AAAM,GAAC,GAAGP,IAAI,CAAA;AAElE,EAAA,IAAI,OAAOO,KAAK,KAAK,SAAS,EAAE;AAC9B,IAAA,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC,CAAA;AAC3D,GAAA;EAEA,MAAMC,oBAAoB,sBAAGV,GAAG,CAACW,UAAU,CAAC,sBAAsB,CAAC,KAAA,IAAA,GAAA,eAAA,GAAIH,KAAK,CAAA;EAC5E,MAAMI,mBAAmB,uBAAGZ,GAAG,CAACW,UAAU,CAAC,qBAAqB,CAAC,KAAA,IAAA,GAAA,gBAAA,GAAIH,KAAK,CAAA;EAC1E,MAAMK,WAAW,uBAAGb,GAAG,CAACW,UAAU,CAAC,aAAa,CAAC,KAAA,IAAA,GAAA,gBAAA,GAAIH,KAAK,CAAA;EAC1D,MAAMM,mBAAmB,uBAAGd,GAAG,CAACW,UAAU,CAAC,qBAAqB,CAAC,KAAA,IAAA,GAAA,gBAAA,GAAIH,KAAK,CAAA;EAE1E,SAASO,gBAAgB,CACvBC,IAAgB,EACmB;IACnC,OAAOT,WAAW,GACd5B,UAAC,CAACsC,gBAAgB,CAACtC,UAAC,CAACa,UAAU,CAAC,QAAQ,CAAC,EAAEb,UAAC,CAACa,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClEwB,IAAI,CAACE,SAAS,CAAC,SAAS,CAAC,CAAA;AAC/B,GAAA;EAEA,SAASC,cAAc,CAACC,IAAW,EAAE;IACnC,IAAIC,gBAAgB,GAAG,KAAK,CAAA;AAC5BC,IAAAA,iBAAiB,CAACF,IAAI,EAAEG,WAAW,IAAI;AACrCF,MAAAA,gBAAgB,GAAG,IAAI,CAAA;MACvBE,WAAW,CAACC,IAAI,EAAE,CAAA;AACpB,KAAC,CAAC,CAAA;AACF,IAAA,OAAOH,gBAAgB,CAAA;AACzB,GAAA;EAEA,SAASI,2BAA2B,CAACL,IAAc,EAAW;IAC5D,IAAIC,gBAAgB,GAAG,KAAK,CAAA;AAC5BC,IAAAA,iBAAiB,CAACF,IAAI,EAAEG,WAAW,IAAI;AACrC,MAAA,IAAIA,WAAW,CAACG,UAAU,CAACnD,eAAe,EAAE,EAAE;AAC5C8C,QAAAA,gBAAgB,GAAG,IAAI,CAAA;QACvBE,WAAW,CAACC,IAAI,EAAE,CAAA;AACpB,OAAA;AACF,KAAC,CAAC,CAAA;AACF,IAAA,OAAOH,gBAAgB,CAAA;AACzB,GAAA;AAEA,EAAA,SAASC,iBAAiB,CACxBF,IAAc,EACdO,OAA+C,EAC/C;IACAP,IAAI,CAACQ,QAAQ,CAAC;MACZC,UAAU,CAACT,IAAI,EAAE;QACf,MAAM;UAAEU,MAAM;AAAEC,UAAAA,GAAAA;AAAI,SAAC,GAAGX,IAAI,CAAA;QAC5B,IACG5C,mBAAmB,CAACsD,MAAM,CAAC,IAAIC,GAAG,KAAK,OAAO,IAC9C1D,gBAAgB,CAACyD,MAAM,CAAC,IAAIA,MAAM,CAACE,QAAQ,IAAID,GAAG,KAAK,KAAM,EAC9D;UACAX,IAAI,CAACa,IAAI,EAAE,CAAA;AACb,SAAA;OACD;AACDC,MAAAA,WAAW,EAAEP,OAAAA;AACf,KAAC,CAAC,CAAA;AACJ,GAAA;EAEA,SAASQ,SAAS,CAACtD,IAAwB,EAAW;AACpD,IAAA,KAAK,MAAMuD,IAAI,IAAIvD,IAAI,CAACM,UAAU,EAAE;AAClC,MAAA,IAAIR,UAAC,CAAC0D,eAAe,CAACD,IAAI,CAAC,EAAE;AAC3B,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AACF,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;;EAMA,SAASE,qBAAqB,CAACzD,IAAqB,EAAE;AAEpD,IAAA,MAAM0D,KAAK,GAAG1D,IAAI,CAACM,UAAgC,CAAA;IACnD,MAAMqD,IAAoB,GAAG,EAAE,CAAA;IAC/B,IAAIC,UAAU,GAAG,IAAI,CAAA;IACrB,IAAIC,kBAAkB,GAAG,KAAK,CAAA;AAE9B,IAAA,KAAK,MAAMN,IAAI,IAAIG,KAAK,EAAE;AACxB,MAAA,IAAI5D,UAAC,CAACD,YAAY,CAAC0D,IAAI,CAACL,GAAG,CAAC,IAAI,CAACK,IAAI,CAACJ,QAAQ,EAAE;AAE9CQ,QAAAA,IAAI,CAACG,IAAI,CAAChE,UAAC,CAACiE,aAAa,CAACR,IAAI,CAACL,GAAG,CAACc,IAAI,CAAC,CAAC,CAAA;OAC1C,MAAM,IAAIlE,UAAC,CAACmE,iBAAiB,CAACV,IAAI,CAACL,GAAG,CAAC,EAAE;QACxCS,IAAI,CAACG,IAAI,CAAChE,UAAC,CAACoE,SAAS,CAACX,IAAI,CAACL,GAAG,CAAC,CAAC,CAAA;AAChCW,QAAAA,kBAAkB,GAAG,IAAI,CAAA;OAC1B,MAAM,IAAI/D,UAAC,CAACqE,SAAS,CAACZ,IAAI,CAACL,GAAG,CAAC,EAAE;AAChCS,QAAAA,IAAI,CAACG,IAAI,CACPhE,UAAC,CAACiE,aAAa,CACbK,MAAM;AAEJb,QAAAA,IAAI,CAACL,GAAG,CAAC1C,KAAK,CACf,CACF,CACF,CAAA;AACH,OAAC,MAAM;QAELmD,IAAI,CAACG,IAAI,CAAChE,UAAC,CAACoE,SAAS,CAACX,IAAI,CAACL,GAAG,CAAC,CAAC,CAAA;AAChCU,QAAAA,UAAU,GAAG,KAAK,CAAA;AACpB,OAAA;AACF,KAAA;IAEA,OAAO;MAAED,IAAI;MAAEC,UAAU;AAAEC,MAAAA,kBAAAA;KAAoB,CAAA;AACjD,GAAA;;AAIA,EAAA,SAASQ,yBAAyB,CAChC/D,UAAwC,EACxCgE,KAAY,EACZ;IACA,MAAMC,iCAAyD,GAAG,EAAE,CAAA;AACpE,IAAA,KAAK,MAAMC,QAAQ,IAAIlE,UAAU,EAAE;AAEjC,MAAA,MAAM4C,GAAG,GAAGsB,QAAQ,CAACC,GAAG,CAAC,KAAK,CAA2B,CAAA;MACzD,IAAID,QAAQ,CAACxE,IAAI,CAACmD,QAAQ,IAAI,CAACD,GAAG,CAACwB,MAAM,EAAE,EAAE;QAC3C,MAAMV,IAAI,GAAGM,KAAK,CAACK,sBAAsB,CAACzB,GAAG,CAAClD,IAAI,CAAC,CAAA;AACnD,QAAA,MAAM4E,UAAU,GAAG9E,UAAC,CAAC+E,kBAAkB,CAAC/E,UAAC,CAACa,UAAU,CAACqD,IAAI,CAAC,EAAEd,GAAG,CAAClD,IAAI,CAAC,CAAA;AACrEuE,QAAAA,iCAAiC,CAACT,IAAI,CAACc,UAAU,CAAC,CAAA;QAClD1B,GAAG,CAAC4B,WAAW,CAAChF,UAAC,CAACa,UAAU,CAACqD,IAAI,CAAC,CAAC,CAAA;AACrC,OAAA;AACF,KAAA;AACA,IAAA,OAAOO,iCAAiC,CAAA;AAC1C,GAAA;EAEA,SAASQ,wBAAwB,CAACxC,IAA+B,EAAQ;AACvE,IAAA,MAAMyC,QAAQ,GAAGzC,IAAI,CAAC0C,8BAA8B,EAAE,CAAA;IAEtDC,MAAM,CAACvB,IAAI,CAACqB,QAAQ,CAAC,CAACG,OAAO,CAACC,WAAW,IAAI;AAC3C,MAAA,MAAMC,iBAAiB,GAAGL,QAAQ,CAACI,WAAW,CAAC,CAACvC,UAAU,CAAA;AAC1D,MAAA,IACEN,IAAI,CAAC+B,KAAK,CAACgB,UAAU,CAACF,WAAW,CAAC,CAACG,UAAU,GACPvE,SAAU,IAChD,CAACqE,iBAAiB,CAAC7F,gBAAgB,EAAE,EACrC;AACA,QAAA,OAAA;AACF,OAAA;MACA6F,iBAAiB,CAACG,MAAM,EAAE,CAAA;AAC5B,KAAC,CAAC,CAAA;AACJ,GAAA;;AAGA,EAAA,SAASC,gBAAgB,CACvBlD,IAA+B,EAC/BJ,IAAgB,EAChBuD,MAAyC,EACW;AACpD,IAAA,MAAMhC,KAAK,GAAGnB,IAAI,CAACkC,GAAG,CAAC,YAAY,CAAC,CAAA;IACpC,MAAMkB,IAAI,GAAGjC,KAAK,CAACA,KAAK,CAACrD,MAAM,GAAG,CAAC,CAAC,CAAA;AACpCP,IAAAA,UAAC,CAAC8F,iBAAiB,CAACD,IAAI,CAAC3F,IAAI,CAAC,CAAA;IAC9B,MAAM0C,WAAW,GAAG5C,UAAC,CAACoE,SAAS,CAACyB,IAAI,CAAC3F,IAAI,CAAC,CAAA;IAC1C2F,IAAI,CAACH,MAAM,EAAE,CAAA;AAEb,IAAA,MAAMjB,iCAAiC,GAAGF,yBAAyB,CACjE9B,IAAI,CAACkC,GAAG,CAAC,YAAY,CAAC,EACtBlC,IAAI,CAAC+B,KAAK,CACX,CAAA;IACD,MAAM;MAAEX,IAAI;MAAEC,UAAU;AAAEC,MAAAA,kBAAAA;AAAmB,KAAC,GAAGJ,qBAAqB,CACpElB,IAAI,CAACvC,IAAI,CACV,CAAA;AAED,IAAA,IAAI2D,IAAI,CAACtD,MAAM,KAAK,CAAC,EAAE;AACrB,MAAA,OAAO,CACLkE,iCAAiC,EACjC7B,WAAW,CAAChC,QAAQ,EACpBZ,UAAC,CAAC+F,cAAc,CAAC3D,gBAAgB,CAACC,IAAI,CAAC,EAAE,CACvCrC,UAAC,CAACgG,gBAAgB,CAAC,EAAE,CAAC,EACtBhG,UAAC,CAACiG,kBAAkB,CAAC,CACnBjG,UAAC,CAAC+F,cAAc,CAAC1D,IAAI,CAACE,SAAS,CAAC,0BAA0B,CAAC,EAAE,CAC3DvC,UAAC,CAACoE,SAAS,CAACwB,MAAM,CAAC,CACpB,CAAC,EACF5F,UAAC,CAACoE,SAAS,CAACwB,MAAM,CAAC,CACpB,CAAC,CACH,CAAC,CACH,CAAA;AACH,KAAA;AAEA,IAAA,IAAIM,aAAa,CAAA;IACjB,IAAI,CAACpC,UAAU,EAAE;AAEfoC,MAAAA,aAAa,GAAGlG,UAAC,CAAC+F,cAAc,CAC9B/F,UAAC,CAACsC,gBAAgB,CAACtC,UAAC,CAACmG,eAAe,CAACtC,IAAI,CAAC,EAAE7D,UAAC,CAACa,UAAU,CAAC,KAAK,CAAC,CAAC,EAChE,CAACwB,IAAI,CAACE,SAAS,CAAC,eAAe,CAAC,CAAC,CAClC,CAAA;AACH,KAAC,MAAM;AACL2D,MAAAA,aAAa,GAAGlG,UAAC,CAACmG,eAAe,CAACtC,IAAI,CAAC,CAAA;AAEvC,MAAA,IAAI,CAACE,kBAAkB,IAAI,CAAC/D,UAAC,CAACoG,SAAS,CAAC3D,IAAI,CAAC+B,KAAK,CAAC6B,KAAK,CAAC,EAAE;AAEzD,QAAA,MAAMC,OAAO,GAAG7D,IAAI,CAAC8D,UAAU,CAAC9D,IAAI,IAAIA,IAAI,CAAC2D,SAAS,EAAE,CAAC,CAAA;QACzD,MAAMI,EAAE,GAAG/D,IAAI,CAAC+B,KAAK,CAACiC,qBAAqB,CAAC,UAAU,CAAC,CAAA;AAEvDH,QAAAA,OAAO,CAAC9B,KAAK,CAACR,IAAI,CAAC;UACjBwC,EAAE;AACFE,UAAAA,IAAI,EAAER,aAAa;AACnBS,UAAAA,IAAI,EAAE,OAAA;AACR,SAAC,CAAC,CAAA;AAEFT,QAAAA,aAAa,GAAGlG,UAAC,CAACoE,SAAS,CAACoC,EAAE,CAAC,CAAA;AACjC,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,CACL/B,iCAAiC,EACjC7B,WAAW,CAAChC,QAAQ,EACpBZ,UAAC,CAAC+F,cAAc,CACd1D,IAAI,CAACE,SAAS,CACX,CAAyBN,uBAAAA,EAAAA,mBAAmB,GAAG,OAAO,GAAG,EAAG,CAAA,CAAC,CAC/D,EACD,CAACjC,UAAC,CAACoE,SAAS,CAACwB,MAAM,CAAC,EAAEM,aAAa,CAAC,CACrC,CACF,CAAA;AACH,GAAA;AAEA,EAAA,SAASU,kBAAkB,CACzB7D,UAAgD,EAChD8D,SAEC,EACDC,SAAmC,EAC7B;AACN,IAAA,IAAID,SAAS,CAAChH,mBAAmB,EAAE,EAAE;MACnC+G,kBAAkB,CAAC7D,UAAU,EAAE8D,SAAS,CAAClC,GAAG,CAAC,MAAM,CAAC,EAAEmC,SAAS,CAAC,CAAA;AAChE,MAAA,OAAA;AACF,KAAA;IAEA,IAAID,SAAS,CAAClH,cAAc,EAAE,IAAI6C,cAAc,CAACqE,SAAS,CAAC,EAAE;AAC3D,MAAA,MAAMzG,QAAQ,GAAGyG,SAAS,CAAClC,GAAG,CAAC,UAAU,CAAC,CAAA;AAE1C,MAAA,KAAK,IAAIoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3G,QAAQ,CAACG,MAAM,EAAEwG,CAAC,EAAE,EAAE;QACxCH,kBAAkB,CAAC7D,UAAU,EAAE3C,QAAQ,CAAC2G,CAAC,CAAC,EAAED,SAAS,CAAC,CAAA;AACxD,OAAA;AACF,KAAA;IAEA,IAAID,SAAS,CAACjH,eAAe,EAAE,IAAI4C,cAAc,CAACqE,SAAS,CAAC,EAAE;MAC5D,MAAMG,GAAG,GAAGjE,UAAU,CAACyB,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;MAEzD,MAAMQ,MAAM,GAAGjH,UAAC,CAACkH,mBAAmB,CAAC,KAAK,EAAE,CAC1ClH,UAAC,CAAC+E,kBAAkB,CAAC8B,SAAS,CAAC3G,IAAI,EAAE8G,GAAG,CAAC,CAC1C,CAAC,CAAA;AAEF,MAAA,IAAIF,SAAS,EAAE;AACbA,QAAAA,SAAS,CAAC9C,IAAI,CAACiD,MAAM,CAAC,CAAA;AACxB,OAAC,MAAM;QACLlE,UAAU,CAACoE,WAAW,EAAE,CAAA;QACxBpE,UAAU,CAAC4B,GAAG,CAAC,MAAM,CAAC,CAACyC,gBAAgB,CAAC,MAAM,EAAEH,MAAM,CAAC,CAAA;AACzD,OAAA;MACAJ,SAAS,CAAC7B,WAAW,CAAChF,UAAC,CAACoE,SAAS,CAAC4C,GAAG,CAAC,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;EAEA,OAAO;AACL9C,IAAAA,IAAI,EAAE,6BAA6B;IACnCmD,QAAQ,EAAEC,sBAAsB,CAACC,OAAO;AAExCvE,IAAAA,OAAO,EAAE;MAEPwE,QAAQ,CAAC/E,IAAI,EAAE;AACb,QAAA,MAAMgF,MAAM,GAAGhF,IAAI,CAACkC,GAAG,CAAC,QAAQ,CAAC,CAAA;AACjC,QAAA,MAAM+C,qBAAqB,GAAG,IAAIC,GAAG,EAAU,CAAA;AAC/C,QAAA,MAAMC,eAAe,GAAG,IAAID,GAAG,EAAE,CAAA;AACjC,QAAA,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,MAAM,CAAClH,MAAM,EAAE,EAAEwG,CAAC,EAAE;AACtC,UAAA,MAAMc,KAAK,GAAGJ,MAAM,CAACV,CAAC,CAAC,CAAA;AACvB,UAAA,IAAIvE,cAAc,CAACqF,KAAK,CAAC,EAAE;AACzBH,YAAAA,qBAAqB,CAACI,GAAG,CAACf,CAAC,CAAC,CAAA;AAC5B,YAAA,KAAK,MAAM7C,IAAI,IAAIkB,MAAM,CAACvB,IAAI,CAACgE,KAAK,CAACE,qBAAqB,EAAE,CAAC,EAAE;AAC7DH,cAAAA,eAAe,CAACE,GAAG,CAAC5D,IAAI,CAAC,CAAA;AAC3B,aAAA;AACF,WAAA;AACF,SAAA;;QAKA,IAAI8D,QAAQ,GAAG,KAAK,CAAA;AAEpB,QAAA,MAAMC,iBAAiB,GAAG,UACxBxF,IAA4B,EAC5ByF,aAAoB,EACpB;AACA,UAAA,MAAMhE,IAAI,GAAGzB,IAAI,CAACvC,IAAI,CAACgE,IAAI,CAAA;UAC3B,IACEzB,IAAI,CAAC+B,KAAK,CAACgB,UAAU,CAACtB,IAAI,CAAC,KAAKgE,aAAa,CAAC1C,UAAU,CAACtB,IAAI,CAAC,IAC9D0D,eAAe,CAACO,GAAG,CAACjE,IAAI,CAAC,EACzB;AACA8D,YAAAA,QAAQ,GAAG,IAAI,CAAA;YACfvF,IAAI,CAACI,IAAI,EAAE,CAAA;AACb,WAAA;SACD,CAAA;AAED,QAAA,IAAIkE,CAAS,CAAA;AACb,QAAA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,MAAM,CAAClH,MAAM,IAAI,CAACyH,QAAQ,EAAE,EAAEjB,CAAC,EAAE;AAC/C,UAAA,MAAMc,KAAK,GAAGJ,MAAM,CAACV,CAAC,CAAC,CAAA;AACvB,UAAA,IAAI,CAACW,qBAAqB,CAACS,GAAG,CAACpB,CAAC,CAAC,EAAE;YACjC,IAAIc,KAAK,CAACO,sBAAsB,EAAE,IAAIP,KAAK,CAACQ,mBAAmB,EAAE,EAAE;AACjEJ,cAAAA,iBAAiB,CAACJ,KAAK,EAAEpF,IAAI,CAAC+B,KAAK,CAAC,CAAA;AACtC,aAAC,MAAM;cACLqD,KAAK,CAAC5E,QAAQ,CACZ;AACE,gBAAA,uCAAuC,EAAER,IAAI,IAAIA,IAAI,CAACa,IAAI,EAAE;AAC5D,gBAAA,wCAAwC,EAAE2E,iBAAAA;AAC5C,eAAC,EACDxF,IAAI,CAAC+B,KAAK,CACX,CAAA;AACH,aAAA;AACF,WAAA;AACF,SAAA;QAEA,IAAI,CAACwD,QAAQ,EAAE;AACb,UAAA,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,MAAM,CAAClH,MAAM,EAAE,EAAEwG,CAAC,EAAE;AACtC,YAAA,MAAMc,KAAK,GAAGJ,MAAM,CAACV,CAAC,CAAC,CAAA;AACvB,YAAA,IAAIW,qBAAqB,CAACS,GAAG,CAACpB,CAAC,CAAC,EAAE;AAChCH,cAAAA,kBAAkB,CAACnE,IAAI,EAAEoF,KAAK,CAAC,CAAA;AACjC,aAAA;AACF,WAAA;AACF,SAAC,MAAM;AACL,UAAA,MAAMS,oBAAoB,GAAIC,GAAW,IACvCA,GAAG,IAAIxB,CAAC,GAAG,CAAC,IAAIW,qBAAqB,CAACS,GAAG,CAACI,GAAG,CAAC,CAAA;UAChDC,+CAAqB,CACnB/F,IAAI,EACJV,oBAAoB,EACpBuG,oBAAoB,EACpB1B,kBAAkB,CACnB,CAAA;AACH,SAAA;OACD;AAID6B,MAAAA,kBAAkB,CAAChG,IAAI,EAAEJ,IAAI,EAAE;QAC7B,IAAI,CAACI,IAAI,CAACkC,GAAG,CAAC,IAAI,CAAC,CAAC/E,eAAe,EAAE,EAAE;AACrC,UAAA,OAAA;AACF,SAAA;QAEA,IAAI8I,aAAa,GAAGjG,IAAI,CAAA;QACxB,MAAMkG,YAAY,GAAGlG,IAAI,CAAA;QAEzBE,iBAAiB,CAACF,IAAI,CAACkC,GAAG,CAAC,IAAI,CAAC,EAAElC,IAAI,IAAI;AACxC,UAAA,IAAI,CAACA,IAAI,CAACM,UAAU,CAACnD,eAAe,EAAE,EAAE;AAItC,YAAA,OAAA;AACF,WAAA;AAEA,UAAA;AAIEK,UAAAA,iCAAiC,CAAC0I,YAAY,CAACzI,IAAI,CAACsG,EAAE,CAAC,IACvD,CAACxG,UAAC,CAACD,YAAY,CAAC4I,YAAY,CAACzI,IAAI,CAACwG,IAAI,CAAC,EACvC;AAKA,YAAA,MAAMkC,OAAO,GAAGnG,IAAI,CAAC+B,KAAK,CAACqE,gCAAgC,CACzDF,YAAY,CAACzI,IAAI,CAACwG,IAAI,EACtB,KAAK,CACN,CAAA;AAEDiC,YAAAA,YAAY,CAACG,YAAY,CACvB9I,UAAC,CAAC+E,kBAAkB,CAAC6D,OAAO,EAAED,YAAY,CAACzI,IAAI,CAACwG,IAAI,CAAC,CACtD,CAAA;YAEDiC,YAAY,CAAC3D,WAAW,CACtBhF,UAAC,CAAC+E,kBAAkB,CAAC4D,YAAY,CAACzI,IAAI,CAACsG,EAAE,EAAExG,UAAC,CAACoE,SAAS,CAACwE,OAAO,CAAC,CAAC,CACjE,CAAA;AAED,YAAA,OAAA;AACF,WAAA;AAEA,UAAA,IAAIG,GAAG,GAAGJ,YAAY,CAACzI,IAAI,CAACwG,IAAI,CAAA;UAChC,MAAMsC,eAA6C,GAAG,EAAE,CAAA;AACxD,UAAA,IAAIrC,IAAI,CAAA;AAERlE,UAAAA,IAAI,CAAC8D,UAAU,CAAE9D,IAAc,IAAc;AAC3C,YAAA,IAAIA,IAAI,CAAC/C,gBAAgB,EAAE,EAAE;AAC3BsJ,cAAAA,eAAe,CAACC,OAAO,CAACxG,IAAI,CAAC,CAAA;AAC/B,aAAC,MAAM,IAAIA,IAAI,CAACyG,oBAAoB,EAAE,EAAE;AACtCvC,cAAAA,IAAI,GAAGlE,IAAI,CAACM,UAAU,CAAC7C,IAAI,CAACyG,IAAI,CAAA;AAChC,cAAA,OAAO,IAAI,CAAA;AACb,aAAA;AACF,WAAC,CAAC,CAAA;UAEF,MAAMwC,+BAA+B,GAAG5E,yBAAyB,CAC/DyE,eAAe,EACfvG,IAAI,CAAC+B,KAAK,CACX,CAAA;AACDwE,UAAAA,eAAe,CAAC3D,OAAO,CAAC5B,IAAI,IAAI;YAC9B,MAAM;AAAEvD,cAAAA,IAAAA;AAAK,aAAC,GAAGuD,IAAI,CAAA;AACrBsF,YAAAA,GAAG,GAAG/I,UAAC,CAACsC,gBAAgB,CACtByG,GAAG,EACH/I,UAAC,CAACoE,SAAS,CAAClE,IAAI,CAACkD,GAAG,CAAC,EACrBlD,IAAI,CAACmD,QAAQ,IAAIrD,UAAC,CAACqE,SAAS,CAACnE,IAAI,CAACkD,GAAG,CAAC,CACvC,CAAA;AACH,WAAC,CAAC,CAAA;;AAGF,UAAA,MAAMgG,iBAA4C,GAAG3G,IAAI,CAAC8D,UAAU,CAClE9D,IAAI,IAAIA,IAAI,CAAC7C,eAAe,EAAE,CAC/B,CAAA;AAED,UAAA,MAAM,CAAC6E,iCAAiC,EAAE7D,QAAQ,EAAEmF,cAAc,CAAC,GACjEJ,gBAAgB,CACdyD,iBAAiB,EACjB/G,IAAI,EACJ0G,GAAG,CACJ,CAAA;AAEH,UAAA,IAAI7G,WAAW,EAAE;YACf+C,wBAAwB,CAACmE,iBAAiB,CAAC,CAAA;AAC7C,WAAA;AAEApJ,UAAAA,UAAC,CAACqJ,gBAAgB,CAACzI,QAAQ,CAAC,CAAA;AAE5B8H,UAAAA,aAAa,CAACI,YAAY,CAACrE,iCAAiC,CAAC,CAAA;AAE7DiE,UAAAA,aAAa,CAACI,YAAY,CAACK,+BAA+B,CAAC,CAAA;AAE3DT,UAAAA,aAAa,GAAGA,aAAa,CAACY,WAAW,CACvCtJ,UAAC,CAAC+E,kBAAkB,CAACnE,QAAQ,EAAEmF,cAAc,CAAC,CAC/C,CAAC,CAAC,CAAmC,CAAA;UAEtCtD,IAAI,CAAC+B,KAAK,CAAC+E,eAAe,CAAC5C,IAAI,EAAE+B,aAAa,CAAC,CAAA;UAE/C,IAAIU,iBAAiB,CAAClJ,IAAI,CAACM,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE;AAClD6I,YAAAA,iBAAiB,CACd7C,UAAU,CACT9D,IAAI,IAAIA,IAAI,CAAC/C,gBAAgB,EAAE,IAAI+C,IAAI,CAACyG,oBAAoB,EAAE,CAC/D,CACAxD,MAAM,EAAE,CAAA;AACb,WAAA;AACF,SAAC,CAAC,CAAA;OACH;MAID8D,sBAAsB,CAAC/G,IAAI,EAAE;AAC3B,QAAA,MAAMgH,WAAW,GAAGhH,IAAI,CAACkC,GAAG,CAAC,aAAa,CAAC,CAAA;AAC3C,QAAA,IAAI,CAAC8E,WAAW,CAACC,qBAAqB,EAAE,EAAE,OAAA;QAE1C,MAAMC,OAAO,GAAGF,WAAW,CACxB9E,GAAG,CAAC,cAAc,CAAC,CACnBiF,IAAI,CAACnH,IAAI,IAAIK,2BAA2B,CAACL,IAAI,CAACkC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC5D,IAAI,CAACgF,OAAO,EAAE,OAAA;QAEd,MAAME,UAAU,GAAG,EAAE,CAAA;AAErB,QAAA,KAAK,MAAM3F,IAAI,IAAIkB,MAAM,CAACvB,IAAI,CAACpB,IAAI,CAACqH,0BAA0B,CAAC,IAAI,CAAC,CAAC,EAAE;UACrED,UAAU,CAAC7F,IAAI,CACbhE,UAAC,CAAC+J,eAAe,CAAC/J,UAAC,CAACa,UAAU,CAACqD,IAAI,CAAC,EAAElE,UAAC,CAACa,UAAU,CAACqD,IAAI,CAAC,CAAC,CAC1D,CAAA;AACH,SAAA;;AAKAzB,QAAAA,IAAI,CAACuC,WAAW,CAACyE,WAAW,CAACvJ,IAAI,CAAC,CAAA;QAClCuC,IAAI,CAAC6G,WAAW,CAACtJ,UAAC,CAACgK,sBAAsB,CAAC,IAAI,EAAEH,UAAU,CAAC,CAAC,CAAA;OAC7D;MAGDI,WAAW,CAACxH,IAAI,EAAE;AAChB,QAAA,MAAMoE,SAAS,GAAGpE,IAAI,CAACkC,GAAG,CAAC,OAAO,CAAC,CAAA;AACnCiC,QAAAA,kBAAkB,CAACnE,IAAI,EAAEoE,SAAS,CAAC,CAAA;OACpC;AAGDqD,MAAAA,oBAAoB,CAACzH,IAAI,EAAEJ,IAAI,EAAE;AAC/B,QAAA,MAAM8H,QAAQ,GAAG1H,IAAI,CAACkC,GAAG,CAAC,MAAM,CAAC,CAAA;QACjC,IAAIwF,QAAQ,CAACvK,eAAe,EAAE,IAAI4C,cAAc,CAAC2H,QAAQ,CAAC,EAAE;UAC1D,MAAMC,KAAK,GAAG,EAAE,CAAA;AAEhB,UAAA,MAAMC,OAAO,GAAG5H,IAAI,CAAC+B,KAAK,CAACK,sBAAsB,CAC/CpC,IAAI,CAACvC,IAAI,CAACoK,KAAK,EACf,KAAK,CACN,CAAA;AAEDF,UAAAA,KAAK,CAACpG,IAAI,CACRhE,UAAC,CAACkH,mBAAmB,CAAC,KAAK,EAAE,CAC3BlH,UAAC,CAAC+E,kBAAkB,CAAC/E,UAAC,CAACa,UAAU,CAACwJ,OAAO,CAAC,EAAE5H,IAAI,CAACvC,IAAI,CAACoK,KAAK,CAAC,CAC7D,CAAC,CACH,CAAA;UAED,MAAM,CAAC7F,iCAAiC,EAAE7D,QAAQ,EAAEmF,cAAc,CAAC,GACjEJ,gBAAgB,CAACwE,QAAQ,EAAE9H,IAAI,EAAErC,UAAC,CAACa,UAAU,CAACwJ,OAAO,CAAC,CAAC,CAAA;AAEzD,UAAA,IAAI5F,iCAAiC,CAAClE,MAAM,GAAG,CAAC,EAAE;YAChD6J,KAAK,CAACpG,IAAI,CACRhE,UAAC,CAACkH,mBAAmB,CAAC,KAAK,EAAEzC,iCAAiC,CAAC,CAChE,CAAA;AACH,WAAA;UAEA,MAAM8F,iBAAiB,GAAGvK,UAAC,CAACoE,SAAS,CAAC3B,IAAI,CAACvC,IAAI,CAAC,CAAA;UAChDqK,iBAAiB,CAACD,KAAK,GAAGtK,UAAC,CAACa,UAAU,CAACwJ,OAAO,CAAC,CAAA;UAC/CD,KAAK,CAACpG,IAAI,CAAChE,UAAC,CAACwK,mBAAmB,CAACD,iBAAiB,CAAC,CAAC,CAAA;AACpDH,UAAAA,KAAK,CAACpG,IAAI,CACRhE,UAAC,CAACwK,mBAAmB,CACnBxK,UAAC,CAACyK,oBAAoB,CAAC,GAAG,EAAE7J,QAAQ,EAAEmF,cAAc,CAAC,CACtD,CACF,CAAA;AACDqE,UAAAA,KAAK,CAACpG,IAAI,CAAChE,UAAC,CAACwK,mBAAmB,CAACxK,UAAC,CAACa,UAAU,CAACwJ,OAAO,CAAC,CAAC,CAAC,CAAA;AAExD5H,UAAAA,IAAI,CAACiI,mBAAmB,CAACN,KAAK,CAAC,CAAA;AACjC,SAAA;OACD;MAGDO,aAAa,CAAClI,IAA+B,EAAE;QAC7C,MAAM;UAAEvC,IAAI;AAAEsE,UAAAA,KAAAA;AAAM,SAAC,GAAG/B,IAAI,CAAA;AAC5B,QAAA,MAAM0H,QAAQ,GAAG1H,IAAI,CAACkC,GAAG,CAAC,MAAM,CAAC,CAAA;AACjC,QAAA,MAAMhE,IAAI,GAAGT,IAAI,CAACS,IAAI,CAAA;AAEtB,QAAA,IAAI,CAACmC,2BAA2B,CAACqH,QAAQ,CAAC,EAAE;AAC1C,UAAA,OAAA;AACF,SAAA;AAEA,QAAA,IAAI,CAACnK,UAAC,CAAC0J,qBAAqB,CAAC/I,IAAI,CAAC,EAAE;AAElC,UAAA,MAAMiK,IAAI,GAAGpG,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;AAE/CvG,UAAAA,IAAI,CAACS,IAAI,GAAGX,UAAC,CAACkH,mBAAmB,CAAC,KAAK,EAAE,CACvClH,UAAC,CAAC+E,kBAAkB,CAAC6F,IAAI,CAAC,CAC3B,CAAC,CAAA;UAEFnI,IAAI,CAAC0E,WAAW,EAAE,CAAA;AAClB,UAAA,MAAM0D,IAAI,GAAGpI,IAAI,CAACvC,IAAI,CAAC2K,IAAI,CAAA;AAE3B,UAAA,IAAIA,IAAI,CAACA,IAAI,CAACtK,MAAM,KAAK,CAAC,IAAIkC,IAAI,CAACqI,kBAAkB,EAAE,EAAE;AACvDD,YAAAA,IAAI,CAACA,IAAI,CAAC5B,OAAO,CACfjJ,UAAC,CAACwK,mBAAmB,CAAChG,KAAK,CAACuG,kBAAkB,EAAE,CAAC,CAClD,CAAA;AACH,WAAA;UAEAF,IAAI,CAACA,IAAI,CAAC5B,OAAO,CACfjJ,UAAC,CAACwK,mBAAmB,CACnBxK,UAAC,CAACyK,oBAAoB,CAAC,GAAG,EAAE9J,IAAI,EAAEX,UAAC,CAACoE,SAAS,CAACwG,IAAI,CAAC,CAAC,CACrD,CACF,CAAA;AACH,SAAC,MAAM;UAEL,MAAM5J,OAAO,GAAGL,IAAI,CAACqK,YAAY,CAAC,CAAC,CAAC,CAACxE,EAAE,CAAA;AAEvC,UAAA,MAAMpD,GAAG,GAAGoB,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;UAC9CvG,IAAI,CAACS,IAAI,GAAGX,UAAC,CAACkH,mBAAmB,CAACvG,IAAI,CAACgG,IAAI,EAAE,CAC3C3G,UAAC,CAAC+E,kBAAkB,CAAC3B,GAAG,EAAE,IAAI,CAAC,CAChC,CAAC,CAAA;UAEFX,IAAI,CAAC0E,WAAW,EAAE,CAAA;AAClB,UAAA,MAAM0D,IAAI,GAAG3K,IAAI,CAAC2K,IAAwB,CAAA;AAE1CA,UAAAA,IAAI,CAACA,IAAI,CAAC5B,OAAO,CACfjJ,UAAC,CAACkH,mBAAmB,CAAChH,IAAI,CAACS,IAAI,CAACgG,IAAI,EAAE,CACpC3G,UAAC,CAAC+E,kBAAkB,CAAC/D,OAAO,EAAEhB,UAAC,CAACoE,SAAS,CAAChB,GAAG,CAAC,CAAC,CAChD,CAAC,CACH,CAAA;AACH,SAAA;OACD;MAGD6H,YAAY,CAACxI,IAAI,EAAE;QACjB,MAAMyI,cAAsC,GAAG,EAAE,CAAA;AAEjDvI,QAAAA,iBAAiB,CAACF,IAAI,EAAEA,IAAI,IAAI;AAC9B,UAAA,IAAI,CAACA,IAAI,CAACM,UAAU,CAACnD,eAAe,EAAE,EAAE;AAItC,YAAA,OAAA;AACF,WAAA;AAEA,UAAA,MAAMqB,aAAa,GAAGwB,IAAI,CAACM,UAAU,CAAA;UAErC,MAAMiE,GAAG,GAAGvE,IAAI,CAAC+B,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;AACnDyE,UAAAA,cAAc,CAAClH,IAAI,CAAChE,UAAC,CAAC+E,kBAAkB,CAAC9D,aAAa,CAACf,IAAI,EAAE8G,GAAG,CAAC,CAAC,CAAA;UAElE/F,aAAa,CAAC+D,WAAW,CAAChF,UAAC,CAACoE,SAAS,CAAC4C,GAAG,CAAC,CAAC,CAAA;UAC3CvE,IAAI,CAACa,IAAI,EAAE,CAAA;AACb,SAAC,CAAC,CAAA;AAEF,QAAA,IAAI4H,cAAc,CAAC3K,MAAM,GAAG,CAAC,EAAE;AAC7B,UAAA,MAAM4K,aAAa,GAAG1I,IAAI,CAAC2I,kBAAkB,EAAE,CAAA;AAC/C,UAAA,MAAMC,aAAa,GAAGF,aAAa,CAACjL,IAAI,CAAA;AACxC,UAAA,MAAMyG,IAAI,GACR0E,aAAa,CAACC,IAAI,KAAK,qBAAqB,GACxCD,aAAa,CAAC1E,IAAI,GAClB,KAAK,CAAA;UACXwE,aAAa,CAAC7B,WAAW,CACvBtJ,UAAC,CAACkH,mBAAmB,CAACP,IAAI,EAAEuE,cAAc,CAAC,CAC5C,CAAA;AACH,SAAA;OACD;AAGDK,MAAAA,gBAAgB,CAAC9I,IAAI,EAAEJ,IAAI,EAAE;AAC3B,QAAA,IAAI,CAACmB,SAAS,CAACf,IAAI,CAACvC,IAAI,CAAC,EAAE,OAAA;AAE3B,QAAA,IAAIsL,MAAyC,CAAA;AAC7C,QAAA,IAAIrJ,mBAAmB,EAAE;AACvBqJ,UAAAA,MAAM,GAAGpJ,gBAAgB,CAACC,IAAI,CAAC,CAAA;AACjC,SAAC,MAAM;UACL,IAAI;AACFmJ,YAAAA,MAAM,GAAGnJ,IAAI,CAACE,SAAS,CAAC,eAAe,CAAC,CAAA;AAC1C,WAAC,CAAC,OAAM,OAAA,EAAA;YAIN,IAAI,CAACF,IAAI,CAAC2I,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAAA;;AAI9CQ,YAAAA,MAAM,GAAGnJ,IAAI,CAACE,SAAS,CAAC,cAAc,CAAC,CAAA;AACzC,WAAA;AACF,SAAA;QAEA,IAAIkJ,GAAqB,GAAG,IAAI,CAAA;QAChC,IAAI7H,KAAuB,GAAG,EAAE,CAAA;AAEhC,QAAA,SAAS8H,IAAI,GAAG;AACd,UAAA,MAAMC,QAAQ,GAAG/H,KAAK,CAACrD,MAAM,GAAG,CAAC,CAAA;AACjC,UAAA,MAAMqL,GAAG,GAAG5L,UAAC,CAACgG,gBAAgB,CAACpC,KAAK,CAAC,CAAA;AACrCA,UAAAA,KAAK,GAAG,EAAE,CAAA;UAEV,IAAI,CAAC6H,GAAG,EAAE;YACRA,GAAG,GAAGzL,UAAC,CAAC+F,cAAc,CAACyF,MAAM,EAAE,CAACI,GAAG,CAAC,CAAC,CAAA;AACrC,YAAA,OAAA;AACF,WAAA;;AAIA,UAAA,IAAI1J,WAAW,EAAE;AACf,YAAA,IAAIyJ,QAAQ,EAAE;AACZF,cAAAA,GAAG,CAACI,SAAS,CAAC7H,IAAI,CAAC4H,GAAG,CAAC,CAAA;AACzB,aAAA;AACA,YAAA,OAAA;AACF,WAAA;AAEAH,UAAAA,GAAG,GAAGzL,UAAC,CAAC+F,cAAc,CAAC/F,UAAC,CAACoE,SAAS,CAACoH,MAAM,CAAC,EAAE,CAC1CC,GAAG;AAIH,UAAA,IAAIE,QAAQ,GAAG,CAAC3L,UAAC,CAACgG,gBAAgB,CAAC,EAAE,CAAC,EAAE4F,GAAG,CAAC,GAAG,EAAE,CAAC,CACnD,CAAC,CAAA;AACJ,SAAA;QAEA,KAAK,MAAMnI,IAAI,IAAIhB,IAAI,CAACvC,IAAI,CAACM,UAAU,EAAE;AACvC,UAAA,IAAIR,UAAC,CAAC0D,eAAe,CAACD,IAAI,CAAC,EAAE;AAC3BiI,YAAAA,IAAI,EAAE,CAAA;YACND,GAAG,CAACI,SAAS,CAAC7H,IAAI,CAACP,IAAI,CAAC7C,QAAQ,CAAC,CAAA;AACnC,WAAC,MAAM;AACLgD,YAAAA,KAAK,CAACI,IAAI,CAACP,IAAI,CAAC,CAAA;AAClB,WAAA;AACF,SAAA;AAEA,QAAA,IAAIG,KAAK,CAACrD,MAAM,EAAEmL,IAAI,EAAE,CAAA;AAExBjJ,QAAAA,IAAI,CAACuC,WAAW,CAACyG,GAAG,CAAC,CAAA;AACvB,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;"}