{"version": 3, "names": ["CODEGEN_NATIVE_COMPONENT_REGEX", "extractComponentDescriptors", "contents", "match"], "sources": ["../../src/config/extractComponentDescriptors.ts"], "sourcesContent": ["// TODO: avoid the regex and improve reliability by reading this data from codegen schema.json.\n// Need to find a way to run \"generateNewArchitectureFiles\" gradle task after each library's \"generateCodegenSchemaFromJavaScript\" task.\nconst CODEGEN_NATIVE_COMPONENT_REGEX = /codegenNativeComponent(<.*>)?\\s*\\(\\s*[\"'`](\\w+)[\"'`](,?[\\s\\S]+interfaceOnly:\\s*(\\w+))?/m;\n\nexport function extractComponentDescriptors(contents: string) {\n  const match = contents.match(CODEGEN_NATIVE_COMPONENT_REGEX);\n  if (!(match?.[4] === 'true') && match?.[2]) {\n    return `${match[2]}ComponentDescriptor`;\n  }\n  return null;\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA,MAAMA,8BAA8B,GAAG,yFAAyF;AAEzH,SAASC,2BAA2B,CAACC,QAAgB,EAAE;EAC5D,MAAMC,KAAK,GAAGD,QAAQ,CAACC,KAAK,CAACH,8BAA8B,CAAC;EAC5D,IAAI,EAAE,CAAAG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC,MAAK,MAAM,CAAC,KAAIA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC,GAAE;IAC1C,OAAQ,GAAEA,KAAK,CAAC,CAAC,CAAE,qBAAoB;EACzC;EACA,OAAO,IAAI;AACb"}