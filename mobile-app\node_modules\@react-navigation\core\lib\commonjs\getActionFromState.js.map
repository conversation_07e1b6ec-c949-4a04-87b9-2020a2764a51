{"version": 3, "names": ["getActionFromState", "state", "options", "normalizedConfig", "createNormalizedConfigItem", "routes", "index", "slice", "length", "undefined", "key", "name", "initialRouteName", "type", "payload", "route", "current", "config", "screens", "params", "path", "Object", "assign", "initial", "screen", "createNormalizedConfigs", "entries", "reduce", "acc", "k", "v"], "sourceRoot": "../../src", "sources": ["getActionFromState.tsx"], "mappings": ";;;;;;AA8Be,SAASA,kBAAkB,CACxCC,KAAoC,EACpCC,OAAiB,EACmD;EAAA;EACpE;EACA,MAAMC,gBAAgB,GAAGD,OAAO,GAC5BE,0BAA0B,CAACF,OAAO,CAAgC,GAClE,CAAC,CAAC;EAEN,MAAMG,MAAM,GACVJ,KAAK,CAACK,KAAK,IAAI,IAAI,GAAGL,KAAK,CAACI,MAAM,CAACE,KAAK,CAAC,CAAC,EAAEN,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC,GAAGL,KAAK,CAACI,MAAM;EAE7E,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOC,SAAS;EAClB;EAEA,IACE,EACGJ,MAAM,CAACG,MAAM,KAAK,CAAC,IAAIH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,IAClDJ,MAAM,CAACG,MAAM,KAAK,CAAC,IAClBH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,IAC3BJ,MAAM,CAAC,CAAC,CAAC,CAACM,IAAI,MAAKR,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAES,gBAAgB,KACrDP,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAU,CAC/B,EACD;IACA,OAAO;MACLI,IAAI,EAAE,OAAO;MACbC,OAAO,EAAEb;IACX,CAAC;EACH;EAEA,MAAMc,KAAK,GAAGd,KAAK,CAACI,MAAM,CAACJ,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACI,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAElE,IAAIQ,OAAkD,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEd,KAAK;EACrE,IAAIgB,MAA8B,GAAGd,gBAAgB,aAAhBA,gBAAgB,gDAAhBA,gBAAgB,CAAEe,OAAO,0DAAzB,sBAA4BH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEJ,IAAI,CAAC;EAC7E,IAAIQ,MAAM,GAAG;IAAE,GAAGJ,KAAK,CAACI;EAAO,CAG9B;EAED,IAAIL,OAAO,GAAGC,KAAK,GACf;IAAEJ,IAAI,EAAEI,KAAK,CAACJ,IAAI;IAAES,IAAI,EAAEL,KAAK,CAACK,IAAI;IAAED;EAAO,CAAC,GAC9CV,SAAS;EAEb,OAAOO,OAAO,EAAE;IAAA;IACd,IAAIA,OAAO,CAACX,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;MAC/B,OAAOC,SAAS;IAClB;IAEA,MAAMJ,MAAM,GACVW,OAAO,CAACV,KAAK,IAAI,IAAI,GACjBU,OAAO,CAACX,MAAM,CAACE,KAAK,CAAC,CAAC,EAAES,OAAO,CAACV,KAAK,GAAG,CAAC,CAAC,GAC1CU,OAAO,CAACX,MAAM;IAEpB,MAAMU,KAAkD,GACtDV,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;;IAE3B;IACAa,MAAM,CAACC,MAAM,CAACH,MAAM,EAAE;MACpBI,OAAO,EAAEd,SAAS;MAClBe,MAAM,EAAEf,SAAS;MACjBU,MAAM,EAAEV,SAAS;MACjBR,KAAK,EAAEQ;IACT,CAAC,CAAC;IAEF,IAAIJ,MAAM,CAACG,MAAM,KAAK,CAAC,IAAIH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,EAAE;MACtDU,MAAM,CAACI,OAAO,GAAG,IAAI;MACrBJ,MAAM,CAACK,MAAM,GAAGT,KAAK,CAACJ,IAAI;IAC5B,CAAC,MAAM,IACLN,MAAM,CAACG,MAAM,KAAK,CAAC,IACnBH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,IAC3BJ,MAAM,CAAC,CAAC,CAAC,CAACM,IAAI,iBAAKM,MAAM,4CAAN,QAAQL,gBAAgB,KAC3CP,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,EAC3B;MACAU,MAAM,CAACI,OAAO,GAAG,KAAK;MACtBJ,MAAM,CAACK,MAAM,GAAGT,KAAK,CAACJ,IAAI;IAC5B,CAAC,MAAM;MACLQ,MAAM,CAAClB,KAAK,GAAGe,OAAO;MACtB;IACF;IAEA,IAAID,KAAK,CAACd,KAAK,EAAE;MACfkB,MAAM,CAACA,MAAM,GAAG;QAAE,GAAGJ,KAAK,CAACI;MAAO,CAAC;MACnCA,MAAM,GAAGA,MAAM,CAACA,MAGf;IACH,CAAC,MAAM;MACLA,MAAM,CAACC,IAAI,GAAGL,KAAK,CAACK,IAAI;MACxBD,MAAM,CAACA,MAAM,GAAGJ,KAAK,CAACI,MAAM;IAC9B;IAEAH,OAAO,GAAGD,KAAK,CAACd,KAAK;IACrBgB,MAAM,eAAGA,MAAM,iEAAN,SAAQC,OAAO,qDAAf,iBAAkBH,KAAK,CAACJ,IAAI,CAAC;EACxC;EAEA,IAAI,CAACG,OAAO,EAAE;IACZ;EACF;;EAEA;EACA;EACA,OAAO;IACLD,IAAI,EAAE,UAAU;IAChBC;EACF,CAAC;AACH;AAEA,MAAMV,0BAA0B,GAAIa,MAAmC,IACrE,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAI,IAAI,GACxC;EACEL,gBAAgB,EAAEK,MAAM,CAACL,gBAAgB;EACzCM,OAAO,EACLD,MAAM,CAACC,OAAO,IAAI,IAAI,GAClBO,uBAAuB,CAACR,MAAM,CAACC,OAAO,CAAC,GACvCT;AACR,CAAC,GACD,CAAC,CAAC;AAER,MAAMgB,uBAAuB,GAAIvB,OAA8B,IAC7DmB,MAAM,CAACK,OAAO,CAACxB,OAAO,CAAC,CAACyB,MAAM,CAA6B,CAACC,GAAG,WAAa;EAAA,IAAX,CAACC,CAAC,EAAEC,CAAC,CAAC;EACrEF,GAAG,CAACC,CAAC,CAAC,GAAGzB,0BAA0B,CAAC0B,CAAC,CAAC;EACtC,OAAOF,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC"}